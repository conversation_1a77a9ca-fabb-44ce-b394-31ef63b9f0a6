package com.ximalaya.ting.android.im.base.sendrecmanage.innereventbus;

import com.squareup.wire.Message;
import com.ximalaya.ting.android.im.base.interf.connect.IConnStateChangeCallback;
import com.ximalaya.ting.android.im.base.interf.connect.IReceiveByteMsgCallback;
import com.ximalaya.ting.android.im.base.model.ImSendMsgTask;
import com.ximalaya.ting.android.im.base.model.SendDataMsgWrapper;

import java.util.List;

public interface IMsgManageInnerBus {


    //========= ! 连接状态变化的监听  BEGIN ================

    void changeImConnState(int state, String msg);

    /**
     * 注册链路层 连接状态变化 监听
     *
     * @param listener 消息监听
     */
    void registerStateChangeListener(IConnStateChangeCallback listener);

    /**
     * 注销链路层 连接状态变化 监听
     *
     * @param listener 消息监听
     */
    void unRegisterStateChangeListener(IConnStateChangeCallback listener);

    //========= ! 连接状态变化的监听  END ================


    /**
     * 将发送Task
     * @param msg
     * @param callback
     */
    void sendMsgToConnection(Message msg, SendDataMsgWrapper.IWriteByteMsgCallback callback);

    void registerSendMsgToConnListener(ISendMsgToConnListener listener);
    void unRegisterSendMsgToConnListener(ISendMsgToConnListener listener);


    interface ISendMsgToConnListener {
        void onSendmsgToConn(Message msg, SendDataMsgWrapper.IWriteByteMsgCallback callback);
    }






    //1 接收到新的消息发送task
    void addGetNewSendTaskListener(IGetNewSendTaskListener listener);
    void removeGetNewSendMsgListener(IGetNewSendTaskListener listener);

    interface IGetNewSendTaskListener {
        void onGetNewImSendMsg(ImSendMsgTask task);
    }


    //2 移动task到等待队列
    void moveWrittedTaskToConfirmDeque(ImSendMsgTask task);

    void addGetWrittedTaskListener(IGetWrittedTaskListener listener);
    void removeGetWrittedMsgListener(IGetWrittedTaskListener listener);

    interface IGetWrittedTaskListener {
        void onGetNewWrittedMsg(ImSendMsgTask task);
    }


    //3 发送等待超时的上报、和 监听
    void reportSendTaskTimeOut(long token);

    void addGetTaskTimeOutListener(IGetTaskTimeOutListener listener);
    void removeGetTaskTimeOutListener(IGetTaskTimeOutListener listener);

    interface IGetTaskTimeOutListener {
        void onGetTaskTimeOut(long token);
    }


    //4 接收链路层的字节消息数据

    /**
     * 注册链路层的接收消息监听
     *
     * 该监听只会回调原始的字节数据，消息内容需要具体解析
     *
     * @param listener 消息监听
     */
    void registerReceiveByteMsgListener(IReceiveByteMsgCallback listener);

    /**
     * 注销链路层的接收消息监听
     *
     * 该监听只会回调原始的字节数据，消息内容需要具体解析
     *
     * @param listener 消息监听
     */
    void unRegisterReceiveByteMsgListener(IReceiveByteMsgCallback listener);




    //5 接收到的字节数据 解析为 具体的业务的消息
    void dispatchParsedContentMsg(long token, Message contentMsg, int msgWhat);

    void addReceiveContentMessageListener(IReceiveContentMessageListener listener);
    void removeReceiveContentMessageListener(IReceiveContentMessageListener listener);

    interface IReceiveContentMessageListener {
        void onReceiveMessage(long token, Message contentMsg, int msgWhat);
    }


    //6 接收到服务端push的消息
    void dispatchParsedPushMsg(Message contentMsg, String msgName);

    void addReceivePushMessageListener(IReceivePushMessageListener listener);
    void removeReceivePushMessageListener(IReceivePushMessageListener listener);

    interface IReceivePushMessageListener {
        void onReceivePushMessage(Message contentMsg, String name);
    }



    //7 对于发送失败或者超时的发送task，进行重试
    void requestRetrySendTask(List<ImSendMsgTask> taskList, int errcode, String errMsg);

    void addGetRetrySendTaskListener(IGetRetrySendTaskListener listener);
    void removeGetRetrySendTaskListener(IGetRetrySendTaskListener listener);

    interface IGetRetrySendTaskListener {
        void onGetRetrySendTask(List<ImSendMsgTask> taskList, int errcode, String errMsg);
    }


}
