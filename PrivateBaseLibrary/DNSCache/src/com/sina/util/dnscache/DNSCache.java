package com.sina.util.dnscache;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.net.NetworkInfo;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.sina.util.dnscache.cache.DnsCacheManager;
import com.sina.util.dnscache.cache.IDnsCache;
import com.sina.util.dnscache.constants.PreferenceConstantsInDNSCache;
import com.sina.util.dnscache.dnsp.DnsConfig;
import com.sina.util.dnscache.dnsp.DnsManager;
import com.sina.util.dnscache.dnsp.IDns;
import com.sina.util.dnscache.model.DomainInfoWrapper;
import com.sina.util.dnscache.model.DomainModel;
import com.sina.util.dnscache.model.HttpDnsPack;
import com.sina.util.dnscache.model.IpModel;
import com.sina.util.dnscache.net.CheckIpUtil;
import com.sina.util.dnscache.net.networktype.Constants;
import com.sina.util.dnscache.net.networktype.NetworkManager;
import com.sina.util.dnscache.net.networktype.NetworkStateReceiver;
import com.sina.util.dnscache.score.IScore;
import com.sina.util.dnscache.score.ScoreManager;
import com.sina.util.dnscache.speedtest.ISpeedtest;
import com.sina.util.dnscache.speedtest.SpeedtestManager;
import com.sina.util.dnscache.thread.RealTimeThreadPool;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by jack.qin
 *
 * <AUTHOR>
 */
public class DNSCache {

    public static final String TAG = "XIMLAYA_DNSCACHE";
    public static boolean isMainProcess = true;
    @SuppressLint("StaticFieldLeak")
    public static Context sContext;
    @SuppressLint("StaticFieldLeak")
    private static volatile DNSCache instance = null;
    private IDnsCache dnsCacheManager = null;
    private IScore scoreManager = null;
    private IDns dnsManager = null;
    private ISpeedtest speedtestManager = null;
    private ConcurrentHashMap<String, Long> badIpMap = new ConcurrentHashMap<String, Long>();
    public ConcurrentHashMap<String, CheckIpUtil.CheckModel> hasCheckedMap = new ConcurrentHashMap<>();
    public CopyOnWriteArrayList<String> hasGetIpListDomainList = new CopyOnWriteArrayList<>();
    private ConcurrentHashMap<String, UpdateTask> mRunningTasks = new ConcurrentHashMap<String, UpdateTask>();
    private ThreadPoolExecutor poolExecutor;

    private static final int ENVIRONMENT_ON_LINE = 1;
    private static final int ENVIRONMENT_TEST = 4;
    private static final int ENVIRONMENT_UAT = 6;
    private int environmentId = ENVIRONMENT_ON_LINE;

    private SharedPreferences sharedPreferences;

    private boolean gatewaySwitch = true;
    private boolean freeFlowSwitch = true;
    private boolean initParamsSwitch = false;

    private static final String URL_SEPARATOR = "__&__";
    private static final String IP_HOST_SEPARATOR = "__#__";
    /**
     * 定时器Obj
     */
    private Timer timer = null;
    /**
     * 上次测速时间
     */
    private long lastSpeedTime;


    private DNSCache() {

        new Thread("dnscache init") {
            @Override
            public void run() {
                if (DNSCache.isMainProcess) {
                    sharedPreferences = sContext.getSharedPreferences(PreferenceConstantsInDNSCache.DNSCache_FILENAME_INIT_CONFIG_SP, Context.MODE_PRIVATE);
                } else {
                    sharedPreferences = sContext.getSharedPreferences(PreferenceConstantsInDNSCache.DNSCache_FILENAME_INIT_CONFIG_SP_2, Context.MODE_PRIVATE);
                }
                NetworkManager.getInstance();
                DNSCacheConfig dnsCacheConfig = new DNSCacheConfig(mEncryptHelper);
                dnsCacheConfig.initCfg(sContext);

                poolExecutor = new ThreadPoolExecutor(1, 1, 1, TimeUnit.SECONDS, new LinkedBlockingDeque<Runnable>(10), new ThreadPoolExecutor.DiscardPolicy());
                dnsCacheManager = DnsCacheManager.getInstance();
                scoreManager = ScoreManager.getInstanse();
                dnsManager = new DnsManager();
                speedtestManager = new SpeedtestManager();
            }
        }.start();

    }

    public static DNSCache getInstance() {
        if (null == instance) {
            synchronized (DNSCache.class) {
                if (instance == null) {
                    instance = new DNSCache();
                }
            }
        }
        return instance;
    }

    private static String mAppName = "";
    private static String mAppId = "";
    private static IEncryptHelper mEncryptHelper = null;

    public static void init(Context ctx, String appName, String appId, IEncryptHelper encryptor) {
        if (null == ctx) {
            throw new RuntimeException("dnscache init params context can not be null!!!");
        }
        if (TextUtils.isEmpty(appName)) {
            throw new RuntimeException("dnscache init params app name can not be null");
        }
        mAppName = appName;
        if (TextUtils.isEmpty(appId)) {
            throw new RuntimeException("dnscache init params app id can not be null");
        }
        mAppId = appId;
        if (null == encryptor) {
            throw new RuntimeException("dnscache init params encryptor can not be null");
        }
        mEncryptHelper = encryptor;
        if (!ProcessUtil.isMainProcess(ctx)) {
            isMainProcess = false;
        }
        sContext = ctx.getApplicationContext();
        getInstance();
        NetworkStateReceiver.register(sContext);
    }

    public static void init(Context ctx, String appName, String appId) {
        if (null == ctx) {
            throw new RuntimeException("dnscache init params context can not be null!!!");
        }
        if (TextUtils.isEmpty(appName)) {
            throw new RuntimeException("dnscache init params app name can not be null");
        }
        mAppName = appName;
        if (TextUtils.isEmpty(appId)) {
            throw new RuntimeException("dnscache init params app id can not be null");
        }
        mAppId = appId;
        if (!ProcessUtil.isMainProcess(ctx)) {
            isMainProcess = false;
        }
        sContext = ctx.getApplicationContext();
        getInstance();
        NetworkStateReceiver.register(sContext);
    }

    public SharedPreferences getSharedPreferences() {
        return sharedPreferences;
    }

    public boolean isUseDnsCache() {
        return gatewaySwitch && freeFlowSwitch && initParamsSwitch;
    }

    public void setGatewaySwitch(boolean gatewaySwitch) {
        this.gatewaySwitch = gatewaySwitch;
    }

    public void setFreeFlowSwitch(boolean freeFlowSwitch) {
        this.freeFlowSwitch = freeFlowSwitch;
    }

    void setInitParamsSwitch(boolean initParamsSwitch) {
        this.initParamsSwitch = initParamsSwitch;
    }

    public void setEnvironmentId(int environmentId) {
        this.environmentId = environmentId;
    }

    public void setBadIp(String domain, String ipUrl, String turl) {
        String host = Tools.getHostName(ipUrl);
        if (TextUtils.isEmpty(host)) {
            return;
        }
        if (Tools.isIP(host.trim())) {
            badIpMap.put(host, System.currentTimeMillis());
        } else {
            removeBadIp(turl);
        }
    }

    public ConcurrentHashMap<String, Long> getBadIpMap() {
        return badIpMap;
    }

    public void removeBadIp(String url) {
        DomainInfo[] domainInfoList = DNSCache.getInstance().getDomainServerIp(url, false, null).domainInfos;
        if (domainInfoList != null) {
            for (DomainInfo domainInfo : domainInfoList) {
                String ip = domainInfo.ip;
                if (!TextUtils.isEmpty(ip)) {
                    badIpMap.remove(ip);
                }
            }
        }
    }

    public String[][] getDomainServerIpString(String url) {
        StringBuilder sb = new StringBuilder();
        String host = Tools.getHostName(url);
        if (!isUseDnsCache() || !(DNSCache.getInstance().hasGetIpListDomainList.contains(host))) {
            if (isUseDnsCache() && !DNSCache.getInstance().hasGetIpListDomainList.contains(host)) {
                DNSCache.getInstance().getDomainServerIp(url);
            }
            return null;
        }
        DomainInfoWrapper domainInfoWrapper = DNSCache.getInstance().getDomainServerIp(url);

        DomainInfo[] doMainInfoArrary = domainInfoWrapper.domainInfos;
        int num = 0;
        if (doMainInfoArrary != null && doMainInfoArrary.length != 0) {
            num = doMainInfoArrary.length;
            for (int i = 0; i < num; i++) {
                sb.append(doMainInfoArrary[i].url);
                sb.append(IP_HOST_SEPARATOR);
                sb.append(doMainInfoArrary[i].host);
                if (i < num - 1) {
                    sb.append(URL_SEPARATOR);
                }
            }
        }
        List<String> backupDomainList = new ArrayList<>();

        backupDomainList = DNSCacheConfig.domainDetail.get(host).
                toList(DNSCacheConfig.domainDetail.get(host).BACKUP_DOMAIN);
        if (domainInfoWrapper.cacheIsNull) {
            backupDomainList.add(0, host);
        }
        int length = backupDomainList.size();
        for (int i = 0; i < length; i++) {
            if (!TextUtils.isEmpty(backupDomainList.get(i))) {
                if (i == 0 && num > 0) {
                    sb.append(URL_SEPARATOR);
                }
                sb.append(url.replaceFirst(host, backupDomainList.get(i)));
                sb.append(IP_HOST_SEPARATOR);
                sb.append(backupDomainList.get(i));
                if (i < length - 1) {
                    sb.append(URL_SEPARATOR);
                }
            }
        }
        String result = sb.toString();
        if (!TextUtils.isEmpty(result)) {
            String ipsResult = (String) result;
            if (TextUtils.isEmpty(ipsResult)) {
                return null;
            }
            String[] urlsStrArr = ipsResult.split(URL_SEPARATOR);
            String[][] ipsAndHostStrArr = new String[urlsStrArr.length + 1][2];
            for (int i = 0; i < urlsStrArr.length; i++) {
                if (!TextUtils.isEmpty(urlsStrArr[i])) {
                    String[] ipsAndHostArr = urlsStrArr[i].split(IP_HOST_SEPARATOR);
                    ipsAndHostStrArr[i] = ipsAndHostArr;
                    Logger.log("getStaticDomainServerIp ipsAndHostStrArr:" + Arrays.toString(ipsAndHostArr));
                }
            }
            ipsAndHostStrArr[urlsStrArr.length][0] = url;
            return ipsAndHostStrArr;
        }
        return null;
    }

    public static List<String> getDomainServerIpList(DomainInfo[] doMainInfoArrary) {
        if (doMainInfoArrary == null || doMainInfoArrary.length == 0) {
            return null;
        }
        int num = doMainInfoArrary.length;
        if (DnsConfig.ip_retry_num < doMainInfoArrary.length) {
            num = DnsConfig.ip_retry_num;
        }
        List<String> ipList = new ArrayList<String>();
        for (int i = 0; i < num; i++) {
            ipList.add(doMainInfoArrary[i].url);
        }
        return ipList;
    }

    public static void releaseDnsCache() {
        if (instance != null) {
            if (instance.hasCheckedMap != null) {
                if (instance.sharedPreferences != null) {
                    instance.sharedPreferences.edit().putString(PreferenceConstantsInDNSCache.DNSCACHE_CHECKED_IP, new Gson().toJson(instance.hasCheckedMap)).apply();
                }
                instance.hasCheckedMap.clear();
            }
            instance.hasGetIpListDomainList.clear();
            instance.badIpMap.clear();
            if (instance.poolExecutor != null) {
                if (instance.poolExecutor.isShutdown()) {
                    try {
                        instance.poolExecutor.shutdownNow();
                    } catch (Throwable t) {
                        t.printStackTrace();
                    }
                }
            }
            if (instance.timer != null) {
                instance.timer.cancel();
                instance.timer.purge();
            }
            instance = null;
        }
        DNSCacheConfig.domainDetail.clear();
        DNSCacheConfig.domainSupportList.clear();
        DnsConfig.SINA_HTTPDNS_SERVER_API.clear();
    }

    public DomainInfoWrapper getDomainServerIp(String url, boolean useBadip, IDomainCallBack domainCallBack) {
        DomainInfoWrapper domainInfoWrapper = new DomainInfoWrapper();
        if (isUseDnsCache()) {
            if (sContext == null) {
                if (domainCallBack != null) {
                    domainCallBack.onCallBack(null);
                }
                return domainInfoWrapper;
            }
            String host = Tools.getHostName(url);
            if (!isSupport(host)) {
                if (domainCallBack != null) {
                    domainCallBack.onCallBack(null);
                }
                return domainInfoWrapper;
            }
            // 查询domain对应的server ip数组
            final DomainModel domainModel = dnsCacheManager.queryDomainIp(String.valueOf(NetworkManager.getInstance().getSPID()), host, domainInfoWrapper);
            // 如果本地cache 和 内置数据都没有 返回null，然后马上查询数据
            if (null == domainModel) {
                boolean isNeedTestSpeed = !((DnsCacheManager) dnsCacheManager).isHostInIgnoreSpeedHostList(host); // 直播间、连麦、私信、群聊，不需要测速 by WangYixuan
                this.checkUpdates(host, isNeedTestSpeed, domainCallBack, url);
                Logger.d(TAG, "未命中" + url);
                if (null == domainModel) {
                    Logger.d(TAG, "未命中" + "localdns查询失败" + url);
                    return domainInfoWrapper;
                }
            }
//            HttpDnsLogManager.getInstance().writeLog(HttpDnsLogManager.TYPE_INFO, HttpDnsLogManager.ACTION_INFO_DOMAIN, domainModel.tojson(), true);
            ArrayList<IpModel> result = filterInvalidIp(domainModel, domainModel.ipModelArr, useBadip);
            String[] scoreIpArray = scoreManager.ListToArr(result);
            if (scoreIpArray == null || scoreIpArray.length == 0) {
                if (domainCallBack != null) {
                    domainCallBack.onCallBack(null);
                }
                return domainInfoWrapper; // 排序错误 中断后续流程
            }
            if (domainCallBack != null) {
                domainCallBack.onCallBack(domainModel);
            }
            // 转换成需要返回的数据模型
            DomainInfo[] domainInfoList = DomainInfo.DomainInfoFactory(scoreIpArray, url, host);
            if (!DNSCache.getInstance().hasGetIpListDomainList.contains(host)) {
                DNSCache.getInstance().hasGetIpListDomainList.add(host);
            }
            domainInfoWrapper.domainInfos = domainInfoList;
            List<DomainInfo> domainInfos = new ArrayList<>();
            if (url.contains("https")) {
                for (DomainInfo domainInfo : domainInfoWrapper.domainInfos) {
                    if (domainInfo != null && !TextUtils.isEmpty(domainInfo.ip)) {
                        CheckIpUtil.CheckModel checkModel = hasCheckedMap.get(domainInfo.ip);
                        if (checkModel != null) {
                            if ("true".equalsIgnoreCase(checkModel.realCheck)) {
                                domainInfos.add(domainInfo);
                            }
                        }
                    }
                }
                if (domainInfos.size() > 0) {
                    domainInfoWrapper.domainInfos = domainInfos.toArray(new DomainInfo[domainInfos.size()]);
                } else {
                    domainInfoWrapper.cacheIsNull = true;
                    domainInfoWrapper.domainInfos = new DomainInfo[0];
                }
            }

            return domainInfoWrapper;
        } else {
            if (domainCallBack != null) {
                domainCallBack.onCallBack(null);
            }
            return domainInfoWrapper;
        }
    }

    public DomainInfoWrapper getDomainServerIp(String url) {
        return getDomainServerIp(url, true, null);
    }

    public interface IDomainCallBack {
        void onCallBack(DomainModel domainModel);
    }

    private TimerTask task = new TimerTask() {

        @Override
        public void run() {
            //无网络情况下不执行任何后台任务操作
            if (NetworkManager.Util.getNetworkType() == Constants.NETWORK_TYPE_UNCONNECTED || NetworkManager.Util.getNetworkType() == Constants.MOBILE_UNKNOWN) {
                return;
            }
            /************************* 更新过期数据 ********************************/
            Thread.currentThread().setName("HTTP DNS TimerTask");
            final ArrayList<DomainModel> list = dnsCacheManager.getExpireDnsCache();
            for (int k = 0; k < list.size(); k++) {
                DomainModel model = list.get(k);
                checkUpdates(model.domain, false, null, "");
            }

            long now = System.currentTimeMillis();
            /************************* 测速逻辑 ********************************/
            if (now - lastSpeedTime > SpeedtestManager.time_interval - 3) {
                lastSpeedTime = now;
                RealTimeThreadPool.getInstance().execute(new SpeedTestTask());
            }
        }
    };

    /**
     * 过滤无效ip数据
     */
    private ArrayList<IpModel> filterInvalidIp(DomainModel domainModel, ArrayList<IpModel> ipModelArr, boolean useBadip) {
        ArrayList<IpModel> result = new ArrayList<IpModel>();
        for (int k = 0; k < ipModelArr.size(); k++) {
            IpModel ipModel = ipModelArr.get(k);
            if (ipModel != null) {
                if (!("" + SpeedtestManager.MAX_OVERTIME_RTT).equals(ipModel.rtt)) {
                    if (badIpMap.containsKey(ipModel.ip) && useBadip) {
                        Long time = badIpMap.get(ipModel.ip);
                        if (time != null && (System.currentTimeMillis() - time.longValue()) > DnsConfig.retry_interval) {
                            result.add(ipModel);
                        }
                    } else {
                        result.add(ipModel);
                    }
                }
            }
        }
        domainModel.ipModelArr = result;
        return result;
    }

    private boolean isSupport(String host) {
        return ((DNSCacheConfig.domainSupportList.contains(host)));
    }

    /**
     * 从httpdns 服务器重新拉取数据
     */
    private void checkUpdates(String domain, boolean speedTest, final IDomainCallBack domainCallBack, final String url) {
        if (isSupport(domain)) {
            final String host = domain;
            final boolean needSpeedTest = speedTest;
            UpdateTask task = mRunningTasks.get(host);
            if (null == task) {
                UpdateTask updateTask = new UpdateTask(new Runnable() {
                    @Override
                    public void run() {
                        Thread.currentThread().setName("Get Http Dns Data");
                        getHttpDnsData(host, domainCallBack);
                        mRunningTasks.remove(host);
                        if (needSpeedTest) {
                            RealTimeThreadPool.getInstance().execute(new SpeedTestTask());
                        }
                    }
                });
                mRunningTasks.put(host, updateTask);
                updateTask.start(poolExecutor);
            } else {
                long beginTime = task.getBeginTime();
                long now = System.currentTimeMillis();
                // 上次拉取超时，这次再开一个线程继续
                if (now - beginTime > 30 * 1000) {
                    task.start(poolExecutor);
                }
            }
        }
    }

    /**
     * 根据 host 更新数据
     *
     * @param host
     */
    private final void getHttpDnsData(String host, IDomainCallBack domainCallBack) {
        // 获取 httpdns 数据
        try {
            HttpDnsPack httpDnsPack = dnsManager.requestDns(host);
            if (httpDnsPack == null) {
                return;
            }
            Logger.i(DNSCache.TAG, "hasGetIpListDomainList add" + host);
            if (!DNSCache.getInstance().hasGetIpListDomainList.contains(host)) {
                DNSCache.getInstance().hasGetIpListDomainList.add(host);
            }
            // 插入本地 cache

            DomainModel domainModel = dnsCacheManager.insertDnsCache(httpDnsPack);
            if (domainCallBack != null) {
                domainCallBack.onCallBack(domainModel);
            }
        } catch (Exception e) {
            if (domainCallBack != null) {
                domainCallBack.onCallBack(null);
            }
        }
    }

    /**
     * 启动定时器
     */
    public void startTimer() {
        timer = new Timer();
        Logger.i("startTimer", DnsConfig.timer_interval + "");
        timer.schedule(task, 0, DnsConfig.timer_interval);
    }

    /**
     * 网络环境发生变化 刷新缓存数据 暂时先不需要 预处理逻辑。 用户直接请求的时候会更新数据。 （会有一次走本地dns ，
     * 后期优化这个方法，主动请求缓存的数据）
     *
     * @param networkInfo
     */
    public void onNetworkStatusChanged(NetworkInfo networkInfo) {
        if (null != dnsCacheManager) {
            dnsCacheManager.clearMemoryCache();
        }
    }

    static class UpdateTask {
        public Runnable runnable;
        public long beginTime;

        public UpdateTask(Runnable runnable) {
            super();
            this.runnable = runnable;
            this.beginTime = System.currentTimeMillis();
        }

        public void start(ThreadPoolExecutor poolExecutor) {
            try {
                poolExecutor.execute(runnable);
            } catch (Throwable t) {
                t.printStackTrace();
            }
        }

        public long getBeginTime() {
            return beginTime;
        }
    }

    private Map<String, Long> ipSpeedTestMap = new ConcurrentHashMap<>();

    class SpeedTestTask implements Runnable {

        public void run() {
            Thread.currentThread().setName("DNSCache-SpeedTestTask");
            ArrayList<DomainModel> list = dnsCacheManager.getAllMemoryCache();
            updateSpeedInfo(list);
        }

        private void updateSpeedInfo(ArrayList<DomainModel> list) {
            for (int j = 0; j < list.size(); j++) {
                DomainModel domainModel = list.get(j);
                if (domainModel == null) {
                    continue;
                }
                ArrayList<IpModel> ipArray = domainModel.ipModelArr;
                if (ipArray == null || ipArray.size() < 1) {
                    continue;
                }
                for (int i = 0; i < ipArray.size(); i++) {
                    IpModel ipModel = ipArray.get(i);
                    try {
                        if (ipSpeedTestMap.containsKey(ipModel.ip)) {
                            Long lastTime = ipSpeedTestMap.get(ipModel.ip);
                            if ((System.currentTimeMillis() - lastTime.longValue()) < DnsConfig.timer_interval) {
                                continue;
                            }
                        }
                        ipSpeedTestMap.put(ipModel.ip, System.currentTimeMillis());
                    } catch (Exception e) {

                    }
                    int rtt = speedtestManager.speedTest(ipModel.ip, domainModel.domain);
                    boolean succ = rtt > SpeedtestManager.OCUR_ERROR;
                    if (succ) {
                        ipModel.rtt = String.valueOf(rtt);
                        ipModel.success_num = String.valueOf((Integer.valueOf(ipModel.success_num) + 1));
                        ipModel.finally_success_time = String.valueOf(System.currentTimeMillis());
                    } else {
                        ipModel.rtt = String.valueOf(SpeedtestManager.MAX_OVERTIME_RTT);
                        ipModel.err_num = String.valueOf((Integer.valueOf(ipModel.err_num) + 1));
                        ipModel.finally_fail_time = String.valueOf(System.currentTimeMillis());
                    }
                }
                scoreManager.serverIpScore(domainModel);
                dnsCacheManager.setSpeedInfo(domainModel);
            }
        }
    }

    private static final String CHECK_IP_URL_RELEASE = "http://linkeye.ximalaya.com/checkIP";
    private static final String CHECK_IP_URL_TEST = "http://linkeye.test.ximalaya.com/checkIP";
    private static final String CHECK_IP_URL_UAT = "http://linkeye.uat.ximalaya.com/checkIP";

    public String getCheckIpUrl() {
        if (ENVIRONMENT_ON_LINE == environmentId) {
            return CHECK_IP_URL_RELEASE;
        } else if (ENVIRONMENT_TEST == environmentId) {
            return CHECK_IP_URL_TEST;
        } else if (ENVIRONMENT_UAT == environmentId) {
            return CHECK_IP_URL_UAT;
        }
        return CHECK_IP_URL_RELEASE;
    }

    String getInitConfigUrl(String version) {
        if (ENVIRONMENT_ON_LINE == environmentId) {
            return "http://linkeye.ximalaya.com/httpdns/init?app=" + DNSCache.mAppName + "&appId=" + DNSCache.mAppId + "&device=android&version=" + version + "e" + "&timestamp=";
        } else if (ENVIRONMENT_TEST == environmentId) {
            return "http://linkeye.test.ximalaya.com/httpdns/init?app=" + DNSCache.mAppName + "&appId=" + DNSCache.mAppId + "&device=android&version=" + version + "e" + "&timestamp=";
        } else if (ENVIRONMENT_UAT == environmentId) {
            return "http://linkeye.uat.ximalaya.com/httpdns/init?app=" + DNSCache.mAppName + "&appId=" + DNSCache.mAppId + "&device=android&version=" + version + "e" + "&timestamp=";
        }
        return "http://linkeye.ximalaya.com/httpdns/init?app=" + DNSCache.mAppName + "&appId=" + DNSCache.mAppId + "&device=android&version=" + version + "e" + "&timestamp=";
    }

    private static final String POST_ERROR_INFO_URL_RELEASE = "http://xdcs-collector.ximalaya.com/api/v1/realtime";
    private static final String POST_ERROR_INFO_URL_TEST = "http://xdcs-collector.test.ximalaya.com/api/v1/realtime";
    private static final String POST_ERROR_INFO_URL_UAT = "http://xdcs-collector.uat.ximalaya.com/api/v1/realtime";

    public String getPostErrorInfoUrl() {
        if (ENVIRONMENT_ON_LINE == environmentId) {
            return POST_ERROR_INFO_URL_RELEASE;
        } else if (ENVIRONMENT_TEST == environmentId) {
            return POST_ERROR_INFO_URL_TEST;
        } else if (ENVIRONMENT_UAT == environmentId) {
            return POST_ERROR_INFO_URL_UAT;
        }
        return POST_ERROR_INFO_URL_RELEASE;
    }
}
