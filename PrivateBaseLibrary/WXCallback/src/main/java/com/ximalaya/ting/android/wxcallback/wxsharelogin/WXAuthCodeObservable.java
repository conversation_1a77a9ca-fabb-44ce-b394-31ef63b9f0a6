package com.ximalaya.ting.android.wxcallback.wxsharelogin;

import android.support.v4.util.ArrayMap;

import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;

/**
 * 用于统一分发微信登录、分享等code的返回 在XMWXEntryActivity 中 不宜处理关于登录、分享的过多逻辑
 * 因此，消息都将经过这里分发，其他对象把监听器注册到此即可
 * <p>
 * 此类并没有考虑多线程调用的设计 如果你的调用有涉及多线程的环境，就需要考虑下了
 * 如果必须在多线程环境下调用，请通知维护人员修改相关同步方式
 * <p>
 * Created by luh<PERSON> on 2017/8/23.
 *
 * <AUTHOR>
 */

public class WXAuthCodeObservable {

    private ArrayMap<String, IWXAuthObserver> observers = new ArrayMap<>();

    private WXAuthCodeObservable() {
    }

    private static class InnerHolder {

        private static WXAuthCodeObservable AUTH_CODE_OBSERVABLE = new WXAuthCodeObservable();

    }

    public static WXAuthCodeObservable getInstance() {
        return InnerHolder.AUTH_CODE_OBSERVABLE;
    }

    public void registerObserver(IWXAuthObserver observe) throws NullPointerException {
        if (observe == null) {
            throw new NullPointerException("注册监听器不能为空！");
        }
        observers.put(observe.getKey(), observe);
    }

    /**
     * 一般这个方法是用不到  在notify时 直接使用remove 获得observer 的引用 就不用反注册了
     *
     * @param observer
     */
    public void unRegisterObserver(IWXAuthObserver observer) {
        if (observer == null) {
            return;
        }
        unRegisterObserver(observer.getKey());
    }

    public void unRegisterObserver(String key) {
        observers.remove(key);
    }

    /**
     * 一般使用这个方法通知结果即可，如果有更多的参数传递 ，可添加相应的方法
     *
     * @param key
     * @param success
     * @param result
     */
    public void notifyWXAuthBack(String key, boolean success, String result, int code) {
        IWXAuthObserver observer = observers.remove(key);
        if (observer != null) {
            observer.onResult(success, result, code);
        }
    }

    public void notifyWXAuthBack(BaseReq basereq) {
        IWXAuthObserver observer = observers.remove(basereq.transaction);
        if (observer != null) {
            observer.onReq(basereq);
        }
    }

    public void notifyWXAuthBack(BaseResp resp) {
        IWXAuthObserver observer = observers.remove(resp.transaction);
        if (observer != null) {
            observer.onResp(resp);
        }
    }

}
