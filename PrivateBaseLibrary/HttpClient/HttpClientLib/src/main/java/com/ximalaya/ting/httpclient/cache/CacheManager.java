package com.ximalaya.ting.httpclient.cache;

import android.content.Context;
import android.util.LruCache;

import com.ximalaya.ting.httpclient.cache.db.DBHelper;
import com.ximalaya.ting.httpclient.cache.db._Request;

import java.util.Map;

/**
 * Created by sigma on 2018/7/9.
 */
public class CacheManager {

    private DBHelper mDBHelper;
    private LruCache<String, _Request> mLruCache;

    public CacheManager(Context context, int memoryCacheSize, int diskCacheSize) {
        mDBHelper = new DBHelper(context, diskCacheSize);
        mLruCache = new LruCache<String, _Request>(memoryCacheSize) {
            @Override
            protected int sizeOf(String key, _Request value) {
                return value.getBytes();
            }
        };
    }

    public _Request query(String url, Map<String, ?> params, Map<String, ?> headers, CacheControl cacheControl) {
        if (cacheControl.noCache) {
            return null;
        }
        _Request _request = mLruCache.get(url);
        if (_request == null) {
            _request = mDBHelper.query(url);
            if (_request == null) {
                return null;
            }
            mLruCache.put(url, _request);
        }
        if (System.currentTimeMillis() - _request.getUpdateTime() > cacheControl.maxAge * 1000) {
            return null;
        }
        if (!cacheControl.requestComparator.compare(params, _request.getRequestParams(), headers, _request.getRequestHeaders())) {
            return null;
        }
        mDBHelper.updateTime(_request.getId());
        return _request;
    }

    public void update(String url, Map<String, ?> requestParams, Map<String, ?> requestHeaders, int responseCode, String responseBody, Map<String, String> responseHeaders, CacheControl cacheControl) {
        if (cacheControl.noStore) {
            return;
        }
        _Request _request = mLruCache.get(url);
        if (_request == null) {
            _request = new _Request();
            mLruCache.put(url, _request);
        }
        _request.setUrl(url);
        _request.setRequestParams(requestParams);
        _request.setRequestHeaders(requestHeaders);
        _request.setResponseCode(responseCode);
        _request.setResponseBody(responseBody);
        _request.setResponseHeaders(responseHeaders);
        _request.setUpdateTime(System.currentTimeMillis());
        if (cacheControl.noDiskStore) {
            return;
        }
        mDBHelper.update(_request);
    }

    public _Request get(String url) {
        _Request _request = mLruCache.get(url);
        if (_request == null) {
            _request = mDBHelper.query(url);
            if (_request != null) {
                mLruCache.put(url, _request);
            }
        }
        return _request;
    }

    public void invalidate(String url) {
        mLruCache.remove(url);
        mDBHelper.delete(url);
    }

}
