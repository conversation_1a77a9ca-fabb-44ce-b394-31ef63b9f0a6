<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:apps="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/framework_color_ffffff_131313"
    android:orientation="vertical">

    <com.ximalaya.ting.android.read.widgets.NotchConstraintLayout
        android:id="@+id/toolBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/read_gray_underline_white_bg">

        <TextView
            android:id="@+id/textTitle"
            style="@style/read_title_bar_text"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:visibility="visible"
            apps:layout_constraintLeft_toLeftOf="parent"
            apps:layout_constraintRight_toRightOf="parent"
            apps:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/imgBack"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:scaleType="centerInside"
            android:src="@drawable/read_ic_home_back"
            apps:layout_constraintBottom_toBottomOf="@id/textTitle"
            apps:layout_constraintLeft_toLeftOf="parent"
            apps:layout_constraintTop_toTopOf="@id/textTitle" />

        <ImageView
            android:id="@+id/imgSearch"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginRight="16dp"
            android:src="@drawable/read_ic_home_search"
            app:layout_constraintRight_toLeftOf="@+id/imgEdit"
            apps:layout_constraintBottom_toBottomOf="@id/textTitle"
            apps:layout_constraintTop_toTopOf="@id/textTitle" />

        <ImageView
            android:id="@+id/imgEdit"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginRight="16dp"
            android:src="@drawable/read_icon_book_shelf_edit"
            app:layout_constraintRight_toRightOf="parent"
            apps:layout_constraintBottom_toBottomOf="@id/textTitle"
            apps:layout_constraintTop_toTopOf="@id/textTitle" />

    </com.ximalaya.ting.android.read.widgets.NotchConstraintLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ximalaya.ting.android.read.view.StickyNavLayout2
            android:id="@+id/stickyNavLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <Space
                android:id="@+id/read_host_id_stickynavlayout_topview"
                android:layout_width="match_parent"
                android:layout_height="100dp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/read_host_id_stickynavlayout_indicator"
                android:layout_width="match_parent"
                android:layout_height="46dp">

                <com.ximalaya.ting.android.read.widgets.ReadRankPageSlidingTab
                    android:id="@+id/pagerTab"
                    android:layout_width="0dp"
                    android:layout_height="46dp"
                    android:background="@color/read_transparent"
                    android:textSize="18sp"
                    app:layout_constraintRight_toLeftOf="@id/textReadDuration"
                    app:layout_constraintTop_toTopOf="parent"
                    apps:layout_constraintLeft_toLeftOf="parent"
                    apps:pstsActivateTabTextBold="true"
                    apps:pstsActivateTextColor="@color/read_color_333333_dcdcdc"
                    apps:pstsDeactivateTextColor="@color/read_color_666666_8d8d91"
                    apps:pstsDividerColor="@color/read_transparent"
                    apps:pstsIndicatorColor="@color/read_color_ffffffff"
                    apps:pstsIndicatorHeight="4dp"
                    apps:pstsIndicatorWidth="20dp"
                    apps:pstsShouldExpand="false"
                    apps:pstsSmoothScroll="true"
                    apps:pstsTabBackground="@color/read_transparent"
                    apps:pstsTabPaddingLeftRight="16dp"
                    apps:pstsTextAllCaps="false"
                    apps:pstsUnderlineColor="@color/read_transparent"
                    apps:pstsUnderlineHeight="4dp" />

                <TextView
                    android:id="@+id/textReadDuration"
                    style="@style/read_tv_12_999"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="16dp"
                    android:drawableLeft="@drawable/read_icon_book_shelf_duration"
                    android:drawablePadding="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="本周已读6时43分钟" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.ximalaya.ting.android.framework.view.ViewPagerInScroll
                android:id="@+id/read_host_id_stickynavlayout_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:focusable="false"
                android:focusableInTouchMode="false" />
        </com.ximalaya.ting.android.read.view.StickyNavLayout2>

        <include layout="@layout/read_layout_book_shelf_top_banner" />

        <ImageView
            android:id="@+id/imgGoToCity"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="62dp"
            android:src="@drawable/read_icon_go_to_book_city" />

    </RelativeLayout>

</LinearLayout>