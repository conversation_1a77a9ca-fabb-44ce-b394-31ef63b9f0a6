<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.ximalaya.ting.android.read.widgets.pageview.NewReadTitleBarView
        android:id="@+id/readChapterView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />


    <com.ximalaya.ting.android.read.widgets.baserecyclerviewadapter.RefreshLayout
        android:id="@+id/scroll_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.ximalaya.ting.android.read.widgets.pageview.ReadInterceptRecyclerView
            android:id="@+id/rv_page"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

    </com.ximalaya.ting.android.read.widgets.baserecyclerviewadapter.RefreshLayout>


    <com.ximalaya.ting.android.read.widgets.pageview.cus.NewReadBottomBatteryView
        android:id="@+id/readBatteryView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>