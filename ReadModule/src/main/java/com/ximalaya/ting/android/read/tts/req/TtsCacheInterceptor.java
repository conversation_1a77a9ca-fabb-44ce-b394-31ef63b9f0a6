package com.ximalaya.ting.android.read.tts.req;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Response;

/**
 * @CreateDate: 2022/6/30 11:25 上午
 * @Author: ypp
 * @Description:
 */
public class TtsCacheInterceptor implements Interceptor {

    private static final String CACHE_CONTROL = "Cache-Control";
    private static final int MAX_AGE = 60;
    private static final String STR_MAX_AGE = "max-age=" + MAX_AGE;


    @Override
    public Response intercept(Chain chain) throws IOException {
        Response response = chain.proceed(chain.request());
        return response.newBuilder()
            .removeHeader("pragma")
            .addHeader(CACHE_CONTROL, STR_MAX_AGE)
            .build();
    }
}
