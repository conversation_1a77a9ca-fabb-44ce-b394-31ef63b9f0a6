package com.ximalaya.ting.android.read.utils;

import android.text.TextUtils;

import com.ximalaya.ting.android.read.bean.HomeContentBean;
import com.ximalaya.ting.android.read.bean.IndexBean;
import com.ximalaya.ting.android.read.fragment.ReadHomePageItemFragment;
import com.ximalaya.ting.android.read.listener.IReadHomeItemActionListener;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;

/**
 * @CreateDate: 2022/3/14 3:53 下午
 * @Author: ypp
 * @Description:
 */
public class HomePageBookExposeManager {

    public static void expose(String xmRequestId, Object data, String tabName, IReadHomeItemActionListener baseFragment2) {
        if (data != null && baseFragment2 != null) {
            String moduleId = "";
            String pageTitle = "";
            String bookId = "";
            String position = "";
            String tabNameStr = "";
            String bookName = "";
            String authorName = "";
            String creatorUid = "";
            if (!TextUtils.isEmpty(tabName)) {
                tabNameStr = tabName;
            }
            if (data instanceof HomeContentBean) {
                moduleId = ((HomeContentBean) data).getNavId();
                bookId = ((HomeContentBean) data).getId();
                position = String.valueOf(((HomeContentBean) data).getInnerPosition() + 1);
                bookName = ((HomeContentBean) data).getName();
                authorName = ((HomeContentBean) data).getAuthor();
                if (TextUtils.isEmpty(xmRequestId)) {
                    xmRequestId = ((HomeContentBean) data).getXmRequestId();
                }
            } else if (data instanceof IndexBean.DataBean.DataListsBean) {
                moduleId = ((IndexBean.DataBean.DataListsBean) data).getNavId();
                bookId = ((IndexBean.DataBean.DataListsBean) data).getBookId();
                position = String.valueOf(((IndexBean.DataBean.DataListsBean) data).getInnerPosition() + 1);
                bookName = ((IndexBean.DataBean.DataListsBean) data).getBookName();
                authorName = ((IndexBean.DataBean.DataListsBean) data).getAuthor();
                if (TextUtils.isEmpty(xmRequestId)) {
                    xmRequestId = ((IndexBean.DataBean.DataListsBean) data).getXmRequestId();
                }
            }
            pageTitle = baseFragment2 instanceof ReadHomePageItemFragment ? ((ReadHomePageItemFragment) baseFragment2).getPageTitle() : "";

            // 主站书城-模块内书卡片  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(41753)
                    .setServiceId("slipPage")
                    .put("currPage", "bookCity")
                    .put("moduleId", moduleId)
                    .put("pageTitle", pageTitle)
                    .put("bookId", bookId)
                    .put("position", position)
                    .put("tabName", tabNameStr)
                    .put("bookName", bookName)
                    .put("authorName", authorName)
                    .put("creatorUid", creatorUid)
                    .put(XmRequestIdManager.CONT_ID, bookId)
                    .put(XmRequestIdManager.CONT_TYPE, "book")
                    .put(XmRequestIdManager.XM_REQUEST_ID, xmRequestId)
                    .createTrace();
            LogUtils.e("home", "主站书城-模块内书卡片  控件曝光: " + bookName);
        }
    }
}
