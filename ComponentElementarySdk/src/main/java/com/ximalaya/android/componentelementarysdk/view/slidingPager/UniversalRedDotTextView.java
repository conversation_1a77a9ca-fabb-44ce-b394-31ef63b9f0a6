package com.ximalaya.android.componentelementarysdk.view.slidingPager;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.Nullable;

import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil;

/**
 * Created by 5Greatest on 2021.11.16
 *
 * <AUTHOR>
 * On 2021/11/16
 */
class UniversalRedDotTextView extends TextView {
    private static final String TAG = "RedDotTextView";
    private GradientDrawable drawable = new GradientDrawable();
    private Rect rect = new Rect();
    private int redDotSize;
    private int lastPaddingLeft = -1;
    private int lastPaddingRight = -1;
    private int strokeSize;
    private int solidSize;
    private int lastMarginLeft = -1;
    private int lastMarginRight = -1;
    private boolean showRedDot;
    private int leftMargin;
    private int topMargin;
    private int retDotHintNum = -1;
    private int redDotHintPadding;
    private int redDotHintLeftRightPadding;
    private Paint redDotHintPaint;
    private String numPoint = "...";
    private String defaultRedDotHint = numPoint;
    public boolean redDotForceUpdateRect;

    public UniversalRedDotTextView(Context context) {
        super(context);
    }

    public UniversalRedDotTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public UniversalRedDotTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void showRedDot() {
        showRedDot = true;
        ViewGroup.MarginLayoutParams ps = cast(getLayoutParams(), ViewGroup.MarginLayoutParams.class);
        if (ps != null) {
            lastMarginLeft = ps.leftMargin;
            lastMarginRight = ps.rightMargin;
        }
        lastPaddingLeft = getPaddingLeft();
        lastPaddingRight = getPaddingRight();
        postInvalidate();
    }

    public void setRetDotHintNum(int retDotHintNum) {
        this.retDotHintNum = retDotHintNum;
    }

    public void setDefaultRedDotHint(String defaultRedDotHint) {
        this.defaultRedDotHint = defaultRedDotHint;
    }

    public void setNumPoint(String numPoint) {
        this.numPoint = numPoint;
    }

    public boolean isShowRedDot(){
        return showRedDot;
    }

    public void showRedDot(int num) {
        retDotHintNum = num;
        ViewGroup.MarginLayoutParams ps = cast(getLayoutParams(), ViewGroup.MarginLayoutParams.class);
        if (ps != null) {
            lastMarginLeft = ps.leftMargin;
            lastMarginRight = ps.rightMargin;
        }
        lastPaddingLeft = getPaddingLeft();
        lastPaddingRight = getPaddingRight();
        postInvalidate();
    }

    public void hideRedDot() {
        if(!showRedDot)return;
        showRedDot = false;
        retDotHintNum = -1;
        ViewGroup.MarginLayoutParams params = cast(getLayoutParams(), ViewGroup.MarginLayoutParams.class);
        if (lastMarginLeft >= 0 && lastMarginRight >= 0 && params != null) {
            params.leftMargin = lastMarginLeft;
            params.rightMargin = lastMarginRight;
            lastMarginLeft = lastMarginRight = -1;
        }
        if (lastPaddingRight >= 0 && lastPaddingLeft >= 0) {
            setPadding(lastPaddingLeft, getPaddingTop(), lastPaddingRight, getPaddingBottom());
            lastPaddingLeft = lastPaddingRight = -1;
        }
        rect.left = rect.right = rect.top = rect.bottom = 0;
        postInvalidate();
    }


    private <T> T cast(Object object, Class<?> tClass) {
        if (object != null && tClass.isInstance(object)) {
            return (T) object;
        }
        return null;
    }

    public void setRedDot(int solidSize, int solidColor, int strokeSize, int strokeColor) {
        this.solidSize = solidSize;
        this.strokeSize = strokeSize;
        this.redDotSize = solidSize + strokeSize;
        drawable = new GradientDrawable();
        drawable.setSize(solidSize, solidSize);
        drawable.setColor(solidColor);
        drawable.setStroke(strokeSize, strokeColor);
    }

    public void setRedDotHintPaint(int textSize, @ColorInt int color, boolean lightBold) {
        redDotHintPaint = new Paint();
        redDotHintPaint.setTextSize(textSize);
        redDotHintPaint.setColor(color);
        if (lightBold) {
            redDotHintPaint.setTypeface(Typeface.create("sans-serif-light", Typeface.BOLD));
        }
        redDotHintPaint.setAntiAlias(true);
    }

    public int getRedDotHintPadding() {
        return redDotHintPadding;
    }

    public void setRedDotHintPadding(int redDotHintPadding) {
        this.redDotHintPadding = redDotHintPadding;
    }

    public void setRedDotHintLeftRightPadding(int redDotHintLeftRightPadding) {
        this.redDotHintLeftRightPadding = redDotHintLeftRightPadding;
    }

    public int getLeftMargin() {
        return leftMargin;
    }

    public void setLeftMargin(int leftMargin) {
        this.leftMargin = leftMargin;
    }

    public int getTopMargin() {
        return topMargin;
    }

    public void setTopMargin(int topMargin) {
        this.topMargin = topMargin;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (showRedDot) {
            drawRedDot(canvas);
        } else if (retDotHintNum > 0) {
            String numText = retDotHintNum <= 99 ? String.valueOf(retDotHintNum) : defaultRedDotHint;
            drawRedDotNumText(canvas, numText);
        }
        SdkBaseUtil.Logger.Companion.d(TAG, "getWidth:" + getWidth());
    }


    private void drawRedDotNumText(Canvas canvas, String numText) {
        if (TextUtils.isEmpty(numText) || redDotHintPaint == null) return;
        if (redDotForceUpdateRect || rect.isEmpty()) {
            Drawable textBgDrawable = getBackground();
            if (textBgDrawable != null && !(textBgDrawable instanceof BitmapDrawable)) {
                textBgDrawable = null;
            }
            int bw = textBgDrawable != null ? textBgDrawable.getIntrinsicWidth() : 0;
            int bh = textBgDrawable != null ? textBgDrawable.getIntrinsicHeight() : 0;
            int tw = 0;
            int th = 0;
            CharSequence text = getText();
            if (getLayout() != null) {
                tw = (int) getLayout().getLineWidth(0);
                th = getLayout().getHeight();
            } else if (!TextUtils.isEmpty(text)) {
                getPaint().getTextBounds(text.toString(), 0, text.length(), rect);
                tw = rect.width();
                th = rect.height();
            }
            //计算当前text文字或背景图片位置区域
            int areaWidth = Math.max(bw, tw);
            int areaHeight = Math.max(bh, th);
            //需要绘制的红点文字的尺寸
            measureRedDotTextRect(numText, rect);
            int numWidth = rect.right - rect.left;
            int numHeight = rect.bottom - rect.top;
            //单个数字使用圆形
            boolean oval = numText.length() == 1;
            int left, top, width, height;

            if (oval) {
                //单个数字高度大于宽度，统一取高度
                width = height = numHeight + 2 * redDotHintPadding;
                this.drawable.setShape(GradientDrawable.OVAL);
            } else {
                //多个数字
                width = numWidth + 2 * (redDotHintLeftRightPadding > 0 ? redDotHintLeftRightPadding : redDotHintPadding);
                height = numHeight + 2 * redDotHintPadding;
                this.drawable.setShape(GradientDrawable.RECTANGLE);
                this.drawable.setCornerRadius(height / 2.0f);
            }
            left = (int) Math.floor(getWidth() / 2.0f + areaWidth / 2.0f + leftMargin);
            top = (int) Math.floor(getHeight() / 2.0f - areaHeight / 2.0f - height + topMargin);
            //高度范围限制
            if (top < 0) {
                int s = (int) Math.floor(getHeight() / 2.0 - height);
                if (s > 0) {
                    top = s;
                } else {
                    top = 0;
                }
            }
            rect.set(left, top, left + width, top + height);
            //超出宽度范围，设置padding改变宽度，
            if (left + width > getWidth()) {
                //左边开始位置+红点文字宽度>控件宽度需要单独计算宽度
                int s = left + width - getWidth();
                ViewGroup.LayoutParams params = getLayoutParams();
                if (params != null && params.width == ViewGroup.LayoutParams.WRAP_CONTENT) {
                    //把margin应用为padding
                    ViewGroup.MarginLayoutParams ps = cast(params, ViewGroup.MarginLayoutParams.class);
                    if (ps != null && lastMarginLeft > s && lastMarginRight > s) {
                        ps.leftMargin = lastMarginLeft - s;
                        ps.rightMargin = lastMarginRight - s;
                    }
                    setPadding(lastPaddingLeft + s, getPaddingTop(), lastPaddingRight + s, getPaddingBottom());
                } else {
                    rect.set(getRight() - width, top, getRight(), top + height);
                }
            }
            //必须要加上scrollX，和scrollY
            rect.left += getScrollX();
            rect.right += getScrollX();
            rect.top += getScrollY();
            rect.bottom += getScrollY();
        }
        //绘制红色背景
        this.drawable.setBounds(rect);
        this.drawable.draw(canvas);
        //计算text绘制位置，不能完全居中
//        float x = rect.left + (rect.right - rect.left) / 2.0f - numWidth / 2.0f;
//        float y = rect.bottom - (rect.bottom - rect.top) / 2.0f + numHeight / 2.0f;
//        canvas.drawText(numText, x, y, redDotHintPaint);
        //...默认是底部
        if (numText.equals(numPoint)) {
            redDotHintPaint.setTextAlign(Paint.Align.CENTER);
            float x = rect.left + (rect.right - rect.left) / 2.0f;
            float y = rect.bottom - (rect.bottom - rect.top) / 2.0f;
            canvas.drawText(numText, x, y, redDotHintPaint);
        } else {
            //使用baseline 居中
            redDotHintPaint.setTextAlign(Paint.Align.CENTER);
            Paint.FontMetricsInt fontMetrics = redDotHintPaint.getFontMetricsInt();
            int baseline = (rect.bottom + rect.top - fontMetrics.bottom - fontMetrics.top) / 2;
            canvas.drawText(numText, rect.centerX(), baseline, redDotHintPaint);
        }
    }

    private void measureRedDotTextRect(String numText, Rect rect) {
        if (redDotHintPaint == null) return;
        String measureText = numText;
        if (measureText.equals(numPoint)) {
            //...高度和普通文字高度不一样
            measureText = String.valueOf(10 * (numPoint.length() - 1));
        }
        redDotHintPaint.getTextBounds(measureText, 0, measureText.length(), rect);
    }

    private void drawRedDot(Canvas canvas) {
        Drawable textBgDrawable = getBackground();
        if (redDotForceUpdateRect || rect.isEmpty()) {
            if (textBgDrawable != null && !(textBgDrawable instanceof BitmapDrawable)) {
                textBgDrawable = null;
            }
            int bw = textBgDrawable != null ? textBgDrawable.getIntrinsicWidth() : 0;
            int bh = textBgDrawable != null ? textBgDrawable.getIntrinsicHeight() : 0;
            int tw = 0;
            int th = 0;
            CharSequence text = getText();
            if (getLayout() != null) {
                tw = (int) getLayout().getLineWidth(0);
                th = getLayout().getHeight();
            } else if (!TextUtils.isEmpty(text)) {
                getPaint().getTextBounds(text.toString(), 0, text.length(), rect);
                tw = rect.width();
                th = rect.height();
            }
            int areaWidth = Math.max(bw, tw);
            int areaHeight = Math.max(bh, th);
            int left = (int) Math.floor(getWidth() / 2.0f + areaWidth / 2.0f + leftMargin);
            int top = (int) Math.floor(getHeight() / 2.0f - areaHeight / 2.0f - redDotSize + topMargin);
            //高度范围限制
            if (top < 0) {
                int s = (int) Math.floor(getHeight() / 2.0 - redDotSize);
                if (s > 0) {
                    top = s;
                } else {
                    top = 0;
                }
            }
            rect.set(left, top, left + redDotSize, top + redDotSize);
            if (left + redDotSize > getWidth()) {
                int s = left + redDotSize - getWidth();
                if (getLayoutParams() != null && getLayoutParams().width == ViewGroup.LayoutParams.WRAP_CONTENT) {
                    ViewGroup.MarginLayoutParams ps = cast(getLayoutParams(), ViewGroup.MarginLayoutParams.class);
                    if (ps != null && lastMarginLeft > s && lastMarginRight > s) {
                        ps.leftMargin = lastMarginLeft - s;
                        ps.rightMargin = lastMarginRight - s;
                    }
                    setPadding(lastPaddingLeft + s, getPaddingTop(), lastPaddingRight + s, getPaddingBottom());
                } else {
                    rect.set(getRight() - redDotSize, top, getRight(), top + redDotSize);
                }
            }
            rect.left += getScrollX();
            rect.right += getScrollX();
            rect.top += getScrollY();
            rect.bottom += getScrollY();
        }
        this.drawable.setBounds(rect);
        this.drawable.setShape(GradientDrawable.OVAL);
        this.drawable.draw(canvas);
    }
}