package com.ximalaya.ting.android.framework.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * <AUTHOR>
 */
public class DomainOrmIp {
	public int ret;
	public List<String> resolve_need;
	public String signature;
	public Map<String, String> ips = new HashMap<String, String>();
	public Map<String, String> domains = new HashMap<String, String>();
	@Override
	public String toString() {
		return "DomainOrmIp [ret=" + ret + ", resolve_need=" + resolve_need
				+ ", signature=" + signature + ", ips=" + ips + ", domains="
				+ domains + "]";
	}
}
