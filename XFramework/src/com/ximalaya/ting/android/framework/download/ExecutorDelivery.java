///**
// * ExecutorDelivery.java
// * com.ximalaya.ting.android.framework.downloadtrack
// *
// * Function： TODO
// *
// *   ver     date      		author
// * ──────────────────────────────────
// *   		 2015-7-17 		jack.qin
// *
// * Copyright (c) 2015, TNT All Rights Reserved.
// */
//
//package com.ximalaya.ting.android.framework.download;
//
//import android.os.Handler;
//
//import com.ximalaya.ting.android.opensdk.model.track.Track;
//
//import java.util.concurrent.Executor;
//
///**
// * ClassName:ExecutorDelivery Function: TODO ADD FUNCTION Reason: TODO ADD
// * REASON
// *
// * <AUTHOR>
// * @version
// * @since Ver 1.1
// * @Date 2015-7-17 下午2:42:44
// *
// * @see
// */
//public class ExecutorDelivery
//{
//	private final Executor mResponsePoster;
//
//	public ExecutorDelivery(final Handler handler)
//	{
//		// Make an Executor that just wraps the handler.
//		mResponsePoster = new Executor()
//		{
//			@Override
//			public void execute(Runnable command)
//			{
//				handler.post(command);
//			}
//		};
//	}
//
//	public void postDownloadProgress(IDownloadCallback callback, Track track)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, track, 1));
//	}
//
//	public void postStartNewTask(IDownloadCallback callback, Track track)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, track, 3));
//	}
//
//	public void postDownloadedTrack(IDownloadCallback callback, Track track)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, track, 4));
//	}
//
//	public void postUpdateTrack(IDownloadCallback callback, Track track)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, track, 5));
//	}
//
//	public void postCancelTrack(IDownloadCallback callback, Track track)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, track, 6));
//	}
//
//	public void postErrorTrack(IDownloadCallback callback, Track track)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, track, 7));
//	}
//	/**
//	 * 传递删除引用操作
//	 * @param callback
//	 * @param track
//	 */
//	public void postDeleteTrack(IDownloadCallback callback)
//	{
//		mResponsePoster
//				.execute(new ResponseDeliveryRunnable(callback, null, 8));
//	}
//
//	private class ResponseDeliveryRunnable implements Runnable
//	{
//		private IDownloadCallback callback;
//		private int code;
//		private Track track;
//
//		public ResponseDeliveryRunnable(IDownloadCallback callback, Track track,
//										int code)
//		{
//			this.callback = callback;
//			this.track = track;
//			this.code = code;
//		}
//
//		@Override
//		public void run()
//		{
//			if (code == 1)
//			{
//				callback.onDownloadProgress(track);
//			}
//			else if (code == 3)
//			{
//				callback.onStartNewTask(track);
//			}
//			else if (code == 4)
//			{
//				callback.onComplete(track);
//			}
//			else if (code == 5)
//			{
//				callback.onUpdateTrack(track);
//			}
//			else if (code == 6)
//			{
//				callback.onCancel(track);
//			}
//			else if (code == 7)
//			{
//				callback.onError(track);
//			}
//			else if (code == 8) {
//				callback.onDelete();
//			}
//		}
//	}
//}
