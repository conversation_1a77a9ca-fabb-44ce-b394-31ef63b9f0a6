<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal">

    <androidx.cardview.widget.CardView
        android:id="@+id/search_cv_album_container"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="4dp"
        app:cardElevation="0dp"
        android:layout_width="96dp"
        android:layout_height="96dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/search_riv_album_cover"
            android:layout_width="96dp"
            android:layout_height="96dp"
            android:scaleType="fitXY"
            android:src="@drawable/host_default_album"
            app:corner_radius="4dp" />

        <ImageView
            android:id="@+id/search_iv_album_cover_tag"
            style="@style/search_album_cover_tag_size_small"
            android:layout_gravity="top|left"
            android:src="@drawable/host_ic_tag_paid"
            android:visibility="gone"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/search_tv_album_title"
        android:layout_width="96dp"
        android:layout_height="36dp"
        android:layout_below="@+id/search_cv_album_container"
        android:ellipsize="end"
        android:layout_marginTop="3dp"
        android:gravity="left|top"
        android:maxLines="2"
        android:textColor="@color/search_color_333333"
        android:textSize="14sp"
        tools:text="专辑标题" />
</RelativeLayout>