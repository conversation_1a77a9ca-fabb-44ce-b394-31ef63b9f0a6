package com.ximalaya.ting.android.search.adapter;

import android.text.TextUtils;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.host.customhome.CustomHomeManager;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.model.homepage.CustomHomeData;
import com.ximalaya.ting.android.host.model.search.Delivery;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.search.adapter.chosen.SearchHotPotProvider;
import com.ximalaya.ting.android.search.adapter.chosen.SearchLabelAlbumCardProvider;
import com.ximalaya.ting.android.search.adapter.chosen.SearchPoolLabelAlbumCardProvider;
import com.ximalaya.ting.android.search.adapter.chosen.SearchSaleButtonProvider;
import com.ximalaya.ting.android.search.base.ISearchDataContext;
import com.ximalaya.ting.android.search.base.ISearchRecyclerViewAdapterProxy;
import com.ximalaya.ting.android.search.base.ISearchRecyclerViewAdapterProxyAndDataWithLifeCircle;
import com.ximalaya.ting.android.search.base.ISearchRecyclerViewDataTrace;
import com.ximalaya.ting.android.search.model.AdapterProxyData;
import com.ximalaya.ting.android.search.model.MainLabel;
import com.ximalaya.ting.android.search.model.SearchItem;
import com.ximalaya.ting.android.search.model.SearchItemModel;
import com.ximalaya.ting.android.search.model.SearchLabel;
import com.ximalaya.ting.android.search.page.sub.SearchChosenFragment;
import com.ximalaya.ting.android.search.utils.CustomHomeSearchUtil;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public abstract class SearchRecyclerViewMultiTypeAdapter2 extends AbRecyclerViewAdapter<RecyclerView.ViewHolder> {
    private SparseArray<ISearchRecyclerViewAdapterProxy> mAdapterProxyArray;
    private List<AdapterProxyData> mListData = new ArrayList<>();
    protected BaseFragment2 adapterCurrentAttachFragment;
    private RecyclerView mRecyclerView = null;

    public SearchRecyclerViewMultiTypeAdapter2(ISearchDataContext searchDataContext, BaseFragment2 adapterCurrentAttachFragment) {
        this.mAdapterProxyArray = createAdapterMap(searchDataContext);
        this.adapterCurrentAttachFragment = adapterCurrentAttachFragment;
    }

    protected abstract SparseArray<ISearchRecyclerViewAdapterProxy> createAdapterMap(ISearchDataContext searchDataContext);

    public ISearchRecyclerViewAdapterProxy getProvider(int viewType) {
        if (mAdapterProxyArray != null) {
            return mAdapterProxyArray.get(viewType);
        }
        return null;
    }

    public List<AdapterProxyData> getListData() {
        return mListData;
    }

    public void setListData(List<AdapterProxyData> data, boolean isRefresh) {
        if (ToolUtil.isEmptyCollects(data)) {
            return;
        }
        if (isRefresh) {
            mListData.clear();
        }
        addHomeCustomCardGuideIfNeed(data);
        int startPos = mListData.size();
        mListData.addAll(data);
        if (isRefresh) {
            notifyDataSetChanged();
        } else {
            notifyItemRangeInserted(startPos, data.size());
        }
    }

    /**
     * 首页自定义提示卡
     * @param data
     */
    private void addHomeCustomCardGuideIfNeed(List<AdapterProxyData> data) {

        boolean enable = CustomHomeManager.INSTANCE.enableGuide();
        if (!enable) {
            return;
        }

        try {
            if (adapterCurrentAttachFragment instanceof SearchChosenFragment) {
                String searchWord = ((SearchChosenFragment) adapterCurrentAttachFragment).getSearchWord();
                if (!TextUtils.isEmpty(searchWord)) {

                    boolean cardAdded = addHomeCustomCardGuideIfNeedInner(searchWord, data);
                    CustomHomeManager.INSTANCE.log("addHomeCustomCardGuideIfNeed cardAdded: " + cardAdded);

                    if (!cardAdded) {
                        return;
                    }

                    //请求后更新卡片状态
                    long begin = System.currentTimeMillis();
                    CustomHomeManager.INSTANCE.requestSearchTitle(searchWord, new IDataCallBack<String>() {
                        @Override
                        public void onSuccess(@androidx.annotation.Nullable String title) {
                            if (!TextUtils.isEmpty(title)) {
                                CustomHomeManager.INSTANCE.log("requestSearchTitle cost: " + (System.currentTimeMillis() - begin));
//                                addHomeCustomCardGuideIfNeedInner(title, data);


                                if (!mListData.isEmpty()) {
                                    AdapterProxyData adapterProxyData = mListData.get(0);
                                    if (adapterProxyData.getData() != null && adapterProxyData.getData() instanceof SearchItem) {
                                        SearchItem searchItem = (SearchItem) adapterProxyData.getData();
                                        if (searchItem.getSearchItemModel() != null && searchItem.getSearchItemModel().getItem() instanceof Delivery) {
                                            Delivery delivery = (Delivery) searchItem.getSearchItemModel().getItem();

                                            if (TextUtils.equals(delivery.getType(), CustomHomeManager.TYPE_LOADING)) {
                                                String cardTitle = CustomHomeSearchUtil.INSTANCE.buildCustomGuideCardTitle(title);
                                                delivery.setTitle(cardTitle);
                                                delivery.setType(CustomHomeManager.TYPE_LOADING_FINISHED);

                                                notifyDataSetChanged();
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        @Override
                        public void onError(int code, String message) {

                        }
                    });
                }
            }

        } catch (Throwable throwable) {
            if (ConstantsOpenSdk.isDebug) {
                throw throwable;
            }
        }
    }

    private boolean addHomeCustomCardGuideIfNeedInner(String searchWord, List<AdapterProxyData> data) {

        //保存搜索词对应的结果数据
        List<AdapterProxyData<SearchItem>> targetList = new ArrayList<>();

        String firstCardModuleType = "";
        for (int i = 0; i < data.size(); i++) {
            AdapterProxyData<?> item = data.get(i);
            if (item instanceof AdapterProxyData<?>) {
                // 确保泛型参数是 SearchItem
                if (item.getData() instanceof SearchItem) {
                    AdapterProxyData<SearchItem> proxyData = (AdapterProxyData<SearchItem>) item;

                    if (proxyData.getData() != null) {
                        targetList.add(proxyData);

                        if (TextUtils.isEmpty(firstCardModuleType)) {
                            firstCardModuleType = proxyData.getData().getModuleType();
                        }
                    }
                } else if (TextUtils.isEmpty(firstCardModuleType) && (item.getExtra() instanceof SearchItem)) {
                    SearchItem searchItem = (SearchItem) item.getExtra();
                    firstCardModuleType = searchItem.getModuleType();
                }
            }
        }

        if (firstCardModuleType == null) {
            return false;
        }

        //如果第一个是特殊类型的卡片，不展示添加入口
        boolean show = CustomHomeManager.INSTANCE.checkShowGuideCard(firstCardModuleType);
        if (!show) {
            return false;
        }

        CustomHomeData.SubElementWrapper subElementWrapper = CustomHomeSearchUtil.INSTANCE.convertSearchResult(searchWord, targetList);

        //超过 6 个才允许添加到首页
        if (subElementWrapper.subElements.size() >= CustomHomeManager.MIN_ELEMENT_SIZE) {

            AdapterProxyData<SearchItem> proxyData = CustomHomeSearchUtil.INSTANCE.buildCustomGuideLoadingCardData();

            int insertIndex = CustomHomeManager.INSTANCE.getInsertIndex();
            data.add(insertIndex, proxyData);

//            mListData.add(insertIndex, proxyData);
//            notifyItemRangeInserted(insertIndex, 1);
//            notifyDataSetChanged();

            CustomHomeManager.INSTANCE.saveSearchResultData(searchWord, subElementWrapper);
            return true;
        }
        return false;
    }

    public void addListData(List<AdapterProxyData> data, int index) {
        if (ToolUtil.isEmptyCollects(data)) {
            return;
        }
        mListData.addAll(index, data);
        notifyItemRangeInserted(index, data.size());
        if (index > 0) {
            notifyItemRangeChanged(index - 1, 1, true);
        }
        notifyItemRangeChanged(index, mListData.size() - index);
    }

    public void removeData(int position, boolean needUpdate) {
        if (position >= mListData.size()) {
            return;
        }
        mListData.remove(position);
        notifyItemRemoved(position);
        if (needUpdate) {
            notifyItemRangeChanged(position, mListData.size() - position);
        }
    }

    public void removeListData(int position, int itemCount) {
        if (position >= mListData.size() || itemCount <= 0 || (position + itemCount) > mListData.size()) {
            return;
        }
        mListData.subList(position, position + itemCount).clear();
        notifyItemRangeRemoved(position, itemCount);
        if (position > 0) {
            notifyItemRangeChanged(position - 1, 1, true);
        }
        notifyItemRangeChanged(position, mListData.size() - position);
    }

    public void clear() {
        mListData.clear();
//        notifyDataSetChanged();
    }

    @Override
    public AdapterProxyData getItem(int position) {
        if (position >= 0 && position < mListData.size()) {
            return mListData.get(position);
        }
        return null;
    }

    @Override
    public int getItemCount() {
        return mListData.size();
    }

    @Override
    public int getItemViewType(int position) {
        AdapterProxyData itemData = getItem(position);
        if (itemData != null) {
            return itemData.getViewType();
        }
        if (ConstantsOpenSdk.isDebug) {
            throw new RuntimeException(getClass().getName() + " : 相关的viewType 没有注册");
        }
        return super.getItemViewType(position);
    }

    public void setRecyclerView(@androidx.annotation.Nullable RecyclerView recyclerView) {
        mRecyclerView = recyclerView;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ISearchRecyclerViewAdapterProxy adapterProxy = getProvider(viewType);
        if (adapterProxy == null) {
            return new DefaultViewHolder(LayoutInflater.from(parent.getContext()).inflate(0, parent, false));
        }
        if (adapterProxy instanceof SearchLabelAlbumCardProvider) {
            //泛搜索卡片回调-刷新数据
            ((SearchLabelAlbumCardProvider)adapterProxy).setOnLabelItemClick(new SearchLabelAlbumCardProvider.OnLabelItemClick() {
                @Override
                public void onLabelItemClick(@NonNull List<SearchLabel> selectList, MainLabel mainLabel) {
                    if(mOnCardLabelChangeListener != null) {
                        mOnCardLabelChangeListener.onLabelAlbumCardChangeListener(selectList, mainLabel);
                    }
                }
            });
        } else if (adapterProxy instanceof SearchHotPotProvider) {
            //泛搜索卡片回调-刷新数据
            ((SearchHotPotProvider)adapterProxy).setOnLabelItemClick(new SearchHotPotProvider.OnLabelItemClick() {
                @Override
                public void onLabelItemClick(int extendClickTimes, SearchLabel selectLabel, List<SearchLabel> labelList) {
                    if(mOnCardLabelChangeListener != null) {
                        mOnCardLabelChangeListener.onHotPotTagChangeListener(extendClickTimes, selectLabel, labelList);
                    }
                }
            });
        } else if (adapterProxy instanceof SearchSaleButtonProvider){
            //大促卡片回调-刷新数据
            ((SearchSaleButtonProvider)adapterProxy).setOnSaleItemClick(new SearchSaleButtonProvider.OnSaleItemClick() {
                @Override
                public void onSaleItemClick(boolean selected, @Nullable String saleFq) {
                    if(mOnCardLabelChangeListener != null) {
                        mOnCardLabelChangeListener.onSaleCardClickListener(selected, saleFq);
                    }
                }
            });
        } else if (adapterProxy instanceof SearchPoolLabelAlbumCardProvider) {
            ((SearchPoolLabelAlbumCardProvider) adapterProxy).setOnLabelItemClick((selectList, extendClickTimes) -> {
                if (mOnCardLabelChangeListener != null) {
                    mOnCardLabelChangeListener.onPoolLabelAlbumCardChangeListener(selectList, extendClickTimes);
                }
            });
        }
        View convertView = adapterProxy.getView(LayoutInflater.from(parent.getContext()), 0, parent);
        RecyclerView.ViewHolder holder = adapterProxy.buildHolder(convertView);
        convertView.setTag(holder);
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        AdapterProxyData itemData = getItem(position);
        int viewType = getItemViewType(position);
        ISearchRecyclerViewAdapterProxy adapterProxy = getProvider(viewType);
        if (adapterProxy != null) {
            adapterProxy.bindViewHolder(holder, itemData, holder.itemView, position);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position, @NonNull List<Object> payloads) {
        if (payloads.size() == 0) {
            super.onBindViewHolder(holder, position, payloads);
        }
        AdapterProxyData itemData = getItem(position);
        int viewType = getItemViewType(position);
        ISearchRecyclerViewAdapterProxy adapterProxy = getProvider(viewType);
        if (adapterProxy != null) {
            adapterProxy.bindViewHolder(holder, itemData, holder.itemView, position, payloads);
        }
    }

    public void updateWhileProgressChange(int position, int progress) {
        if (mRecyclerView == null) {
             return;
        }
        AdapterProxyData itemData = getItem(position);
        int viewType = getItemViewType(position);
        RecyclerView.ViewHolder holder = mRecyclerView.findViewHolderForAdapterPosition(position);
        ISearchRecyclerViewAdapterProxy adapterProxy = getProvider(viewType);
        if (adapterProxy != null && itemData != null && holder != null && holder.itemView != null && holder.itemView.isAttachedToWindow()) {
            adapterProxy.onPlayProgressChange(holder, itemData.getData(), itemData.getExtra(), progress);
        }
    }

    public void refreshHotPotCard(int position, View convertView) {
        if (mAdapterProxyArray == null || mAdapterProxyArray.size() < 1) {
            return;
        }
        try {
            AdapterProxyData itemModel = getItem(position);
            int viewType = getItemViewType(position);
            ISearchRecyclerViewAdapterProxy searchAdapterProxy = getProvider(viewType);
            if (viewType == SearchChosenAdapter.VIEW_TYPE_HOT_POT_CARD && convertView != null && itemModel != null && searchAdapterProxy instanceof SearchHotPotProvider) {
                RecyclerView.ViewHolder holder = null;
                if (convertView.getTag() instanceof RecyclerView.ViewHolder) {
                    holder = (RecyclerView.ViewHolder) convertView.getTag();
                }
                searchAdapterProxy.bindViewHolder(holder,itemModel, convertView, position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void refreshLabelAlbumCard(int position, View convertView) {
        if (mAdapterProxyArray == null || mAdapterProxyArray.size() < 1) {
            return;
        }
        try {
            AdapterProxyData itemModel = getItem(position);
            int viewType = getItemViewType(position);
            ISearchRecyclerViewAdapterProxy searchAdapterProxy = getProvider(viewType);
            if (viewType == SearchChosenAdapter.VIEW_TYPE_UNIVERSAL_QUERY_CARD && convertView != null && itemModel != null && searchAdapterProxy instanceof SearchLabelAlbumCardProvider) {
                RecyclerView.ViewHolder holder = null;
                if (convertView.getTag() instanceof RecyclerView.ViewHolder) {
                    holder = (RecyclerView.ViewHolder) convertView.getTag();
                }
                searchAdapterProxy.bindViewHolder(holder,itemModel, convertView, position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void refreshPoolLabelAlbumCard(int position, View convertView) {
        if (mAdapterProxyArray == null || mAdapterProxyArray.size() < 1) {
            return;
        }
        try {
            AdapterProxyData itemModel = getItem(position);
            int viewType = getItemViewType(position);
            ISearchRecyclerViewAdapterProxy searchAdapterProxy = getProvider(viewType);
            if (viewType == SearchChosenAdapter.VIEW_TYPE_POOL_LABEL_ALBUM_CARD && convertView != null && itemModel != null && searchAdapterProxy instanceof SearchPoolLabelAlbumCardProvider) {
                RecyclerView.ViewHolder holder = null;
                if (convertView.getTag() instanceof RecyclerView.ViewHolder) {
                    holder = (RecyclerView.ViewHolder) convertView.getTag();
                }
                searchAdapterProxy.bindViewHolder(holder,itemModel, convertView, position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onResume(int position, View convertView) {
        if (mAdapterProxyArray == null || mAdapterProxyArray.size() < 1) {
            return;
        }
        try {
            AdapterProxyData itemModel = getItem(position);
            int viewType = getItemViewType(position);
            ISearchRecyclerViewAdapterProxy searchAdapterProxy = getProvider(viewType);
            if (convertView != null && itemModel != null && searchAdapterProxy instanceof ISearchRecyclerViewAdapterProxyAndDataWithLifeCircle) {
                RecyclerView.ViewHolder holder = null;
                if (convertView.getTag() instanceof RecyclerView.ViewHolder) {
                    holder = (RecyclerView.ViewHolder) convertView.getTag();
                }
                ((ISearchRecyclerViewAdapterProxyAndDataWithLifeCircle) searchAdapterProxy).onResume(itemModel.getData(), position, holder);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void onPause(int position, View convertView) {
        if (mAdapterProxyArray == null || mAdapterProxyArray.size() < 1) {
            return;
        }
        try {
            AdapterProxyData itemModel = getItem(position);
            int viewType = getItemViewType(position);
            ISearchRecyclerViewAdapterProxy searchAdapterProxy = getProvider(viewType);
            if (convertView != null && itemModel != null && searchAdapterProxy instanceof ISearchRecyclerViewAdapterProxyAndDataWithLifeCircle) {
                RecyclerView.ViewHolder holder = null;
                if (convertView.getTag() instanceof RecyclerView.ViewHolder) {
                    holder = (RecyclerView.ViewHolder) convertView.getTag();
                }
                ((ISearchRecyclerViewAdapterProxyAndDataWithLifeCircle) searchAdapterProxy).onPause(itemModel.getData(), position, holder);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void traceOnItemShow(int position, View convertView) {
        try {
            if (mAdapterProxyArray == null) return;
            AdapterProxyData itemModel = getItem(position);
            int viewType = getItemViewType(position);
            ISearchRecyclerViewAdapterProxy searchAdapterProxy = getProvider(viewType);
            if (convertView != null && itemModel != null && searchAdapterProxy instanceof ISearchRecyclerViewDataTrace) {
                RecyclerView.ViewHolder holder = null;
                if (convertView.getTag() instanceof RecyclerView.ViewHolder) {
                    holder = (RecyclerView.ViewHolder) convertView.getTag();
                }
                ((ISearchRecyclerViewDataTrace) searchAdapterProxy).traceOnItemShow(itemModel.getData(), position, holder, itemModel.getExtra());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public BaseFragment2 getAdapterCurrentAttachFragment() {
        return adapterCurrentAttachFragment;
    }

    class DefaultViewHolder extends RecyclerView.ViewHolder {
        public DefaultViewHolder(View itemView) {
            super(itemView);
        }
    }

    public interface OnCardLabelChangeListener {
        //泛搜索卡片的筛选回调
        void onLabelAlbumCardChangeListener(List<SearchLabel> selectList, MainLabel mainLabel);
        void onHotPotTagChangeListener(int extendClickTimes, SearchLabel searchLabel, List<SearchLabel> labelList);
        void onSaleCardClickListener(boolean selected, String saleFq);
        void onPoolLabelAlbumCardChangeListener(List<SearchLabel> selectList, int extendClickTimes);
    }
    private OnCardLabelChangeListener mOnCardLabelChangeListener = null;
    public void setOnCardChangeListener(OnCardLabelChangeListener onCardLabelChangeListener){
        mOnCardLabelChangeListener = onCardLabelChangeListener;
    }
}
