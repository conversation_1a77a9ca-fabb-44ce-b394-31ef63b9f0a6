package com.ximalaya.ting.android.search.adapter.dub;

import android.content.Context;
import android.util.SparseArray;

import com.ximalaya.ting.android.search.adapter.SearchMultiTypeAdapter;
import com.ximalaya.ting.android.search.adapter.chosenNew.SearchDocsRecommendWordProvider;
import com.ximalaya.ting.android.search.base.ISearchAdapterProxy;
import com.ximalaya.ting.android.search.base.ISearchDataContext;
import com.ximalaya.ting.android.search.model.AdapterProxyData;

import java.util.List;

public class SearchDubResultAdapter extends SearchMultiTypeAdapter {
    private static int VIEW_TYPE_BASE = 0;
    public static final int VIEW_TYPE_COMMON_DUB = VIEW_TYPE_BASE++; //趣配音
    public static final int VIEW_TYPE_RECOMMEND_WORDS = VIEW_TYPE_BASE++; //相关搜索

    public SearchDubResultAdapter(Context context, List<AdapterProxyData> itemModels, ISearchDataContext searchDataContext) {
        super(context, itemModels, searchDataContext);
    }

    @Override
    protected SparseArray<ISearchAdapterProxy> createAdapterMap(ISearchDataContext searchDataContext) {
        SparseArray<ISearchAdapterProxy> adapterProxyArray = new SparseArray<>();
        adapterProxyArray.put(VIEW_TYPE_COMMON_DUB, new SearchCommonDubProvider(searchDataContext));
        adapterProxyArray.put(VIEW_TYPE_RECOMMEND_WORDS, new SearchDocsRecommendWordProvider(searchDataContext));
        return adapterProxyArray;
    }
}
