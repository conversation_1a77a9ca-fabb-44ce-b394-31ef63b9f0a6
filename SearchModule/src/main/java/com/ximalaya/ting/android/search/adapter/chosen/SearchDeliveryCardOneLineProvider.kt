package com.ximalaya.ting.android.search.adapter.chosen

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.customhome.CustomHomeManager
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.model.search.Delivery
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.search.R
import com.ximalaya.ting.android.search.SearchConstants
import com.ximalaya.ting.android.search.adapter.SearchRecyclerViewMultiTypeAdapter2
import com.ximalaya.ting.android.search.base.BaseSearchRecyclerViewAdapterProxy
import com.ximalaya.ting.android.search.base.ISearchDataContext
import com.ximalaya.ting.android.search.dialog.LoadingDialog
import com.ximalaya.ting.android.search.model.SearchItem
import com.ximalaya.ting.android.search.page.sub.SearchChosenFragment
import com.ximalaya.ting.android.search.utils.SearchTraceUtils
import com.ximalaya.ting.android.search.utils.SearchUiUtils
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager

class SearchDeliveryCardOneLineProvider(
    searchDataContext: ISearchDataContext?,
    val adapter: SearchRecyclerViewMultiTypeAdapter2
    )
    : BaseSearchRecyclerViewAdapterProxy<SearchDeliveryCardOneLineProvider.ViewHolder?, SearchItem?>(searchDataContext) {

    override fun bindView(holder: ViewHolder?, data: SearchItem?, extra: Any?, convertView: View?, position: Int) {
        if (holder == null || data == null) {
            return
        }
        val searchItemModel = data.searchItemModel ?: return
        val delivery = searchItemModel.item
        if (delivery !is Delivery) {
            return
        }

        if (TextUtils.equals(delivery.type, CustomHomeManager.TYPE_LOADING)) {
            holder.vLoading.visibility = View.VISIBLE
            holder.vContainer.visibility = View.GONE
            return
        }
        holder.vLoading.visibility = View.GONE
        holder.vContainer.visibility = View.VISIBLE

        val currentFragment: BaseFragment2 = adapter.adapterCurrentAttachFragment
        var searchWord : String? = null
        if (currentFragment is SearchChosenFragment) {
            searchWord = (currentFragment as SearchChosenFragment).getSearchWord()
        }
        SearchUiUtils.setTextSizeWithLargeScreen(holder.tvTitle, holder.tvSubtitle)
        SearchUiUtils.setVisible(if (position == 0) View.VISIBLE else View.GONE, holder.vTopBg)
        ImageManager.from(context).displayImageNotIncludeDownloadCache(holder.ivCover, delivery.icon, com.ximalaya.ting.android.host.R.drawable.host_default_album)

        holder.tvTitle.text = delivery.title
        holder.tvSubtitle.text = delivery.subTitle
        holder.itemView.setOnClickListener { v ->
            if (!OneClickHelper.getInstance().onClick(v)) {
                return@setOnClickListener
            }
            val activity = activity
            if (!delivery.url.isNullOrEmpty() && activity is MainActivity) {
                if (delivery.url == CustomHomeManager.searchGuideCardClickUrl) {
                    if (searchWord?.isEmpty() == false) {
//                        if (!UserInfoMannage.hasLogined()) {
//                            UserInfoMannage.gotoLogin(currentFragment.context)
//                            return@setOnClickListener
//                        }
                        showLoading()
                        CustomHomeManager.requestSearchResult(searchWord, object : IDataCallBack<Boolean> {
                            override fun onSuccess(data: Boolean?) {
                                Toast.makeText(context, "您的定制首页已生成～", Toast.LENGTH_SHORT).show()
                            }

                            override fun onError(code: Int, message: String?) {
                                Toast.makeText(context, "定制首页生成失败，请稍后重试～", Toast.LENGTH_SHORT).show()
                            }

                        });
                    }

                    // 搜索精选tab页-定制首页入口  点击事件
                    XMTraceApi.Trace()
                        .click(68959) // 用户点击时上报
                        .put("currPage", "searchChosen")
                        .put("Item", searchWord)
                        .put("xmRequestId", searchItemModel.xmRequestId)
                        .createTrace()
                    return@setOnClickListener
                } else {
                    NativeHybridFragment.start(activity, delivery.url, true)
                }
            }
            SearchTraceUtils.traceCardClick(SearchConstants.TRACE_TYPE_DELIVERY_CARD,
                    SearchConstants.TRACE_PAGE_CHOSEN, data.modulePosition, searchItemModel.abInfo, searchItemModel.xmRequestId)
            SearchTraceUtils.traceChannelOrDeliveryClick(SearchConstants.TRACE_TYPE_DELIVERY_CARD,
                    SearchConstants.TRACE_PAGE_CHOSEN,"", "", "", delivery.url, searchItemModel.abInfo, searchItemModel.xmRequestId)
        }
        SearchTraceUtils.traceChannelOrDeliveryView(holder.vContainer, SearchConstants.TRACE_TYPE_DELIVERY_CARD,
                SearchConstants.TRACE_PAGE_CHOSEN,"", "", "", delivery.url, searchItemModel.abInfo, searchItemModel.xmRequestId)
        SearchTraceUtils.traceCardExposure(holder.itemView, SearchConstants.TRACE_TYPE_DELIVERY_CARD,
                SearchConstants.TRACE_PAGE_CHOSEN, data.modulePosition, searchItemModel.abInfo, data.xmRequestId)

        // 搜索精选tab页-定制首页入口  控件曝光
        XMTraceApi.Trace()
            .setMetaId(68960)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "searchChosen")
            .put("xmRequestId", searchItemModel.xmRequestId)
            .put("Item", searchWord)
            .put(XmRequestIdManager.CONT_TYPE, SearchConstants.TRACE_TYPE_DELIVERY_CARD)
            .put(XmRequestIdManager.CONT_ID, (data.modulePosition + 1).toString())
            .createTrace()
    }

    private fun showLoading() {
        // Show loading dialog
        val loadingDialog = LoadingDialog(context)
        loadingDialog.show()

        // After 2 seconds, show success message and dismiss loading
        Handler(Looper.getMainLooper()).postDelayed({
            loadingDialog.dismiss()
        }, 2000)
    }

    override fun getLayoutId(): Int {
        return R.layout.search_item_delivery_one_line_2025
    }

    override fun buildHolder(convertView: View): ViewHolder {
        return ViewHolder(convertView)
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val vTopBg: View? = view.findViewById(R.id.search_v_top_bg)
        val vContainer: View = view.findViewById(R.id.search_v_content)
        val ivCover: ImageView = view.findViewById(R.id.search_iv_cover)
        val tvTitle: TextView = view.findViewById(R.id.search_tv_title)
        val tvSubtitle: TextView = view.findViewById(R.id.search_tv_action)
        val vLoading: View = view.findViewById(R.id.search_loading)
    }
}