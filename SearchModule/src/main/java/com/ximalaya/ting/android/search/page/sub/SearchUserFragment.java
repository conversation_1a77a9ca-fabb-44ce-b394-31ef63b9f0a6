package com.ximalaya.ting.android.search.page.sub;

import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.model.anchor.Anchor;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.search.R;
import com.ximalaya.ting.android.search.SearchConstants;
import com.ximalaya.ting.android.search.adapter.SearchAnchorNewAdapter;
import com.ximalaya.ting.android.search.base.BaseFilterDataSubTabFragment;
import com.ximalaya.ting.android.search.model.SearchSortFilterData;
import com.ximalaya.ting.android.search.out.SearchRouterUtils;
import com.ximalaya.ting.android.search.utils.SearchTraceUtils;

import java.util.Arrays;
import java.util.List;


/**
 * Created by ervin.li on 2018/10/30.
 * 搜索用户tab
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchUserFragment extends BaseFilterDataSubTabFragment implements AnchorFollowManage.IFollowAnchorListener {
    private SearchAnchorNewAdapter mAdapter;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AnchorFollowManage.getSingleton().addFollowListener(this);
    }

    @Override
    protected String getDefaultCore() {
        return SearchConstants.CORE_USER;
    }

    @Override
    protected Class<?> getCurrClass() {
        return Anchor.class;
    }

    @Override
    protected HolderAdapter<?> getAdapter() {
        allowItemLongClick = false;
        mAdapter = new SearchAnchorNewAdapter(getActivity(), null, this, SearchAnchorNewAdapter.TYPE_SEARCH_USER);
        return mAdapter;
    }

    @Override
    protected List<SearchSortFilterData> createSimpleSortFilterData() {
        SearchSortFilterData filter1 = new SearchSortFilterData(SearchConstants.CONDITION_RELATION, SearchConstants.CONDITION_RELATION_NAME, true);
        SearchSortFilterData filter2 = new SearchSortFilterData(SearchConstants.CONDITION_FANS, SearchConstants.CONDITION_FANS_NAME);
        SearchSortFilterData filter3 = new SearchSortFilterData(SearchConstants.CONDITION_VOICE, SearchConstants.CONDITION_VOICE_NAME);
        return Arrays.asList(filter1, filter2, filter3);
    }

    @Override
    protected void onListViewItemClick(AdapterView<?> parent, View view, int position, Object data) {
        super.onListViewItemClick(parent, view, position, data);
        Anchor anchor = (Anchor) data;
        anchor.setSearchModuleItemClicked(true);
        SearchTraceUtils.traceAnchorClick(SearchConstants.TRACE_TYPE_USER, SearchConstants.TRACE_PAGE_USER, anchor.getId(), anchor.getAbInfo(), anchor.getXmRequestId());
        SearchTraceUtils.traceWithSearchInfo("searchResult", position + 1, "user", "searchCustomer", String.valueOf(anchor.getUid()), XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_SEARCH_PAGE_CLICK);
        startFragment(SearchRouterUtils.newAnchorSpaceFragment(
                anchor.getUid(), ConstantsOpenSdk.PLAY_FROM_SEARCH), view);
    }

    @Override
    protected boolean onPrepareNoContentView() {
        setNoContentImageView(R.drawable.search_default_none_compass_n_line_n_120);
        return false;
    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        resumeInnerDisplay();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        if (isVisibleToUser) {
            resumeInnerDisplay();
        }
    }

    private void resumeInnerDisplay() {
        if (refreshLoadMoreListView == null || refreshLoadMoreListView.getRefreshableView() == null
                || mAdapter == null || mAdapter.getCount() <= 0) {
            return;
        }
        int headerViewCount = refreshLoadMoreListView.getRefreshableView().getHeaderViewsCount();
        for (int i = mCurrentFirstVisibleItem; i < mCurrentFirstVisibleItem + mCurrentVisibleItemCount; i++) {
            int position = i - headerViewCount;
            if (position >= 0 && position < mAdapter.getCount()){
                View convertView = refreshLoadMoreListView.getRefreshableView().getChildAt(i - mCurrentFirstVisibleItem);
                Anchor anchor = mAdapter.getItem(position);
                if (convertView != null && anchor != null) {
                    HolderAdapter.BaseViewHolder holder = null;
                    if (convertView.getTag() instanceof HolderAdapter.BaseViewHolder) {
                        holder = (HolderAdapter.BaseViewHolder) convertView.getTag();
                    }
                    mAdapter.onResume(anchor, position, holder);
                }
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        AnchorFollowManage.getSingleton().removeFollowListener(this);
    }

    @Override
    public void onFollow(long uid, boolean follow) {
        if (mAdapter == null){
            return;
        }
        for (int i = 0; i < mAdapter.getCount(); i++) {
            Anchor anchor = mAdapter.getItem(i);
            if (anchor != null && anchor.getUid() == uid){
                anchor.setFollowingStatus(follow? 1 : 3);
                break;
            }
        }
    }


}
