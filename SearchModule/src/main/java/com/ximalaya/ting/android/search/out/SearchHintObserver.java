package com.ximalaya.ting.android.search.out;

import com.ximalaya.ting.android.host.listener.ISearchHintCallback;
import com.ximalaya.ting.android.host.model.base.ListModeBase;
import com.ximalaya.ting.android.host.model.search.SearchHotWord;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by ervin.li on 2018/11/15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchHintObserver implements ISearchHintCallback<ListModeBase<SearchHotWord>> {
    WeakReference<ISearchHintCallback<ListModeBase<SearchHotWord>>> outWeakObserver;

    public SearchHintObserver(ISearchHintCallback<ListModeBase<SearchHotWord>> callback) {
        outWeakObserver = new WeakReference<>(callback);
    }

    public ISearchHintCallback<ListModeBase<SearchHotWord>> getOutObserver() {
        return outWeakObserver != null ? outWeakObserver.get() : null;
    }

    @Override
    public void onSuccess(ListModeBase<SearchHotWord> listModeBase) {
        ISearchHintCallback<ListModeBase<SearchHotWord>> callback = getOutObserver();
        if (callback != null) {
            callback.onSuccess(listModeBase);
        }
    }

    @Override
    public void onFailed(int code, String message) {
        ISearchHintCallback<ListModeBase<SearchHotWord>> callback = getOutObserver();
        if (callback != null) {
            callback.onFailed(code, message);
        }
    }
}
