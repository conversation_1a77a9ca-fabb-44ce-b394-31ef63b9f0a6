package com.ximalaya.ting.android.search.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.widget.GridView;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.handmark.pulltorefresh.library.internal.LoadingLayout;
import com.handmark.pulltorefresh.library.internal.XmLoadingLayout;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreGridView;

/**
 * Created by ervin.li on 2018/11/9.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class SearchPullToRefreshGridView extends RefreshLoadMoreGridView {
    private int allHeaderViewColor;

    //刷新view最小间隔控制，请求时间太短，刷新感受不到
    private boolean mOpenRefreshMinInterval;
    private long mLastRefreshTime;
    private long mRefreshMinInterval = 400;
    private Runnable mRefreshStateDelayResetRunnable;

    public SearchPullToRefreshGridView(Context context) {
        super(context);
        setShowIndicator(false);
    }

    public SearchPullToRefreshGridView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setShowIndicator(false);
    }

    public SearchPullToRefreshGridView(Context context, Mode mode) {
        super(context, mode);
        setShowIndicator(false);
    }

    public SearchPullToRefreshGridView(Context context, Mode mode, AnimationStyle style) {
        super(context, mode, style);
        setShowIndicator(false);
    }

    @Override
    public LoadingLayout getLoadingLayout(Context context, Mode mode, Orientation scrollDirection, TypedArray attrs) {
        XmLoadingLayout xmLoadingLayout = new XmLoadingLayout(context, mode, scrollDirection, attrs);
        if (allHeaderViewColor == 0) {
            allHeaderViewColor = Color.BLACK;
        }
        xmLoadingLayout.setAllViewColor(allHeaderViewColor);
        // 因为其他地方会设置这个,所以在这里重置下
        xmLoadingLayout.setLoadingDrawable(null);
        return xmLoadingLayout;
    }
    /**
     * 请在adapter更新完数据之后调用
     *
     * @param hasMore
     */
    public void onRefreshComplete(final boolean hasMore) {
        if (mOpenRefreshMinInterval && tooShort() && isRefreshing()) {
            clearRefreshStateDelayResetRunnable();
            mRefreshStateDelayResetRunnable = new Runnable() {
                @Override
                public void run() {
                    onRefreshComplete(hasMore);
                }
            };
            postDelayed(mRefreshStateDelayResetRunnable, mRefreshMinInterval / 2);
        } else {
            if(mOpenRefreshMinInterval){
                clearRefreshStateDelayResetRunnable();
            }
            super.onRefreshComplete(hasMore);
        }
    }

    /**
     * 如果开启，从开始刷新到数据结果返回时间小于mRefreshMinInterval，还原刷新状态会delay mRefreshMinInterval/2，直到满足
     * @param open
     */
    public void setRefreshMinIntervalState(boolean open){
        this.mOpenRefreshMinInterval = open;
    }

    private void clearRefreshStateDelayResetRunnable(){
        removeCallbacks(mRefreshStateDelayResetRunnable);
        mRefreshStateDelayResetRunnable = null;
    }

    private boolean tooShort() {
        return (System.currentTimeMillis() - mLastRefreshTime) < mRefreshMinInterval;
    }

    @Override
    protected void onReleaseToRefresh() {
        super.onReleaseToRefresh();
    }

    @Override
    public void onPullDownToRefresh(PullToRefreshBase<GridView> refreshView) {
        super.onPullDownToRefresh(refreshView);
        if (mOpenRefreshMinInterval) {
            mLastRefreshTime = System.currentTimeMillis();
        }
    }
}
