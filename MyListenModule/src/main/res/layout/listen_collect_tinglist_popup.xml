<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    tools:ignore="UnusedAttribute">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/listen_bg_tinglist_popup"
        android:orientation="vertical"
        android:paddingTop="4dp"
        android:paddingBottom="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/listen_popup_menu_all"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:background="@drawable/host_bg_list_selector"
            android:contentDescription="全部"
            android:drawableStart="@drawable/host_ic_gongge_n_n_line_regular_24"
            android:drawablePadding="8dp"
            android:drawableTint="@color/host_color_textColor"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="20dp"
            android:text="全部"
            android:textColor="@color/host_color_textColor" />

        <TextView
            android:id="@+id/listen_popup_menu_selfCreate"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:background="@drawable/host_bg_list_selector"
            android:contentDescription="自建听单"
            android:drawableStart="@drawable/host_ic_add_n_n_line_regular_24"
            android:drawablePadding="8dp"
            android:drawableTint="@color/host_color_textColor"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="20dp"
            android:text="自建听单"
            android:textColor="@color/host_color_textColor" />

        <TextView
            android:id="@+id/listen_popup_menu_collect"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:background="@drawable/host_bg_list_selector"
            android:contentDescription="收藏听单"
            android:drawableStart="@drawable/host_ic_collect_no_n_line_regular_24"
            android:drawablePadding="8dp"
            android:drawableTint="@color/host_color_textColor"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="20dp"
            android:text="收藏听单"
            android:textColor="@color/host_color_textColor" />
    </LinearLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="3dp"
        android:rotation="180"
        android:src="@drawable/listen_chase_popup_triangle"
        android:tint="@color/host_color_white_darkFill2"
        android:translationX="10dp"
        tools:ignore="UseAppTint" />
</FrameLayout>