package com.ximalaya.ting.android.opensdk.httputil.util;

import android.content.Context;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import com.ximalaya.ting.android.opensdk.constants.DTransferConstants;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * ClassName:BaseUtil Function: TODO ADD FUNCTION Reason: TODO ADD REASON
 * 
 * <AUTHOR>
 * @version
 * @since Ver 1.1
 * @Date 2015-4-20 下午3:54:37
 * 
 * @see
 */
public class Util
{
	public static JSONObject HttpParameters2Json(Map<String, Object> map)
	{
		return new JSONObject(map);
	}

	public static String ConvertMap2HttpParams(Map<String, String> map)
	{
		if(map == null) {
			return "";
		}
		StringBuilder buffer = new StringBuilder();
		
		Map<String, String> sortedMap = new TreeMap<String, String>();
		sortedMap.putAll(map);//http请求参数需要按首字母从a-z进行排序
		for (Map.Entry<String, String> entry : sortedMap.entrySet())
		{
			String value = entry.getValue();
			if(value!=null) {
				buffer.append(entry.getKey()).append("=").append(value)
						.append("&");
			} else {
				if(entry.getKey() != null) {
					map.remove(entry.getKey());
				}
			}
		}
		if (!map.isEmpty() && buffer.length() > 0) {
			buffer.deleteCharAt(buffer.length() - 1);
		}

		return buffer.toString();
	}

	public static Map<String, String> encoderName(Map<String, String> map)
	{
		for (Map.Entry<String, String> entry : map.entrySet())
		{
			String key = entry.getKey();
			if (key.equals(DTransferConstants.SEARCH_KEY)
					|| key.equals(DTransferConstants.TAG_NAME))
			{
				String value = null;
				try
				{
					value = URLEncoder.encode(entry.getValue(), "UTF-8");
				}
				catch (UnsupportedEncodingException e)
				{
					e.printStackTrace();
				}
				map.put(key, value);
				break;
			}
		}
		return map;
	}

	public static String readInputStream(InputStream inStream) throws Exception
	{
		BufferedReader bufferedReader = null;
		bufferedReader = new BufferedReader(new InputStreamReader(inStream));
		StringBuffer buffer = new StringBuffer();
		String line;
		while ((line = bufferedReader.readLine()) != null)
		{
			buffer.append(line.trim());
		}
		return buffer.toString();
	}

	public static boolean isEmpty(String str)
	{
		if (TextUtils.isEmpty(str) || "null".equals(str))
		{
			return false;
		}
		else
		{
			return true;
		}

	}

	public static Map<String, String> cType(Map<String, Object> map)
	{
		Map<String, String> param = new HashMap<String, String>();
		for (Map.Entry<String, Object> entry : map.entrySet())
		{
			param.put(entry.getKey(), String.valueOf(entry.getValue()));
		}
		return param;
	}

	private static String sUserAgent;
	public static String getUserAgent(Context context){
		if (TextUtils.isEmpty(sUserAgent)) {
			StringBuilder sb = new StringBuilder();
			sb.append("ting_");
			sb.append(getVersionName(context));
			sb.append("(");
			try {
				sb.append(URLEncoder.encode(android.os.Build.MODEL, "utf-8"));
			} catch (UnsupportedEncodingException e) {
			}
			sb.append(",");
			sb.append("Android");
			sb.append(android.os.Build.VERSION.SDK_INT);
			sb.append(")");
			sUserAgent = sb.toString();
		}
		return sUserAgent;
	}

	private static String sVersionName;
	private static String getVersionName(Context context) {
		if (TextUtils.isEmpty(sVersionName)) {
			try {
				sVersionName = context.getPackageManager()
						.getPackageInfo(context.getPackageName(), 0).versionName;
				if (!TextUtils.isEmpty(sVersionName)) {
					String[] str = sVersionName.split("\\.");
					if (str != null && str.length > 3) {
						StringBuilder sb = null;
						for (int i = 0; i < 3; i++) {
							if (sb == null) {
								sb = new StringBuilder();
								sb.append(str[i]);
							} else {
								sb.append(".");
								sb.append(str[i]);
							}
						}
						if (sb != null) sVersionName = sb.toString();
					}
				}
			} catch (PackageManager.NameNotFoundException e) {
			}
		}
		return sVersionName;
	}
}
