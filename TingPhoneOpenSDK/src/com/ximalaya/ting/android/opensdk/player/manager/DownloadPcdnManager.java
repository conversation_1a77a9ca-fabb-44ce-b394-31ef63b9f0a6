//package com.ximalaya.ting.android.opensdk.player.manager;
//
//import android.content.Context;
//import android.net.Uri;
//import androidx.collection.ArrayMap;
//import com.baidu.pcdn.EdgeManager;
//import com.ximalaya.ting.android.encryptservice.DeviceTokenUtil;
//import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
//import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
//import com.ximalaya.ting.android.opensdk.httputil.XmDns;
//import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
//import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
//import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
//import com.ximalaya.ting.android.opensdk.util.SystemUtil;
//import com.ximalaya.ting.android.routeservice.RouterServiceManager;
//import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
//import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
//import com.ximalaya.ting.android.xmutil.Logger;
//
//import java.util.Map;
//
///**
// * <AUTHOR>
// * @date 2023/3/23 14:52
// */
//public class DownloadPcdnManager {
//    private static final String TAG = "DownloadPcdnManager";
//
//    public static Context sContext;
//
//    private Boolean mCanUse = null;
//    private boolean mCanUseForLocalCheck = true;
//    private boolean mHasInitialized = false;
//    private final Map<String, String> mWhiteMap = new ArrayMap<>();
//
//    private boolean mUseOutCn = false;
//
//    private DownloadPcdnManager() {
//        String whiteList = MmkvCommonUtil.getInstance(sContext)
//                .getString(PreferenceConstantsInOpenSdk.ITEM_DOWNLOAD_SDK_PCDN_WHITE_DOMAIN_LIST, null);
//        setDomainWhiteList(whiteList);
//
//        mUseOutCn = MmkvCommonUtil.getInstance(sContext).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_SDK_PCDN_OUT_CN, false);
//        Logger.d(TAG, "DownloadPcdnManager DownloadPcdnManager: " + mUseOutCn);
//    }
//
//    private static class Holder {
//        private static DownloadPcdnManager INSTANCE = new DownloadPcdnManager();
//    }
//
//    public static DownloadPcdnManager getInstance() {
//        return Holder.INSTANCE;
//    }
//
//    public boolean canUse() {
//        if (mCanUse == null) {
//            mCanUse = MmkvCommonUtil.getInstance(sContext).getBoolean(PreferenceConstantsInOpenSdk.KEY_USE_SDK_PCDN_FOR_DOWNLOAD, false);
//            String useBaiduPcdn = SystemUtil.getSystemProperty("debug.xmly.useBaiduPcdn", "");
//            Logger.d(TAG, "DownloadPcdnManager canUse: useBaiduPcdn=" + useBaiduPcdn + ", mCanUse=" + mCanUse);
//            if (useBaiduPcdn != null && useBaiduPcdn.length() > 0) {
//                mCanUse = "1".equals(useBaiduPcdn);
//            }
//            if (ConstantsOpenSdk.isDebug) {
//                if (XmDns.detectIfProxyExist(XmPlayerService.getPlayerSrvice())) {
//                    Logger.d(TAG, "DownloadPcdnManager detectIfProxyExist true");
//                    mCanUse = false;
//                }
//            }
//            mCanUseForLocalCheck = MmkvCommonUtil.getInstance(sContext).getBoolean(
//                    PreferenceConstantsInOpenSdk.KEY_USE_PCDN_SDK_LOCAL_CHECK, true);
//            Logger.d(TAG, "mCanUse: " + mCanUse + ",mCanUseForLocalCheck=" + mCanUseForLocalCheck);
//        }
//        return mCanUse;
//    }
//
//    public void init(Context context) {
//        if (!canUse()) {
//            return;
//        }
//
//        // 喜马线上businessId、ak、sk、clientId
//        final int iBusinessId = 3015;
//        final String strAk = "ecF88A54806FB851A2";
//        final String strSk = "E14C349081E647EC";
//
//        String deviceId = DeviceTokenUtil.getDeviceToken(context);
//        mHasInitialized = EdgeManager.init(context, "exGes5PzKRgxPuTMwU2nN9LGz5GG0s9x", iBusinessId, deviceId, strAk, strSk);
////        IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(IXdcsPost.class);
////        if (xdcsPost != null) {
////            xdcsPost.statErrorToXDCS("baiduSdkPcdn", "init failed, " + EdgeManager.getVersion());
////        }
//        Logger.d(TAG, "edgeSDK:" + EdgeManager.getVersion());
//    }
//
//    public void setDomainWhiteList(String domainWhiteList) {
//        Logger.d(TAG, "DownloadPcdnManager setDomainWhiteList: " + domainWhiteList);
//        if (domainWhiteList != null) {
//            String[] list = domainWhiteList.split(";");
//            if (list != null) {
//                int len = list.length;
//                for (int i = 0; i < len; i++) {
//                    String item = list[i];
//                    if (item != null && item.length() > 0) {
//                        mWhiteMap.put(item, null);
//                    }
//                }
//            }
//            if (Logger.isDebug) {
//                StringBuilder sb = new StringBuilder();
//                for (String key : mWhiteMap.keySet()) {
//                    sb.append(key).append(", ");
//                }
//                Logger.d(TAG, "DownloadPcdnManager setDomainWhiteList: " + sb.toString());
//            }
//        }
//    }
//
//    public String getUrl(String url, int type) {
//        if (!canUse()) {
//            Logger.d(TAG, "DownloadPcdnManager getUrl 1 canUse=false");
//            return url;
//        }
//        if (!mCanUseForLocalCheck) {
//            return url;
//        }
//        if (!mHasInitialized) {
//            Logger.d(TAG, "DownloadPcdnManager getUrl 2 mHasInitialized=false");
//            return url;
//        }
//        if (isFreeFlowServiceOn()) {
//            Logger.d(TAG, "DownloadPcdnManager getUrl 2-1 isFreeFlowServiceOn=true");
//            return url;
//        }
//        if (!mUseOutCn && !LocationManager.getInstance().isInCn()) {
//            return url;
//        }
//        try {
//            if (!EdgeManager.isInitialized()) {
//                Logger.d(TAG, "DownloadPcdnManager getUrl 3 isInitialized=false");
//                return url;
//            }
//            Uri uri = Uri.parse(url);
//            String domain = uri.getHost();
//
//            Logger.d(TAG, "DownloadPcdnManager getUrl 4 domain=" + domain);
//            if (mWhiteMap.containsKey(domain)) {
//                Logger.d(TAG, "DownloadPcdnManager getUrl 5 white map contain " + domain);
//                String rewriteUrl = EdgeManager.urlRewrite(url, type, "");
//                Logger.d(TAG, "DownloadPcdnManager getUrl 6 rewriteUrl=" + rewriteUrl);
//                return rewriteUrl;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        Logger.d(TAG, "DownloadPcdnManager getUrl 7 " + url);
//        return url;
//    }
//
//    private boolean isFreeFlowServiceOn() {
//        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
//        if (freeFlowService != null && freeFlowService.isUsingFreeFlow()) {
//            Logger.d(TAG, "DownloadPcdnManager isFreeFlowServiceOn true");
//            return true;
//        }
//        return false;
//    }
//}
