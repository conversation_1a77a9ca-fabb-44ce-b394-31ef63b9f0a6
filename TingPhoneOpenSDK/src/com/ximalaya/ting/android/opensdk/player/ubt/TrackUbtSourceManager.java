package com.ximalaya.ting.android.opensdk.player.ubt;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.ximalaya.ting.android.xmlog.XmLogger;

import com.ximalaya.ting.android.xmutil.Logger;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class TrackUbtSourceManager {
    private static final String TAG = "TrackUbtSourceManager";

    private static Map<String, String> sourceMap = new HashMap<>();
    private static String ubtSource;
    private static Map<String, Map<String, String>> ubtSourceMap = new HashMap<>();
    private static Gson gson = new Gson();

    public static synchronized void put(Long traceId, String ubtSource) {
        if (traceId == null) {
            return;
        }
        Logger.d(TAG, "TrackUbtSourceManager put traceId=" + traceId + ", ubtSource=" + ubtSource);
        if (!sourceMap.containsKey(String.valueOf(traceId))) {
            Logger.d(TAG, "TrackUbtSourceManager clear traceId=" + traceId + ", ubtSource=" + ubtSource);
            sourceMap.clear();
        }
        sourceMap.put(String.valueOf(traceId), ubtSource);
    }

    public static synchronized Map<String, String> parseUbtSource(String ubtSource) {
        Map<String, String> params = new HashMap<>();
        if (ubtSourceMap.containsKey(ubtSource)) {
            Map<String, String> existParams = ubtSourceMap.get(ubtSource);
            if (existParams != null) {
                params.putAll(existParams);
            }
            if (!params.containsKey("ubtSource") || TextUtils.isEmpty(params.get("ubtSource"))) {
                params.put("ubtSource", "[]");
            }
            return params;
        }
        if (!TextUtils.isEmpty(ubtSource)) {
            try {
                JSONObject object = new JSONObject(ubtSource);
                Iterator<String> iterator = object.keys();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    Object value = object.get(key);
                    if (value == null) {
                        continue;
                    }
                    params.put(key, value.toString());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (!params.containsKey("ubtSource") || TextUtils.isEmpty(params.get("ubtSource"))) {
            params.put("ubtSource", "[]");
        }
        return params;
    }

    public static synchronized String getUbtSourceString(String traceId) {
        if (traceId == null || !sourceMap.containsKey(traceId)) {
            return null;
        }
        return sourceMap.get(traceId);
    }

    public static synchronized void setUbtSource(String ubtSource) {
        TrackUbtSourceManager.ubtSource = ubtSource;
        Map<String, String> params = parseUbtSource(ubtSource);
        ubtSourceMap.put(ubtSource, params);
        if (!TextUtils.isEmpty(ubtSource) && ubtSource.contains("38745") && !ubtSource.contains("3047") && TextUtils.isEmpty(params.get("ubtTraceId"))) {
            StackTraceElement[] traceElements = Thread.currentThread().getStackTrace();
            StringBuilder sb = null;
            if (traceElements != null) {
                sb = new StringBuilder("{\"content\":[");
                int size = traceElements.length;
                if (size > 25) {
                    size = 25;
                }
                StackTraceElement element;
                for (int i = 2; i < size; i++) {
                    element = traceElements[i];
                    if (element.getClassName() != null) {
                        String className = element.getClassName();
                        if (className.startsWith("android.") || className.startsWith("androidx.")) {
                            break;
                        }
                    }
                    sb.append("\"").append(element.toString()).append("\",");
                }
                if (sb.charAt(sb.length() - 1) == ',') {
                    sb.deleteCharAt(sb.length() - 1);
                }
                sb.append("]}");
            }

            String time = String.valueOf(System.currentTimeMillis());
            HashMap<String, Object> playError = new HashMap<>();
            playError.put("time", time);
            playError.put("errorType", "99999");
            playError.put("log", sb == null ? "empty" : sb.toString());
            HashMap<String, Object> logParams = new HashMap<>();
            logParams.put("time", time);
            logParams.put("type", "1");
            logParams.put("playError", playError);
            XmLogger.log("apm", "appData", gson.toJson(logParams));
        }
    }

    public static String getUbtSource() {
        return ubtSource;
    }
}
