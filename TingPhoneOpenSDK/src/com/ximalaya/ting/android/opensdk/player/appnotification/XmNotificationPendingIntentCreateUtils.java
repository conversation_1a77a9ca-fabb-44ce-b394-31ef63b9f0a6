package com.ximalaya.ting.android.opensdk.player.appnotification;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.opensdk.player.receive.PlayerReceiver;
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerService;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

/**
 * Created by le.xin on 2020/12/7.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class XmNotificationPendingIntentCreateUtils {
    public static final String ITEM_NOTIFICATION_FLAG_IMMUTABLE_FIX = "item_notification_flag_immutable_fix";

    private PendingIntent mStartOrPauseIntent;
    private PendingIntent mNextIntent;
    private PendingIntent mPreIntent;
    private PendingIntent mCloseIntent;
    private PendingIntent mLikeIntent;
    private PendingIntent mListIntent;
    private PendingIntent mSignIntent;
    private PendingIntent mPlusTimeIntent;
    private PendingIntent mLessTimeIntent;
    private PendingIntent mDoNothingIntent; // 今日热点的广播不支持上下首切换 单独设置空的PendingIntent

    private String packageName;
    private Context mContext;
    private Class mTargetClass;

    public XmNotificationPendingIntentCreateUtils(Context context) {
        mContext = context;
    }

    void initPendingAsync(Context context, String packageName, Class targetClass) {
        this.mContext = context;
        this.packageName = packageName;
        this.mTargetClass = targetClass;

        MyAsyncTask.execute(() -> {
            getStartOrPausePendingIntent();
            getNextPendingIntent();
            getPrePendingIntent();
            getClosePendingIntent();
            getLikePendingIntent();
            getListPendingIntent();
            getSignPendingIntent();
            getPlusTimePendingIntent();
            getLessTimePendingIntent();
            getDoNothingPendingIntent();
        });
    }

    private Context getContext() {
        if(mContext == null) {
            if(XmPlayerService.getPlayerSrvice() != null) {
                mContext = XmPlayerService.getPlayerSrvice().getApplicationContext();
            }
        }

        return mContext;
    }

    public PendingIntent getStartOrPausePendingIntent() {
        if (mStartOrPauseIntent != null) {
            return mStartOrPauseIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mStartOrPauseIntent != null) {
                return mStartOrPauseIntent;
            }

            String actionName = XmNotificationCreater.ACTION_CONTROL_START_PAUSE;
            if ("com.ximalaya.ting.android".equals(packageName)) {
                actionName = XmNotificationCreater.ACTION_CONTROL_START_PAUSE_MAIN;
            }
            Intent intent = new Intent(actionName);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            intent.setClass(getContext(), PlayerReceiver.class);
            mStartOrPauseIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_PLAY,
                    intent, getPendingIntentFlag());

            return mStartOrPauseIntent;
        }
    }

    PendingIntent getNextPendingIntent() {
        if (mNextIntent != null) {
            return mNextIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mNextIntent != null) {
                return mNextIntent;
            }

            String actionName = XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT;
            if ("com.ximalaya.ting.android".equals(packageName)) {
                actionName = XmNotificationCreater.ACTION_CONTROL_PLAY_NEXT_MAIN;
            }
            Intent intent = new Intent(actionName);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            intent.setClass(getContext(), PlayerReceiver.class);
            mNextIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_NEXT, intent, getPendingIntentFlag());
            return mNextIntent;
        }
    }

    PendingIntent getPrePendingIntent() {
        if (mPreIntent != null) {
            return mPreIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mPreIntent != null) {
                return mPreIntent;
            }

            String actionName = XmNotificationCreater.ACTION_CONTROL_PLAY_PRE;
            if ("com.ximalaya.ting.android".equals(packageName)) {
                actionName = XmNotificationCreater.ACTION_CONTROL_PLAY_PRE_MAIN;
            }
            Intent intent = new Intent(actionName);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            intent.setClass(getContext(), PlayerReceiver.class);
            mPreIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_PRE, intent, getPendingIntentFlag());

            return mPreIntent;
        }
    }

    /**
     * 今日热点的广播直播 只支持暂停、开播
     * */
    PendingIntent getDoNothingPendingIntent() {
        if (mDoNothingIntent != null) {
            return mDoNothingIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mDoNothingIntent != null) {
                return mDoNothingIntent;
            }
            mDoNothingIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_NOTHING, new Intent(), getPendingIntentFlag());

            return mDoNothingIntent;
        }
    }

    PendingIntent getClosePendingIntent() {
        if (mCloseIntent != null) {
            return mCloseIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mCloseIntent != null) {
                return mCloseIntent;
            }

            String actionName = XmNotificationCreater.ACTION_CONTROL_CLOSE;
            if ("com.ximalaya.ting.android".equals(packageName)) {
                actionName = XmNotificationCreater.ACTION_CONTROL_CLOSE_MAIN;
            }
            Intent intent = new Intent(actionName);
            intent.setClass(getContext(), PlayerReceiver.class);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            mCloseIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_CLOSE, intent, getPendingIntentFlag());

            return mCloseIntent;
        }
    }

    PendingIntent getLikePendingIntent() {
        if (mLikeIntent != null) {
            return mLikeIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mLikeIntent != null) {
                return mLikeIntent;
            }
            String actionName = XmNotificationCreater.ACTION_CONTROL_LIKE;
            Intent intent = new Intent(actionName);
            intent.setClass(getContext(), PlayerReceiver.class);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            mLikeIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_LIKE, intent, getPendingIntentFlag());

            return mLikeIntent;
        }
    }

    @Nullable
    PendingIntent getListPendingIntent() {
        if (mTargetClass == null) {
            return null;
        }

        if (mListIntent != null) {
            return mListIntent;
        }

        synchronized (XmPlayerService.class) {
            if (mListIntent != null) {
                return mListIntent;
            }

            Intent notificationIntent = new Intent(getContext(), mTargetClass);
            notificationIntent.setAction(XmNotificationCreater.ACTION_CONTROL_SHOW_LIST);
            notificationIntent.putExtra(XmNotificationCreater.NOTIFICATION_EXTRY_KEY,
                    XmNotificationCreater.NOTIFICATION_EXTYR_DATA);
            mListIntent = PendingIntent.getActivity(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_LIST, notificationIntent, getPendingIntentFlag());

            return mListIntent;
        }
    }


    PendingIntent getSignPendingIntent() {
        if (mTargetClass == null) {
            return null;
        }

        if (mSignIntent != null) {
            return mSignIntent;
        }


        synchronized (XmPlayerService.class) {
            if (mSignIntent != null) {
                return mSignIntent;
            }

            Intent notificationIntent = new Intent(getContext(), mTargetClass);
            notificationIntent.setAction(XmNotificationCreater.ACTION_CONTROL_SHOW_SIGNIN);
            notificationIntent.putExtra(XmNotificationCreater.NOTIFICATION_EXTRY_KEY,
                    XmNotificationCreater.NOTIFICATION_EXTYR_DATA);
            mSignIntent = PendingIntent.getActivity(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_SIGN, notificationIntent, getPendingIntentFlag());

            return mSignIntent;
        }
    }

    PendingIntent getPlusTimePendingIntent(){
        if(mPlusTimeIntent != null) {
            return mPlusTimeIntent;
        }

        synchronized (XmPlayerService.class) {
            if(mPlusTimeIntent != null) {
                return mPlusTimeIntent;
            }

            String actionName = XmNotificationCreater.ACTION_CONTROL_PLUS_TIME;
            if ("com.ximalaya.ting.android".equals(packageName)) {
                actionName = XmNotificationCreater.ACTION_CONTROL_PLUS_TIME_MAIN;
            }
            Intent intent = new Intent(actionName);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            intent.setClass(getContext(), PlayerReceiver.class);
            try {
                mPlusTimeIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_PLUS, intent, getPendingIntentFlag());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return mPlusTimeIntent;
        }
    }

    PendingIntent getLessTimePendingIntent(){
        if(mLessTimeIntent != null) {
            return mLessTimeIntent;
        }

        synchronized (XmPlayerService.class) {
            if(mLessTimeIntent != null) {
                return mLessTimeIntent;
            }

            String actionName = XmNotificationCreater.ACTION_CONTROL_LESS_TIME;
            if ("com.ximalaya.ting.android".equals(packageName)) {
                actionName = XmNotificationCreater.ACTION_CONTROL_LESS_TIME_MAIN;
            }
            Intent intent = new Intent(actionName);
            intent.putExtra(XmNotificationCreater.EXTRA_FROM_NOTIFICATION, true);
            try {
                //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/42092886?pid=1
                intent.setClass(getContext(), PlayerReceiver.class);
                mLessTimeIntent = PendingIntent.getBroadcast(getContext(), PendingIntentRequestConstants.NOTIFICATION_REQUEST_LESS, intent, getPendingIntentFlag());
            } catch (Exception e) {
                e.printStackTrace();
            }

            return mLessTimeIntent;
        }
    }


    public static int getPendingIntentFlag() {
        int flag = PendingIntent.FLAG_UPDATE_CURRENT;
        boolean fixImmutable = MMKVUtil.getInstance().getBoolean(ITEM_NOTIFICATION_FLAG_IMMUTABLE_FIX, true);
        if (android.os.Build.VERSION.SDK_INT >= 23 && fixImmutable) {
            // 修复PendingIntent安全漏洞
            flag = flag | PendingIntent.FLAG_IMMUTABLE;
        }
        return flag;
    }
}
