package com.tmall.wireless.vaf.virtualview.Helper;

import androidx.annotation.DrawableRes;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/23 20:58
 */
public class LocalImageProvider {
    private static final Map<String, Integer> sImageMap = new HashMap<>();

    public static void addLocalImage(String key, @DrawableRes int resId) {
        sImageMap.put(key, resId);
    }

    @DrawableRes
    public static int getLocalImage(String key) {
        if (sImageMap.containsKey(key)) {
            return sImageMap.get(key);
        }
        return -1;
    }
}
