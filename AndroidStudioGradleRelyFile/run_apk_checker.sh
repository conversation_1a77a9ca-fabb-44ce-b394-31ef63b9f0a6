#!/bin/bash

thisFilePath=$(cd "$(dirname "$0")"; pwd)
pipelineHistoryId=$1
canCheckParam=false

function getdir(){
    for file in $1/*
    do
    if test -f $file
    then
        if [[ "$file" = *apk ]]; then
          echo $file
          arr=(${arr[*]} $file)
        fi
    else
        getdir $file
    fi
    done
}
getdir "$thisFilePath/TingMainHost/Application/build/outputs/apk"
echo ${arr[0]}
apkFile=${arr[0]}

checkerPath="$thisFilePath/apkChecker.jar"
outputPath="$thisFilePath/TingMainHost/TingMainApp/constant-resource/manifest_check"
comparePath="$thisFilePath/manifest/manifest_9.1.75.3.json"
completeParamString="--format json --apk ${apkFile} --output ${outputPath} --compareUrl ${comparePath} -manifest"
comLine=" -jar $checkerPath $completeParamString"
echo "[RunApkChe<PERSON>]  comLine: $comLine"
echo -e "\n"
echo -e "\n"
java $comLine

if [ "$canCheckParam" = "true" ]
then
  echo "[RunApkChecker]  开始 runCheck"
  checkerPath="$thisFilePath/apkChecker.jar"
  echo "[RunApkChecker]  checkerPath: $checkerPath"
  time=$(date "+%y%m%d")
  apkPath="$thisFilePath"
  targetDir="$thisFilePath/TingMainHost/Application/build/outputs/apk/and-f5-ocpa/release"
  for fileOrDir in `ls $targetDir`
  do
    [[ "$fileOrDir" = *apk ]] && apkPath="$targetDir/$fileOrDir" || echo "[RunApkChecker]  This file is not apk file, which is $fileOrDir"
  done
  targetDir="$thisFilePath/TingMainHost/Application/build/outputs/mapping/and-f5-ocpaRelease"
  mappingPath="$thisFilePath/TingMainHost/Application/build/outputs/mapping/and-f5-ocpaRelease/mapping.txt"
  for fileOrDir in `ls $targetDir`
  do
    [[ "$fileOrDir" = *mapping.txt ]] && mappingPath="$targetDir/$fileOrDir" || echo "[RunApkChecker]  This file is not mapping file, which is $fileOrDir"
  done
  echo "[RunApkChecker]  apkFile: $apkFile"
  echo "[RunApkChecker]  mappingPath: $mappingPath"
  rFilePath="$thisFilePath/TingMainHost/Application/build/intermediates/runtime_symbol_list/and-f5-ocpaRelease/R.txt"
  echo "[RunApkChecker]  rFilePath: $rFilePath"
  moduleJsonPath="$thisFilePath/modules.json"
  echo "[RunApkChecker]  moduleJsonPath: $moduleJsonPath"
  targetDir="/Users/<USER>/dev/sdk/build-tools"
  nmtool=""
  outputPath="$thisFilePath/TingMainHost/TingMainApp/constant-resource/manifest_check"
  comparePath="$thisFilePath/XAndroidFramework/manifest_9.1.93.6_729.json"

#  completeParamString="--apk ${apkPath} --rtxt ${rFilePath} --mapping ${mappingPath} --nmtool ${nmtool} --module ${moduleJsonPath} --result_url http://pack.xmly.work/mobile-devops-backend-prod/packageSizeHistory/file/upload --pipeLineHistoryId $pipelineHistoryId"
  completeParamString="--apk ${apkFile} --rtxt ${rFilePath} --mapping ${mappingPath} --nmtool ${nmtool} --module ${moduleJsonPath} --format json --output ${outputPath} --compareUrl ${comparePath} -manifest"
  comLine=" -jar $checkerPath $completeParamString"
  echo "[RunApkChecker]  comLine: $comLine"
  echo -e "\n"
  echo -e "\n"
  java $comLine
fi
