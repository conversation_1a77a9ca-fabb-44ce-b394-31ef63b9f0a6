package com.ximalaya.ting.android.update;

import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.Map;

/**
 * Created by nali on 2018/7/11.
 * <AUTHOR>
 */

public interface UpdateManagerCallback {

    DialogBuilder getDialogBuilder();

    void showCustomToast(String message);

    void finishActivity();

    void baseGetRequest(String url, Map<String, String> params,
                        IDataCallBack<CheckVersionResult> iDataCallBack);
}
