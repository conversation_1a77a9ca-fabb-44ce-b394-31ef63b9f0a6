package com.ximalaya.ting.android.reactnative.modules.aiagent

import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.AudioManager.OnAudioFocusChangeListener
import android.os.Build
import android.util.Base64
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.LifecycleEventListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.WritableMap
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.uimanager.UIManagerModule
import com.ximalaya.aiagentconnect.sdk.connection.listener.IConnectStateListener
import com.ximalaya.aiagentconnect.sdk.connection.network.AgentCharlesUtil
import com.ximalaya.aiagentconnect.sdk.logger.XYLogger
import com.ximalaya.aiagentconnect.sdk.logger.XYTrace
import com.ximalaya.ting.android.aiagent.IRealTimeCallStatusListener
import com.ximalaya.ting.android.aiagent.XiaoYaSDKConfig
import com.ximalaya.ting.android.aiagent.XmAgent
import com.ximalaya.ting.android.aiagent.core.logger.AgentLog
import com.ximalaya.ting.android.aiagent.core.record.Callback
import com.ximalaya.ting.android.aiagent.message.IMessageDirectiveListener
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.chatxmly.ChatXmlyPopupManager
import com.ximalaya.ting.android.host.chatxmly.ChatXmlyPopupManager.checkRecordPermission
import com.ximalaya.ting.android.host.chatxmly.ChatXmlyPopupManager.newChatXmlyPopup
import com.ximalaya.ting.android.host.chatxmly.util.markRedDotClick
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ai.AiAgentInstance
import com.ximalaya.ting.android.host.manager.ai.radio.AiRadioOnlinePlayUtil
import com.ximalaya.ting.android.host.manager.ai.radio.AiRadioPlayHelper
import com.ximalaya.ting.android.host.manager.ai.radio.IRadioPlayerListener
import com.ximalaya.ting.android.host.manager.ai.radio.RadioPart
import com.ximalaya.ting.android.host.manager.ai.radio.RadioPlayHistoryUtil
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.SpeechRecognitionRouterUtil
import com.ximalaya.ting.android.host.util.common.ConcurrentHashSet
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.reactnative.utils.RNUtils
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.xmutil.SystemServiceManager
import org.json.JSONArray
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicInteger

/**
 * 不要瞎JB往这里面加方法，这是智能体小雅/波波专用的
 * */
@ReactModule(name = ChatxmlyAiAgentModule.NAME)
class ChatxmlyAiAgentModule(reactContext: ReactApplicationContext?) :
    ReactContextBaseJavaModule(reactContext) {
    private val wkContext = WeakReference(reactApplicationContext)
    private val mAudioManager: AudioManager? by lazy {
        SystemServiceManager.getAudioManager(
            wkContext.get()
        )
    }
    private val mWindow = BaseApplication.getTopActivity()?.window
    private var disableSendBinary = false
    private var lastToken: String? = null
    private var mXmAgent: XmAgent? = null

    private var connectStateListener: IConnectStateListener = object : IConnectStateListener {
        override fun onConnectFail(errorCode: Int) {
            sendEvent("AiAgentConnectError", errorCode)
        }

        override fun onAudioConnectFail(errorCode: Int) {
            sendEvent("AiAgentAudioChannelConnectError", errorCode)
        }

        override fun onConnected() {
            sendEvent("AiAgentConnectState", true)
        }

        override fun onAudioConnected() {
            super.onAudioConnected()
            sendEvent("AiAgentAudioChannelConnectState", true)
        }

        override fun onDisconnected() {
            sendEvent("AiAgentConnectState", false)
        }

        override fun onAudioDisconnected() {
            sendEvent("AiAgentAudioChannelConnectState", false)
        }
    }
    private var radioPlayerListener: IRadioPlayerListener = object : IRadioPlayerListener {
        override fun onPlayStart() {
            sendEvent("AiAgentPlayerStatusBegin", createAgentMap())
        }

        override fun onPlayPause() {
            sendEvent("AiAgentPlayerStatusPause", createAgentMap())
        }

        override fun onPlayProgress(currPos: Int, duration: Int) {
            val params = createAgentMap()
            params.putString("currentTime", currPos.toString())
            RNUtils.sendEvent(
                wkContext.get(),
                "AiAgentPlayerStatusProgress",
                params
            )
        }

        override fun onPlayEnd() {
            val params = createAgentMap()
            params.putBoolean("end", false)
            sendEvent("AiAgentPlayerStatusEnd", params)
        }

        override fun onPlayFinish() {
            super.onPlayFinish()
            // 完播直接清除历史记录
            RadioPlayHistoryUtil.removePlayHistory(AiRadioPlayHelper.getCurPart()?.radioId)
            val params = createAgentMap()
            params.putBoolean("end", true)
            sendEvent("AiAgentPlayerStatusEnd", params)
        }

        override fun onPlayStop() {
            val params = createAgentMap()
            sendEvent("AiAgentPlayerStatusStop", params)
        }

        override fun onPlayError(msg: String) {
            val params = createAgentMap()
            params.putString("msg", msg)
            sendEvent("AiAgentPlayerStatusError", params)
        }
    }

    private var directiveListener: IMessageDirectiveListener =
        object : IMessageDirectiveListener() {

            override fun onBinaryMessage(data: ByteArray) {
                super.onBinaryMessage(data)
                if (!disableSendBinary) {
//                dlog("onBinaryMessage ${this@ChatxmlyAiAgentModule}")
                    RNUtils.sendEvent(
                        wkContext.get(), "AiAgentReceiveBufferBinary",
                        Base64.encodeToString(
                            data,
                            Base64.DEFAULT
                        )
                    )
                }
            }

            override fun onAllMessage(message: String) {
                // 临时代码，屏蔽aiRadio的消息
                if (message.contains("\"moduleType\":\"list\"") && message.contains("ttsStart|ttsEnd|ttsFinish".toRegex())) {
                    if (message.contains("ttsStart")) {
//                        dlog("ttsStart $disableSendBinary")
                        disableSendBinary = true
                    }
                    if (message.contains("ttsEnd")) {
//                        dlog("ttsEnd $disableSendBinary")
                        disableSendBinary = false
                    }
                } else {
                    RNUtils.sendEvent(
                        wkContext.get(), "AiAgentReceiveMessage",
                        message
                    )
                    if (saveMessageId.isNullOrEmpty().not() && message.contains(saveMessageId!!)) {
                        saveMessageSet.add(message)
                    }
                }
            }
        }
    private var callStatusListener: IRealTimeCallStatusListener =
        object : IRealTimeCallStatusListener {
            override fun onStatusChanged(status: XmAgent.CallState) {
                sendEvent("AiRealTimeCallStatus", status.name)
            }

            override fun onStatusChanged(status: XmAgent.CallState, text: String) {}
        }
//    private val lifecycleEventListener = object : LifecycleEventListener {
//        override fun onHostResume() {
//            AiRadioPlayHelper.agentPageState = "resume"
//        }
//
//        override fun onHostPause() {
//            AiRadioPlayHelper.agentPageState = "pause"
//        }
//
//        override fun onHostDestroy() {
//            dlog("agent page destroy >>>")
//            reactApplicationContext.removeLifecycleEventListener(this)
//            exitPage()
//        }
//    }

    private val pingCount = AtomicInteger(0)

    private val pingRunnable = Runnable {
        val currentCount = pingCount.incrementAndGet()
        if (currentCount > 10) {
            dlog("ping timeout >>>")
            pingExit()
            return@Runnable
        } else {
            sendPingAndDelay()
        }
    }

    private fun pingExit() {
        HandlerManager.removeCallbacks(pingRunnable)
        AiRadioPlayHelper.agentPageState = ""
        mAudioManager?.abandonAudioFocus(mAudioFocusChangeListener)
        removeListener()
        AiRadioPlayHelper.checkDisconnectAgent()
    }

    private fun sendPingAndDelay() {
//        dlog("ping rn >>>")
        AiRadioPlayHelper.agentPageState = "resume"
        RNUtils.sendEvent(
            wkContext.get(), "AiAgentSendPingMessageEvent",
            null
        )
        HandlerManager.postOnBackgroundThreadDelay(pingRunnable, 2 * 1000)
    }

    init {
        AgentLog.init(reactApplicationContext, true, "")
        AgentCharlesUtil.setAllow(ConstantsOpenSdk.isDebug)
        dlog("init agent >>> $this")
    }

    private fun createAgentMap(): WritableMap {
        val params = Arguments.createMap().apply {
            putString("partId", AiRadioPlayHelper.getCurPart()?.partId ?: "")
            putString("radioId", AiRadioPlayHelper.getCurPart()?.radioId ?: "")
            putString("episodeId", AiRadioPlayHelper.getCurPart()?.episodeId ?: "")
        }
        return params
    }

    private fun addListener() {
        mXmAgent?.addConnectStateListener(connectStateListener)
        mXmAgent?.addDirectiveListener(directiveListener)
        mXmAgent?.addRealTimeCallStatusListener(callStatusListener)
        AiRadioPlayHelper.addPlayListener(radioPlayerListener)
        AiRadioOnlinePlayUtil.startListen()
//        reactApplicationContext.addLifecycleEventListener(lifecycleEventListener)
    }

    private fun removeListener() {
        mXmAgent?.removeConnectStateListener(connectStateListener)
        mXmAgent?.removeDirectiveListener(directiveListener)
        mXmAgent?.removeRealTimeCallStatusListener(callStatusListener)
        AiRadioPlayHelper.removePlayListener(radioPlayerListener)
        AiRadioOnlinePlayUtil.removeListener()
//        reactApplicationContext.removeLifecycleEventListener(lifecycleEventListener)
    }

    override fun getName(): String {
        return NAME
    }

    companion object {
        const val NAME = "ChatxmlyAiAgent"
        private var rnInitConfig: AiAgentInitConfig? = null
        private val saveMessageSet = LinkedHashSet<String>()
        private var saveMessageId: String? = null
    }

    @ReactMethod
    fun useRTCServer(promise: Promise) {
        if (ConstantsOpenSdk.isDebug) {
            val debugValue = ToolUtil.getSystemProperty("debug.ai_agent_rtc", "0")
            if (debugValue == "1") {
                promise.resolve(true)
                return
            } else if (debugValue == "-1") {
                promise.resolve(false)
                return
            }
        }
        promise.resolve(AiAgentInstance.useRTCServer())
    }

    @ReactMethod
    fun isFullCallMode(promise: Promise) {
        promise.resolve(AiAgentInstance.useFullCallMode)
    }

    @ReactMethod
    fun updateContext(str: String) {
        HandlerManager.postOnUIThread {
            mXmAgent?.updateContext(str)
        }
    }

    @ReactMethod
    fun initAiAgentSDK(promise: Promise) {
        initAgentSDK(null, promise)
    }

    @ReactMethod
    fun initAgentSDK(map: ReadableMap?, promise: Promise) {
        HandlerManager.postOnUIThread {
            dlog("initAgentSDK start >>> $map ${this@ChatxmlyAiAgentModule}")
            sendPingAndDelay()
            val connectType = runCatching {
                RNUtils.optStringFromRMap(
                    map,
                    "connectType"
                ).toInt()
            }.getOrDefault(XiaoYaSDKConfig.CONNECT_TYPE_WEB_SOCKET)
            val channelMode =
                RNUtils.optStringFromRMap(map, "channelMode") ?: XiaoYaSDKConfig.CHANNEL_ALL
            val onlyServer = RNUtils.optBooleanFromRMap(map, "onlyServer", true)
            val forceInit =
                rnInitConfig == null || (rnInitConfig?.connectType != connectType)
            if (!forceInit && AiAgentInstance.wkAgent?.get() != null) {
                dlog("SDK已初始化，无需再次初始化")
                mXmAgent = AiAgentInstance.wkAgent?.get()
                removeListener()
                addListener()
                promise.resolve(true)
                return@postOnUIThread
            }
            rnInitConfig = AiAgentInitConfig(connectType)
            AiAgentInstance.init(connectType, channelMode, onlyServer)
            mXmAgent = AiAgentInstance.wkAgent?.get()
            AiRadioPlayHelper.initAiRadioPlayer()
            XYTrace.callback = object : XYTrace.XYTraceCallback {
                override fun trace(actionKey: String, map: Map<String, String>?) {
                    XDCSCollectUtil.statErrorToXDCS(
                        NAME,
                        "key >>> $actionKey , map>>>${map.toString()}"
                    )
                }
            }
            addListener()
            if (connectType == XiaoYaSDKConfig.CONNECT_TYPE_RTC) {
                SpeechRecognitionRouterUtil.getBundle(object : Router.SimpleBundleInstallCallback {
                    override fun onInstallSuccess(bundleModel: BundleModel?) {
                        promise.resolve(true)
                    }

                    override fun onLocalInstallError(t: Throwable?, bundleModel: BundleModel?) {
                        super.onLocalInstallError(t, bundleModel)
                        promise.resolve(false)
                    }

                    override fun onRemoteInstallError(t: Throwable?, bundleModel: BundleModel?) {
                        super.onRemoteInstallError(t, bundleModel)
                        promise.resolve(false)
                    }
                })
            } else {
                promise.resolve(true)
            }
            dlog("initAgentSDK end >>>")
        }
    }

    @ReactMethod
    fun sendCustomMessage(message: String) {
        HandlerManager.postOnUIThread {
//            dlog("sendCustomMessage >>> $message")
            mXmAgent?.sendCustomMsg(message)
        }
    }

    @ReactMethod
    fun sendCustomAudioData(data: String?) {
        data ?: return
//        dlog("sendCustomAudioData >>> $data")
        HandlerManager.postOnUIThread {
            mXmAgent?.sendCustomAudioData(Base64.decode(data, Base64.DEFAULT))
        }
    }

    @ReactMethod
    fun interrupt(promise: Promise) {
        HandlerManager.postOnUIThread {
            val interrupt = mXmAgent?.sendInterruptCode()
            promise.resolve(interrupt)
        }
    }

    @ReactMethod
    fun connect() {
        HandlerManager.postOnUIThread {
            dlog("connect >>> $mXmAgent , connected=${mXmAgent?.isConnected()}")
            if (mXmAgent?.isConnected() == true) {
                dlog("当前已连接，无需再次连接")
                return@postOnUIThread
            }
            if (lastToken == null || lastToken != UserInfoMannage.getToken()) {
                lastToken = UserInfoMannage.getToken()
                mXmAgent?.setToken(UserInfoMannage.getUid().toString(), UserInfoMannage.getToken())
            }
            mXmAgent?.connect()
        }
    }

    @ReactMethod
    fun disconnect() {
        HandlerManager.postOnUIThread {
            dlog("disconnect >>>")
            mXmAgent?.disconnect()
        }
    }

    @ReactMethod
    fun isConnected(promise: Promise) {
        HandlerManager.postOnUIThread {
            promise.resolve(mXmAgent?.isConnected() == true)
        }
    }

    @ReactMethod
    fun startRecord(promise: Promise) {
        HandlerManager.postOnUIThread {
            mXmAgent?.startRecord(object : Callback {
                override fun resolve(isSuccess: Any?) {
                    promise.resolve(isSuccess)
                }

                override fun reject(code: String?, message: String?) {
                    promise.reject(code, message)
                }
            })
        }
    }

    @ReactMethod
    fun startRecognizeAndRecord(promise: Promise) {
        HandlerManager.postOnUIThread {
            mXmAgent?.startRecognizeAndRecord(object : Callback {
                override fun resolve(p0: Any?) {
                    promise.resolve(p0)
                }

                override fun reject(p0: String?, p1: String?) {
                    promise.reject(p0, p1)
                }
            })
        }
    }


    @ReactMethod
    fun muteAudio(mute: Boolean) {
        HandlerManager.postOnUIThread {
            mXmAgent?.muteLocalAudio(mute)
        }
    }

    @ReactMethod
    fun mutePlay(mute: Boolean) {
        HandlerManager.postOnUIThread {
            mXmAgent?.mutePlay(mute)
        }
    }

    @ReactMethod
    fun stopRecord(promise: Promise) {
        HandlerManager.postOnUIThread {
            mXmAgent?.stopRecord(object : Callback {
                override fun resolve(p0: Any?) {
                    promise.resolve(p0)
                }

                override fun reject(p0: String?, p1: String?) {
                    promise.reject(p0, p1)
                }
            })
        }
    }

    @ReactMethod
    fun startRealTimeCall(
        agent: String?,
        scene: String?,
        dialogId: String?,
        functionAgentId: String?,
        trackId: String?,
        callMode: Boolean?,
        voiceProcessingEnabled: Boolean?
    ) {
        HandlerManager.postOnUIThread {
//            disableSendBinary = true
//            mXmAgent?.updateContext( "{\"app\":{\"package\":\"com.gemd.iting\",\"page\":\"rn_ai_chat\",\"params\":{\"agent\":{\"name\":\"xiaoya\",\"scene\":\"Search\"}}}}")
            mXmAgent?.startRealTimeCall(
                agent,
                scene,
                dialogId,
                functionAgentId?.toLongOrNull() ?: 0L,
                trackId?.toLongOrNull() ?: 0L,
                callMode,
                voiceProcessingEnabled
            )
        }
    }

    @ReactMethod
    fun endRealTimeCall() {
        HandlerManager.postOnUIThread {
//            disableSendBinary = false
            lastToken = null
            mXmAgent?.endRealTimeCall()
        }
    }

    @ReactMethod
    fun destroy() {
        HandlerManager.postOnUIThread {
            dlog("destroy >>>")
            removeListener()
            mAudioManager?.abandonAudioFocus(mAudioFocusChangeListener)
            mXmAgent = null
            AiRadioPlayHelper.release()
            AiAgentInstance.release()
        }
    }

    @ReactMethod
    fun exitPage() {
    }

    @ReactMethod
    fun exitPageNew() {
        HandlerManager.postOnUIThread {
            dlog("exitPage >>>")
            HandlerManager.removeCallbacks(pingRunnable)
            removeListener()
            AiRadioPlayHelper.agentPageState = ""
            mAudioManager?.abandonAudioFocus(mAudioFocusChangeListener)
            AiRadioPlayHelper.checkDisconnectAgent()
//            mXmAgent = null
        }
    }

    @ReactMethod
    fun requestAudioFocus() {
        HandlerManager.postOnUIThread {
            if (mAudioManager == null) {
                return@postOnUIThread
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val result = mAudioManager?.requestAudioFocus(
                    AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                        .setWillPauseWhenDucked(true)
                        .setOnAudioFocusChangeListener(mAudioFocusChangeListener)
                        .build()
                )
                Logger.e(
                    "ChatXmlyAiAgentModule",
                    "requestAudioFocus = $result"
                )
            } else {
                mAudioManager?.requestAudioFocus(
                    mAudioFocusChangeListener,
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
                )
            }
        }
    }

    @ReactMethod
    fun getKeyBoardMode(promise: Promise) {
        promise.resolve(mWindow?.attributes?.softInputMode ?: -1)
    }

    @ReactMethod
    fun setKeyBoardMode(mode: Int) {
        HandlerManager.postOnUIThread {
            mWindow?.setSoftInputMode(mode)
        }
    }

    private val mAudioFocusChangeListener =
        OnAudioFocusChangeListener { focusChange ->
            if (focusChange == AudioManager.AUDIOFOCUS_LOSS || focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
                try {
                    Logger.e(
                        "ChatXmlyAiAgentModule",
                        "onAudioFocusChange = $focusChange"
                    )
                    sendEvent("AiAgentInterruptBegin", null)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

    private fun sendEvent(eventName: String, data: Any?) {
        runCatching {
            dlog("sendEvent: event=$eventName , data=$data")
            RNUtils.sendEvent(wkContext.get(), eventName, data)
        }
    }

    @ReactMethod
    fun playAiRadio(map: ReadableMap) {
        HandlerManager.postOnUIThread {
            // 实时流启播可能有空调用，直接忽略
            val list = RNUtils.optArrayFromRMap(map, "list")
            if (list == null || list.size() == 0) {
                dlog("playAiRadio >>> list==null")
                return@postOnUIThread
            }
            val radioParts = mutableListOf<RadioPart>()
            for (i in 0 until list.size()) {
                val mm = list.getMap(i)
                radioParts.add(
                    RadioPart(
                        radioId = RNUtils.optStringFromRMap(mm, "radioId") ?: "0",
                        episodeId = RNUtils.optStringFromRMap(mm, "episodeId") ?: "0",
                        title = RNUtils.optStringFromRMap(mm, "title") ?: "",
                        coverUrl = RNUtils.optStringFromRMap(mm, "coverUrl") ?: "",
                        partId = RNUtils.optStringFromRMap(mm, "partId") ?: "",
                        seekTime = RNUtils.optStringFromRMap(mm, "seekTime")?.toIntOrNull() ?: 0,
                        messageId = RNUtils.optStringFromRMap(mm, "messageId") ?: "",
                        trackId = RNUtils.optStringFromRMap(mm, "trackId")?.toLongOrNull() ?: 0L,
                        url = RNUtils.optStringFromRMap(mm, "url") ?: "",
                    )
                )
            }
            AiRadioPlayHelper.playOfflineAiRadioList(radioParts)
        }
    }

    @ReactMethod
    fun resumeRadioPlay(promise: Promise?) {
        HandlerManager.postOnUIThread {
            dlog("resumeRadioPlay >>>")
            val result = AiRadioPlayHelper.play()
            promise?.resolve(result)
        }
    }

    @ReactMethod
    fun pauseRadioPlay() {
        dlog("pauseRadioPlay >>>")
        HandlerManager.postOnUIThread {
            AiRadioPlayHelper.pause()
        }
    }

    @ReactMethod
    fun stopRadioPlay() {
        HandlerManager.postOnUIThread {
            dlog("stopRadioPlay >>>")
            AiRadioPlayHelper.stop()
        }
    }

    @ReactMethod
    fun clearAiRadio() {
        dlog("clearAiRadio >>>")
        HandlerManager.postOnUIThread {
            AiRadioPlayHelper.clear()
        }
    }

    /**
     * 存储启播的电台
     * */
    @ReactMethod
    fun updateAiRadioInfo(map: ReadableMap) {
        HandlerManager.postOnUIThread {
            dlog("updateAiRadioInfo >>> $map")
            val radioId = if (map.hasKey("radioId")) map.getString("radioId") ?: "" else ""
            val title = RNUtils.optStringFromRMap(map, "title") ?: ""
            val coverUrl = RNUtils.optStringFromRMap(map, "coverUrl") ?: ""
            val episodeId = RNUtils.optStringFromRMap(map, "episodeId") ?: ""
            val partId = RNUtils.optStringFromRMap(map, "partId") ?: ""
            val url = RNUtils.optStringFromRMap(map, "url") ?: ""
            AiRadioPlayHelper.rnAgent =
                RNUtils.optStringFromRMap(map, "agent") ?: AiRadioPlayHelper.rnAgent
            AiRadioPlayHelper.rnScene =
                RNUtils.optStringFromRMap(map, "scene") ?: AiRadioPlayHelper.rnScene
            AiRadioPlayHelper.rnintent =
                RNUtils.optStringFromRMap(map, "chatXmlyIntent") ?: AiRadioPlayHelper.rnintent
            val part = RadioPart(
                radioId = radioId,
                episodeId = episodeId,
                title = title,
                coverUrl = coverUrl,
                partId = partId,
                messageId = RNUtils.optStringFromRMap(map, "messageId") ?: "",
                url = url
            )
            AiRadioPlayHelper.updateAiRadioInfo(part)
        }
    }

    @ReactMethod
    fun getAiRadioInfo(map: ReadableMap?, promise: Promise) {
        HandlerManager.postOnUIThread {
            val radioId = RNUtils.optStringFromRMap(map, "radioId") ?: ""
            val episodeId = RNUtils.optStringFromRMap(map, "episodeId") ?: ""
            val radioPart = AiRadioPlayHelper.getAiRadioInfo(radioId, episodeId)
            if (radioPart == null) {
                promise.resolve("")
            } else {
                promise.resolve(RNUtils.jsonToReact(JSONObject(GsonUtils.toJson(radioPart))))
            }
        }
    }

    /**
     * 存储指定消息
     * */
    @ReactMethod
    fun saveMessageToMemory(map: ReadableMap) {
        val messageId = RNUtils.optStringFromRMap(map, "messageId") ?: ""
        saveMessageId = messageId
        saveMessageSet.clear()
        dlog("saveMessageToMemory >>> $messageId")
    }

    @ReactMethod
    fun getMemoryMessage(map: ReadableMap, promise: Promise) {
        val index = RNUtils.optIntFromRMap(map, "index")
        val id = RNUtils.optStringFromRMap(map, "messageId") ?: ""
        if (saveMessageId != id || index >= saveMessageSet.size) {
            dlog("getMemoryMessage >>> messageId or index error ,$saveMessageId==$id $index==${saveMessageSet.size}")
            promise.resolve(null)
            return
        }
        val result = kotlin.runCatching {
            val jsonArray = JSONArray()
            val sms = saveMessageSet.toList()
            sms.subList(index, sms.size).forEach {
                jsonArray.put(it)
            }
            RNUtils.jsonToReact(jsonArray)
        }
            .onFailure { dlog("getMemoryMessage error , messageById >>> $it , index >>> $index , ${it.message}") }
            .getOrNull()
        dlog("getMemoryMessage $saveMessageId==$id >>> size=${result?.size()},total=${saveMessageSet.size} $saveMessageSet")
        promise.resolve(result)
    }

    /**
     * ping-pong
     * */
    @ReactMethod
    fun checkAiAgentViewLeave(map: ReadableMap) {
//        dlog("rn pong >>>")
        pingCount.set(0)
    }


    /**
     * 电台播放 >>> end
     * */

    private fun dlog(msg: String) {
        XYLogger.log(NAME, msg)
    }

    @ReactMethod
    fun dlog(tag: String, msg: String) {
        XYLogger.log(tag, msg)
    }

    @Deprecated("")
    @ReactMethod
    fun initWebSocket(promise: Promise?) {
    }

    @Deprecated("")
    @ReactMethod
    fun initRTC(promise: Promise?) {
    }

//    override fun onCatalystInstanceDestroy() {
//        super.onCatalystInstanceDestroy()
//        dlog("rn >>> destroy complete")
//        exitPage()
//    }

    @ReactMethod
    fun canUseSDK(promise: Promise) {
        promise.resolve(true)
    }

    /**
     * 反面教材 》》》 瞎JB乱加的代码 ，by zhangYang
     * */

    private val NORMAL_MIC = 1
    private val XIAOYA_MIC = 2

    @ReactMethod
    fun getMicStyle(promise: Promise) {
        if (ChildXmlyTipManager.shouldShowTip()) {
            promise.resolve(ChildXmlyTipManager.showTipType())
        } else {
            promise.resolve(NORMAL_MIC)
        }
    }

    @ReactMethod
    fun clickVoiceAssistantInSearch(tag: Int) {
        HandlerManager.postOnUIThread {
            val activity =
                BaseApplication.getTopActivity() as? MainActivity ?: return@postOnUIThread
            val useChatXmly = ChatXmlyPopupManager.useChatXmlyVoiceAssistant()
            if (ChildXmlyTipManager.shouldShowTip()) {
                ChildXmlyTipManager.goChildChatXmly("newCategoryPage")
            } else if (useChatXmly) {
                checkRecordPermission(ToolUtil.getCtx()) {
                    try {
                        val context = wkContext.get()
                        val uiManager = context?.getNativeModule(UIManagerModule::class.java)
                        uiManager?.addUIBlock { nvhm ->
                            val view = nvhm.resolveView(tag)
                            if (view != null && view.isShown) {
                                val mChatXmlyPopupWindow =
                                    newChatXmlyPopup(activity, "首页", null, false)
                                mChatXmlyPopupWindow.show(view, BaseUtil.dp2px(context, 6f))
                                markRedDotClick(context)
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            } else {
                try {
                    SpeechRecognitionRouterUtil.getBundle { bundleModel: BundleModel? ->
                        val iTing = "iting://open?msg_type=94&bundle=rn_assistant&reuse=true&type=0"
                        ToolUtil.clickUrlAction(activity, iTing, null)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
}