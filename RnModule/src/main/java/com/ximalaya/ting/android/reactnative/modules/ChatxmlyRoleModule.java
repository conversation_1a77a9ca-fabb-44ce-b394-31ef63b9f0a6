package com.ximalaya.ting.android.reactnative.modules;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.module.annotations.ReactModule;
import com.ximalaya.ting.android.host.manager.ChildXmlyTipManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.xmutil.Logger;


@ReactModule(name = ChatxmlyRoleModule.NAME)
public class ChatxmlyRoleModule extends ReactContextBaseJavaModule {
    private static final String TAG = "ChatxmlyRoleModule";

    public static final String NAME = "ChatxmlyRole";


    public ChatxmlyRoleModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return NAME;
    }


    @ReactMethod
    public void onRoleChange(final String role) {
        if (role == null) {
            return;
        }
        HandlerManager.postOnUIThread(new Runnable() {
            @Override
            public void run() {
                Logger.e(TAG, "onRoleChange = " + role);
                ChildXmlyTipManager.INSTANCE.changeTypeForRN(role);
            }
        });
    }
}