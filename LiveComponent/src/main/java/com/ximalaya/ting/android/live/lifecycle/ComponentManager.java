package com.ximalaya.ting.android.live.lifecycle;

import android.view.View;
import android.view.ViewGroup;

import androidx.lifecycle.Lifecycle;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 */
public abstract class ComponentManager implements IComponentManager {

    protected IComponentHost<?> mComponentHost;

    protected List<Component> mChildComponents = new CopyOnWriteArrayList<>();

    public List<Component> getChildComponents() {
        return mChildComponents;
    }

    protected java.util.concurrent.CopyOnWriteArrayList<ComponentLifecycleCallback> mLifecycleCallbacks = new CopyOnWriteArrayList<>();

    public void registerComponentLifecycleCallbacks(ComponentLifecycleCallback callback) {
        if (mLifecycleCallbacks.contains(callback)) {
            return;
        }
        mLifecycleCallbacks.add(callback);
    }

    public void unRegisterComponentLifecycleCallbacks(ComponentLifecycleCallback callback) {
        mLifecycleCallbacks.remove(callback);
    }

    public ComponentManager(IComponentHost<?> parent) {
        this.mComponentHost = parent;
        this.mComponentHost.setComponentManager(this);
    }

    @Override
    public IComponentHost<?> getComponentHost() {
        return mComponentHost;
    }

    protected void onComponentAdd(Component component) {
        mChildComponents.add(component);
    }

    protected void onComponentRemove(Component component) {
        mChildComponents.remove(component);
    }

    public void addComponent(Component component) {
        component.setState(State.NONE);
        component.setLifecycleDispatcher(mComponentLifecycleDispatcher);
        component.setComponentHost(mComponentHost);
        component.setFragment(mComponentHost.getHostContext().getFragment());
        Operation operation = new AddOperation(mComponentHost, component, mComponentHost.getState());
        operation.run();
        onComponentAdd(component);
    }

    @Override
    public boolean removeComponent(String name) {
        try {
            Component component = getCreatedComponent(name);
            if(component != null){
                removeComponent(component);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void removeComponent(Component component) {
        Operation operation = new RemoveOperation(mComponentHost, component);
        operation.run();
        onComponentRemove(component);
    }

    @Override
    public void dispatchCustomLifecycle(Lifecycle.Event event) {
        State state = ComponentStateExchange.getComponentStateByLifeEvent(event);
        for (Component component : mChildComponents) {
            Operation operation = new MoveOperation(mComponentHost, component, state);
            operation.run();
        }
    }

    public void dispatchChildrenState(State toState) {
        if (mChildComponents != null && mChildComponents.size() > 0) {
            for (Component component : mChildComponents) {
                Operation operation = new MoveOperation(mComponentHost, component, toState);
                operation.run();
            }
        }
    }


    static class MoveOperation extends Operation {

        public MoveOperation(IComponentHost<?> componentParent, Component component, State state) {
            super(componentParent, component, state);
        }

        void moveState(Component component, State destState) {
            switch (destState) {
                case INITIALIZING:
                    if (component.getState() == State.NONE) {
                        component.dispatchInitializing(mComponentHost.getHostContext().getActivity());
                    }
                    break;
                case CREATED:
                    if (component.getState() == State.INITIALIZING) {
                        dispatchComponentCreateAndCreateViewLifecycle(component);
                    }
                    break;
                case STARTED:
                    if (component.getState() == State.STOP
                            || component.getState() == State.VIEW_CREATED) {
                        component.dispatchStart();
                    }
                    break;
                case RESUMED:
                    if (component.getState() == State.STARTED
                            || component.getState() == State.PAUSED) {
                        component.dispatchResume();
                    }
                    break;
                case PAUSED:
                    if (component.getState() == State.RESUMED) {
                        component.dispatchPause();
                    }
                    break;
                case STOP:
                    if (component.getState() == State.PAUSED) {
                        component.dispatchStop();
                    }
                    break;
                case DESTROY:
                    if (component.getState() == State.STOP) {
                        component.dispatchDestroy();
                        Utils.removeFromParentView(component.getView());
                        component.setParentComponent(null);
                    }
                    break;
            }
        }
    }

    static class RemoveOperation extends Operation {

        public RemoveOperation(IComponentHost<?> componentParent, Component component) {
            super(componentParent, component, componentParent.getState());
        }

        @Override
        void moveState(Component component, State state) {
            switch (state) {
                case INITIALIZING:
                case CREATED:
                case VIEW_CREATED:
                case STARTED:
                case RESUMED:
                case PAUSED:
                    component.dispatchPause();
                    component.dispatchStop();
                    component.dispatchRemove();
                    component.dispatchDestroy();
            }
        }
    }

    static class AddOperation extends Operation {

        public AddOperation(IComponentHost<?> componentParent, Component component, State fromState) {
            super(componentParent, component, fromState);
        }

        void moveState(Component component, State fromState) {
            CompLogger.i("AddOperation moveState " + fromState);
            switch (fromState) {
                case INITIALIZING:
                    component.dispatchInitializing(mComponentHost.getHostContext().getActivity());
                    component.setFragment(mComponentHost.getHostContext().getFragment());
                    break;
                case CREATED:
                    if (component.getState() == State.NONE) {
                        component.dispatchInitializing(mComponentHost.getHostContext().getActivity());
                        component.setFragment(mComponentHost.getHostContext().getFragment());
                    }
                    dispatchComponentCreateAndCreateViewLifecycle(component);
                    break;
                case STARTED:
                case RESUMED:
                    if (component.getState() == State.NONE) {
                        component.dispatchInitializing(mComponentHost.getHostContext().getActivity());
                        component.setFragment(mComponentHost.getHostContext().getFragment());
                    }
                    dispatchComponentCreateAndCreateViewLifecycle(component);
                    component.dispatchStart();
                    component.dispatchResume();
                    break;
                case PAUSED:
                    component.dispatchInitializing(mComponentHost.getHostContext().getActivity());
                    component.setFragment(mComponentHost.getHostContext().getFragment());
                    dispatchComponentCreateAndCreateViewLifecycle(component);
                    component.dispatchStart();
                    component.dispatchResume();
                    component.dispatchPause();
                    break;
                case STOP:
                    component.dispatchInitializing(mComponentHost.getHostContext().getActivity());
                    component.setFragment(mComponentHost.getHostContext().getFragment());
                    dispatchComponentCreateAndCreateViewLifecycle(component);
                    component.dispatchStart();
                    component.dispatchResume();
                    component.dispatchPause();
                    component.dispatchStop();
                    break;
            }
        }
    }


    abstract static class Operation {

        IComponentHost<?> mComponentHost;

        Component mComponent;

        State mFromState;

        public Operation(IComponentHost<?> componentHost, Component component, State state) {
            mComponentHost = componentHost;
            mComponent = component;
            mFromState = state;
        }

        abstract void moveState(Component component, State state);

        public void run() {
            moveState(mComponent, mFromState);
        }

        void dispatchComponentCreateAndCreateViewLifecycle(Component component) {
            component.dispatchCreate();
            ViewGroup container = null;
            if (mComponent.getComponentContainerId() != 0) {
                try {
                    container = mComponentHost.getHostContext().findViewById(mComponent.getComponentContainerId());
                } catch (Exception e) {
                    XDCSCollectUtil.statErrorToXDCS("liveComponent", "component " + component.getComponentName()
                            + "dispatchComponentCreateAndCreateView error containerId = " + component.getComponentContainerId()
                            + " " + e.getMessage());
                }
            }
            component.dispatchCreateView(container);
            component.tryAddComponentViewInternal();
        }
    }

    public interface ComponentLifecycleCallback {
        default void onInit(Component component) {
        }

        default void onCreate(Component component) {
        }

        default void onViewCreated(Component component, View view) {
        }

        default void onStart(Component component) {
        }

        default void onResume(Component component) {
        }

        default void onPause(Component component) {
        }

        default void onStop(Component component) {
        }

        default void onDestroy(Component component) {
        }
    }

    protected IComponentLifecycleDispatcher mComponentLifecycleDispatcher = new IComponentLifecycleDispatcher() {
        @Override
        public void onComponentInit(Component component) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onInit(component);
            }
        }

        @Override
        public void onComponentCreate(Component component, View view) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onCreate(component);
            }
        }

        @Override
        public void onComponentViewCreated(Component component, View view) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onViewCreated(component, view);
            }
            ComponentEventManager.componentInflated(component);
        }

        @Override
        public void onComponentStart(Component component) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onStart(component);
            }
        }

        @Override
        public void onComponentResume(Component component) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onResume(component);
            }
        }

        @Override
        public void onComponentPause(Component component) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onPause(component);
            }
        }

        @Override
        public void onComponentStop(Component component) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onStop(component);
            }
        }

        @Override
        public void onComponentDestroy(Component component) {
            for (ComponentLifecycleCallback callback : mLifecycleCallbacks) {
                callback.onDestroy(component);
            }
        }
    };

}
