<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_container"
    android:background="@color/host_color_ffffff_131313"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:visibility="visible">

        <ImageView
            android:id="@+id/main_album_back_btn"
            android:layout_height="50dp"
            android:layout_width="44dp"
            android:paddingTop="13dp"
            android:paddingBottom="13dp"
            android:paddingLeft="14dp"
            android:paddingEnd="6dp"
            android:scaleType="fitXY"
            android:layout_gravity="center_vertical"
            android:contentDescription="返回"
            android:layout_marginLeft="3dp"
            android:src="@drawable/host_ic_standard_back_arrow_left"
            android:tint="@color/host_color_131313_ffffff"
            tools:ignore="UseAppTint" />

        <com.ximalaya.ting.android.main.albumModule.view.AlbumPagerSlidingTabStrip3
            android:id="@+id/main_album_content_indicator"
            android:layout_gravity="center"
            style="@style/host_my_pager_sliding_tab_strip_style"
            android:textSize="15sp"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"
            android:background="@drawable/main_finding_titlebar_bg"
            app:pstsActivateTextColor="@color/main_color_131313_ffffff"
            app:pstsDeactivateTextColor="@color/main_color_131313_dcdcdc"
            app:pstsActivateTabTextBold="true"
            app:pstsShouldExpand="true"
            android:fadingEdgeLength="30dp"
            android:requiresFadingEdge="horizontal"
            android:fadingEdge="horizontal"
            app:pstsSmoothScroll="true"
            app:pstsTabPaddingLeftRight="10dp"
            app:pstsTextAllCaps="true"
            app:pstsshouldExpandByContent="true"
            app:pstsIndicatorColor="@color/host_color_xmRed"
            app:pstsIndicatorWidth="20dp"
            app:pstsIndicatorHeight="4dp"
            app:pstsIndicatorCornerRadius="1dp"
            app:pstsUnderlineHeight="4dp"
            app:pstsUnderlineColor="@color/host_transparent"/>
    </FrameLayout>

    <com.ximalaya.ting.android.host.view.other.MyViewPager
        android:id="@+id/main_view_pager"
        app:layout_constraintTop_toBottomOf="@+id/main_title_bar"
        app:layout_constraintBottom_toTopOf="@+id/main_album3_bottom_divider_view"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <View
        android:id="@+id/main_album3_bottom_divider_view"
        android:background="@color/host_color_14000000"
        app:layout_constraintBottom_toTopOf="@+id/main_album_bottom_control_layout"
        android:layout_width="match_parent"
        android:layout_height="1px" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_album_bottom_control_layout"
        android:layout_gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@color/main_color_ffffff_181818"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="74dp">

        <View
            android:id="@+id/main_album3_bottom_shadow2"
            android:background="@color/main_transparent_14ffffff"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/main_album_single_book_tv_layout_bottom"
            android:orientation="vertical"
            android:layout_width="44dp"
            android:layout_marginEnd="10dp"
            android:layout_height="44dp"
            android:visibility="visible"
            android:layout_marginStart="@dimen/host_x16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:gravity="center"
            android:layout_gravity="center">

            <ImageView
                android:id="@+id/main_album_single_book_iv_bottom"
                android:layout_gravity="center"
                android:src="@drawable/main_album3_bottom_book_icon"
                android:layout_width="16dp"
                android:layout_height="16dp" />

            <TextView
                android:id="@+id/main_album_single_book_tv_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="sans-serif-light"
                android:gravity="center_vertical"
                android:layout_marginTop="2dp"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/main_color_111111_ffffff"
                android:textSize="13sp"
                android:textStyle="bold"
                android:ellipsize="end"
                android:text="看书" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/main_album_single_subscribe_tv_layout_bottom"
            android:orientation="horizontal"
            android:layout_marginStart="10dp"
            app:layout_goneMarginStart="@dimen/host_x16"
            android:layout_width="0dp"
            android:layout_height="44dp"
            app:layout_constraintStart_toEndOf="@+id/main_album_single_book_tv_layout_bottom"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_play_control_layout_bottom"
            app:layout_constraintHorizontal_weight="1"
            android:gravity="center"
            android:background="@drawable/main_8corner_f8f8f8_33ffffff"
            android:layout_gravity="center">

            <TextView
                android:id="@+id/main_album_single_subscribe_tv_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="sans-serif-light"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/main_color_111111_ffffff"
                android:textSize="15sp"
                android:textStyle="bold"
                android:ellipsize="end"
                android:text="免费订阅" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/main_tv_play_control_layout_bottom"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:orientation="horizontal"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="@dimen/host_x16"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/main_album_single_subscribe_tv_layout_bottom"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="center"
            android:background="@drawable/main_8corner_xm_red"
            android:layout_gravity="center">

            <ImageView
                android:id="@+id/main_iv_play_control_bottom"
                android:layout_gravity="center"
                android:background="@drawable/main_album3_bottoom_play_bg_icon"
                android:layout_width="16dp"
                android:layout_height="16dp" />

            <TextView
                android:id="@+id/main_tv_play_control_start_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:textColor="@color/main_color_ffffff"
                android:textSize="15sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-light"
                android:maxLines="1"
                tools:text="续播" />

            <View
                android:id="@+id/main_tv_play_control_divider_bottom"
                android:layout_marginStart="4dp"
                android:background="#66ffffff"
                android:layout_width="1px"
                android:layout_height="12dp"/>

            <TextView
                android:id="@+id/main_tv_play_control_end_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="1px"
                android:fontFamily="sans-serif-light"
                android:gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:textColor="@color/main_color_ffffff"
                android:textSize="13sp"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="白夜行 第111白夜行白夜行白夜行白夜行" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>