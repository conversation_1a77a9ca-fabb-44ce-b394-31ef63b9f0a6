<?xml version="1.0" encoding="utf-8"?><!-- 对这个布局文件的修改应该同步到main_item_track_comment_dark.xml的暗色版本 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:makeramen="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_track_comment_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical"
    tools:background="@color/main_white">

    <View
        android:id="@+id/main_divide"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="60dp"
        android:background="@color/main_color_e8e8e8_2a2a2a" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_comment_image"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="8dp"
            android:scaleType="fitXY"
            android:src="@drawable/host_default_avatar_88"
            makeramen:border_color="@color/main_color_e8e8e8_2a2a2a"
            makeramen:border_width="0.5px"
            makeramen:corner_radius="72dp"
            makeramen:pressdown_shade="true" />

        <com.ximalaya.ting.android.host.view.EllipsizeLayout
            android:id="@+id/main_ll_author"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/main_comment_image"
            android:layout_toLeftOf="@+id/main_iv_recommend_hot_comment"
            android:layout_toRightOf="@+id/main_comment_image">

            <TextView
                android:id="@+id/main_comment_name"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/main_color_666666_888888"
                android:textSize="15sp"
                tools:text="西瓜" />

            <ImageView
                android:id="@+id/main_iv_owner_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:src="@drawable/host_ic_anchor_tag"
                android:gravity="center"
                android:text="主播"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_rl_concern_people"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:background="@drawable/host_bg_rect_f8f9fa_radius_8"
                android:gravity="center_vertical"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:text="关注的人"
                android:textColor="@color/host_color_999999_888888"
                android:textSize="9sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.TalentLogoView
                android:id="@+id/main_v_talent_logo"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignTop="@id/main_comment_name"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="6dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.socialModule.view.CommentTagLogoView
                android:id="@+id/main_iv_comment_ximi_tag"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="6dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.socialModule.view.CommentTagLogoView
                android:id="@+id/main_vip_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:contentDescription="@string/main_iv_cd_vip"
                android:src="@drawable/main_vip_fra_vip_colorful"
                android:visibility="gone"
                tools:visibility="visible" />

        </com.ximalaya.ting.android.host.view.EllipsizeLayout>


        <TextView
            android:id="@+id/main_create_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/main_ll_author"
            android:layout_toRightOf="@+id/main_comment_image"
            android:textColor="@color/main_color_999999_888888"
            android:textSize="12sp"
            tools:text="9天前" />


        <LinearLayout
            android:id="@+id/main_v_like"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="-6dp"
            android:clipChildren="false"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/main_like_count"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:textColor="@color/main_color_999999_888888"
                android:textSize="12sp"
                tools:text="999" />

            <RelativeLayout
                android:id="@+id/main_rl_like"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="-6dp"
                android:clipChildren="false">

                <ImageView
                    android:id="@+id/main_iv_like"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:scaleType="centerCrop"
                    android:src="@drawable/host_ic_comment_like_selector"
                    android:visibility="visible"
                    tools:visibility="visible" />

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_iv_lottie_like"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:padding="10dp"
                    android:visibility="visible"
                    android:scaleType="centerInside"
                    app:lottie_autoPlay="false"
                    app:lottie_fileName="lottie/host_lottie_for_like_action.json" />
            </RelativeLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/main_iv_recommend_hot_comment"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/main_checkbox_selector"
            android:visibility="invisible" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_layout_main_comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">
            <com.ximalaya.ting.android.main.view.text.StaticLayoutView
                android:id="@+id/main_comment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:visibility="visible"
                tools:layout_height="100dp"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.main.view.VisibilityPerceptionView
                android:id="@+id/main_v_tip_anchor"
                android:layout_width="1px"
                android:layout_height="1px"
                android:layout_alignBottom="@id/main_comment"/>
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/main_voice_comment"
            android:layout_width="162dp"
            android:layout_height="44dp"
            android:layout_marginTop="16dp"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/main_pic_voice_background" />

            <ImageView
                android:id="@+id/main_iv_voice"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="12dp"
                android:layout_marginBottom="10dp"
                android:src="@drawable/main_anim_voice" />

            <TextView
                android:id="@+id/main_tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:text="18"
                android:textColor="@color/main_white"
                android:textSize="16sp" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/main_v_pic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            android:layout_marginBottom="16dp"
            tools:layout_height="100dp"
            tools:layout_width="100dp"
            tools:visibility="visible" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/main_v_like_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginBottom="8dp"
        android:layout_marginRight="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/main_tv_like_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.2"
            android:maxLines="1"
            tools:text="觉得很赞"
            android:textColor="@color/main_color_333333_ffffff"
            android:textSize="12sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_layout_album_reply"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginBottom="8dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/main_bg_track_comment_reply"
        android:orientation="vertical"
        android:padding="10dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/main_iv_pic_anchor"
            android:layout_width="1px"
            android:layout_height="1px" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:layout_height="48dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/main_comment_reply_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/main_ic_comment_arrow_right"
            android:drawablePadding="4dp"
            android:drawableTint="@color/main_color_4990e2"
            android:includeFontPadding="false"
            android:paddingTop="10dp"
            android:textColor="@color/main_blue_4990E2"
            android:textSize="13sp"
            android:visibility="gone"
            tools:text="查看更多评论"
            tools:visibility="visible" />
    </LinearLayout>

    <View
        android:id="@+id/main_v_bottom_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="60dp"
        android:background="@color/main_color_e8e8e8_2a2a2a" />
</LinearLayout>
