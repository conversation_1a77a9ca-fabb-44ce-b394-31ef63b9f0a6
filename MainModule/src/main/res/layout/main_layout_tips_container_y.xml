<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    tools:background="@color/host_color_111111">

    <LinearLayout
        android:id="@+id/main_function_area_tips_part"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:adjustViewBounds="true"
        android:gravity="center_horizontal|center_vertical"
        android:visibility="visible"
        android:orientation="vertical"
        android:layout_marginHorizontal="76dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
    </LinearLayout>

    <FrameLayout
        android:id="@+id/main_ai_guide_container"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <Space
        android:id="@+id/main_v_tips_placeholder"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/main_danmu_entry"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:src="@drawable/main_ic_danmaku_entry"
        android:layout_marginEnd="14dp"
        android:layout_width="32dp"
        android:layout_height="32dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>