<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    tools:background="@color/main_gray">

    <View
        android:id="@+id/main_space_rec_album"
        android:layout_width="match_parent"
        android:layout_marginLeft="@dimen/host_x16"
        android:layout_marginRight="@dimen/host_x16"
        android:layout_height="@dimen/host_default_divider_height"
        android:background="@color/main_color_ffffff_alpha_20"
        android:visibility="gone"
        tools:visibility="visible"
        tools:layout_marginTop="@dimen/host_y10" />

    <TextView
        android:id="@+id/main_tv_rec_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/host_x15"
        android:gravity="center_vertical"
        android:minHeight="44dp"
        android:text="@string/main_related_album"
        android:textStyle="bold"
        android:textColor="@color/main_color_ffffff_cfcfcf"
        android:textSize="15sp" />

    <LinearLayout
        android:id="@+id/main_layout_related_albums"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone" />

    <View
        android:id="@+id/main_divider1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_default_divider_height"
        android:background="@color/main_divide"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_more_recommend_album"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@drawable/main_bg_rect_black_alpha_20_radius_4"
        android:layout_marginLeft="@dimen/host_x16"
        android:layout_marginRight="@dimen/host_x16"
        android:gravity="center"
        android:text="@string/main_album_recommend_more"
        android:textColor="@color/main_color_cfcfcf"
        android:textSize="14sp"
        android:visibility="visible" />

</LinearLayout>