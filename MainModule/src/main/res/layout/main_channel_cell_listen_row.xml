<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/main_riv_cover"
        android:layout_width="76dp"
        android:layout_height="67dp"
        app:albumCoverSize="67dp"
        app:corner_radius="4dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/host_default_album" />

    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:ellipsize="end"
        app:layout_constraintVertical_chainStyle="packed"
        android:lineSpacingMultiplier="1.1"
        android:maxLines="2"
        android:textStyle="bold"
        android:fontFamily="sans-serif-light"
        android:textColor="@color/main_color_333333_dcdcdc"
        android:textSize="15sp"
        app:layout_constraintBottom_toTopOf="@+id/main_tv_anchor_score"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/main_riv_cover"
        app:layout_constraintTop_toTopOf="@id/main_riv_cover"
        tools:text="老人与海（海明威代表作）" />

    <LinearLayout
        android:id="@+id/main_tv_anchor_score"
        android:orientation="horizontal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/host_y7"
        app:layout_constraintBottom_toBottomOf="@id/main_riv_cover"
        app:layout_constraintEnd_toEndOf="@id/main_tv_title"
        app:layout_constraintStart_toStartOf="@id/main_tv_title"
        app:layout_constraintTop_toBottomOf="@id/main_tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>