<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/main_ll_content"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/main_corner10_bg_ccffffff_1e1e1e"
        android:orientation="vertical">

        <TextView
            android:id="@+id/main_tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:fontFamily="sans-serif"
            android:text="关注成功"
            android:textColor="@color/main_color_111111_cfcfcf"
            android:textSize="17sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="你可以在【动态】页面查看主播的动态"
            android:textColor="@color/host_color_333333_cfcfcf"
            android:textSize="15sp" />

        <ImageView
            android:id="@+id/main_iv_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal" />

        <TextView
            android:id="@+id/main_tv_submit"
            android:layout_width="200dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="20dp"
            android:background="@drawable/main_bg_ff4646_radius_29"
            android:gravity="center"
            android:text="我知道了"
            android:textColor="#ffffff"
            android:textSize="15sp" />
    </LinearLayout>
</FrameLayout>