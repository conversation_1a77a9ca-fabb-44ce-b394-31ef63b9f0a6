<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="36dp"
    android:background="@color/main_color_fce9e1_3c322e">

    <ImageView
        android:id="@+id/main_iv_vip_tag"
        android:layout_width="wrap_content"
        android:layout_height="11dp"
        android:layout_marginStart="@dimen/host_default_side_margin"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/host_ic_child_vip_label" />

    <TextView
        android:id="@+id/main_tv_vip_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_vip_action_btn"
        app:layout_constraintStart_toEndOf="@+id/main_iv_vip_tag"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="本节目为会员畅听内容"
        android:textColor="@color/main_color_732f06_ffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/main_tv_vip_action_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/host_default_side_margin"
        android:drawableRight="@drawable/host_ic_jump_n_thin_line_regular_6x12"
        android:drawablePadding="3dp"
        android:textSize="13sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:drawableTint="#E87B4E"
        tools:text="首月6元"
        android:textColor="#E87B4E" />


</androidx.constraintlayout.widget.ConstraintLayout>