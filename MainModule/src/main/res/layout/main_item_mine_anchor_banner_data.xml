<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="16dp"
    android:paddingEnd="16dp">

    <View
        android:id="@+id/main_v_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/main_bg_rect_eef1f4_0ffff_radius_4"/>

    <ImageView
        android:id="@+id/main_iv_title"
        android:layout_width="55dp"
        android:layout_height="15dp"
        android:layout_marginStart="12dp"
        android:scaleType="centerInside"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/main_ic_topic_tag" />

    <ImageView
        android:id="@+id/main_iv_bracket"
        android:layout_width="7dp"
        android:layout_height="24dp"
        android:layout_marginStart="10dp"
        android:scaleType="centerInside"
        android:singleLine="true"
        android:src="@drawable/main_ic_anchor_bracket"
        android:textColor="@color/main_color_666666_8d8d91"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/main_iv_title"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_tv_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/main_color_666666_8d8d91"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/main_tv_subscribe"
        app:layout_constraintStart_toEndOf="@id/main_iv_bracket"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="播放" />

    <TextView
        android:id="@+id/main_tv_subscribe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/main_color_666666_8d8d91"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/main_tv_fans"
        app:layout_constraintStart_toEndOf="@id/main_tv_play"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="订阅" />

    <TextView
        android:id="@+id/main_tv_fans"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textColor="@color/main_color_666666_8d8d91"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/main_tv_subscribe"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="粉丝" />

</androidx.constraintlayout.widget.ConstraintLayout>