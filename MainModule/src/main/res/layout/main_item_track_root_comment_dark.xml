<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_track_comment_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical"
    tools:background="@color/main_black">

    <View
        android:id="@+id/main_divide"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="60dp"
        android:background="@color/main_color_2a2a2a" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="8dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView xmlns:makeramen="http://schemas.android.com/apk/res-auto"
            android:id="@+id/main_comment_image"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="8dp"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_avatar_88"
            makeramen:border_color="@color/main_color_2a2a2a"
            makeramen:border_width="0.5px"
            makeramen:corner_radius="72dp"
            makeramen:pressdown_shade="true" />

        <com.ximalaya.ting.android.host.view.EllipsizeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/main_ll_author"
            android:layout_alignTop="@id/main_comment_image"
            android:layout_toRightOf="@+id/main_comment_image"
            android:layout_toLeftOf="@+id/main_iv_recommend_hot_comment">

            <TextView
                android:id="@+id/main_comment_name"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:textColor="@color/main_color_888888"
                android:textSize="15sp"
                tools:text="西瓜"
                android:maxLines="1"
                android:ellipsize="end"/>

            <RelativeLayout
                android:id="@+id/main_iv_owner_title"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignTop="@id/main_comment_name"
                android:layout_marginRight="4dp"
                android:layout_toRightOf="@id/main_comment_name">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text=" (主播)"
                    android:textColor="@color/main_color_888888"
                    android:textSize="15sp"
                    android:visibility="visible"
                    tools:visibility="visible" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/main_vip_tag"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignTop="@id/main_comment_name"
                android:layout_toRightOf="@+id/main_iv_owner_title"
                android:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="4dp"
                    android:contentDescription="@string/main_iv_cd_vip"
                    android:scaleType="fitCenter"
                    android:src="@drawable/main_vip_fra_vip_colorful"
                    tools:visibility="visible" />
            </RelativeLayout>

            <com.ximalaya.ting.android.host.view.TalentLogoView
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignTop="@id/main_comment_name"
                android:layout_toEndOf="@+id/main_vip_tag"
                android:layout_marginStart="6dp"
                android:id="@+id/main_v_talent_logo"
                android:layout_gravity="center_vertical"
                tools:visibility="visible"
                android:visibility="invisible"/>

        </com.ximalaya.ting.android.host.view.EllipsizeLayout>

        <TextView
            android:id="@+id/main_create_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/main_ll_author"
            android:layout_marginTop="4dp"
            android:layout_toRightOf="@+id/main_comment_image"
            android:textColor="@color/main_color_888888"
            android:textSize="12sp"
            tools:text="9天前" />

        <ImageView
            android:id="@+id/main_iv_recommend_hot_comment"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/main_checkbox_selector"
            android:visibility="invisible" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_layout_main_comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:orientation="vertical">

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:layout_height="100dp"
            android:textSize="15sp"
            android:visibility="visible" />

        <RelativeLayout
            android:id="@+id/main_voice_comment"
            android:layout_width="162dp"
            android:layout_height="44dp"
            android:layout_marginTop="16dp"
            android:visibility="gone">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/main_pic_voice_background" />

            <ImageView
                android:id="@+id/main_iv_voice"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="12dp"
                android:layout_marginBottom="10dp"
                android:src="@drawable/main_anim_voice" />

            <TextView
                android:id="@+id/main_tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:text="18"
                android:textColor="@color/main_white"
                android:textSize="16sp" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/main_v_pic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="gone" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/main_v_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="14dp"
        android:clipChildren="false">

        <TextView
            android:id="@+id/main_like_count"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            tools:text="999"
            android:visibility="gone"
            android:textColor="@color/main_color_888888"
            android:textSize="12sp" />

        <RelativeLayout
            android:id="@+id/main_rl_like"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="-10dp"
            android:clipChildren="false">

            <ImageView
                android:id="@+id/main_iv_like"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_centerInParent="true"
                android:scaleType="centerCrop"
                android:visibility="visible"
                android:src="@drawable/main_ic_root_comment_like_selector"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                android:id="@+id/main_iv_lottie_like"
                android:layout_width="80dp"
                android:layout_height="80dp"
                app:lottie_autoPlay="false"
                android:visibility="visible" />
        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/main_v_like_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/main_tv_like_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:maxLines="1"
            tools:text="他们都点赞了他们都点赞了他们都点赞了他们都点赞了他们都点赞了"
            android:lineSpacingMultiplier="1.2"
            android:textColor="@color/main_color_888888"
            android:textSize="12sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_layout_album_reply"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="4dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/main_bg_track_comment_reply"
        android:orientation="vertical"
        android:paddingLeft="11dp"
        android:paddingTop="12dp"
        android:paddingRight="11dp"
        android:paddingBottom="4dp"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/main_iv_pic_anchor"
            android:layout_width="1px"
            android:layout_height="1px" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="gone" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/main_comment_reply_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="12dp"
            android:paddingBottom="8dp"
            android:paddingRight="16dp"
            android:drawableRight="@drawable/main_ic_comment_arrow_right"
            android:textColor="@color/main_blue_4990E2"
            android:textSize="13sp"
            tools:text="查看更多评论"
            tools:visibility="visible"
            android:drawableTint="@color/main_color_4990e2"
            android:drawablePadding="4dp"
            android:includeFontPadding="false"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/main_v_bottom_divider"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="16dp"
        android:background="@color/main_color_2a2a2a" />
</LinearLayout>
