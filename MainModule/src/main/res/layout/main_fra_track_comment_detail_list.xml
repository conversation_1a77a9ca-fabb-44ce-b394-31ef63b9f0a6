<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.main.playModule.view.TopSlideView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_slide_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/main_comment_list_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/host_bg_rect_ffffff_131313_radius_10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/main_title_bar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/host_title_bar_height" />

            <include
                android:id="@+id/main_v_comment_fail"
                layout="@layout/main_layout_comment_fail"
                android:visibility="gone" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView xmlns:ptr="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/main_listview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:divider="@null"
                    android:dividerHeight="0dp"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:listSelector="@color/main_transparent"
                    android:overScrollMode="always"
                    android:paddingBottom="@dimen/host_bottom_bar_height"
                    android:scrollbars="none"
                    ptr:ptrDrawable="@drawable/host_ic_loading_circle"
                    ptr:ptrHeaderTextColor="@color/main_text_medium"
                    ptr:ptrShowIndicator="false" />

                <include
                    android:id="@+id/main_track_comment_header"
                    layout="@layout/main_layout_track_comment_detail_header_all_replies"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </FrameLayout>
        </LinearLayout>

        <include
            android:id="@+id/main_empty_view"
            layout="@layout/main_track_comment_no_content_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone"
            tools:visibility="visible" />

        <include
            android:id="@+id/main_error_view"
            layout="@layout/host_no_net_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible" />

        <include
            android:id="@+id/main_loading_view"
            layout="@layout/host_view_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone"
            tools:visibility="visible" />

        <View
            android:id="@+id/main_touch_handle_layer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/main_emotion_view"
            android:background="#33000000"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/main_v_bottom_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignWithParentIfMissing="true"
            android:layout_alignParentBottom="true"
            android:background="@drawable/main_bg_comment_detail_list_bottom"
            android:minHeight="50dp"
            android:visibility="visible">

            <ImageView
                android:id="@+id/main_iv_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:contentDescription=" "
                android:visibility="gone" />

            <TextView
                android:id="@+id/main_tv_comment"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="7dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="7dp"
                android:layout_toLeftOf="@+id/main_tv_comment_count"
                android:layout_toRightOf="@+id/main_iv_play"
                android:background="@drawable/main_bg_rect_f6f7f8_444444_radius_20"
                android:drawableLeft="@drawable/host_ic_write_new"
                android:drawableTint="@color/host_color_7b7b7b_dcdcdc"
                android:drawablePadding="4dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingLeft="12dp"
                android:paddingTop="5dp"
                android:paddingRight="12dp"
                android:paddingBottom="5dp"
                android:maxLines="1"
                android:text="@string/main_input_comment_hint"
                android:textColor="@color/host_color_7b7b7b_dcdcdc"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/main_tv_comment_count"
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:drawableTop="@drawable/main_playpage_icon_comment_normal_copy"
                android:drawablePadding="3dp"
                android:gravity="center"
                android:textColor="@color/main_color_666666_888888"
                android:textSize="9sp"
                android:visibility="gone" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/main_v_deleted"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            android:background="@color/main_color_ffffff_1e1e1e"
            android:clickable="true"
            android:orientation="vertical"
            android:visibility="invisible">

            <ImageView
                android:id="@+id/main_iv_deleted"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:duplicateParentState="true"
                android:scaleType="centerCrop"
                android:src="@drawable/host_no_content" />

            <TextView
                android:id="@+id/main_tv_deleted"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/main_iv_deleted"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="8dp"
                android:text="该评论已被删除"
                android:textColor="@color/main_color_bbbbbb_888888"
                android:textSize="13sp" />
        </RelativeLayout>

        <com.ximalaya.ting.android.main.view.comment.CommonCommentQuoraInputLayout
            android:id="@+id/main_emotion_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignWithParentIfMissing="true"
            android:layout_alignParentBottom="true"
            android:visibility="gone" />
    </RelativeLayout>
</com.ximalaya.ting.android.main.playModule.view.TopSlideView>