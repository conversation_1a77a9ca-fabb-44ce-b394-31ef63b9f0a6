<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_sound_ad_float_anchor_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#b3000000">

    <View
        android:id="@+id/main_top_close_area"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/main_float_anchor_layout" />

    <RelativeLayout
        android:id="@+id/main_float_anchor_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:text="推广"
            android:textColor="#ff111111"
            android:textSize="17sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/main_float_anchor_close"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentRight="true"
            android:layout_marginTop="12dp"
            android:scaleType="centerInside"
            android:src="@drawable/main_sound_float_web_close" />

        <RelativeLayout
            android:id="@+id/main_rl_top_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="57dp">

            <com.ximalaya.ting.android.host.view.CornerRelativeLayout
                android:id="@+id/main_cover_container"
                android:layout_width="80dp"
                android:layout_height="80dp"
                app:corner="all"
                app:corner_radius="4dp">

                <ImageView
                    android:id="@+id/main_cover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    tools:background="@color/host_red"
                    tools:src="@drawable/main_icon_feed_back_head" />

                <ImageView
                    android:id="@+id/main_cover_angel_tag"
                    android:layout_width="wrap_content"
                    android:scaleType="fitStart"
                    android:layout_height="17dp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/main_track_play_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentRight="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginRight="6dp"
                    android:layout_marginBottom="6dp"
                    android:src="@drawable/main_anchor_ad_play_tag"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </com.ximalaya.ting.android.host.view.CornerRelativeLayout>

            <TextView
                android:id="@+id/main_top_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignTop="@id/main_cover_container"
                android:layout_marginLeft="12dp"
                android:layout_toRightOf="@id/main_cover_container"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="#ff131313"
                android:textSize="18sp"
                android:fontFamily="sans-serif-black"
                tools:text="白夜行 | 东野圭吾巅峰之作｜韩雪 朱亚文领啦啦啦啦" />

            <LinearLayout
                android:id="@+id/main_score_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignLeft="@id/main_top_title"
                android:gravity="center_vertical"
                android:layout_marginBottom="4dp"
                android:layout_alignBottom="@id/main_cover_container"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/main_album_score"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="9dp"
                    android:gravity="center_vertical"
                    android:drawableLeft="@drawable/main_ic_album_ad_score"
                    android:drawableTint="#ffF97373"
                    android:textColor="#ffF97373"
                    android:textSize="13sp"
                    tools:text="9.2" />

                <TextView
                    android:id="@+id/main_play_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="7dp"
                    android:drawableLeft="@drawable/main_ic_divide"
                    android:drawablePadding="7dp"
                    android:drawableTint="#ff999999"
                    android:textColor="#ff999999"
                    android:textSize="12sp"
                    android:includeFontPadding="false"
                    tools:text="播放 60 万" />

                <TextView
                    android:id="@+id/main_subscribe_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@drawable/main_ic_divide"
                    android:drawablePadding="7dp"
                    android:drawableTint="#ff999999"
                    android:textColor="#ff999999"
                    android:textSize="12sp"
                    android:includeFontPadding="false"
                    tools:text="订阅 60 万" />
            </LinearLayout>

            <HorizontalScrollView
                android:id="@+id/main_tag_container_scroll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/main_cover_container"
                android:layout_marginTop="12dp"
                android:fadeScrollbars="true"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/main_tag_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/main_rank_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="22dp"
                        android:layout_marginRight="8dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <TextView
                            android:id="@+id/main_rank_tag_number"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:background="@drawable/main_bg_gradient_album_tag_left"
                            android:gravity="center_vertical"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:textColor="#954D3F"
                            android:textSize="11sp"
                            tools:text="NO.4" />

                        <TextView
                            android:id="@+id/main_rank_tag_name"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:background="@drawable/main_bg_gradient_album_tag_right"
                            android:gravity="center_vertical"
                            android:includeFontPadding="false"
                            android:maxLines="1"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:textColor="#ff954D3F"
                            android:textSize="12sp"
                            tools:text="有声书热播榜" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/main_promote_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="22dp"
                        android:layout_marginRight="8dp"
                        android:background="@drawable/main_anchor_ad_tag_bg_f1f2f7"
                        android:drawablePadding="3dp"
                        android:gravity="center_vertical"
                        android:paddingLeft="8dp"
                        android:paddingRight="8dp"
                        android:textColor="#ff666666"
                        android:textSize="12sp"
                        tools:drawableLeft="@drawable/main_promote_tag_dark_grey_icon"
                        tools:text="热门订阅" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="22dp"
                        android:layout_marginRight="8dp"
                        android:background="@drawable/main_anchor_ad_tag_bg_f1f2f7"
                        android:gravity="center_vertical"
                        android:paddingLeft="6dp"
                        android:paddingRight="6dp"
                        android:text="东野圭吾"
                        android:textColor="#ff666666"
                        android:textSize="12sp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>
            </HorizontalScrollView>

            <TextView
                android:id="@+id/main_recommend"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/main_tag_container_scroll"
                android:layout_marginTop="12dp"
                android:ellipsize="end"
                android:lines="1"
                android:textColor="#ff666666"
                android:textSize="12sp"
                tools:text="亮点推荐：我一直走在白夜里，从来没有太阳，所以不怕失去啦啦啦阿拉拉阿拉蕾" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/main_rl_comment_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/main_rl_top_container"
            android:layout_marginTop="12dp"
            android:background="@drawable/main_play_ad_anchor_recommend_bg_float">

            <ImageView
                android:id="@+id/main_iv_good_comment_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@id/main_tv_good_comment"
                android:layout_alignParentRight="true"
                android:layout_marginRight="-10dp"
                android:layout_marginBottom="-32dp"
                android:alpha="0.6"
                android:src="@drawable/main_good_comment_tag_gray"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/main_iv_recommend_tag"
                android:layout_width="34dp"
                android:layout_height="31dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="7dp"
                android:layout_marginRight="8dp"
                android:scaleType="fitCenter"
                android:src="@drawable/main_recommend_tag_grey"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_recommend_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="16dp"
                android:text="亮点推荐："
                android:textColor="#ff8F8F8F"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_recommend_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/main_tv_recommend_title"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="4dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="16dp"
                android:ellipsize="end"
                android:maxLines="3"
                android:textColor="#ff333333"
                android:textSize="14sp"
                android:visibility="gone"
                tools:text="我一直走在白夜里，从来没有太阳，所以不怕失去啦啦啦啦拉拉阿拉了啦啦啦啦。"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/main_tv_good_comment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="24dp"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="24dp"
                android:ellipsize="end"
                android:maxLines="3"
                android:textColor="#ff333333"
                android:textSize="14sp"
                android:visibility="gone"
                tools:text="《白夜行》是挖掘人性背后寒冷和阴暗的书，听起来让人不寒而栗却又觉得欲罢不能…这就是东野圭吾作品的绝妙之处。" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/main_rl_comment_container"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="34dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/main_button_open_album"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/host_bg_rect_f6f7f8_corner_30"
                android:gravity="center"
                android:includeFontPadding="false"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="查看专辑"
                android:textColor="#ff666666"
                android:textSize="15sp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/main_album_collect"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="11dp"
                android:layout_weight="1"
                android:background="@drawable/host_bg_rect_ff4444_corner_30"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/main_collect_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginRight="4dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/main_album_ic_collect" />

                <TextView
                    android:id="@+id/main_collect_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    tools:text="免费订阅"
                    android:textColor="#ffFFFFFF"
                    android:textSize="15sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/main_track_play"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="11dp"
                android:layout_weight="1"
                android:background="@drawable/host_bg_rect_ff4444_corner_30"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/main_play_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginRight="4dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/main_track_ic_play" />

                <TextView
                    android:id="@+id/main_track_play_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    tools:text="立即播放"
                    android:textColor="#ffFFFFFF"
                    android:textSize="15sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>