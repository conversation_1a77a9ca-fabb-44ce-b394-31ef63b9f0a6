<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/main_16corner_26000000"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

<!--    app:lottie_fileName="lottie/play_cover/playpage_ai_nie.json"-->
    <com.ximalaya.ting.android.host.view.XmLottieAnimationView
        android:id="@+id/main_ai_guide_ic"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:lottie_loop="false"
        app:lottie_autoPlay="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="MissingConstraints"/>

    <TextView
        android:id="@+id/main_ai_guide_txt"
        app:layout_constraintLeft_toRightOf="@+id/main_ai_guide_ic"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textSize="14dp"
        android:visibility="gone"
        android:paddingRight="16dp"
        android:layout_marginStart="6dp"
        android:textColor="@color/main_color_b2ffffff"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>