<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="false"
    android:orientation="vertical"
    tools:parentTag="android.widget.RelativeLayout">

    <!-- 可能已经没用了 -->
    <RadioGroup
        android:id="@+id/rg_title"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <RadioButton
            android:id="@+id/rb_comment"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/main_orange_underline_selector"
            android:button="@null"
            android:gravity="center"
            android:text="@string/main_comment"
            android:textColor="@drawable/host_tab_main_text_selector" />

        <View
            android:id="@+id/divide_group"
            android:layout_width="20dp"
            android:layout_height="wrap_content" />

        <RadioButton
            android:id="@+id/rb_quora"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/main_orange_underline_selector"
            android:button="@null"
            android:gravity="center"
            android:text="@string/main_quora_anchor"
            android:textColor="@drawable/host_tab_main_text_selector"
            android:textSize="16sp" />
    </RadioGroup>

    <ViewStub
        android:id="@+id/main_album_rate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/main_layout_album_rate_header" />

    <HorizontalScrollView
        android:id="@+id/main_v_scrollview_pics"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:scrollbars="none"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/main_v_pics"
            android:layout_width="wrap_content"
            android:layout_height="90dp"
            android:orientation="horizontal" />
    </HorizontalScrollView>

    <TextView
        android:id="@+id/main_tv_track_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="2dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="#FF888888"
        android:textSize="12sp"
        android:visibility="gone"
        tools:text="评论给声音：《特朗普前私人律师科恩出席闭门听证会提供…》"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/rl_input"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:background="@drawable/host_bg_emotion_input">

        <com.ximalaya.ting.android.host.view.edittext.HighlightSpanEditText
            android:id="@+id/et_input"
            style="@style/host_edit_text_cursor_drawable"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/main_corner8_solid_f3f4f5_2a2a2a"
            android:gravity="top"
            android:imeOptions="flagNoExtractUi"
            android:inputType="textMultiLine"
            android:maxLines="4"
            android:minLines="2"
            android:paddingLeft="8dp"
            android:paddingTop="5dp"
            android:paddingRight="8dp"
            android:paddingBottom="13dp"
            android:textColorHint="@color/main_color_aaaaaa_888888"
            android:textSize="13sp"
            tools:text="这是一条评论这是一条评论这是一条评论这是一条评论这是一条评论" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="8dp">

        <com.ximalaya.ting.android.host.view.other.CommentTimeMarkView
            android:id="@+id/main_v_comment_time_mark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:visibility="gone"
            tools:visibility="visible" />

        <CheckBox
            android:id="@+id/main_checkbox_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8dp"
            android:layout_toRightOf="@id/main_v_comment_time_mark"
            android:button="@drawable/host_sync_dynamic_check_selector"
            android:checked="false"
            android:drawablePadding="2dp"
            android:paddingLeft="6dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="置顶"
            android:textColor="@color/host_color_666666_999999"
            android:textSize="12sp" />


        <CheckBox
            android:id="@+id/checkbox_sync_ting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8dp"
            android:layout_toRightOf="@id/main_checkbox_top"
            android:button="@drawable/host_sync_dynamic_check_selector"
            android:checked="false"
            android:contentDescription="@string/main_ting_sync_comment"
            android:drawablePadding="2dp"
            android:paddingLeft="6dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:text="@string/main_ting_sync_comment"
            android:textColor="@color/host_color_666666_999999"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/main_tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:text="0/100"
            android:textColor="@color/main_color_999999_888888"
            android:textSize="12sp" />
    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="10dp">

        <RelativeLayout
            android:id="@+id/rl_action"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/main_iv_voice"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:clickable="true"
                android:gravity="center"
                android:padding="5dp"
                android:src="@drawable/main_ic_voice"
                android:visibility="gone"
                app:image_type="circle"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/main_iv_emoji"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toRightOf="@id/main_iv_voice"
                android:clickable="true"
                android:contentDescription="表情选择"
                android:gravity="center"
                android:padding="5dp"
                android:src="@drawable/host_bottom_emoji_selector"
                app:image_type="circle"
                tools:ignore="TouchTargetSizeCheck" />

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/main_iv_color"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toRightOf="@id/main_iv_emoji"
                android:clickable="true"
                android:gravity="center"
                android:padding="5dp"
                android:src="@drawable/host_ic_danmuku_color_default"
                android:visibility="visible"
                app:image_type="circle"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/main_iv_bottom_bullet"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toRightOf="@id/main_iv_color"
                android:clickable="true"
                android:gravity="center"
                android:padding="5dp"
                android:src="@drawable/main_bottom_bullet_selector"
                android:visibility="gone"
                app:image_type="circle"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/main_iv_pic_add"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toRightOf="@id/main_iv_bottom_bullet"
                android:clickable="true"
                android:gravity="center"
                android:padding="5dp"
                android:src="@drawable/host_ic_comment_input_pic"
                android:visibility="gone"
                android:contentDescription="照片选择"
                app:image_type="circle"
                tools:visibility="visible" />

            <com.ximalaya.ting.android.host.view.image.TouchableImageView
                android:id="@+id/main_iv_link_add"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_toRightOf="@id/main_iv_pic_add"
                android:clickable="true"
                android:gravity="center"
                android:padding="5dp"
                android:src="@drawable/host_ic_comment_input_link"
                android:visibility="gone"
                app:image_type="circle"
                tools:visibility="visible" />

            <CheckBox
                android:id="@+id/anonymity_quora"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:button="@drawable/host_rec_check_selector"
                android:drawablePadding="5dp"
                android:paddingLeft="4dp"
                android:text="@string/main_anonymity_quora"
                android:textColor="@color/main_color_666666_888888"
                android:textSize="15sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_quora_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginRight="25dp"
                android:layout_toLeftOf="@+id/tv_send"
                android:textColor="#FFFF6D4B"
                android:textSize="15sp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_send"
                android:layout_width="58dp"
                android:layout_height="24dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@drawable/main_emotion_send_select"
                android:gravity="center"
                android:text="发送"
                android:textColor="#fff"
                android:textSize="12sp" />


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_voice_action"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:visibility="invisible"
            tools:visibility="gone">

            <TextView
                android:id="@+id/tv_quit_voice"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_marginLeft="10dp"
                android:drawableLeft="@drawable/main_ic_keyboard"
                android:drawablePadding="4dp"
                android:gravity="center"
                android:text="切回文字评论"
                android:textColor="@color/main_color_666666_888888"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_rerecord"
                android:layout_width="60dp"
                android:layout_height="28dp"
                android:layout_marginRight="16dp"
                android:layout_toLeftOf="@+id/tv_send_voice"
                android:background="@drawable/main_bg_rect_stroke_979797_radius_24"
                android:gravity="center"
                android:text="重录"
                android:textColor="@color/main_color_666666_888888"
                android:textSize="14sp" />

            <TextView
                android:id="@id/tv_send_voice"
                android:layout_width="60dp"
                android:layout_height="28dp"
                android:layout_alignParentRight="true"
                android:layout_marginRight="10dp"
                android:background="@drawable/main_bg_rect_f86442_radius_24"
                android:gravity="center"
                android:text="发送"
                android:textColor="@color/main_white"
                android:textSize="14sp" />
        </RelativeLayout>
    </FrameLayout>

    <com.ximalaya.ting.android.host.view.other.EmotionSelector
        android:id="@+id/emotion_selector"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:emotion_icon="@array/emotion_icons"
        app:emotion_name="@array/emotion_names"
        app:show_emotion_bar="false"
        app:show_input="false"
        tools:visibility="visible" />
</LinearLayout>
