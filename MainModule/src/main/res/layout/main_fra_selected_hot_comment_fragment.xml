<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_v_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_color_222222"
    android:clipChildren="false">

    <com.ximalaya.ting.android.framework.view.BlurableImageView
        android:id="@+id/main_iv_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false">

        <RelativeLayout
            android:id="@+id/main_v_title"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/main_iv_back"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="8dp"
                android:src="@drawable/host_icon_back_white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="喜马热评"
                android:textColor="@color/main_color_white"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/main_tv_progress"
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="8dp"
                android:textColor="@color/main_color_white_50"
                android:textSize="14sp"
                tools:text="11/20" />
        </RelativeLayout>

        <View
            android:id="@+id/main_v_bottom_shadow"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/main_bg_rect_00000000_ff000000" />

        <RelativeLayout
            android:id="@+id/main_v_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/main_v_title"
            android:visibility="invisible">

            <ListView
                android:id="@+id/main_lv_comment"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_above="@id/main_v_bottom_bar"
                android:divider="@null"
                android:dividerHeight="0dp"
                android:listSelector="@color/host_color_translucent_00ffffff"
                android:scrollbars="none" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/main_pager"
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:layout_marginTop="30dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingLeft="32dp"
                android:paddingRight="32dp" />

            <LinearLayout
                android:id="@+id/main_v_visitor"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="32dp"
                android:layout_marginRight="32dp"
                android:layout_marginBottom="32dp"
                android:visibility="invisible">

                <com.ximalaya.ting.android.main.view.MultiImageBar
                    android:id="@+id/main_iv_visitor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    app:count="4"
                    app:imgSize="26dp"
                    app:overlapRate="0.4" />

                <TextView
                    android:id="@+id/main_tv_visitor_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="8dp"
                    android:ellipsize="end"
                    android:lines="1"
                    android:textColor="@color/main_color_999999"
                    android:textSize="14sp" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/main_v_bottom_bar"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_alignParentBottom="true">

                <TextView
                    android:id="@+id/main_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="16dp"
                    android:layout_toLeftOf="@id/main_tv_comment"
                    android:background="@drawable/main_bg_rect_1af5f5f5_radius_20"
                    android:gravity="center_vertical"
                    android:paddingLeft="40dp"
                    android:text="我猜测，此刻你可能有话想说"
                    android:textColor="@color/main_color_999999"
                    android:textSize="14sp" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignLeft="@id/main_edit_text"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="12dp"
                    android:src="@drawable/host_ic_write" />

                <TextView
                    android:id="@+id/main_tv_comment"
                    android:layout_width="48dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="-2dp"
                    android:layout_toLeftOf="@id/main_tv_like"
                    android:drawableTop="@drawable/main_ic_selected_hot_comment_comment"
                    android:drawablePadding="2dp"
                    android:gravity="center_horizontal"
                    android:text="评论"
                    android:textColor="@color/main_color_999999"
                    android:textSize="9sp" />

                <TextView
                    android:id="@+id/main_tv_like"
                    android:layout_width="48dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:drawableTop="@drawable/main_selected_hot_comment_like_selector"
                    android:drawablePadding="2dp"
                    android:gravity="center_horizontal"
                    android:text="喜欢"
                    android:textColor="@color/main_color_999999"
                    android:textSize="9sp" />
            </RelativeLayout>
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/main_touch_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="50dp"
            android:background="#20000000"
            android:visibility="invisible" />

        <FrameLayout
            android:id="@+id/main_float_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="50dp"/>

        <com.ximalaya.ting.android.main.view.other.CommentQuoraInputLayout
            android:id="@+id/main_emotion_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignWithParentIfMissing="true"
            android:layout_alignParentBottom="true"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/main_v_no_net"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/main_v_title"
            android:background="@color/main_color_222222"
            android:visibility="invisible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:contentDescription="@string/host_network_error"
                    android:duplicateParentState="true"
                    android:scaleType="centerCrop"
                    android:src="@drawable/host_no_net" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:text="无法连接到网络"
                    android:textColor="#BBBBBB"
                    android:textSize="13sp" />
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>
</FrameLayout>