<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

    <com.ximalaya.ting.android.host.view.XmLottieAnimationView
        android:id="@+id/main_anchor_rank_lottie"
        android:layout_width="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_height="wrap_content"
        />

    <Space
        android:id="@+id/main_anchor_rank_space"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/main_anchor_rank_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_anchor_rank_space"
        android:layout_marginTop="30dp"
        android:layout_centerHorizontal="true"
        android:textColor="@color/main_color_ffffff_111111"
        android:visibility="gone"
        android:textSize="58sp"/>

    <TextView
        android:id="@+id/main_anchor_rank_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:textColor="@color/main_color_ffffff_111111"
        android:layout_marginTop="10dp"
        android:layout_below="@id/main_anchor_rank_title"
        android:visibility="gone"
        android:textSize="20sp"/>
</RelativeLayout>