<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
       xmlns:makeramen="http://schemas.android.com/apk/res-auto"
       xmlns:tools="http://schemas.android.com/tools"
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:orientation="vertical"
       tools:background="@color/main_black"
       tools:parentTag="android.widget.LinearLayout">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_round_img"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:scaleType="fitXY"
            makeramen:border_color="#7fffffff"
            makeramen:border_width="1px"
            makeramen:corner_radius="60dp"/>

        <TextView
            android:id="@+id/main_anchor_state"
            android:background="@drawable/main_dubb_anchor"
            android:text="＋关注"
            android:textColor="@color/host_white"
            android:textSize="14sp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="18dp"
            android:gravity="center"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/main_anchor_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="10dp"
            android:layout_toLeftOf="@id/main_anchor_state"
            android:layout_toRightOf="@id/main_round_img"
            android:orientation="vertical">

            <TextView
                android:id="@+id/main_anchor_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/main_white"
                android:textSize="18sp"
                tools:text="深夜访谈，我的舞台深夜访谈，我的舞台深夜访谈，我的舞台深夜访谈，我的舞台"/>

            <TextView
                android:id="@+id/main_anchor_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/main_color_999999"
                android:textSize="12sp"
                tools:text="我就是我，不一样的声音，你要来听我的声音么我就是我"/>

        </LinearLayout>
    </RelativeLayout>

    <TextView
        android:id="@+id/main_user_info_community_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="15dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:text="ta的社团 >"
        android:textColor="#fffc3b5d"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="18dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/main_user_info_like_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/main_white"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="242"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="18dp"
            android:text="获赞"
            android:textColor="#d3d3d3"
            android:textSize="12sp"/>

        <TextView
            android:id="@+id/main_user_info_fensi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/main_white"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="242"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="18dp"
            android:text="粉丝"
            android:textColor="#d3d3d3"
            android:textSize="12sp"/>

        <TextView
            android:id="@+id/main_user_info_attention"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/main_white"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="58"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="18dp"
            android:text="关注"
            android:textColor="#d3d3d3"
            android:textSize="12sp"/>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="16dp"
        android:background="@color/host_color_333333"/>

    <TextView
        android:id="@+id/main_user_info_works_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:text="所有作品(12)"
        android:textColor="@color/main_white"
        android:textSize="14sp"/>
</merge>