<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main_ad_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/main_ad_click_lay"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/main_image_wrapper"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:background="@drawable/main_recently_ad_cover_border_4dp">

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/main_image"
                android:layout_width="127dp"
                android:layout_height="72dp"
                android:padding="0.5dp"
                app:corner_radius="4dp" />
        </FrameLayout>

        <TextView
            android:id="@+id/main_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@id/main_image_wrapper"
            android:layout_centerVertical="true"
            android:maxLines="2"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="20dp"
            android:ellipsize="end"
            android:textColor="@color/main_color_111111_cfcfcf"
            android:textSize="14sp"/>

    </RelativeLayout>
</com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer>