<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:background="@null"
  android:layout_marginStart="50dp"
  android:layout_marginEnd="50dp"
  xmlns:tools="http://schemas.android.com/tools"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <View
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_marginTop="30dp"
    android:background="@drawable/main_bg_rect_ffffff_282828_corner_10"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"/>


  <com.ximalaya.ting.android.framework.view.image.RoundImageView
    android:id="@+id/main_anchor_avatar"
    android:layout_width="60dp"
    android:layout_height="60dp"
    android:scaleType="centerCrop"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:border_width="2dp"
    app:border_color="@color/main_color_ffffff_282828"
    app:corner_radius="30dp"/>

  <TextView
    android:id="@+id/main_title"
    app:layout_constraintTop_toBottomOf="@+id/main_anchor_avatar"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    android:textSize="16dp"
    android:layout_marginTop="12dp"
    android:textStyle="bold"
    android:textColor="@color/main_color_111111_ffffff"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content"
    android:text="感谢你的投票"/>

  <TextView
    android:id="@+id/main_desc"
    app:layout_constraintTop_toBottomOf="@+id/main_title"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    android:includeFontPadding="false"
    android:textSize="13dp"
    android:layout_marginTop="8dp"
    android:textColor="@color/main_color_666666_8d8d91"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content"
    android:text="你的投票让更多人在首页看到它"/>

  <ImageView
      android:id="@+id/main_iv_up"
      android:src="@drawable/main_vote_up"
      app:layout_constraintTop_toBottomOf="@+id/main_title"
      android:layout_marginTop="9dp"
      app:layout_constraintStart_toEndOf="@+id/main_desc"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"/>

  <View
    android:id="@+id/main_album_info_bg"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_marginTop="16dp"
    android:layout_marginStart="20dp"
    android:layout_marginEnd="20dp"
    android:background="@drawable/main_bg_rect_131313_f6f7f8_radius_4"
    app:layout_constraintTop_toBottomOf="@+id/main_desc"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toBottomOf="@+id/main_barrier" />

  <com.ximalaya.ting.android.framework.view.image.RoundImageView
    android:id="@+id/main_album_cover"
    android:layout_width="64dp"
    android:layout_height="64dp"
    android:layout_marginStart="10dp"
    android:layout_marginTop="10dp"
    android:scaleType="centerCrop"
    app:layout_constraintTop_toTopOf="@+id/main_album_info_bg"
    app:layout_constraintStart_toStartOf="@+id/main_album_info_bg"
    app:corner_radius="4dp"/>

  <TextView
    android:id="@+id/main_album_title"
    android:textSize="13dp"
    android:maxLines="2"
    android:ellipsize="end"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="6dp"
    app:layout_constraintVertical_chainStyle="packed"
    android:textColor="@color/main_color_333333_ffffff"
    app:layout_constraintStart_toEndOf="@+id/main_album_cover"
    app:layout_constraintTop_toTopOf="@+id/main_album_cover"
    app:layout_constraintEnd_toEndOf="@id/main_album_info_bg"
    app:layout_constraintBottom_toTopOf="@+id/main_album_author_name"
    android:includeFontPadding="false"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    tools:text="梦华录 赵盼儿风月救风尘-关汉卿"/>

  <TextView
    android:id="@+id/main_album_author_name"
    android:textSize="12dp"
    android:maxLines="1"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="6dp"
    android:layout_marginTop="10dp"
    android:textColor="@color/main_color_8f8f8f_66666b"
    app:layout_constraintStart_toEndOf="@+id/main_album_cover"
    app:layout_constraintTop_toBottomOf="@+id/main_album_title"
    app:layout_constraintBottom_toBottomOf="@+id/main_album_cover"
    app:layout_constraintEnd_toEndOf="@id/main_album_info_bg"
    android:includeFontPadding="false"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    tools:text="有声的紫荆"/>

  <androidx.constraintlayout.widget.Barrier
    android:id="@+id/main_barrier"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    app:barrierMargin="10dp"
    app:constraint_referenced_ids="main_album_cover"
    app:barrierDirection="bottom"/>

  <androidx.appcompat.widget.AppCompatTextView
    android:id="@+id/main_vote_history_entry"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    android:layout_marginTop="20dp"
    android:text="查看你的月票贡献排名"
    android:textSize="12dp"
    android:textColor="@color/main_color_8f8f8f_66666b"
    android:drawablePadding="2dp"
    android:drawableEnd="@drawable/main_vote_right_ic"
    app:layout_constraintTop_toBottomOf="@+id/main_barrier"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"/>

  <TextView
    android:id="@+id/main_vote_confirm"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    android:textSize="14dp"
    android:textColor="@color/main_white"
    android:gravity="center"
    android:layout_marginTop="14dp"
    android:layout_marginStart="38dp"
    android:layout_marginEnd="38dp"
    android:layout_marginBottom="20dp"
    android:background="@drawable/main_bg_rect_ff4444_radius_24"
    app:layout_constraintTop_toBottomOf="@+id/main_vote_history_entry"
    app:layout_constraintBottom_toBottomOf="parent"
    android:layout_height="40dp"
    android:layout_width="0dp"
    android:text="我知道了"/>

</androidx.constraintlayout.widget.ConstraintLayout>