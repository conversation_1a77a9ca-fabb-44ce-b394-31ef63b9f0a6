<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="SmallSp">

    <com.ximalaya.ting.android.framework.view.image.RoundImageView
        android:id="@+id/main_iv_avatar"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="13dp"
        android:layout_marginBottom="13dp"
        app:corner_radius="17dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/mine_icon_space_default_avatar_210" />

    <TextView
        android:id="@+id/main_tv_nickname"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="14sp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/main_color_333333_cfcfcf"
        android:textSize="15sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/main_iv_gender"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/main_iv_avatar"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="用户昵称" />

    <ImageView
        android:id="@+id/main_iv_gender"
        android:layout_width="11dp"
        android:layout_height="11dp"
        android:layout_marginStart="2dp"
        android:importantForAccessibility="no"
        android:scaleType="centerCrop"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_nickname"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_times"
        app:layout_constraintStart_toEndOf="@+id/main_tv_nickname"
        app:layout_constraintTop_toTopOf="@+id/main_tv_nickname"
        tools:src="@drawable/host_v_sex_female"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_tv_times"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="9dp"
        android:layout_marginEnd="5dp"
        android:background="@drawable/main_bg_recent_visitors_anchor_times"
        android:includeFontPadding="false"
        android:paddingStart="3dp"
        android:paddingTop="2dp"
        android:paddingEnd="3dp"
        android:paddingBottom="2dp"
        android:textColor="#9EA4AC"
        android:textSize="8sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/main_tv_nickname"
        app:layout_constraintEnd_toStartOf="@+id/main_tv_date"
        app:layout_constraintStart_toEndOf="@+id/main_iv_gender"
        app:layout_constraintTop_toTopOf="@+id/main_tv_nickname"
        tools:text="Ta第5次访问了你"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/main_tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="18dp"
        android:textColor="@color/main_color_999999"
        android:textSize="11sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="3天前" />

    <View
        android:id="@+id/main_v_divider"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:background="@color/main_color_eeeeee_2a2a2a"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>