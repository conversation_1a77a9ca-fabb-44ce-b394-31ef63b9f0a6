<?xml version="1.0" encoding="utf-8"?><!-- 对这个布局文件的修改应该同步到main_item_track_comment_dark.xml的暗色版本 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_track_comment_lay"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical"
    tools:background="@color/main_white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="8dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView xmlns:makeramen="http://schemas.android.com/apk/res-auto"
            android:id="@+id/main_comment_image"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginTop="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="8dp"
            android:scaleType="fitXY"
            android:src="@drawable/host_default_avatar_88"
            makeramen:border_color="@color/main_color_e8e8e8"
            makeramen:border_width="0.5px"
            makeramen:corner_radius="72dp"
            makeramen:pressdown_shade="true" />

        <TextView
            android:id="@+id/main_comment_name"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_alignTop="@id/main_comment_image"
            android:layout_toRightOf="@+id/main_comment_image"
            android:textColor="@color/main_color_666666"
            android:textSize="15sp"
            tools:text="西瓜" />

        <RelativeLayout
            android:id="@+id/main_iv_owner_title"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_alignTop="@id/main_comment_name"
            android:layout_marginRight="4dp"
            android:layout_toRightOf="@id/main_comment_name">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text=" (主播)"
                android:textColor="@color/main_color_666666"
                android:textSize="15sp"
                android:visibility="visible"
                tools:visibility="visible" />
        </RelativeLayout>

        <TextView
            android:id="@+id/main_create_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/main_comment_name"
            android:layout_marginTop="4dp"
            android:layout_toRightOf="@+id/main_comment_image"
            android:textColor="@color/main_color_999999"
            android:textSize="12sp"
            tools:text="9天前" />

        <com.ximalaya.ting.android.host.view.CommonIPLayout
            android:id="@+id/main_common_ip_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:layout_marginTop="4dp"
            android:layout_below="@id/main_comment_name"
            android:layout_toRightOf="@+id/main_create_time" />

        <RelativeLayout
            android:id="@+id/main_vip_tag"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_alignTop="@id/main_comment_name"
            android:layout_toRightOf="@+id/main_iv_owner_title">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginLeft="4dp"
                android:contentDescription="@string/main_iv_cd_vip"
                android:scaleType="fitCenter"
                android:src="@drawable/main_vip_fra_vip_colorful"
                android:visibility="invisible"
                tools:visibility="visible" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/main_v_like"
            android:layout_width="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignTop="@id/main_comment_name"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="-6dp"
            android:clipChildren="false">

            <TextView
                android:id="@+id/main_like_count"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:text="999"
                android:textColor="@color/main_color_999999"
                android:textSize="12sp"
                android:layout_marginRight="4dp"/>

            <RelativeLayout
                android:id="@+id/main_rl_like"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:clipChildren="false">

                <ImageView
                    android:id="@+id/main_iv_like"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:scaleType="centerCrop"
                    android:visibility="visible"
                    android:src="@drawable/host_ic_comment_like_selector"
                    tools:visibility="visible" />

                <com.ximalaya.ting.android.host.view.XmLottieAnimationView
                    android:id="@+id/main_iv_lottie_like"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:scaleType="centerInside"
                    app:lottie_autoPlay="false"
                    android:visibility="visible"
                    app:lottie_fileName="lottie/host_lottie_for_like_action.json" />
            </RelativeLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/main_iv_recommend_hot_comment"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/main_checkbox_selector"
            android:visibility="invisible" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_layout_main_comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:orientation="vertical">

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="15sp"
            android:visibility="visible" />

        <RelativeLayout
            android:id="@+id/main_voice_comment"
            android:layout_width="162dp"
            android:layout_height="44dp"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            tools:visibility="visible">

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/main_pic_voice_background" />

            <ImageView
                android:id="@+id/main_iv_voice"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="12dp"
                android:layout_marginBottom="10dp"
                android:src="@drawable/main_anim_voice" />

            <TextView
                android:id="@+id/main_tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:layout_marginRight="16dp"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:text="18"
                android:textColor="@color/main_white"
                android:textSize="16sp" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/main_v_pic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/main_v_like_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/main_tv_like_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textColor="@color/main_color_null_888888"
            android:textSize="12sp" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/main_layout_album_reply"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/main_bg_rect_gray"
        android:orientation="vertical"
        android:paddingLeft="11dp"
        android:paddingTop="12dp"
        android:paddingRight="11dp"
        android:paddingBottom="12dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/main_iv_pic_anchor"
            android:layout_width="1px"
            android:layout_height="1px" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone" />

        <com.ximalaya.ting.android.main.view.text.StaticLayoutView
            android:id="@+id/main_comment_reply_3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/main_comment_reply_more"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/main_blue_4990E2"
            android:textSize="13sp"
            android:visibility="gone" />
    </LinearLayout>

    <View
        android:id="@+id/main_divide"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginLeft="60dp"
        android:layout_marginTop="4dp"
        android:background="@color/main_color_e8e8e8" />
</LinearLayout>
