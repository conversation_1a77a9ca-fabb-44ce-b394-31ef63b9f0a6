<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/host_y5">

    <TextView
        android:id="@+id/main_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/host_y4"
        android:fontFamily="sans-serif-light"
        android:maxLines="1"
        android:textColor="@color/main_color_111111_ffffff"
        android:textSize="17sp"
        android:textStyle="bold"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@+id/main_play_all_layout"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="百科知识电台" />

    <LinearLayout
        android:id="@+id/main_play_all_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/main_title_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_title_tv">

        <TextView
            android:id="@+id/main_sub_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="查看全部"
            android:textColor="@color/main_color_666666_8d8d91"
            android:textSize="13sp" />

        <ImageView
            android:id="@+id/main_sub_iv"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="2dp"
            android:src="@drawable/main_channel_header_right_arrow_icon" />

    </LinearLayout>

    <ImageView
        android:id="@+id/main_iv_left"
        android:layout_width="180dp"
        android:layout_height="72dp"
        android:layout_marginTop="@dimen/host_y12"
        android:scaleType="fitXY"
        android:src="@drawable/main_channel_vote_left"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_title_tv" />

    <ImageView
        android:id="@+id/main_iv_right"
        android:layout_width="179dp"
        android:layout_height="72dp"
        android:layout_marginTop="@dimen/host_y12"
        android:scaleType="fitXY"
        android:src="@drawable/main_channel_vote_right"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_title_tv" />

    <ImageView
        android:layout_width="87dp"
        android:layout_height="72dp"
        android:layout_marginTop="@dimen/host_y12"
        android:scaleType="fitXY"
        android:src="@drawable/main_channel_vote_pk"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_title_tv" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/main_guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <View
        android:id="@+id/main_guide_view"
        android:layout_width="1px"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/host_y12"
        android:layout_marginRight="20dp"
        app:layout_constraintRight_toRightOf="@+id/main_guide"
        app:layout_constraintTop_toBottomOf="@+id/main_title_tv" />

    <TextView
        android:id="@+id/main_tv_vote_msg1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="@dimen/host_y7"
        android:ellipsize="end"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/main_color_ffffff"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_left"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/main_guide_view"
        app:layout_constraintTop_toTopOf="@+id/main_iv_left"
        tools:text="非常好看非常好看非常好看" />

    <TextView
        android:id="@+id/main_tv_vote_msg2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="52dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="@dimen/host_y7"
        android:ellipsize="end"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/main_color_ffffff"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_left"
        app:layout_constraintLeft_toLeftOf="@+id/main_guide_view"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/main_iv_left"
        tools:text="不靠谱不靠谱不靠谱不靠谱不靠谱" />

    <TextView
        android:id="@+id/main_tv_vote_agree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/main_bg_rect_ff4646_ffffff_radius_32in"
        android:paddingLeft="16dp"
        android:paddingTop="@dimen/host_y4"
        android:paddingRight="16dp"
        android:paddingBottom="@dimen/host_y4"
        android:text="投票"
        android:textColor="@color/main_color_ffffff_666666_selector"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_left"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/main_guide_view"
        app:layout_constraintTop_toBottomOf="@+id/main_iv_left" />

    <TextView
        android:id="@+id/main_tv_vote_disagree"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/main_bg_rect_5977ff_ffffff_radius_32in"
        android:paddingLeft="16dp"
        android:paddingTop="@dimen/host_y4"
        android:paddingRight="16dp"
        android:paddingBottom="@dimen/host_y4"
        android:text="投票"
        android:textColor="@color/main_color_ffffff_666666_selector"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_left"
        app:layout_constraintLeft_toRightOf="@+id/main_guide_view"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/main_iv_left" />

</androidx.constraintlayout.widget.ConstraintLayout>