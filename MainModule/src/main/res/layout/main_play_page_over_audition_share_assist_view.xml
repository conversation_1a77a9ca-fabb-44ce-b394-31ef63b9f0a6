<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/main_tv_share_assist"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/main_btn_bg_play_page_share_assist"
        android:gravity="center"
        android:maxLines="1"
        android:paddingStart="21dp"
        android:paddingEnd="21dp"
        android:textColor="@color/host_color_333333"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        tools:text="邀1人助力，双方0元免费听" />

    <Space
        android:id="@+id/main_tv_share_assist_space"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="78dp"
        app:layout_constraintEnd_toEndOf="@id/main_tv_share_assist"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ximalaya.ting.android.host.view.text.XmTextSwitcher
        android:id="@+id/main_xts_share_assist"
        android:layout_width="100dp"
        android:layout_height="14dp"
        android:background="@drawable/main_xts_bg_play_page_share_assist"
        app:layout_constraintStart_toEndOf="@+id/main_tv_share_assist_space"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>