<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="22dp"
    android:layout_height="50dp"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/main_view_line"
        android:layout_width="0.5dp"
        android:layout_height="26dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="5dp"
        android:alpha="0.5"
        app:layout_constraintBottom_toTopOf="@id/main_tv_item_speed"
        android:background="@color/host_color_000000_ffffff" />

    <TextView
        android:id="@+id/main_tv_item_speed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="1.0x"
        android:gravity="center"
        android:alpha="0.5"
        android:textColor="@color/host_color_000000_ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>