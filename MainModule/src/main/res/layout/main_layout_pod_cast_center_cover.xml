<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/main_center_top_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_y10"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.ximalaya.ting.android.host.view.image.RatioRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/main_center_top_view"
        android:background="@drawable/main_icon_pod_cast_center_cover"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:xRelativeRatio="0.9">

        <com.ximalaya.ting.android.host.view.image.RatioImageView
            android:id="@+id/main_center_iv_cover"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:scaleType="centerCrop"
            app:corner_radius="8dp"
            app:needColorFilter="false"
            app:ratio="1.0" />

    </com.ximalaya.ting.android.host.view.image.RatioRelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>