<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    android:paddingBottom="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:background="@drawable/host_bg_rect_gray_background_line_1_color_radius_4">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_avatar"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="12dp"
            android:scaleType="centerCrop"
            android:src="@drawable/host_default_album"
            app:corner_radius="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/main_title_ll"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@+id/main_tv_reason"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_follow"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/main_iv_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed">

            <TextView
                android:id="@+id/main_tv_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="sans-serif-light"
                android:lines="1"
                android:textColor="@color/main_color_131313_dcdcdc"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="手工泡芙" />

        </LinearLayout>

        <TextView
            android:id="@+id/main_tv_reason"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:textColor="@color/host_color_lightTextColor"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_follow"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/main_iv_avatar"
            app:layout_constraintTop_toBottomOf="@+id/main_title_ll"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="也评价了 遮天（辰东作品，头精品双播" />

        <TextView
            android:id="@+id/main_tv_subscribe_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/host_color_lightTextColor"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/main_tv_follow"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/main_iv_avatar"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_reason"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="10人订阅" />

        <TextView
            android:id="@+id/main_tv_follow"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_marginRight="12dp"
            android:background="@drawable/main_item_anchor_follow_selector"
            android:textSize="12sp"
            android:drawableTint="@color/host_color_xmRed"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:gravity="center_vertical"
            android:textColor="@color/main_item_follow_anchor_selector"
            app:layout_constraintBottom_toBottomOf="@+id/main_iv_avatar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_iv_avatar"
            tools:text="订阅" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>