<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_album_header_cl"
    tools:layout_height="300dp"
    android:background="@color/host_color_f7f9fc_181818"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/main_gray">

    <Space
        android:id="@+id/main_album3_space_before_head"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/main_album3_top_bg_big_ip_vs"
        android:layout="@layout/main_reserve_top_bg_big_ip_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ViewStub
        android:id="@+id/main_album3_top_bg_pod_cast_vs"
        android:layout_width="match_parent"
        android:layout_height="265dp"
        android:layout="@layout/main_album3_top_bg_pod_cast_layout"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <FrameLayout
        android:id="@+id/main_album3_header_group_layout"
        app:layout_constraintTop_toBottomOf="@+id/main_album3_space_before_head"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ViewStub
            android:id="@+id/main_album3_header_pod_cast_vs"
            android:layout="@layout/main_album3_header_pod_cast"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <ViewStub
            android:id="@+id/main_album3_header_novel_vs_new"
            android:layout="@layout/main_album3_header_novel_new"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <ViewStub
            android:id="@+id/main_album3_header_big_ip_vs"
            android:layout="@layout/main_album3_header_big_ip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </FrameLayout>

    <LinearLayout
        android:id="@+id/main_album3_detail_container"
        app:layout_constraintTop_toBottomOf="@+id/main_album3_header_group_layout"
        android:orientation="vertical"
        android:paddingBottom="@dimen/host_y20"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumTagsView3
            android:id="@+id/main_album3_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/host_y6"
            android:fadingEdge="horizontal"
            android:visibility="gone"
            tools:visibility="visible"
            android:fadingEdgeLength="23dp"
            android:requiresFadingEdge="horizontal"
            tools:layout_height="30dp" />

        <com.ximalaya.ting.android.host.view.RecyclerViewCanDisallowInterceptInHost
            android:id="@+id/main_reserve_track_recycler"
            android:layout_marginTop="@dimen/host_y16"
            android:overScrollMode="never"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>