<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.host.view.CornerRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_container_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginRight="10dp"
    android:focusableInTouchMode="true"
    android:importantForAccessibility="yes"
    android:orientation="vertical"
    app:corner_radius="8dp"
    tools:layout_height="200dp">

    <!--背景底部颜色 当文字放大时,背景图骗不拉伸,底部空间使用图骗颜色填充-->
    <ImageView
        android:id="@+id/main_iv_color_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignLeft="@+id/main_cl_module_title"
        android:layout_alignTop="@+id/main_cl_module_title"
        android:layout_alignRight="@+id/main_cl_module_title"
        android:layout_alignBottom="@+id/main_cl_module_title" />

    <!--图片背景 不能拉伸-->
    <ImageView
        android:id="@+id/main_iv_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/main_cl_module_title"
        android:layout_alignTop="@+id/main_cl_module_title"
        android:layout_alignRight="@+id/main_cl_module_title"
        android:scaleType="fitXY"
        tools:src="@drawable/main_icon_scene_sleep_bg" />

    <ImageView
        android:id="@+id/main_iv_cover"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="56dp"
        android:scaleType="fitXY"
        tools:src="@drawable/ic_launcher" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/main_iv_cover"
        android:layout_alignLeft="@+id/main_iv_bg"
        android:layout_alignRight="@+id/main_iv_bg"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/main_iv_fly_left"
            android:layout_width="6dp"
            android:layout_height="10dp"
            android:layout_marginEnd="2dp"
            android:src="@drawable/main_ic_scene_listen_item_fly_left" />

        <TextView
            android:id="@+id/main_tv_content_title_real"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="我是标题"
            android:textColor="@color/host_white"
            android:textSize="12sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/main_iv_fly_right"
            android:layout_width="6dp"
            android:layout_height="10dp"
            android:layout_marginStart="2dp"
            android:src="@drawable/main_ic_scene_listen_item_fly_right" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_cl_module_title"
        android:layout_width="136dp"
        android:layout_height="match_parent"
        android:minHeight="185dp"
        android:paddingLeft="12dp">

        <TextView
            android:id="@+id/main_tv_module_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:singleLine="true"
            android:text="没理想编"
            android:textColor="@color/host_color_ffffff_40"
            android:textSize="13sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/main_iv_more"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/main_iv_more"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:contentDescription="更多"
            android:src="@drawable/main_ic_scene_listen_more"
            app:layout_constraintBottom_toBottomOf="@+id/main_tv_module_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/main_tv_module_title" />

        <!--用于占位  不能移除-->
        <TextView
            android:id="@+id/main_tv_content_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="104dp"
            android:layout_marginRight="12dp"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.1"
            android:maxLines="2"
            android:minLines="2"
            android:text="没理想编辑部看理想新媒体编辑出品"
            android:textColor="@color/host_white"
            android:textSize="13sp"
            android:textStyle="bold"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--占位用的 跟上面一样的-->
        <TextView
            android:id="@+id/main_tv_content_title_holder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="104dp"
            android:layout_marginRight="12dp"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.1"
            android:maxLines="2"
            android:minLines="2"
            android:visibility="invisible"
            android:text="您已领取VIP畅听权益您已领取VIP畅听权益您已领取VIP"
            android:textColor="@color/host_white"
            android:textSize="13dp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/main_ll_play_action"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/main_24corner_ffffff"
            android:gravity="center_vertical"
            android:minHeight="24dp"
            android:orientation="horizontal"
            android:paddingLeft="7dp"
            android:paddingRight="10dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/main_tv_content_title"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_max="wrap">

            <ImageView
                android:id="@+id/main_iv_play_btn_in_action"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:gravity="center_vertical"
                android:importantForAccessibility="no"
                android:src="@drawable/main_icon_btn_play" />

            <TextView
                android:id="@+id/main_tv_play_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:paddingVertical="4dp"
                android:text="立即收听"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="visible" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.ximalaya.ting.android.host.view.CornerRelativeLayout>