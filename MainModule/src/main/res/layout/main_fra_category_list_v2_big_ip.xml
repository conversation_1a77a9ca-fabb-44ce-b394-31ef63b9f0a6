<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.host.view.CornerRelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_big_ip_contain"
    android:visibility="gone"
    tools:visibility="visible"
    app:corner_radius="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_big_ip_contain_layout"
        android:background="@drawable/main_bg_rect_ececec_radius_8"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_video_layout_wrap"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintDimensionRatio="343:172"
            android:layout_width="match_parent"
            android:layout_height="0dp">

            <Space
                android:id="@+id/main_start_guide"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="parent"
                android:layout_width="0dp"
                android:layout_height="match_parent" />

            <Space
                android:id="@+id/main_top_guide"
                app:layout_constraintDimensionRatio="343:20"
                app:layout_constraintBottom_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_width="match_parent"
                android:layout_height="0dp" />

            <ImageView
                android:id="@+id/main_iv_banner_item"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/main_start_guide"
                app:layout_constraintTop_toTopOf="@+id/main_top_guide"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:background="@color/host_color_green" />

            <FrameLayout
                android:id="@+id/main_video_container"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="@+id/main_top_guide"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/main_start_guide"
                app:layout_constraintVertical_bias="1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constrainedWidth="false" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/main_voice_control"
            android:padding="8dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintEnd_toEndOf="@id/main_video_layout_wrap"
            android:src="@drawable/main_voice_icon_select_state"
            app:layout_constraintTop_toTopOf="@id/main_video_layout_wrap"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_cover_bottom_mask_pure"
            tools:background="@color/main_color_ddb159"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/main_video_layout_wrap"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/main_tv_banner_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:layout_marginStart="16dp"
                android:maxLines="1"
                android:paddingEnd="24dp"
                android:textColor="@color/host_color_ffffff"
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_iv_banner_play_tag"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="惜花芷惜花芷惜花芷惜花芷惜花芷惜花芷惜花芷" />

            <TextView
                android:id="@+id/main_view_recommend_reason"
                android:paddingEnd="24dp"
                android:maxLines="1"
                android:layout_marginStart="16dp"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:textSize="11sp"
                tools:text="推荐理由"
                android:textColor="@color/main_color_b3ffffff"
                app:layout_constraintTop_toBottomOf="@+id/main_tv_banner_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/main_iv_banner_play_tag"
                android:layout_height="wrap_content"
                android:layout_width="0dp" />

            <TextView
                android:id="@+id/main_iv_banner_play_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginEnd="16dp"
                android:text="查看详情"
                android:textColor="@color/host_color_ffffff"
                android:textSize="12sp"
                android:fontFamily="sans-serif-light"
                android:textStyle="bold"
                android:paddingVertical="5dp"
                android:gravity="center"
                android:paddingLeft="14dp"
                android:includeFontPadding="false"
                android:paddingRight="10dp"
                android:background="@drawable/main_bg_rect_1affffff_corner_100"
                android:drawablePadding="2dp"
                android:drawableRight="@drawable/main_category_v2_right_more" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</com.ximalaya.ting.android.host.view.CornerRelativeLayout>

