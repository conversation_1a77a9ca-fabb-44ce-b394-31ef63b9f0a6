<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="58dp"
    android:background="@drawable/main_new_yellow_bar_pure_color_bg">

    <ImageView
        android:id="@+id/main_auto_recharge_guide_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:src="@drawable/main_ic_close_auto_recharge_toast"
        android:paddingLeft="10dp"
        android:paddingBottom="10dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="余额不足，自动购买未成功"
        android:textColor="#9F6B2D"
        android:textSize="13sp"
        android:textStyle="bold"
        android:layout_marginTop="11dp"
        android:layout_marginLeft="15dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true" />

    <TextView
        android:id="@+id/main_tv_auto_recharge_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开通自动充值购买，连续收听不中断"
        android:textColor="#DCB488"
        android:textSize="11sp"
        android:textStyle="bold"
        android:layout_marginLeft="15dp"
        android:layout_marginBottom="10dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true" />

    <TextView
        android:id="@+id/main_auto_recharge_btn"
        android:layout_width="74dp"
        android:layout_height="30dp"
        android:text="立即开通"
        android:textSize="14sp"
        android:textColor="#9F6B2D"
        android:textStyle="bold"
        android:gravity="center"
        android:background="@drawable/main_bg_rect_a16e31_radius_22_strokes_1"
        android:layout_marginRight="30dp"
        android:layout_centerVertical="true"
        android:layout_alignParentRight="true" />

</RelativeLayout>