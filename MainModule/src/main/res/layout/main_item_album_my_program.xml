<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical"
    android:paddingTop="15dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginLeft="15dp"
            app:cardBackgroundColor="@color/host_transparent"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp">


            <!--专辑图-->
            <ImageView
                style="@style/main_album_item_cover"
                android:layout_marginBottom="0dp"
                android:layout_marginLeft="0dp"
                android:contentDescription="@string/main_content_description_album_default"
                />

            <!--标签-->
            <ImageView
                android:id="@+id/main_iv_tag"
                style="@style/main_album_item_pay_cover_tag_bar_style"
                android:visibility="invisible"
                tools:visibility="visible" />

        </androidx.cardview.widget.CardView>


        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!--专辑标题-->
            <TextView style="@style/main_album_item_title" />

            <!-- 更新时间 -->
            <TextView
                style="@style/main_album_item_subtitle"
                android:layout_marginLeft="10dp"
                tools:text="更新 29天前" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="6dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/main_tv_play_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="15dp"
                    android:drawableLeft="@drawable/host_ic_play_count"
                    android:drawablePadding="5dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/main_color_999999_888888"
                    android:textSize="12sp"
                    tools:text="19" />

                <TextView
                    android:id="@+id/main_tv_track_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@drawable/host_ic_track_count"
                    android:drawablePadding="5dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/main_color_999999_888888"
                    android:textSize="12sp"
                    tools:text="1集" />

            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/main_iv_album_manage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:contentDescription="@string/main_album_manage"
            android:src="@drawable/host_ic_album_edit_more_selector" />

    </LinearLayout>

    <View
        style="@style/main_album_item_border"
        android:layout_width="match_parent"
        android:layout_marginLeft="15dp" />

    <LinearLayout
        android:id="@+id/main_ll_click_rate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="11dp"
        android:paddingTop="11dp">

        <TextView
            android:id="@+id/main_tv_click_rate_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/main_bg_album_click_rate_tag"
            android:paddingBottom="4dp"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="4dp"
            android:text="@string/main_7_days_click_rate"
            android:textColor="@color/main_color_f86442"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/main_tv_click_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:textColor="@color/main_color_black"
            android:textSize="12sp"
            tools:text="点击率23.2%，比同类专辑" />

        <ImageView
            android:id="@+id/main_iv_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:contentDescription="@string/main_content_description_arrow_up"
            android:src="@drawable/main_ic_arrow_up_album_click_rate" />

        <TextView
            android:id="@+id/main_tv_relative_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:textColor="@color/main_color_f86442"
            android:textSize="12sp"
            tools:text="10.1%" />

        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/main_tv_improve_btn"
            android:layout_width="wrap_content"
            android:layout_height="26dp"
            android:layout_marginRight="15dp"
            android:background="@drawable/main_bg_btn_f86442_100corner"
            android:gravity="center"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:text="@string/main_go_to_improve"
            android:textColor="@color/host_white"
            android:textSize="12sp" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="10dp"
        android:background="@color/main_color_f3f4f5_1e1e1e" />

</LinearLayout>