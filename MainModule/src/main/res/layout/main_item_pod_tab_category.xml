<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingStart="6dp"
    android:paddingEnd="6dp">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/main_iv_icon"
        android:layout_width="80dp"
        android:layout_height="0dp"
        android:alpha=".6"
        android:scaleType="fitXY"
        app:layout_constraintDimensionRatio="72:56"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="6dp"
        android:alpha=".3"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@color/host_color_111111"
        app:layout_constraintBottom_toBottomOf="@id/main_iv_icon"
        app:layout_constraintEnd_toEndOf="@id/main_iv_icon"
        app:layout_constraintStart_toStartOf="@id/main_iv_icon" />

</androidx.constraintlayout.widget.ConstraintLayout>