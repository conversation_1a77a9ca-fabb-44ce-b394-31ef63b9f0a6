<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/main_tv_title"
        style="@style/host_style_text_h2_m"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:textColor="@color/host_color_titleColor"
        android:textSize="17sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="继续播放" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/main_tv_title">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/main_iv_cover"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:src="@drawable/host_default_album"
            app:corner_radius="4dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:round_background="true" />

        <TextView
            android:id="@+id/main_tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:gravity="center_vertical"
            android:textColor="@color/host_color_listTitleColor"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_iv_cover"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="0dp"
            tools:text="今日热点" />

        <ImageView
            android:id="@+id/main_iv_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:src="@drawable/main_alarm_setting_select"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_tv_name"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/main_tv_right"
            style="@style/main_setting_title_right"
            android:layout_marginStart="8dp"
            android:drawableEnd="@drawable/host_ic_more"
            tools:text="费水电费水电费水电费水电费都是粉色的"
            android:textColor="@color/host_color_lightTextColor"
            android:maxLines="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/main_iv_check"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>