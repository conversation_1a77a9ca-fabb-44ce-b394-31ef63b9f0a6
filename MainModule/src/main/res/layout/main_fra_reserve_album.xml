<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.host.view.StickyNavLayout3
        android:id="@+id/main_album_stickynav3"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <ScrollView
            android:id="@+id/host_id_stickynavlayout_topview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:overScrollMode="never"
            android:scrollbars="none">

            <include layout="@layout/main_reserve_album_detail_view" />

        </ScrollView>

        <include layout="@layout/main_fra_reserve_content" />

    </com.ximalaya.ting.android.host.view.StickyNavLayout3>

    <include
        android:id="@+id/main_album_fail_view"
        layout="@layout/main_album3_load_fail_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:background="@color/main_color_ffffff_131313"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:visibility="visible">

        <ImageView
            android:id="@+id/main_album_back_btn"
            android:layout_height="50dp"
            android:layout_width="44dp"
            android:paddingTop="13dp"
            android:paddingBottom="13dp"
            android:paddingLeft="14dp"
            android:paddingEnd="6dp"
            android:scaleType="fitXY"
            android:layout_gravity="center_vertical"
            android:contentDescription="返回"
            android:layout_marginLeft="3dp"
            android:src="@drawable/host_ic_standard_back_arrow_left"
            android:tint="@color/host_color_131313_ffffff" />

        <TextView
            android:id="@+id/main_album_single_page_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_marginEnd="16dp"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:textColor="@color/host_color_131313_ffffff"
            android:textSize="15sp"
            tools:text="专辑名称专辑名称专辑名称专辑名称专辑名称" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_album3_bottom_activity_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/main_album_bottom_control_layout"
        android:visibility="gone"
        tools:visibility="visible">

        <View
            android:id="@+id/main_album3_activity_bg_layout"
            android:layout_width="0dp"
            android:layout_height="64dp"
            android:background="@drawable/main_gradient_album3_activity"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_activity_click_area"
            android:layout_width="match_parent"
            android:layout_height="26dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/main_activity_banner_background"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                app:round="4dp"
                tools:background="#66ff9999"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/main_activity_banner_iv"
                android:layout_width="49dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="1dp"
                android:layout_marginBottom="1dp"
                android:scaleType="centerCrop"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/host_news_ximalaya" />

            <TextView
                android:id="@+id/main_activity_banner_tv"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_marginStart="4dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:singleLine="true"
                android:textColor="#461717"
                android:textSize="11sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/main_activity_banner_iv"
                app:layout_constraintEnd_toStartOf="@+id/main_activity_jump_arrow"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="8dp"
                app:layout_goneMarginEnd="8dp"
                tools:text="底部活动底部活动底部活动底底部活动底部活动底部活动底部活动底部活动底底部活动底部活动" />



            <ImageView
                android:id="@+id/main_activity_jump_arrow"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:contentDescription="跳转"
                android:layout_marginRight="2dp"
                android:padding="6dp"
                android:scaleType="centerCrop"
                android:visibility="gone"
                android:src="@drawable/main_album_activity_right_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/main_album3_bottom_divider_view"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_above="@+id/main_album_bottom_control_layout"
        android:background="@color/host_color_14000000" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_album_bottom_control_layout"
        android:layout_gravity="center"
        android:orientation="horizontal"
        android:background="@color/main_color_ffffff_181818"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="74dp">

        <LinearLayout
            android:id="@+id/main_album_single_subscribe_tv_layout_bottom"
            android:orientation="vertical"
            android:layout_marginStart="10dp"
            android:layout_width="0dp"
            android:gravity="center_vertical"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/main_reserve_bottom_reserve_layout"
            app:layout_constraintHorizontal_weight="1">

            <TextView
                android:id="@+id/main_reserve_bottom_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="sans-serif-light"
                android:gravity="center"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/main_color_393942_dcdcdc"
                android:textSize="15dp"
                android:textStyle="bold"
                android:ellipsize="end"
                tools:text="2025年12月上新" />

            <TextView
                android:id="@+id/main_reserve_bottom_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-light"
                android:gravity="center"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="2dp"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:alpha="0.6"
                android:textColor="@color/main_color_393942_dcdcdc"
                android:textSize="12dp"
                android:ellipsize="end"
                tools:text="360万人已预约" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/main_reserve_bottom_reserve_layout"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:orientation="horizontal"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="@dimen/host_x16"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toEndOf="@+id/main_album_single_subscribe_tv_layout_bottom"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="center"
            android:background="@drawable/main_8corner_xm_red"
            android:layout_gravity="center">

            <TextView
                android:id="@+id/main_reserve_bottom_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:textColor="@color/main_color_ffffff"
                android:textSize="15sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-light"
                android:maxLines="1"
                android:text="立即预约" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>