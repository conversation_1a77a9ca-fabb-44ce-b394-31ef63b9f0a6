<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingEnd="40dp">

    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/main_iv_album_cover"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:albumCoverSize="70dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginTop="0dp" />

    <TextView
        android:id="@+id/main_tv_album_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="《哈利·波特》精品中文有声书哈利·波特》精品中文有声书"
        android:textColor="@color/host_color_textColor"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/main_tv_hot_reason"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/main_iv_album_cover"
        app:layout_constraintTop_toTopOf="@id/main_iv_album_cover" />

    <TextView
        android:id="@+id/main_tv_hot_reason"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/host_color_8d8d91"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/main_layout_show_tag"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/main_tv_album_title"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_album_title"
        tools:text="太喜欢哈利波特系列了太喜欢哈利波特系列了太喜欢哈利波特系列了太喜欢哈利波特系列了" />

    <LinearLayout
        android:id="@+id/main_layout_show_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="2dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/main_iv_album_cover"
        app:layout_constraintStart_toStartOf="@+id/main_tv_album_title"
        app:layout_constraintTop_toBottomOf="@+id/main_tv_hot_reason" />

</androidx.constraintlayout.widget.ConstraintLayout>