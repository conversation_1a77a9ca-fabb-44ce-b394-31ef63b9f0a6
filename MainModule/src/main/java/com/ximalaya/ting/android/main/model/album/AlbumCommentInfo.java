package com.ximalaya.ting.android.main.model.album;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.host.model.album.AlbumSubscript;

/**
 * <AUTHOR> feiwen
 * date   : 2019-08-23
 * desc   :
 */
public class AlbumCommentInfo {
    private String albumCoverPath;
    private String albumTitle;
    private long incrCommentsNum;
    private long totalCommentsNum;
    private long albumId;
    private int vipFreeType;
    private boolean isVipFree;
    private boolean isPaid;
    private String albumSubscript;
    private AlbumSubscript albumSubscriptObject;

    public int getVipFreeType() {
        return vipFreeType;
    }

    public void setVipFreeType(int vipFreeType) {
        this.vipFreeType = vipFreeType;
    }

    public boolean isVipFree() {
        return isVipFree;
    }

    public void setVipFree(boolean vipFree) {
        isVipFree = vipFree;
    }

    public boolean isPaid() {
        return isPaid;
    }

    public void setPaid(boolean paid) {
        isPaid = paid;
    }

    public String getAlbumCoverPath() {
        return albumCoverPath;
    }

    public void setAlbumCoverPath(String albumCoverPath) {
        this.albumCoverPath = albumCoverPath;
    }

    public String getAlbumTitle() {
        return albumTitle;
    }

    public void setAlbumTitle(String albumTitle) {
        this.albumTitle = albumTitle;
    }

    public long getIncrCommentsNum() {
        return incrCommentsNum;
    }

    public void setIncrCommentsNum(long incrCommentsNum) {
        this.incrCommentsNum = incrCommentsNum;
    }

    public long getTotalCommentsNum() {
        return totalCommentsNum;
    }

    public void setTotalCommentsNum(long totalCommentsNum) {
        this.totalCommentsNum = totalCommentsNum;
    }

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public String getAlbumSubscript() {
        return albumSubscript;
    }

    public void setAlbumSubscript(String albumSubscript) {
        this.albumSubscript = albumSubscript;
    }

    public int getAlbumSubscriptValue() {
        if (TextUtils.isEmpty(albumSubscript)) {
            return 0;
        }
        if (albumSubscriptObject == null) {
            albumSubscriptObject = new AlbumSubscript(albumSubscript);
        }
        return albumSubscriptObject.getAlbumSubscriptValue();
    }
}
