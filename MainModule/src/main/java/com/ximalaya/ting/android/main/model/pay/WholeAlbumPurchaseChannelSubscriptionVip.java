package com.ximalaya.ting.android.main.model.pay;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class WholeAlbumPurchaseChannelSubscriptionVip implements Serializable {
    @SerializedName("behavior")
    public SubscriptionVipBehavior behavior;
    @SerializedName("price")
    public WholeAlbumPurchasePrice price;

    public static class SubscriptionVipBehavior extends WholeAlbumPurchaseBehavior {
        @SerializedName("subscriptionItemId")
        public long subscriptionItemId;
        @SerializedName("buttonText")
        public String buttonText;
        @SerializedName("tips")
        public String tips;
        @SerializedName("subscriptionAlert")
        public SubscriptionAlert subscriptionAlert;
    }

    public static class SubscriptionAlert {
        @SerializedName("subscriptionTitle")
        public String subscriptionTitle;
        @SerializedName("subscriptionTexts")
        public List<String> subscriptionTexts;
    }
}
