package com.ximalaya.ting.android.main.anchorModule.v5.works

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.host.adapter.brv4.viewholder.QuickViewHolder
import com.ximalaya.ting.android.host.listener.ICollectWithFollowStatusCallback
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.v5.AnchorCardData
import com.ximalaya.ting.android.main.anchorModule.v5.util.AnchorSpaceUtil
import com.ximalaya.ting.android.main.anchorModule.v5.util.AnchorTraceUtil
import com.ximalaya.ting.android.main.mine.extension.onSingleClick

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2024/10/18
 */
class AnchorWorkItemProvider(fragment: AnchorSpaceWorksTabFragment) :
    BaseItemTypeProvider(fragment) {
    override val itemViewType: Int
        get() = AnchorCardData.TYPE_ALL_WORK_ITEM
    private var holder: QuickViewHolder? = null

    override fun onCreate(context: Context, parent: ViewGroup, viewType: Int): QuickViewHolder {
        return QuickViewHolder(
            LayoutInflater.from(context)
                .inflate(R.layout.main_item_anchor_works_common_album, parent, false).apply {
                    setBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            R.color.host_color_f7f9fc_131313
                        )
                    )
                }
        )
    }

    override fun onBind(holder: QuickViewHolder, position: Int, card: AnchorCardData?) {
        super.onBind(holder, position, card)
        this.holder = holder
        val item = card?.body?.getOrNull(0) ?: return
        holder.itemView.layoutParams = (holder.itemView.layoutParams as MarginLayoutParams).apply {
            bottomMargin = 16f.dp
        }
        holder.itemView.setOnClickListener {
            it.onSingleClick {
                AnchorTraceUtil.traceClickTabPageItem(cardData, item)
                AnchorSpaceUtil.goAlbumPage(
                    mFragment,
                    mFragment.getViewModel()?.anchorUid ?: 0,
                    item.refId
                )
            }
        }
        val coverView = holder.getView<AlbumCoverLayoutView>(R.id.main_album_cover_view)
        coverView.apply {
            setAlbumCover(item.cover ?: "")
        }
        holder.setText(R.id.main_album_tv_title, item.title)
        val title = if (item.subTitle.isNullOrEmpty()) {
            item.summary
        } else {
            item.subTitle
        }
        val subTitle = holder.getView<TextView>(R.id.main_album_sub_title)
        if (title.isNullOrEmpty()) {
            subTitle.visibility = View.GONE
        } else {
            subTitle.text = title
            subTitle.visibility = View.VISIBLE
        }
        holder.getView<TextView>(R.id.main_album_subscribe_btn).apply {
            visibility = View.GONE
            setOnClickListener {
                it.onSingleClick {
                    AnchorSpaceUtil.clickAlbumSubscribe(item,
                        mFragment,
                        object : ICollectWithFollowStatusCallback {
                            override fun onCollectSuccess(code: Int, isCollected: Boolean) {
                                isSelected = isCollected
                                text = if (isCollected) "已订阅" else "订阅"
                            }

                            override fun onError() {}

                            override fun followDialogAction(status: Int) {}
                        })
                }
            }
            text = if (item.interact?.isSubscribed == true) "已订阅" else "订阅"
            isSelected = item.interact?.isSubscribed ?: false
            setTextColor(
                ContextCompat.getColor(
                    context, if (item.interact?.isSubscribed == true)
                        R.color.host_color_668d8d91 else R.color.main_color_anchor_title
                )
            )
        }
        if (item.albumExt?.showTags.isNullOrEmpty()) {
            holder.getView<LinearLayout>(R.id.main_ll_part_tags).visibility = View.GONE
        } else {
            holder.getView<LinearLayout>(R.id.main_ll_part_tags).visibility = View.VISIBLE
            holder.getView<LinearLayout>(R.id.main_ll_part_tags).apply {
                post {
                    RecommendShowTagsUtilNew.bindTagsView(
                        warpLayout = this,
                        tags = item.albumExt?.showTags,
                        containerWidthInPx = measuredWidth,
                        null,
                        null
                    )
                }
            }
        }
//        holder.getView<View>(R.id.main_iv_avatar).visibility = View.GONE
//        holder.getView<View>(R.id.main_tv_author_name).visibility = View.GONE
//        val constraintSet = ConstraintSet()
//        constraintSet.clone(holder.itemView as ConstraintLayout)
//        constraintSet.connect(
//            R.id.main_album_cover_view,
//            ConstraintSet.BOTTOM,
//            ConstraintSet.PARENT_ID,
//            ConstraintSet.BOTTOM
//        )
//        constraintSet.applyTo(holder.itemView as ConstraintLayout)
//        holder.getView<TextView>(R.id.main_album_tv_title).apply {
//            post {
//                layoutParams = (layoutParams as ConstraintLayout.LayoutParams).apply {
//                    topMargin = 2f.dp
//                }
//            }
//        }
    }
}