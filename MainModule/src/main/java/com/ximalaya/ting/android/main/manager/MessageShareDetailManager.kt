package com.ximalaya.ting.android.main.manager

import android.text.TextUtils
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ChatActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.util.xchat.XChatUtil
import com.ximalaya.ting.android.host.xchat.constants.GroupChatMsgType
import com.ximalaya.ting.android.host.xchat.constants.IMXChatConstants
import com.ximalaya.ting.android.host.xchat.constants.SingleChatMsgType
import com.ximalaya.ting.android.host.xchat.model.message.GroupChatMessage
import com.ximalaya.ting.android.host.xchat.model.message.SingleChatMessage
import com.ximalaya.ting.android.im.base.utils.base.ImBaseUtils
import com.ximalaya.ting.android.main.fragment.listenergroup.ChooseResourcePageFragment
import com.ximalaya.ting.android.main.model.MessageShareDetailModel
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmutil.Logger

/**
 * Create by {jian.kang} on 2023/10/25
 * <AUTHOR>
 */
object MessageShareDetailManager {

    private var mShareContent: String? = ""
    private var mTextMsgContent: String? = ""
    private var mAlbumOrTrackUrl: String? = ""

    private var shareList: MutableList<MessageShareDetailModel> = mutableListOf()
    private var listenerList: MutableList<ShareDataChangedListener> = mutableListOf()

    fun addData(data: MessageShareDetailModel?) {
        if (data == null) {
           return
        }

        if (isFull()) {
            return
        }

        shareList.add(data)
        notifyDataChanged()
    }

    fun setMsgContent(msgContent: String?) {
        mTextMsgContent = msgContent
    }

    fun getMegContent(): String? {
        return mTextMsgContent
    }

    fun getShareContentMsg(): String? {
        return mShareContent
    }

    fun setShareContentMsg(shareMessage: String?) {
        mShareContent = shareMessage
    }

    fun getAlbumOrTrackUrl(): String? {
        return mAlbumOrTrackUrl
    }

    fun setAlbumOrTrackUrl(albumOrTrackUrl: String) {
        mAlbumOrTrackUrl = albumOrTrackUrl
    }

    fun addDataList(data: MutableList<MessageShareDetailModel>?) {
        if (data == null || data.size <= 0) {
            return
        }

        if (isFull()) {
            CustomToast.showToast("最多分享30个")
            return
        }

        notifyDataChanged()
    }

    fun removeData(id: Long, sessionType: Int) {
        if (shareList.isEmpty()) {
            return
        }

        val iterator = shareList.iterator() ?: return
        while (iterator.hasNext()) {
            val model = iterator.next() ?: continue
            if (model.id == id && sessionType == model.sessionType) {
                iterator.remove()
            }
        }
        notifyDataChanged()
    }

    fun getDataList(): MutableList<MessageShareDetailModel>? {
        return shareList
    }

    fun getDataSize(): Int {
        return shareList?.size ?: 0
    }

    fun clear() {
        shareList.clear()
        mShareContent = ""
        mTextMsgContent = ""
        mAlbumOrTrackUrl = ""
        notifyDataChanged()
    }

    fun isFull(): Boolean {
        return shareList.size >= 30
    }

    private fun notifyDataChanged() {
        if (listenerList == null || listenerList.isEmpty()) {
            return
        }

        listenerList.forEach {
            it.shareDataChanged()
        }
    }

    private fun notifyFinishCallable(fisrtModel: MessageShareDetailModel?) {
        if (listenerList == null || listenerList.isEmpty()) {
            return
        }

        listenerList.forEach {
            it.finishCallable(fisrtModel)
        }
    }

    interface ShareDataChangedListener {
        fun shareDataChanged()

        fun finishCallable(fisrtModel: MessageShareDetailModel?)
    }

    fun addShareDataChangedListener(listener: ShareDataChangedListener) {
        if (listener == null || listenerList.contains(listener)) {
            return
        }

        listenerList.add(listener)
    }

    fun removeShareDataChangedListener(listener: ShareDataChangedListener) {
        listenerList.remove(listener)
    }

    fun sendGroupAndSingleMsg2AllSession() {
        traceOnShareDialogClick(shareList?.size ?: 0, TextUtils.isEmpty(mTextMsgContent))
        if (TextUtils.isEmpty(mShareContent)) {
            CustomToast.showToast("分享的声音为空")
            return
        }
        val dataList = mutableListOf<MessageShareDetailModel>()
        dataList.addAll(shareList)

        var fisrtModel: MessageShareDetailModel? = null
        for (shareModel in dataList) {
            if (shareModel == null || shareModel.id < 0) {
                continue
            }

            if (fisrtModel == null) {
                fisrtModel = shareModel
            }
            if (shareModel.sessionType == IMXChatConstants.TYPE_SESSION_GROUP) {
                sendShareGroupMsg(shareModel.id)
            } else if (shareModel.sessionType == IMXChatConstants.TYPE_SESSION_SINGLE) {
                sendShareSingleMsg(shareModel.id)
            }
        }
        notifyFinishCallable(fisrtModel)
    }

    private fun sendShareGroupMsg(groupId: Long) {

        val timeMillis = System.currentTimeMillis()
        val newMsg = GroupChatMessage()
        newMsg.mSenderUid = UserInfoMannage.getUid()
        newMsg.mGroupId = groupId
        newMsg.mMsgContent = mShareContent
        newMsg.mMsgType = GroupChatMsgType.TYPE_LINK
        newMsg.mSendStatus = IMXChatConstants.SENDING
        newMsg.mTime = timeMillis
        newMsg.mUniqueId = XChatUtil.genUniqueId()
        newMsg.mIsReaded = true
        newMsg.isRetreat = false
        try {
            val action = Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)!!
                .functionAction
            action?.sendGroupMessage(BaseApplication.getMyApplicationContext(), newMsg) {
                if (!TextUtils.isEmpty(mTextMsgContent)) {
                    //发送留言部分的文字
                    sendTextMsg(groupId)
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun sendTextMsg(groupId: Long) {
        val timeMillis = System.currentTimeMillis()
        val newMsg = GroupChatMessage()
        newMsg.mSenderUid = UserInfoMannage.getUid()
        newMsg.mGroupId = groupId
        newMsg.mMsgContent = mTextMsgContent
        newMsg.mMsgType = GroupChatMsgType.TYPE_TXT
        newMsg.mSendStatus = IMXChatConstants.SENDING
        newMsg.mTime = timeMillis
        newMsg.mUniqueId = XChatUtil.genUniqueId()
        newMsg.mIsReaded = true
        newMsg.isRetreat = false
        try {
            val action = Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)!!
                .functionAction
            action?.sendGroupMessage(
                BaseApplication.getMyApplicationContext(), newMsg
            ) {}
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun sendShareSingleMsg(toUid: Long) {
        val linkMsg =
            createNewImMsgForSend(toUid, mShareContent ?: "", SingleChatMsgType.TYPE_LINK)

        try {
            val action = Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)!!
                .functionAction
            action?.sendSingleMessage(BaseApplication.getMyApplicationContext(), linkMsg) {
                if (!TextUtils.isEmpty(mTextMsgContent)) {
                    //发送留言部分的文字
                    sendSingleTextMsg(toUid)
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }

    }

    private fun sendSingleTextMsg(toUid: Long) {
        val txtMsg =
            createNewImMsgForSend(toUid, mTextMsgContent!!, SingleChatMsgType.TYPE_TXT)
        try {
            val action = Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)!!
                .functionAction
            action?.sendSingleMessage(BaseApplication.getMyApplicationContext(), txtMsg) {
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun createNewImMsgForSend(
        toUid:Long,
        content: String,
        msgType: Int,
    ): SingleChatMessage? {
        val currentTime = System.currentTimeMillis()
        val newMsg = SingleChatMessage()
        newMsg.mSenderUid = UserInfoMannage.getUid()
        newMsg.mMsgType = msgType
        newMsg.mTime = currentTime
        newMsg.mMsgContent = content
        newMsg.mSessionId = toUid
        newMsg.mIsReaded = true
        newMsg.isRetreat = false
        newMsg.mSendStatus = IMXChatConstants.SENDING
        newMsg.mUniqueId = ImBaseUtils.getMsgUniqueId()
        return newMsg
    }

    private fun traceOnShareDialogClick(number: Int, msgEmpty: Boolean) {
        // 分享至站内好友页面-发送至半浮层  点击事件
        XMTraceApi.Trace()
            .click(58644) // 用户点击时上报
            .put("currPage", "shareInsiteFriendPage")
            .put("numbers", "$number") // 传发送人数
            .put("Text", if (!msgEmpty) "1" else "") // 若存在文本内容则传 1，若不存在文本内容则传空
            .createTrace()
    }


}