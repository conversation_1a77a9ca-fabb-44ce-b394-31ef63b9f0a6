package com.ximalaya.ting.android.main.adModule.manager;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.ad.IAdEngineProviderExtend;
import com.ximalaya.ting.android.host.manager.ad.IPlayAdEngine;
import com.ximalaya.ting.android.host.manager.ad.model.AdTypeInfo;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.radio.IRadioFragmentAction;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.main.playpage.audioplaypage.AudioPlayPageAdaptationUtilKt;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.XAudioPlayCoverAdEngine;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.advertis.IXmAdsStatusListener;

public class RadioAdManagerNew implements IMainFunctionAction.IRadioAdManagerNew, IXmAdsStatusListener {
    private IRadioFragmentAction.IRadioFragmentNew fragment;
    private ViewGroup fragmentRootLay;
    private RelativeLayout adRootLay;
    private IPlayAdEngine mPlayAdEngine;

    private boolean isAdShowing;

    public RadioAdManagerNew(@NonNull IRadioFragmentAction.IRadioFragmentNew fragment) {
        this.fragment = fragment;
        adRootLay = fragment.getAdContainer();
        fragmentRootLay = fragment.getFragmentRootLay();
        mPlayAdEngine = getNewPlayAdEngine();
        XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).addAdsStatusListener(this);
    }

    private IPlayAdEngine getNewPlayAdEngine() {
        XAudioPlayCoverAdEngine playCoverAdEngineNew = new XAudioPlayCoverAdEngine(getAdEngineProvider());
        return playCoverAdEngineNew;
    }

    private IAdEngineProviderExtend getAdEngineProvider() {
        return new IAdEngineProviderExtend() {
            @Override
            public int getAdContentHeight() {
                return 0;
            }

            @Override
            public Context getContext() {
                return fragment.getContext();
            }

            @Override
            public boolean canUpdateUi() {
                return fragment.canUpdateUi();
            }

            @Override
            public boolean isRealVis() {
                return fragment.isRealVisable();
            }

            @Override
            public BaseFragment2 getBaseFragment2() {
                return (BaseFragment2) fragment;
            }

            @Override
            public ViewGroup getFragmentRootLayout() {
                return fragmentRootLay;
            }

            @Override
            public RelativeLayout getAdContentLayout() {
                return adRootLay;
            }

            @Override
            public RelativeLayout getAdContentLayoutById(int resId) {
                return adRootLay;
            }

            @Override
            public int getAlbumCoverHeight() {
                return 0;
            }

            @Override
            public int getAdContentWidth() {
                return BaseUtil.getScreenWidth(getContext()) - BaseUtil.dp2px(getContext(), 10) * 2;
            }

            @Override
            public boolean isLargeDevice() {
                return AudioPlayPageAdaptationUtilKt.isLargeDevice();
            }

            @Override
            public boolean hasAdShowing() {
                return isAdShowing;
            }

            @Override
            public void onAdStateChange(boolean show, boolean hideAnimationIng) {
                isAdShowing = show;
                if (show) {
                    adRootLay.setVisibility(View.VISIBLE);
                } else if (!hideAnimationIng) {
                    adRootLay.setVisibility(View.GONE);
                }
            }

            @Override
            public void onHideAnimationOver() {
                adRootLay.setVisibility(View.GONE);
            }

            @Override
            public void onShowDanMuAd(IAbstractAd abstractAd) {
            }

            @Override
            public void onHideDanMu() {
            }

            @Override
            public boolean isDanMuOpen() {
                return false;
            }

            @Override
            public void onShowDanMuFlowerAd(IAbstractAd advertis) {
            }

            @Override
            public void onHideDanMuFlowerAd() {
            }

            @Override
            public int getCategoryId() {
                Track curTrack = PlayTools.getCurTrack(getContext());
                if(curTrack != null) {
                    return curTrack.getCategoryId();
                }
                return 0;
            }

            @Override
            public View getCheckCoverVisView() {
                return null;
            }

            @Override
            public int getControlViewTop() {
                return 0;
            }

            @Override
            public int getListViewScrollY() {
                return 0;
            }

            @Override
            public void resetAllAd() {
                mPlayAdEngine.removeAd();
                XmPlayerManager.getInstance(getContext()).exitSoundAds();
            }

            @Override
            public int getPageMode() {
                return Advertis.PAGE_MODE_RADIO;
            }

            @Override
            public void setScrollHeightListenerView(View view) {
            }

            @Override
            public void curAdViewShow(View curAdView, AdTypeInfo adType) {

            }

            @Override
            public boolean isYellowBarShow() {
                return false;
            }
        };
    }


    @Override
    public void playFragmentOnResume() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.doResumeAction();
        }
    }

    @Override
    public void playFragmentOnPause() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.doOnFragmentPauseAction();
        }
    }

    @Override
    public void playFragmentOnDestroy() {
        XmPlayerManager.getInstance(MainApplication.getMyApplicationContext()).removeAdsStatusListener(this);
        if (mPlayAdEngine != null) {
            mPlayAdEngine.doOnFragmentDestoryAction();
        }
    }

    @Override
    public void onPlayStart() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.doOnPlayStartAction();
        }
    }

    @Override
    public void onPlayPause() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.doOnPlayPauseAction();
        }
    }

    @Override
    public void onSoundSwitch(PlayableModel lastModel, PlayableModel curModel) {
        if (lastModel == null) {
            return; // 首次冷启场景，不通知回调，避免误移除了广告
        }
        if (mPlayAdEngine != null) {
            mPlayAdEngine.doOnSoundSwitchAction(lastModel, curModel);
        }
        adRootLay.setVisibility(View.GONE);
    }

    @Override
    public void onStartGetAdsInfo(int playMethod, boolean duringPlay, boolean isPaused) {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onStartGetAdsInfo(playMethod, duringPlay, isPaused);
        }
    }

    @Override
    public void onGetAdsInfo(AdvertisList ads) {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onGetAdsInfo(ads);
        }
    }

    @Override
    public void onAdsStartBuffering() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onAdsStartBuffering();
        }
    }

    @Override
    public void onAdsStopBuffering() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onAdsStopBuffering();
        }
    }

    @Override
    public void onStartPlayAds(Advertis ad, int position) {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onStartPlayAds(ad, position);
        }
        if (fragment != null) {
            fragment.onStartPlayAds();
        }
    }

    @Override
    public void onCompletePlayAds() {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onCompletePlayAds();
        }
        if (fragment != null) {
            fragment.onCompletePlayAds();
        }
    }

    @Override
    public void onError(int what, int extra) {
        if (mPlayAdEngine != null) {
            mPlayAdEngine.onError(what, extra);
        }
    }
}
