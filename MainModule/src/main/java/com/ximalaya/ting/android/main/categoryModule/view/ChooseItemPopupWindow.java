package com.ximalaya.ting.android.main.categoryModule.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2017/11/30.
 */

public class ChooseItemPopupWindow extends PopupWindow implements View.OnClickListener,View.OnKeyListener {
    private List<OnChosenChangeListener> mListeners = new ArrayList<>();
    private int mChosenId;
    private int mChosenIdPosition;
    private BaseAdapter mAdapter;
    private View mContainer;
    private Context mContext;
    private View mContentView;
    private TextView mTvTitle;
    private String mTitle;
    private View mContainerView;

    private int mFrom = FROM_CATEGORY;
    public static final int FROM_CATEGORY = 1;
    public static final int FROM_HOME_PAGE = 2;

    public void setFrom(int from){
        mFrom = from;
    }

    private String mCategoryId;

    public void setCategoryId(String categoryId){
        mCategoryId = categoryId;
    }

    public ChooseItemPopupWindow(Context context, List<ChooseItemM> items){
        this(context, items, -1);
    }

    public ChooseItemPopupWindow(Context context, List<ChooseItemM> items, int chosenId){
        super(context);

        mContext = context;
        mChosenId = chosenId;
        mContentView = LayoutInflater.from(context).inflate(R.layout.main_pw_choose_category, null);
        mContainerView = mContentView.findViewById(R.id.main_ll_container);
        mContentView.findViewById(R.id.main_pull_down_btn_up).setOnClickListener(this);
        mContainerView.setOnClickListener(this);
        AutoTraceHelper.bindData(mContentView.findViewById(R.id.main_pull_down_btn_up),"");
        AutoTraceHelper.bindData(mContainerView,"");

        mContainer = mContentView.findViewById(R.id.main_category_layout);
        GridView gvItems = mContentView.findViewById(R.id.main_gv_items);
        mTvTitle = mContentView.findViewById(R.id.main_category_tips_tv);
        if (!TextUtils.isEmpty(mTitle)) {
            mTvTitle.setText(mTitle);
        }

        //当有标题大于等于5个字符的选项时，每行只显示3列
        boolean hasLongText = false;
        for(ChooseItemM itemM : items){
            if(itemM.displayName.length() >= 5){
                hasLongText = true;
                break;
            }
        }

        if(hasLongText){
            gvItems.setNumColumns(3);
        }

        mAdapter = new ChooseItemAdapter(context, items);
        gvItems.setAdapter(mAdapter);

        mContentView.setFocusable(true);
        mContentView.setFocusableInTouchMode(true);
        mContentView.setOnKeyListener(this);
        setAnimationStyle(0);
        setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        setBackgroundDrawable(null);
        setTouchable(true);
        setFocusable(true);
        setContentView(mContentView);
    }

    public void setChosenId(int id){
        mChosenId = id;
        mAdapter.notifyDataSetChanged();
    }

    public void setChosenPosition(int position){
        ChooseItemM itemM = (ChooseItemM)mAdapter.getItem(position);
        setChosenId(itemM.id);
    }

    public void setTitle(String title) {
        mTitle = title;
        if (mTvTitle != null && !TextUtils.isEmpty(mTitle)) {
            mTvTitle.setText(mTitle);
        }
    }

    @Override
    public void showAsDropDown(View anchor) {
        if (mContainerView != null) {
            int anchorHeight = anchor.getHeight();
            int paddingTop = StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR ? anchorHeight - BaseUtil.getStatusBarHeight(anchor.getContext()) : anchorHeight;
            mContainerView.setPadding(0,paddingTop,0,0);
        }
        super.showAsDropDown(anchor);

        int measuredHeight = mContainer.getMeasuredHeight();
        if(measuredHeight <= 0){  //第一次show时，mContainer还未进行measure操作
            mContentView.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
                @Override
                public void onLayoutChange(View v, int left, int top, int right, int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                    v.removeOnLayoutChangeListener(this);
                    showAnimation();
                }
            });
        }else {
            showAnimation();
        }
    }

    private void showAnimation(){
        float curTranslationY = mContainer.getTranslationY();
        int measuredHeight = mContainer.getMeasuredHeight();
        ObjectAnimator animator = ObjectAnimator.ofFloat(mContainer, "translationY", -measuredHeight, curTranslationY);
        animator.setDuration(500);
        animator.start();
    }

    @Override
    public void onClick(View v) {
        if(v.getId() == R.id.main_pull_down_btn_up || v.getId() == R.id.main_ll_container){
            if(v.getId() == R.id.main_pull_down_btn_up){
                if(mFrom == FROM_CATEGORY){
                    new UserTracking().setSrcPage("category").
                            setSrcPageId(mCategoryId).
                            setSrcModule("所有分类").
                            setItem("button").
                            setItemId("收起").
                            statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);

                } else if (mFrom == FROM_HOME_PAGE) {
                    new UserTracking()
                            .setSrcPage("首页")
                            .setSrcModule("所有分类")
                            .setItem("button")
                            .setItemId("收起")
                            .statIting(UserTracking.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
                }
            }

            dismiss();
        }
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            dismiss();
            return true;
        }
        return false;
    }

    private class ChooseItemAdapter extends HolderAdapter<ChooseItemM> {
        @Override
        public void onClick(View view, ChooseItemM t, int position,
                            BaseViewHolder holder) {
            if (OneClickHelper.getInstance().onClick(view)) {
                mChosenId = t.id;
                mChosenIdPosition = position;

                if(mFrom == FROM_CATEGORY ){
                    if(position == 0) {
                        new UserTracking().setSrcPage("category").
                                setSrcPageId(mCategoryId).
                                setSrcModule("全部").
                                setItem("page").
                                setItemId("全部分类页").
                                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);
                    }else{
                        new UserTracking().setSrcPage("category").
                                setSrcPageId(mCategoryId).
                                setSrcModule("所有分类").
                                setItem("hotword").
                                setItemId(t.displayName).
                                statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_CATEGORY_PAGE_CLICK);
                    }
                }

                notifyDataSetChanged();

                dismiss();

                notifyListeners();
            }
        }

        public ChooseItemAdapter(Context context, List<ChooseItemM> listData) {
            super(context, listData);
        }

        @Override
        public int getConvertViewId() {
            return R.layout.main_item_choose_item_grid;
        }

        @Override
        public BaseViewHolder buildHolder(View convertView) {
            ChooseItemAdapter.ViewHolder holder = new ChooseItemAdapter.ViewHolder();
            holder.tvName = (TextView) convertView;
            return holder;
        }

        @Override
        public void bindViewDatas(BaseViewHolder holder, ChooseItemM t,
                                  int position) {
            ChooseItemAdapter.ViewHolder h = (ChooseItemAdapter.ViewHolder) holder;
            h.tvName.setText(t.displayName);
            h.tvName.setTextColor(mContext.getResources()
                    .getColor(t.id == mChosenId ? R.color.main_color_ffffff
                            : R.color.main_color_111111_888888));
            h.tvName.setBackgroundResource(t.id == mChosenId ? R.drawable.main_round_bg_red_radius_100
                    : R.drawable.main_round_bg_popup_radius_100);
            setClickListener(h.tvName, t, position, holder);
//            if (t.getCategoryId() == mId) {
//                h.tvName.setBackgroundResource(R.drawable.orange_underline2);
//            }
        }

        class ViewHolder extends BaseViewHolder {
            TextView tvName;
        }
    }

    private void notifyListeners(){
        for(OnChosenChangeListener listener : mListeners){
            listener.onChosenChange(mChosenId, mChosenIdPosition);
        }
    }

    public void addOnChosenChangeListener(OnChosenChangeListener listener){
        if(listener != null && ! mListeners.contains(listener)){
            mListeners.add(listener);
        }
    }

    public void removeOnChosenChangeListener(OnChosenChangeListener listener){
        if(listener != null){
            mListeners.remove(listener);
        }
    }

    public interface OnChosenChangeListener {
        void onChosenChange(int chosenId, int chosenIdPosition);
    }

    public static class ChooseItemM{
        public String displayName;
        public int id;
    }
}
