package com.ximalaya.ting.android.main.playpage.dialog

import android.content.DialogInterface
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.DialogFragment
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment
import com.ximalaya.ting.android.host.manager.dialog.GlobalDialogManager
import com.ximalaya.ting.android.host.manager.dialog.IGlobalDialogControlType
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.play.TrackPlayQualityManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.play.*
import com.ximalaya.ting.android.host.util.common.SpanUtils
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playModule.quality.ChooseTrackPlayQualityDialog
import com.ximalaya.ting.android.main.playModule.quality.PlayQualityRightRequestDialog
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager
import com.ximalaya.ting.android.main.playpage.playy.dialog.ChooseTrackSoundEffectAiDialogXNew
import com.ximalaya.ting.android.main.playpage.util.XPlayCommercialRelatedUtils
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.ref.WeakReference

/**
 * <AUTHOR>
 * @time 2023/6/27 14:52
 * @description: 免广告权益、音质音效权益强化领取弹窗
 **/
class XFreeVipPrivilegeGrantFloatDialog : BaseLoadDialogFragment(), View.OnClickListener {
    enum class SourceType {
        TYPE_AD_CLOSE, TYPE_DEFAULT, TYPE_CLICK_SHQ_BTN, TYPE_FULL_DEPTH_QUALITY_GRANT
    }

    lateinit var yellowZoneModel: CommonGuidanceInfo
    lateinit var playingSoundInfo: PlayingSoundInfo
    private var chosenTrackQualityInfo: TrackQualities? = null

    private var ivBg: ImageView? = null
    private var tvAction: TextView? = null
    private var tvTitle: TextView? = null
    private var tvSubTitle: TextView? = null
    private var tvActionTag: TextView? = null
    private var sourceType: SourceType = SourceType.TYPE_DEFAULT
    private var isRequestingPrivilege = false

    init {
        parentNeedBg = false
    }

    override fun onStart() {
        super.onStart()
        setStyle(
            DialogFragment.STYLE_NO_TITLE,
            com.ximalaya.ting.android.host.R.style.host_share_dialog
        )
        dialog?.window?.let {
            it.setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_animation_fade)
            it.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            it.decorView?.setPadding(0, 0, 0, 0)
            val params: WindowManager.LayoutParams? = it.attributes
            params?.gravity = Gravity.BOTTOM
            params?.width = WindowManager.LayoutParams.MATCH_PARENT
            params?.height = WindowManager.LayoutParams.WRAP_CONTENT
            it.attributes = params
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.main_iv_close -> {
                XPlayCommercialRelatedUtils.markFreeVipPrivilegeGrantFloatBtnClick(
                    yellowZoneModel.guidanceType ?: "", sourceType, "关闭"
                )
                dismissAllowingStateLoss()
            }
            R.id.main_view_click_area -> {
                if (isRequestingPrivilege) return
                val btnUrl = getBtnUrl()
                if (btnUrl.isEmpty()) {
                    CustomToast.showToast("当前网络较差，领取失败")
                    return
                }
                onPageLoadingCompleted(LoadCompleteType.LOADING)
                isRequestingPrivilege = true
                XPlayCommercialRelatedUtils.markFreeVipPrivilegeGrantFloatBtnClick(
                    yellowZoneModel.guidanceType ?: "", sourceType, "现在领取"
                )
                CommonRequestM.baseGetRequest(
                    btnUrl,
                    emptyMap(),
                    object : IDataCallBack<String> {
                        override fun onSuccess(data: String?) {
                            if (canUpdateUi()) {
                                isRequestingPrivilege = false
                                onGetPrivilegePackSuccess()
                            }
                        }

                        override fun onError(code: Int, message: String?) {
                            if (canUpdateUi()) {
                                isRequestingPrivilege = false
                                CustomToast.showToast("当前网络较差，领取失败")
                                dismissAllowingStateLoss()
                            }
                        }
                    }
                ) { content -> content ?: "" }
            }
            else -> return
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (sourceType == SourceType.TYPE_CLICK_SHQ_BTN && CommonGuidanceInfo.TYPE_SHQ_PRIVILEGE_FLOAT == yellowZoneModel.guidanceType
        ) {
            showSoundEffectDialog()
        }
    }

    private fun showSoundEffectDialog() {
        if (parentFragment is BaseFragment2 && (parentFragment as BaseFragment2).canUpdateUi()) {
//            ChooseTrackSoundEffectDialogX.newInstance(
//                parentFragment as BaseFragment2,
//                playingSoundInfo.albumInfo?.albumId ?: 0,
//                playingSoundInfo.trackInfo?.trackId ?: 0,
//                null,
//                null
//            ).show()
            ChooseTrackSoundEffectAiDialogXNew.newInstance(playingSoundInfo.albumInfo?.albumId ?: 0,playingSoundInfo.trackInfo?.trackId ?: 0)
                .show(childFragmentManager,ChooseTrackSoundEffectAiDialogXNew::class.java.simpleName)
        }
    }

    override fun getContainerLayoutId() = R.layout.main_dialog_free_vip_benefits_x

    override fun initUi(view: View?, savedInstanceState: Bundle?) {
        if (yellowZoneModel.type == YellowZoneModel.YELLOW_ZONE_TYPE_SHQ_PRIVILEGE_FLOAT
            || yellowZoneModel.type == YellowZoneModel.YELLOW_ZONE_TYPE_AD_PRIVILEGE_FLOAT
        ) {
            XPlayCommercialRelatedUtils.markFreeVipPrivilegeDialogShow(
                context,
                yellowZoneModel.type
            )
        }
        if (yellowZoneModel.type == YellowZoneModel.YELLOW_ZONE_TYPE_AD_PRIVILEGE_FLOAT) {
            XPlayCommercialRelatedUtils.removeAdCloseCountByUser(context)
        } else if (yellowZoneModel.type == YellowZoneModel.YELLOW_ZONE_TYPE_SHQ_PRIVILEGE_FLOAT) {
            XPlayCommercialRelatedUtils.isSHQDialogShowed = true
        }
        ivBg = view?.findViewById<ImageView>(R.id.main_iv_cover)
        tvTitle = view?.findViewById(R.id.main_tv_title)
        tvSubTitle = view?.findViewById(R.id.main_tv_sub_title)
        view?.findViewById<View>(R.id.main_iv_close)?.setOnClickListener(this)
        tvAction = view?.findViewById<TextView>(R.id.main_tv_action)
        view?.findViewById<View>(R.id.main_view_click_area)?.setOnClickListener(this)
        tvActionTag = view?.findViewById(R.id.main_tv_action_tag)
        setView()
        XPlayCommercialRelatedUtils.markFreeVipPrivilegeGrantFloatShow(
            yellowZoneModel.guidanceType ?: "", sourceType
        )
    }

    private fun getBgUrl(): String? {
        var url: String? = yellowZoneModel.backgroundImage
        if (BaseFragmentActivity.sIsDarkMode) {
            val extraInfo: Map<String, String>? = yellowZoneModel.extraInfo
            val darkUrl = if (extraInfo == null) null else extraInfo["darkImage"]
            if (!TextUtils.isEmpty(darkUrl)) {
                url = darkUrl
            }
        }
        return url
    }

    private fun getBtnUrl(): String {
        var url = yellowZoneModel.buttonList?.firstOrNull()?.url ?: ""
        if (url.isEmpty()) {
            yellowZoneModel.buttonList?.forEach {
                if (!it.url.isNullOrEmpty()) {
                    url = it.url
                    return@forEach
                }
            }
        }
        return url
    }

    private fun setView() {
        when (yellowZoneModel.guidanceType) {
            CommonGuidanceInfo.TYPE_AD_PRIVILEGE_FLOAT -> {
                ImageManager.from(context)
                    .displayImage(ivBg, getBgUrl(), -1)
                updateAdPrivilegeText()
                tvAction?.text = "立即免费体验"
                tvActionTag?.visibility = View.VISIBLE
            }
            CommonGuidanceInfo.TYPE_SHQ_PRIVILEGE_FLOAT -> {
                ImageManager.from(context)
                    .displayImage(ivBg, getBgUrl(), -1)
                updateAdPrivilegeText()
                tvAction?.text = "立即免费体验"
                tvActionTag?.visibility = View.VISIBLE
            }
            CommonGuidanceInfo.TYPE_SURROUND_SOUND_FLOAT_LAYER -> {
                if (getBgUrl().isNullOrEmpty()) {
                    ivBg?.setImageResource(R.drawable.main_ic_surround_sound_effect_icon)
                } else {
                    ImageManager.from(context)
                        .displayImage(ivBg, getBgUrl(), -1)
                }
                updateAdPrivilegeText()
                tvAction?.text = "立即去体验"
                tvActionTag?.visibility = View.INVISIBLE
            }
        }
    }

    private fun updateAdPrivilegeText() {
        val text = yellowZoneModel.text
        text ?: return
        // "恭喜您，获得#高亮#会员专属免广告权益\\n畅享纯净收听体验"
        // 文案必须匹配这个格式：文本#高亮文本#文本\\n副文本
        val filterText = text.replace("\n", "\\n")
        if (filterText.contains("#") && filterText.contains("\\n")) {
            val textList = text.split("#", "\\n")
            val spanUtils = SpanUtils.with(tvTitle)
            if (textList.size == 4) {
                spanUtils.append(textList[0]).append(textList[1])
                    .setForegroundColor(Color.parseColor("#FF4444"))
                    .append(textList[2]).create()
                tvSubTitle?.text = textList[3]
            }
        } else if (filterText.contains("\\n")) {
            val textList = text.split("\\n")
            if (textList.size == 2) {
                tvTitle?.text = textList[0]
                tvSubTitle?.text = textList[1]
            }
        } else {
            tvTitle?.text = filterText
        }
    }

    override fun loadData() {
    }

    fun onGetPrivilegePackSuccess() {

        when (yellowZoneModel.guidanceType) {
            CommonGuidanceInfo.TYPE_SURROUND_SOUND_FLOAT_LAYER -> {
                delayCheckPrivilege()
            }
            else -> {
                onPageLoadingCompleted(LoadCompleteType.OK)
                if (yellowZoneModel.type == YellowZoneModel.YELLOW_ZONE_TYPE_SHQ_PRIVILEGE_FLOAT
                    && sourceType == SourceType.TYPE_DEFAULT
                ) {
                    showSoundEffectDialog()
                }
                CustomToast.showToast("领取成功")
                dismissAllowingStateLoss()
            }
        }
    }

    private fun delayCheckPrivilege() {
        HandlerManager.postOnUIThreadDelay4Kt(1000) {
            if (canUpdateUi()) {
                val soundInfo = PlayPageDataManager.getInstance().soundInfo
                if (soundInfo?.trackInfo != null && soundInfo.albumInfo != null) {
                    CommonRequestM.getPlayPageQualityAndEffectList(soundInfo.albumInfo!!.albumId,
                        soundInfo.trackInfo!!.trackId,
                        object : IDataCallBack<TrackQualityAndEffectInfo> {
                            override fun onSuccess(data: TrackQualityAndEffectInfo?) {
                                if (canUpdateUi()) {
                                    if (null != data && null != data.trackQuliatyInfo?.trackQualities) {
                                        var foundQuality: TrackQualities? = null
                                        Logger.i(
                                            "XFreeVipPrivilegeGrantFloatDialog",
                                            "startChooseQuality"
                                        )
                                        data.trackQuliatyInfo!!.trackQualities!!.forEach {
                                            if (it.qualityLevel == chosenTrackQualityInfo?.qualityLevel) {
                                                foundQuality = it
                                                Logger.i(
                                                    "XFreeVipPrivilegeGrantFloatDialog",
                                                    "found Quality:$foundQuality"
                                                )
                                                return@forEach
                                            }
                                        }
                                        Logger.i(
                                            "XFreeVipPrivilegeGrantFloatDialog",
                                            "canChoose:${foundQuality?.canChoose}"
                                        )
                                        if (foundQuality?.canChoose == true) {
                                            TrackPlayQualityManager.getInstance().trackPlayQualityLevel =
                                                foundQuality!!.qualityLevel
                                            val dialog =
                                                ChooseTrackPlayQualityDialog.getCurrentDialog()
                                            dialog?.dismiss()
                                            if (TrackPlayQualityManager.getInstance()
                                                    .isFullDepth(foundQuality!!.qualityLevel)
                                            ) {
                                                PlayQualityRightRequestDialog.showDialog(
                                                    PlayQualityRightRequestDialog.TYPE_FULL_DEPTH_QUALITY
                                                )
                                            }
                                        }
                                    }
                                    CustomToast.showToast("领取成功")
                                    dismissAllowingStateLoss()
                                }
                            }

                            override fun onError(code: Int, message: String?) {
                                if (canUpdateUi()) {
                                    CustomToast.showToast("领取成功")
                                    dismissAllowingStateLoss()
                                }
                            }
                        })
                }
            }
        }
    }

    companion object {
        const val TAG = "XAdPrivilegeFloatDialog"

        @JvmStatic
        fun createDialogRecord(
            fragment2: BaseFragment2, yellowZoneModel: CommonGuidanceInfo,
            playingSoundInfo: PlayingSoundInfo,
            sourceType: SourceType,
            chosenTrackQualityInfo: TrackQualities? = null
        ): GlobalDialogRecord {
            return GlobalDialogRecord(
                fragment2,
                playingSoundInfo,
                yellowZoneModel,
                sourceType,
                chosenTrackQualityInfo
            )
        }

        fun newInstance(
            yellowZoneModel: CommonGuidanceInfo,
            playingSoundInfo: PlayingSoundInfo,
            source: SourceType,
            chosenTrackQualityInfo: TrackQualities?
        ): XFreeVipPrivilegeGrantFloatDialog {
            return XFreeVipPrivilegeGrantFloatDialog().also {
                it.yellowZoneModel = yellowZoneModel
                it.playingSoundInfo = playingSoundInfo
                it.sourceType = source
                it.chosenTrackQualityInfo = chosenTrackQualityInfo
            }
        }
    }

    class GlobalDialogRecord :
        GlobalDialogManager.DialogRecord {

        companion object {
            private val POSITION_TYPES = mutableListOf<Int>()
            private val TO_BULLY_TYPES = mutableListOf<Long>()
        }

        private var mYellowZoneModel: CommonGuidanceInfo
        private var mPlayingSoundInfo: PlayingSoundInfo
        private var mChosenTrackQualityInfo: TrackQualities? = null
        private var mFraRef: WeakReference<BaseFragment2>
        private var mSourceType: SourceType = SourceType.TYPE_DEFAULT
        private var dialogRef: WeakReference<XFreeVipPrivilegeGrantFloatDialog>? = null

        constructor(
            fragment2: BaseFragment2,
            playingSoundInfo: PlayingSoundInfo,
            yellowZoneModel: CommonGuidanceInfo,
            sourceType: SourceType,
            chosenTrackQualityInfo: TrackQualities? = null
        ) : super(
            IGlobalDialogControlType.GlobalDialogSpecificId.ID_FREE_VIP_PRIVILEGE_DIALOG,
            POSITION_TYPES
        ) {
            mFraRef = WeakReference(fragment2)
            mYellowZoneModel = yellowZoneModel
            mPlayingSoundInfo = playingSoundInfo
            mSourceType = sourceType
            mChosenTrackQualityInfo = chosenTrackQualityInfo
        }

        init {
            POSITION_TYPES.add(IGlobalDialogControlType.POSITION_TYPE_DIALOG_FROM_BOTTOM)
            TO_BULLY_TYPES.add(IGlobalDialogControlType.GlobalDialogSpecificId.ID_FREE_VIP_PRIVILEGE_DIALOG)
        }

        override fun forceDismissThis(): Boolean {
            dialogRef?.get()?.dismissAllowingStateLoss()
            return true
        }

        override fun isThisStillValid(): Boolean {
            return dialogRef?.get()?.isAddFix == true
        }

        override fun buildAndShowThis(): Int {
            val fragmentManager = mFraRef?.get()?.childFragmentManager
                ?: return IGlobalDialogControlType.GlobalDialogBuildingResult.BUILDING_ERROR
            val dialog: XFreeVipPrivilegeGrantFloatDialog =
                newInstance(
                    mYellowZoneModel,
                    mPlayingSoundInfo,
                    mSourceType,
                    mChosenTrackQualityInfo
                )
            dialogRef = WeakReference(dialog)
            dialog.show(fragmentManager, TAG)
            return IGlobalDialogControlType.GlobalDialogBuildingResult.BUILDING_OK
        }
    }
    private var mWindowWidthDp = 0;
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
//        Logger.i(TAG, "onConfigurationChanged:" +
//                "\n config.width:${newConfig.screenWidthDp}"+
//                "\n config.height:${newConfig.screenHeightDp}"+
//                "\n config.densityDpi:${newConfig.densityDpi}"+
//                "\n window.width:${dialog?.window?.decorView?.measuredWidth}"+
//                "\n window.height:${dialog?.window?.decorView?.measuredHeight}" )
        if (mWindowWidthDp != newConfig.screenWidthDp) {
            mWindowWidthDp = newConfig.screenWidthDp
            if (mContainerView != null) {
                mContainerView?.updateLayoutParams<ViewGroup.LayoutParams> {
                    width = BaseUtil.dp2px(context, mWindowWidthDp.toFloat())
                }
            }
            dialog?.window?.let {
                val lp = it.attributes
                lp.width = BaseUtil.dp2px(context, mWindowWidthDp.toFloat())
                it.attributes = lp
            }
        }
    }
}