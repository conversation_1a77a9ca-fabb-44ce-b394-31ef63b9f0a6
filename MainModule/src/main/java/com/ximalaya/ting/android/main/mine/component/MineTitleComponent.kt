package com.ximalaya.ting.android.main.mine.component

import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils
import com.ximalaya.ting.android.framework.manager.ImageManager
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.manager.account.NoReadManage
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.ad.RewardAgainAdManager
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.ChatActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.feedback.CustomerFeedBackManager
import com.ximalaya.ting.android.host.manager.play.FreeListenConfigManager
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager
import com.ximalaya.ting.android.host.model.account.FreeListenV2
import com.ximalaya.ting.android.host.model.account.HomePageModelNew
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis
import com.ximalaya.ting.android.host.util.BarUtils
import com.ximalaya.ting.android.host.util.XmRequestPage
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.host.util.safeAs
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.host.view.RedDotView
import com.ximalaya.ting.android.host.view.ShadowView
import com.ximalaya.ting.android.host.view.popupwindow.CustomPopWindow
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.util.newAnchorSpaceFragment
import com.ximalaya.ting.android.main.fragment.myspace.child.SettingFragment
import com.ximalaya.ting.android.main.mine.extension.onSingleClick
import com.ximalaya.ting.android.main.mine.extension.visible
import com.ximalaya.ting.android.main.mine.fragment.AbsMineFragmentV9
import com.ximalaya.ting.android.main.mine.fragment.MineFragmentV9
import com.ximalaya.ting.android.main.mine.manager.MineTraceManagerV9
import com.ximalaya.ting.android.main.mine.util.MineTopUtil
import com.ximalaya.ting.android.main.mine.util.traceOnAvatarClick
import com.ximalaya.ting.android.main.mine.util.traceOnAvatarShow
import com.ximalaya.ting.android.main.mine.util.traceOnClickAvatarNoLogin
import com.ximalaya.ting.android.main.mine.util.traceOnMineKefuClick
import com.ximalaya.ting.android.main.mine.util.traceOnMineMessageClick
import com.ximalaya.ting.android.main.mine.util.traceOnMineSettingClick
import com.ximalaya.ting.android.main.mine.util.traceOnTitleAvatarClick
import com.ximalaya.ting.android.main.util.MyListenAbUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2023/8/14
 * desc : 同时维护带广告支持滚动的titleBar以及固定顶部的titleBar
 */
class MineTitleComponent(
    private val mMineFragment: AbsMineFragmentV9,
    private val mFixTitleBar: View,
    private val mTitleBar: View,
    private val mIvAnimateAvatar: RoundImageView,
    private val mKKIvAnimateAvatar: ImageView
) {
    private var mIsFixShow = true
    private var homePageModel: HomePageModelNew? = null

    private val mIvTitleAvatar: RoundImageView by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<RoundImageView>(R.id.main_iv_title_avatar)
    }
    private val mKKIvTitleAvatar: RoundImageView by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<RoundImageView>(R.id.main_iv_title_avatar_kk)
    }
    private val mTvTitleNickname: TextView by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<TextView>(R.id.main_tv_title_nickname)
    }
    private var mRdMessage: RedDotView? = null
    private val mIvMessage: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<ImageView>(R.id.main_iv_message)
    }
    private val mMsgContainer: View? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById(R.id.main_message_container)
    }
    private val mIvSetting: ImageView by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<ImageView>(R.id.main_iv_setting)
    }
    private val mIvRecordLive: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<ImageView>(R.id.main_iv_record_live)
    }
    private val mIvDressUp: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<ImageView>(R.id.main_iv_dress_up)
    }
    private val mIvKefu: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById<ImageView>(R.id.main_iv_kefu)
    }
    private val mScrollIvMessage: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById<ImageView>(R.id.main_iv_message)
    }
    private val mScrollMsgContainer: View? by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById(R.id.main_message_container)
    }
    private val mScrollIvSetting: ImageView by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById<ImageView>(R.id.main_iv_setting)
    }
    private val mScrollIvKefu: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById<ImageView>(R.id.main_iv_kefu)
    }
    private val mScrollIvDressUp: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById<ImageView>(R.id.main_iv_dress_up)
    }

    // 畅听
    private val mIvOpenListen: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById(R.id.main_iv_open_listen_entry)
    }

    private val mIvScrollOpenListen: ImageView? by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById(R.id.main_iv_open_listen_entry)
    }

    private val mTvScrollListenerMake: TextView? by lazy(LazyThreadSafetyMode.NONE) {
        mTitleBar.findViewById(R.id.main_tv_open_listen_mark)
    }

    private val mTvListenerMake: TextView? by lazy(LazyThreadSafetyMode.NONE) {
        mFixTitleBar.findViewById(R.id.main_tv_open_listen_mark)
    }

    // 录音直播
    private val mLlRecordLive: View? by lazy {
        mFixTitleBar.findViewById(R.id.main_ll_record_live)
    }
    private val mScrollLlRecordLive: View? by lazy {
        mTitleBar.findViewById(R.id.main_ll_record_live)
    }
    private val mTvRecordLive: TextView? by lazy {
        mFixTitleBar.findViewById(R.id.main_tv_record_live)
    }
    private val mTvScrollRecordLive: TextView? by lazy {
        mTitleBar.findViewById(R.id.main_tv_record_live)
    }
    private val mSlideEntryIv: ImageView? by lazy { mFixTitleBar.findViewById(R.id.main_slide_entry_iv) }
    private val mSlideEntry: View? by lazy { mFixTitleBar.findViewById(R.id.main_slide_entry) }
    private val mSlideRedDot: TextView? by lazy { mFixTitleBar.findViewById(R.id.main_slide_entry_red_dot) }

    private var mTipsText: String? = ""
    private var mListenerUrl: String? = ""

    private val context by lazy {
        mMineFragment.context
    }
    private val avatarHeight = if (MineTopUtil.isUseVipInfoCard()) 54 else 64
    private val avatarGapDistance = if (MineTopUtil.isVipNewStyle()) 25 else if (MineTopUtil.isUseVipInfoCard()) 31 else 12
    private var mAvatarScrollX = (avatarHeight.dpFloat - 28.dpFloat) / 2
    private var mAvatarScrollY =
        avatarHeight.dpFloat / 2 + avatarGapDistance.dpFloat + 28.dpFloat / 2
    private var mScale = 28 * 1f / avatarHeight
    private var mHasKK = false
    private var mIvAvatarTopMargin = 0

    private val mOnClickListener = View.OnClickListener {
        it.onSingleClick { v ->
            when (v) {
                mIvTitleAvatar, mKKIvTitleAvatar -> {
                    clickAvatar()
                    traceOnTitleAvatarClick()
                }

                mIvAnimateAvatar, mKKIvAnimateAvatar -> {
                    clickAvatar()
                    if (UserInfoMannage.hasLogined()) {
                        traceOnAvatarClick(mHasKK)
                    } else {
                        traceOnClickAvatarNoLogin()
                    }
                }

                mTvTitleNickname -> clickAvatar()

                mMsgContainer, mScrollMsgContainer -> {
                    clickMessage()
                    traceOnMineMessageClick()
                }

                mIvSetting, mScrollIvSetting -> {
                    clickSetting()
                    traceOnMineSettingClick()
                }

                mIvKefu, mScrollIvKefu -> {
                    clickKefu()
                    traceOnMineKefuClick()
                }

                mIvDressUp, mScrollIvDressUp -> {
                    clickDressUp()
                    // 我的-装扮中心入口  点击事件
                    XMTraceApi.Trace()
                        .click(59089) // 用户点击时上报
                        .put("currPage", "mySpace9.0")
                        .put(
                            XmRequestIdManager.XM_REQUEST_ID,
                            XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_MINE)
                        )
                        .createTrace()
                }

                mIvOpenListen, mIvScrollOpenListen -> {
                    // 畅听入口
                    traceClickListenerEntry()
                    if (mTvListenerMake?.visibility == View.VISIBLE || mTvScrollListenerMake?.visibility == View.VISIBLE) {
                        MMKVUtil.getInstance().saveBoolean("key_has_show_listener_tag", true)
                        ViewStatusUtil.setVisible(View.GONE, mTvListenerMake, mTvScrollListenerMake)
                    }
                    if (FreeListenConfigManager.getInstance().listenConfig != null &&
                            !TextUtils.isEmpty(FreeListenConfigManager.getInstance().listenConfig.offLineTip)) {
                        CustomToast.showFailToast(FreeListenConfigManager.getInstance().listenConfig.offLineTip)
                        return@onSingleClick
                    }
                    if (FreeListenConfigManager.getInstance().isNeedDirectToVideo) {
                        // 直接看激励视频
                        AdUnLockTimeManagerV2.getInstance().unlockTrack(FreeListenConfigManager.getInstance().currentRewardTime, "myAsset2", object : AdUnLockTimeManagerV2.IAdUnLockStatusCallBack {
                            override fun onRewardSuccess(currentAd: AdUnLockVipTrackAdvertis?, rewardTime: Int) {
                                FreeListenConfigManager.getInstance().increaseTimeIndex() // 进度条+1

                                var listenConfig: FreeListenConfigManager.ListenConfig? = FreeListenConfigManager.getInstance().listenConfig
                                if (listenConfig != null && (FreeListenTimeManager.getListenTime(context) < listenConfig.playDurationLimit) && RewardAgainAdManager.isNeedRewardAgain()) {
                                    // 支持再看一个
                                    if (RewardAgainAdManager.getNextRewardType() == RewardAgainAdManager.NEXT_REWARD_INSPIRE && RewardAgainAdManager.getCurrentInspireAd() != null) {
                                        // 展示唤端弹窗
                                        RewardAgainAdManager.showInspireDialog(rewardTime * 60, RewardAgainAdManager.getNextRewardTime(), RewardAgainAdManager.getCurrentInspireAd(), "myAsset2", true)
                                    } else {
                                        // 展示激励视频弹窗
                                        RewardAgainAdManager.showWatchVideoDialog(rewardTime * 60, RewardAgainAdManager.getNextRewardTime(), "myAsset2")
                                    }
                                } else {
                                    if (RewardAgainAdManager.isNeedRewardAgain()) {
                                        RewardAgainAdManager.resetInspireAd()
                                    }
                                    CustomToast.showFailToast("恭喜你获得" + rewardTime + "分钟收听时长", Toast.LENGTH_LONG.toLong())
                                    ToolUtil.clickUrlAction(mMineFragment.activity as MainActivity, mListenerUrl, null)
                                }
                            }

                            override fun onRewardFail(code: Int) {
                                ToolUtil.clickUrlAction(mMineFragment.activity as MainActivity, mListenerUrl, null)
                            }
                        })
                    } else {
                        ToolUtil.clickUrlAction(mMineFragment.activity as MainActivity, mListenerUrl, null)
                    }
                }

                mLlRecordLive, mScrollLlRecordLive -> {
                    MineTopUtil.clickRecordLive(mMineFragment.activity as MainActivity)
                }
            }
        }
    }

    init {
        initFixTitleBar()
        initScrollTitlBar()
    }

    private fun initFixTitleBar() {
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            val titleLp = mFixTitleBar.layoutParams
            if (titleLp is ConstraintLayout.LayoutParams) {
                val statusBarHeight = BaseUtil.getStatusBarHeight(mMineFragment.context)
                titleLp.height += statusBarHeight
                mFixTitleBar.setPadding(
                    mFixTitleBar.paddingLeft,
                    mFixTitleBar.paddingTop + statusBarHeight,
                    mFixTitleBar.paddingRight,
                    mFixTitleBar.paddingBottom
                )
                mFixTitleBar.layoutParams = titleLp
            }
        }

        mFixTitleBar.background?.mutate()?.alpha = 0
        ViewStatusUtil.setVisible(
            View.INVISIBLE,
            mIvTitleAvatar,
            mKKIvTitleAvatar,
            mTvTitleNickname
        )

        mIvTitleAvatar.setOnClickListener(mOnClickListener)
        mKKIvTitleAvatar.setOnClickListener(mOnClickListener)
        mTvTitleNickname.setOnClickListener(mOnClickListener)
        mMsgContainer?.setOnClickListener(mOnClickListener)
        mIvSetting.setOnClickListener(mOnClickListener)
        mIvKefu?.setOnClickListener(mOnClickListener)
        mScrollIvSetting.setOnClickListener(mOnClickListener)
        mScrollIvMessage?.setOnClickListener(mOnClickListener)
        mScrollIvKefu?.setOnClickListener(mOnClickListener)
        mIvDressUp?.setOnClickListener(mOnClickListener)
        mScrollIvDressUp?.setOnClickListener(mOnClickListener)
        mIvAnimateAvatar.setOnClickListener(mOnClickListener)
        mKKIvAnimateAvatar.setOnClickListener(mOnClickListener)
        mIvOpenListen?.setOnClickListener(mOnClickListener)
        mIvScrollOpenListen?.setOnClickListener(mOnClickListener)
        mLlRecordLive?.setOnClickListener(mOnClickListener)
        mScrollLlRecordLive?.setOnClickListener(mOnClickListener)
        mIvAnimateAvatar.visible(View.INVISIBLE)
        mKKIvAnimateAvatar.visible(View.INVISIBLE)
        if (MineTopUtil.getCardStyle() == MineTopUtil.CARD_STYLE_VIP_NEW) {
            mLlRecordLive.visible(View.VISIBLE)
            mScrollLlRecordLive.visible(View.VISIBLE)
        }
        if (MyListenAbUtil.hasSlideBar()) {
            mSlideEntry?.visibility = View.VISIBLE
            mSlideEntry?.setOnClickListener {
                MyListenAbUtil.openSlideBar("mySpace9.0")
            }
        }
        if (MineTopUtil.isVipNewStyle()) {
            context?.let {
                if (MineTopUtil.isDarkMode()) {
                    mLlRecordLive?.setBackgroundResource(R.drawable.main_bg_mine_title_bar_icon_vip_new_dark)
                    mTvTitleNickname.setTextColor(Color.parseColor("#bfffffff"))
                } else {
                    mLlRecordLive?.setBackgroundResource(R.drawable.main_bg_mine_title_bar_icon_vip_new)
                    mTvTitleNickname.setTextColor(Color.parseColor("#b3131313"))
                }
            }
        } else if (MineTopUtil.isUseVipInfoCard()) {
            context?.let {
                mTvTitleNickname.setTextColor(
                    ContextCompat.getColor(
                        it,
                        R.color.host_color_titleColor
                    )
                )
            }
        }

        if (MineTopUtil.isUseVipInfoCard()) {
            mIvAnimateAvatar.layoutParams =
                (mIvAnimateAvatar.layoutParams as MarginLayoutParams).apply {
                    width = avatarHeight.dp
                    height = avatarHeight.dp
                    if (MineTopUtil.isVipNewStyle()) {
                        leftMargin = 16f.dp
                        topMargin += 10f.dp
                    } else {
                        leftMargin = 32f.dp
                        topMargin += 16f.dp
                    }
                    mIvAvatarTopMargin = topMargin
                }
            mKKIvAnimateAvatar.layoutParams =
                (mKKIvAnimateAvatar.layoutParams as MarginLayoutParams).apply {
                    width = 72.dp
                    height = 72.dp
                }
            mIvAnimateAvatar.setBorderWidth(0)
        }
        val statusBarHeight = if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            BaseUtil.getStatusBarHeight(context)
        } else {
            0
        }
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            val lp = mIvAnimateAvatar.layoutParams
            if (lp is ConstraintLayout.LayoutParams) {
                lp.topMargin += statusBarHeight
                mIvAvatarTopMargin = lp.topMargin
                mIvAnimateAvatar.layoutParams = lp
            }
        }

        MineTraceManagerV9.addTrace(mIvAnimateAvatar) {
            if (UserInfoMannage.hasLogined()) {
                traceOnAvatarShow(it, mHasKK)
            }
        }

        mRdMessage = RedDotView(mMineFragment.context).apply {
            setTargetView(mIvMessage)
            setImageResource(com.ximalaya.ting.android.host.R.drawable.host_ic_red_dot_with_stroke)
            layoutParams.safeAs<FrameLayout.LayoutParams>()?.also {
                it.topMargin = 6.dp
                it.rightMargin = 6.dp
            }?.let { layoutParams = it }
            visibility = View.INVISIBLE
        }
        mTvScrollRecordLive.setTextIfChanged(MineTopUtil.getRecordLiveBtnText(context))
    }

    private fun initScrollTitlBar() {
    }

    private fun clickAvatar() {
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mMineFragment.context)
            return
        }
        if (ChildProtectManager.isChildProtectOpen(mMineFragment.context)) {
            ChildProtectManager.showFeatureCannotUseToast()
            return
        }

        mMineFragment.startFragment(newAnchorSpaceFragment(UserInfoMannage.getUid()))
    }

    private fun clickMessage() {
        if (ChildProtectManager.isChildProtectOpen(context)) {
            ChildProtectManager.showFeatureCannotUseToast()
            return
        }

        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(context)
            return
        }

        // 触发chat模块下载安装
        Router.getActionByCallback(Configure.BUNDLE_CHAT, object : Router.IBundleInstallCallback {
            override fun onInstallSuccess(bundleModel: BundleModel) {
                if (Configure.chatBundleModel.bundleName == bundleModel.bundleName) {
                    try {
                        Router.getActionRouter<ChatActionRouter>(Configure.BUNDLE_CHAT)?.fragmentAction
                            ?.newFragmentByFid(Configure.ChatFragmentFid.NEWS_CENTER_FRAGMENT)
                            ?.let {
                                mMineFragment.startFragment(it)
                            }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

            override fun onLocalInstallError(t: Throwable, bundleModel: BundleModel) {
            }

            override fun onRemoteInstallError(t: Throwable, bundleModel: BundleModel) {
            }
        }, true, BundleModel.DOWNLOAD_ASK_USER)
    }

    private fun clickSetting() {
        mMineFragment.startFragment(SettingFragment())
    }

    private fun clickDressUp() {
        ToolUtil.clickUrlAction(
            mMineFragment,
            homePageModel?.userDecoratorInfo?.imageBoxUrl ?: "",
            null
        )
    }

    private fun clickKefu() {
        var transformTsUrl = if (mMineFragment.isUserMode.not())
            "https://m.ximalaya.com/cs-bridge-web/page/contact-cs?systemNum=TcHk5Ox_cA577mymhAi3qA&_fix_keyboard=1"
        else
            "https://mobile.ximalaya.com/abtest-jump/jump/urlRedirect/contact_customer_service?_fix_keyboard=1"
        val xmFeedBackFragment = CustomerFeedBackManager.createXmFeedBackFragment(transformTsUrl,
            ConfigureCenter.getInstance().getBool("android", "fix_kefu_full_land", true))
        mMineFragment.startFragment(xmFeedBackFragment)
    }

    fun init() {
    }

    fun updateTitleColor(isDark: Boolean) {
        if (MineTopUtil.isUseVipInfoCard() && MineTopUtil.isVipNewStyle().not()) {
            return
        }
        var iconColor = if (isDark) 0xCCFFFFFF.toInt() else 0xFF333333.toInt()
        if (MineTopUtil.isVipNewStyle()) {
            mTvRecordLive?.setTextColor(if (isDark) 0xb3FFFFFF.toInt() else 0xb32C2C3C.toInt())
            iconColor = if (isDark) 0xa6ffffff.toInt() else 0xb32c2c3c.toInt()
        }
        if (MineTopUtil.isVipNewStyle()) {
            context?.let {
                if (MineTopUtil.isDarkMode()) {
                    mLlRecordLive?.setBackgroundResource(R.drawable.main_bg_mine_title_bar_icon_vip_new_dark)
                    mTvTitleNickname.setTextColor(Color.parseColor("#bfffffff"))
                } else {
                    mLlRecordLive?.setBackgroundResource(R.drawable.main_bg_mine_title_bar_icon_vip_new)
                    mTvTitleNickname.setTextColor(Color.parseColor("#b3131313"))
                }
            }
        }
        PorterDuffColorFilter(iconColor, PorterDuff.Mode.SRC_IN).let {
            mIvMessage?.colorFilter = it
            mScrollIvMessage?.colorFilter = it
            mIvSetting.colorFilter = it
            mSlideEntryIv?.colorFilter = it
            mIvRecordLive?.colorFilter = it
            mScrollIvSetting.colorFilter = it
            mIvKefu?.colorFilter = it
            mScrollIvKefu?.colorFilter = it
            mIvDressUp?.colorFilter = it
            mScrollIvDressUp?.colorFilter = it
            mIvOpenListen?.colorFilter = it
            mIvScrollOpenListen?.colorFilter = it
        }
    }

    fun myResume() {
        updateSlideMsgCount()
        mLlRecordLive?.postDelayed({
            checkShowRecordLiveGuide()
        }, 100)
    }

    fun updateSlideMsgCount() {
        if (MyListenAbUtil.hasSlideBar()) {
            val unReadMessageCount = NoReadManage.getInstance(context).noReadModel.unReadMessageCount
            if (MyListenAbUtil.hasSlideBarWithMessage() && unReadMessageCount > 0) {
                ViewStatusUtil.setVisible(View.VISIBLE, mSlideRedDot)
                if (unReadMessageCount < 10) {
                    mSlideRedDot?.setPadding(4.dp, 0, 4.dp, 1.dp)
                } else {
                    mSlideRedDot?.setPadding(3.dp, 0, 3.dp, 1.dp)
                }
                mSlideRedDot?.text = if (unReadMessageCount > 99) "99+" else unReadMessageCount.toString()
            } else {
                ViewStatusUtil.setVisible(View.INVISIBLE, mSlideRedDot)
            }
            MyListenAbUtil.exploreSlideBar("mySpace9.0")
        }
    }

    fun notifyChildProtectChange() {
        mTvRecordLive.setTextIfChanged(MineTopUtil.getRecordLiveBtnText(context))
        mTvScrollRecordLive.setTextIfChanged(MineTopUtil.getRecordLiveBtnText(context))
    }

    fun showTopSkeletonViewState() {
        ViewStatusUtil.setVisible(
            View.INVISIBLE,
            mIvTitleAvatar,
            mKKIvTitleAvatar,
            mTvTitleNickname
        )
    }

    fun updateMessageView() {
        if ((mMineFragment as MineFragmentV9).hasAddMessageTab()) {
            mMsgContainer.visible(View.GONE)
            return
        }
        mMsgContainer.visible(if (ChildProtectManager.isChildProtectOpen(context)) View.GONE else View.VISIBLE)
        if (ChildProtectManager.isChildProtectOpen(context) || !UserInfoMannage.hasLogined()) {
            mRdMessage.visible(View.GONE)
        }
    }

    fun setRdMessageVisible(visible: Int) {
        mRdMessage.visible(visible)
    }

    fun bindTitleData(model: HomePageModelNew? = null) {
        homePageModel = model
        bindAvatarData(model, model?.mobileLargeLogo, model?.avatarKKUrl)
        bindTitleNickname(model)
        bindDressUp(model)
    }

    private fun bindDressUp(model: HomePageModelNew?) {
        mIvDressUp.visible(
            if (model?.userDecoratorInfo?.imageBoxUrl.isNullOrEmpty()
                    .not()
            ) View.VISIBLE else View.GONE
        )
        mScrollIvDressUp?.visible(
            if (model?.userDecoratorInfo?.imageBoxUrl.isNullOrEmpty()
                    .not()
            ) View.VISIBLE else View.GONE
        )
    }

    private fun bindAvatarData(
        model: HomePageModelNew?,
        url: String? = null,
        kkUrl: String? = null
    ) {
        if (!UserInfoMannage.hasLogined() || url.isNullOrEmpty()) {
            mIvTitleAvatar.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_default_avatar_210)
            mIvAnimateAvatar.setImageResource(com.ximalaya.ting.android.host.R.drawable.host_default_avatar_210)
            mKKIvTitleAvatar.setImageBitmap(null)
            mKKIvAnimateAvatar.setImageBitmap(null)
        } else {
            ImageManager.from(context).displayImageSizeInDp(
                mKKIvTitleAvatar,
                kkUrl,
                -1,
                35,
                35
            )
            ImageManager.from(context).displayImageSizeInDp(
                mKKIvAnimateAvatar,
                kkUrl,
                -1,
                85,
                85
            )
            ImageManager.from(context).displayImageSizeInDp(
                mIvTitleAvatar,
                url,
                com.ximalaya.ting.android.host.R.drawable.host_default_avatar_210,
                24,
                24
            )
            ImageManager.from(context).displayImageSizeInDp(
                mIvAnimateAvatar,
                url,
                com.ximalaya.ting.android.host.R.drawable.host_default_avatar_210,
                avatarHeight,
                avatarHeight
            )
        }
    }

    private fun bindTitleNickname(model: HomePageModelNew?) {
        if (!UserInfoMannage.hasLogined() || model == null) {
            mTvTitleNickname.setTextIfChanged("立即登录")
            mTvTitleNickname.isSelected = false
        } else {
            mTvTitleNickname.setTextIfChanged(model.nickname)
            mTvTitleNickname.isSelected = model.vipInfo?.isVip == true
        }
    }

    fun updateTitleBarBackgroundAlpha(scrollY: Int) {
        if (!mIsFixShow) {
            return
        }
        val fra = realAlpha(scrollY * 1f / mAvatarScrollY)
        mFixTitleBar.background?.mutate()?.alpha = (255 * fra).toInt()
    }

    fun animateAvatar(scrollY: Int) {
        if (!mIsFixShow) {
            return
        }
        val fra = realAlpha(scrollY * 1f / mAvatarScrollY)
        if (fra == 1f) {
            mTvTitleNickname.alpha = realAlpha((scrollY - mAvatarScrollY) / 20.dpFloat)
        }
        when (fra) {
            0f -> {
                ViewStatusUtil.setVisible(
                    View.INVISIBLE,
                    mIvTitleAvatar,
                    mKKIvTitleAvatar,
                    mIvAnimateAvatar,
                    mKKIvAnimateAvatar
                )
                mTvTitleNickname.visible(View.GONE)
                (mMineFragment as MineFragmentV9).changeAvatarVisible(true)
            }

            1f -> {
                mIvTitleAvatar.visible(View.VISIBLE)
                mKKIvTitleAvatar.visible(if (mHasKK) View.VISIBLE else View.INVISIBLE)
                mIvAnimateAvatar.visible(View.INVISIBLE)
                mKKIvAnimateAvatar.visible(View.INVISIBLE)
                mTvTitleNickname.visible(View.VISIBLE)
                (mMineFragment as MineFragmentV9).changeAvatarVisible(false)
            }

            else -> {
                if (!MyListenAbUtil.hasSlideBar()) {
                    mIvTitleAvatar.visible(View.INVISIBLE)
                    mKKIvTitleAvatar.visible(View.INVISIBLE)
                    mIvAnimateAvatar.visible(View.VISIBLE)
                    mKKIvAnimateAvatar.visible(if (mHasKK) View.VISIBLE else View.INVISIBLE)
                    mTvTitleNickname.visible(View.GONE)
                    (mMineFragment as MineFragmentV9).changeAvatarVisible(false)
                }
            }
        }
        mIvAnimateAvatar.let {
            val scale = (mScale - 1) * fra + 1
            it.scaleX = scale
            it.scaleY = scale
            it.translationX = -mAvatarScrollX / mAvatarScrollY * scrollY
            it.translationY = -scrollY.toFloat()
            Logger.d(
                "animateAvatar",
                "fra:$fra mScale=$mScale scale=$scale scrollY=$scrollY mAvatarScrollY=$mAvatarScrollY translationX=${it.translationX} translationY=${it.translationY}"
            )
        }
        mKKIvAnimateAvatar.let {
            val scale = (mScale - 1) * fra + 1
            it.scaleX = scale
            it.scaleY = scale
            it.translationX = -mAvatarScrollX / mAvatarScrollY * scrollY
            it.translationY = -scrollY.toFloat()
        }
    }

    private fun realAlpha(alpha: Float): Float {
        return when {
            alpha < 0 -> 0f
            alpha > 1 -> 1f
            else -> alpha
        }
    }

    fun setHasKK(hasKK: Boolean) {
        if (mHasKK == hasKK) {
            if (MineTopUtil.isVipNewStyle()) {
                mAvatarScrollX = (avatarHeight.dpFloat - 28.dpFloat) / 2
                mAvatarScrollY = avatarHeight.dpFloat / 2 + avatarGapDistance.dpFloat + 28.dpFloat / 2
                mAvatarScrollY = mAvatarScrollY - 5.dp
                mAvatarScrollX = if (hasKK) mAvatarScrollX - 3.dp else mAvatarScrollX - 3.dp
                mIvAnimateAvatar.layoutParams =
                    (mIvAnimateAvatar.layoutParams as MarginLayoutParams).apply {
                        topMargin = mIvAvatarTopMargin + 2.dp
                    }
            }
            return
        }
        this.mHasKK = hasKK
        if (!mHasKK) {
            mKKIvTitleAvatar.setImageBitmap(null)
            mKKIvAnimateAvatar.setImageBitmap(null)
        }
        (mMineFragment as MineFragmentV9).setHasKK(hasKK)
        if (MineTopUtil.isUseVipInfoCard()) {
            mAvatarScrollX = (avatarHeight.dpFloat - 28.dpFloat) / 2
            mAvatarScrollY = avatarHeight.dpFloat / 2 + avatarGapDistance.dpFloat + 28.dpFloat / 2
            if (MineTopUtil.isVipNewStyle()) {
                mAvatarScrollY = mAvatarScrollY - 5.dp
                mAvatarScrollX = if (hasKK) mAvatarScrollX - 3.dp else mAvatarScrollX - 3.dp
                mIvAnimateAvatar.layoutParams =
                    (mIvAnimateAvatar.layoutParams as MarginLayoutParams).apply {
                        topMargin = mIvAvatarTopMargin + 2.dp
                    }
            } else {
                mIvAnimateAvatar.layoutParams =
                    (mIvAnimateAvatar.layoutParams as MarginLayoutParams).apply {
                        topMargin = if (hasKK) topMargin + 7.dp else topMargin - 7.dp
                    }
                mAvatarScrollY = if (hasKK) mAvatarScrollY + 7.dp else mAvatarScrollY - 7.dp
                mAvatarScrollX = if (hasKK) mAvatarScrollX + 9f.dp else mAvatarScrollX + 16f.dp
            }
        } else {
            val params = mIvAnimateAvatar.layoutParams as? MarginLayoutParams
            params?.let {
                it.topMargin = if (hasKK) it.topMargin + 17.dp else it.topMargin - 17.dp
                mIvAnimateAvatar.layoutParams = it
            }
            mIvAnimateAvatar.setBorderWidth(if (mHasKK) 0 else 2.dp)
            mIvTitleAvatar.setBorderWidth(if (mHasKK) 0 else 1.dp)
            mAvatarScrollY = if (hasKK) mAvatarScrollY + 17.dp else mAvatarScrollY - 17.dp
        }
    }

    fun switchMode(isFix: Boolean) {
        mIsFixShow = isFix
        if (mIsFixShow) {
            mFixTitleBar.visibility = View.VISIBLE
            mTitleBar.visibility = View.GONE
        } else {
            mTitleBar.visibility = View.VISIBLE
            mFixTitleBar.visibility = View.GONE
            (mMineFragment as MineFragmentV9).changeAvatarVisible(true)
        }
        mRdMessage?.setTargetView(if (isFix) mIvMessage else mScrollIvMessage)
    }

    fun getStickTopPadding(): Int {
        return BarUtils.getStatusBarHeight() + if (mIsFixShow) 50f.dp else 0
    }

    /**
     * 更新我的页畅听入口数据
     */
    fun updateFreeListenerInfo(freeListenInfo: FreeListenV2?) {
        val isVip = UserInfoMannage.isVipUser()
        if (isVip) {
            ViewStatusUtil.setVisible(
                View.GONE,
                mIvScrollOpenListen,
                mIvOpenListen,
                mTvListenerMake,
                mTvScrollListenerMake
            )
            return
        }
        freeListenInfo?.let {
            mListenerUrl = it.url
            mIvScrollOpenListen?.visibility = if (it.canFreeListen) View.VISIBLE else View.GONE
            mIvOpenListen?.visibility = if (it.canFreeListen) View.VISIBLE else View.GONE
            val hasShowMark = MMKVUtil.getInstance().getBoolean("key_has_show_listener_tag", false)
            mTvListenerMake?.visibility =
                if (it.text.isNullOrEmpty() || hasShowMark || !it.canFreeListen) View.GONE else View.VISIBLE
            mTvScrollListenerMake?.visibility =
                if (it.text.isNullOrEmpty() || hasShowMark || !it.canFreeListen) View.GONE else View.VISIBLE
            mTipsText = it.text
            mTvScrollListenerMake?.text = it.text
            mTvListenerMake?.text = it.text
            ImageManager.from(context)
                .displayImage(mIvOpenListen, it.icon, R.drawable.main_mine_open_listen_entry_icon)
            ImageManager.from(context)
                .displayImage(
                    mIvScrollOpenListen,
                    it.icon,
                    R.drawable.main_mine_open_listen_entry_icon
                )
            traceShowListenerEntry()
        } ?: run {
            ViewStatusUtil.setVisible(
                View.GONE,
                mIvScrollOpenListen,
                mIvOpenListen,
                mTvListenerMake,
                mTvScrollListenerMake
            )
        }
    }

    private fun traceClickListenerEntry() {
        if (mIvScrollOpenListen?.visibility == View.VISIBLE || mIvOpenListen?.visibility == View.VISIBLE) {
            // 我的-顶部畅听模式入口  点击事件
            var tipsText = ""
            if (mTvScrollListenerMake?.visibility == View.VISIBLE || mTvListenerMake?.visibility == View.VISIBLE) {
                tipsText = mTipsText ?: ""
            }
            Logger.d("fsy", "traceClickListenerEntry tipsText $tipsText")
            XMTraceApi.Trace()
                .click(59016) // 用户点击时上报
                .put("currPage", "mySpace9.0")
                .put("tipsText", tipsText)
                .createTrace()
        }
    }

    private fun traceShowListenerEntry() {
        if (mIvScrollOpenListen?.visibility == View.VISIBLE || mIvOpenListen?.visibility == View.VISIBLE) {
            var tipsText = ""
            if (mTvScrollListenerMake?.visibility == View.VISIBLE || mTvListenerMake?.visibility == View.VISIBLE) {
                tipsText = mTipsText ?: ""
            }
            // 我的-顶部畅听模式入口  控件曝光
            Logger.d("fsy", "traceShowListenerEntry tipsText $tipsText")
            XMTraceApi.Trace()
                .setMetaId(59017)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "mySpace9.0")
                .put("tipsText", tipsText)
                .put(XmRequestIdManager.CONT_ID, "免费听")
                .put(XmRequestIdManager.CONT_TYPE, "user")
                .put(
                    XmRequestIdManager.XM_REQUEST_ID,
                    XmRequestPage.getPageUniqueRequestId(XmRequestPage.PAGE_MINE)
                )
                .createTrace()
        }
    }

    private fun checkShowRecordLiveGuide() {
        if (!com.ximalaya.ting.android.host.util.view.ViewStatusUtil.viewIsRealShowing(
                mLlRecordLive
            )
        ) {
            return
        }
        if (MineTopUtil.isVipNewStyle()) {
            return
        }
        if (MMKVUtil.getInstance()
                .getBoolean(PreferenceConstantsInHost.KEY_MINE_TITLE_RECORD_LIVE_GUIDE_SHOW, false)
        ) return
        if (!mMineFragment.isRealVisable || !mMineFragment.canUpdateUi() || (mMineFragment.activity as? MainActivity)?.currentTopFragment !is MineFragmentV9) {
            return
        }
        if (ChildProtectManager.isChildMode(BaseApplication.getMyApplicationContext())) {
            return
        }
        val activity = BaseApplication.getTopActivity()
        if (activity == null || activity !is MainActivity) {
            return
        }
        if (ViewUtil.haveDialogIsShowing(activity)) {
            return
        }
        showRecordLiveBtnGuide(activity)
    }

    private fun showRecordLiveBtnGuide(activity: MainActivity) {
        mLlRecordLive ?: return
        val sv = ShadowView(activity)
        val popView = View.inflate(activity, R.layout.main_popup_mine_title_record_live, null)
        val popWindow = CustomPopWindow.PopupWindowBuilder(activity)
            .enableOutsideTouchableDissmiss(true)
            .setView(popView)
            .setOnDissmissListener {
                ViewUtil.setHasDialogShow(
                    false
                )
                (activity.window?.decorView as? ViewGroup)?.removeView(sv)
            }
            .create()
        sv.setMode(ShadowView.MODE_FLAT)
        val location = IntArray(2)
        mLlRecordLive!!.getLocationOnScreen(location)
        val focus: ShadowView.Focus = sv.Focus(
            ShadowView.Focus.SHAPE_ROUND_RECT,
            location[0] + mLlRecordLive!!.measuredWidth / 2f,
            location[1] + mLlRecordLive!!.measuredHeight / 2f,
            mLlRecordLive!!.measuredWidth,
            mLlRecordLive!!.measuredHeight
        )
        focus.radius = BaseUtil.dp2px(activity, mLlRecordLive!!.measuredWidth / 2f)
        sv.addFocus(focus)
        val view = activity.window?.decorView ?: return
        (view as ViewGroup).addView(sv)
        val llContent = popView.findViewById<View>(R.id.listen_ll_content)
        popWindow.showAsDropDown(
            mLlRecordLive,
            -(llContent.measuredWidth - mLlRecordLive!!.measuredWidth) / 2,
            0,
            Gravity.START
        )
        ViewUtil.setHasDialogShow(true)
        MMKVUtil.getInstance()
            .saveBoolean(PreferenceConstantsInHost.KEY_MINE_TITLE_RECORD_LIVE_GUIDE_SHOW, true)
    }
}