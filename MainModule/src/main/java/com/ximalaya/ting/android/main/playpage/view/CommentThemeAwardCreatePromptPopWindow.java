package com.ximalaya.ting.android.main.playpage.view;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;

import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.main.R;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2020/3/18.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
public class CommentThemeAwardCreatePromptPopWindow extends PopupWindow {

    private final Context mContext;

    public CommentThemeAwardCreatePromptPopWindow(Context context) {
        super(context);
        mContext = context;
        initUi();
    }

    private void initUi() {
        setContentView(LayoutInflater.from(mContext)
            .inflate(R.layout.main_layout_comment_theme_award_create_prompt, null));
        setOutsideTouchable(true);
        // 6.0以下，没有背景时，点击PopupWindow外面区域无法让PopupWindow消失。
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            setBackgroundDrawable(null);
        } else {
            setBackgroundDrawable(new ColorDrawable(0x01000000));
        }
        setFocusable(true);
        setWidth(ViewGroup.LayoutParams.WRAP_CONTENT);
        setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    @Override
    public void showAtLocation(View parent, int gravity, int x, int y) {
        if (PadAdaptUtil.isPad(parent.getContext())) {
            return;
        }
        super.showAtLocation(parent, gravity, x, y);
    }
}
