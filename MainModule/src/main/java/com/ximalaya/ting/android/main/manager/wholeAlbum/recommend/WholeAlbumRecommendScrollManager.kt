package com.ximalaya.ting.android.main.manager.wholeAlbum.recommend

import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.main.albumModule.album.wholeAlbum.WholeAlbumRecommendFragment
import com.ximalaya.ting.android.main.manager.wholeAlbum.IWholeAlbumFragmentManager
import com.ximalaya.ting.android.main.manager.wholeAlbum.IWholeAlbumFragmentPresenter
import java.lang.ref.WeakReference

/**
 * Created by 5Greatest on 2021.09.16
 *
 * <AUTHOR>
 *   On 2021/9/16
 */
class WholeAlbumRecommendScrollManager(val mPresenter: WholeAlbumRecommendPresenter, fragment: WholeAlbumRecommendFragment) :
    IWholeAlbumFragmentManager<WholeAlbumRecommendFragment> {

    private val mFragmentReference: WeakReference<WholeAlbumRecommendFragment> = WeakReference(fragment)
    private val scrollListener: RecyclerView.OnScrollListener by lazy {
        ScrollListener(mPresenter)
    }

    fun getOnScrollListener(): RecyclerView.OnScrollListener {
        return scrollListener
    }

    override fun getPresenter(): IWholeAlbumFragmentPresenter<WholeAlbumRecommendFragment> {
        return mPresenter
    }

    override fun doOnDestroy() {
        // do Nothing
    }

    override fun getFragment(): WholeAlbumRecommendFragment? {
        mFragmentReference.get() ?: return null
        return if (mFragmentReference.get()!!.canUpdateUi()) {
            mFragmentReference.get()
        } else {
            null
        }
    }

    private class ScrollListener(val mPresenter: WholeAlbumRecommendPresenter) : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            if (RecyclerView.SCROLL_STATE_IDLE == newState) {
                mPresenter.getFragment()?.getAlbumManager()?.markPointForAllAttachedHolders()
            }
        }
    }
}