package com.ximalaya.ting.android.main.manager.myspace.footPrintV2

import com.ximalaya.ting.android.host.manager.history.AlbumFootPrintUtil
import com.ximalaya.ting.android.host.manager.history.MyFootPrintAlbum
import com.ximalaya.ting.android.host.manager.history.MyFootPrintModel
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.fragment.myspace.child.MyFootPrintFragmentV2
import com.ximalaya.ting.android.main.manager.myspace.footPrint.IMyFootPrintOperate
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import java.lang.ref.WeakReference

/**
 * Created by 5Greatest on 2021.02.04
 *
 * <AUTHOR>
 *   On 2021/2/4
 */
class MyFootPrintDataProcessManagerV2(private val mPresenter: MyFootPrintPresenterV2, fragment: MyFootPrintFragmentV2) : IMyFootPrintManagerV2,
    IMyFootPrintOperate {
    private val mFragmentReference: WeakReference<MyFootPrintFragmentV2> = WeakReference(fragment)
    private var adapter: MyFootPrintAlbumAdapterV2 = MyFootPrintAlbumAdapterV2(
        mPresenter.orderedAlbums
    )

    init {
        adapter.setPresenter(mPresenter)
        adapter.setUpdateUiListener(object : MyFootPrintAlbumAdapterV2.UpdateUiListener {
            override fun updateUi() {
                getFragment()?.updateUi(MyFootPrintFragmentV2.MSG_UPDATE_UI_ON_CHANGE_DELETE_AMOUNT)
            }
        })
    }

    fun getAdapter(): MyFootPrintAlbumAdapterV2 {
        return adapter
    }

    override fun operateLoadData() {
        clearData()
        adapter.notifyDataSetChanged()
        val fragment: MyFootPrintFragmentV2? = getFragment()
        fragment?.let {
            if (mPresenter.isHasLogin) {
                it.afterLoginManager.operateLoadData()
            } else {
                it.beforeLoginManager.operateLoadData()
            }
        }
    }

    override fun operateDelete() {
        val fragment: MyFootPrintFragmentV2? = getFragment()
        fragment?.let {
            if (mPresenter.isHasLogin) {
                it.afterLoginManager.operateDelete()
            } else {
                it.beforeLoginManager.operateDelete()
            }
        }
    }

    /**
     * 执行删除成功后：
     *  未登录时不需要进行任何操作
     *  登录时重新请求记录
     * */
    override fun operateAfterDelete() {
        val fragment: MyFootPrintFragmentV2? = getFragment()
        fragment?.let {
            if (mPresenter.isHasLogin) {
                it.afterLoginManager.operateAfterDelete()
            } else {
                it.beforeLoginManager.operateAfterDelete()
            }
        }
    }

    override fun operateDeleteSingle(album: MyFootPrintAlbum) {
        val fragment: MyFootPrintFragmentV2? = getFragment()
        fragment?.let {
            if (mPresenter.isHasLogin) {
                it.afterLoginManager.operateDeleteSingle(album)
            } else {
                it.beforeLoginManager.operateDeleteSingle(album)
            }
        }
    }

    /**
     * 执行加载更多：
     *  未登录时不需要进行任何操作
     *  登录时请求后续页的记录
     * */
    fun operateLoadMore() {
        if (mPresenter.isHasLogin) {
            mPresenter.requestMoreData(false)
        }
    }

    fun prepareData(albums: List<MyFootPrintAlbum>?): Boolean {
        if (ToolUtil.isEmptyCollects(albums)) {
            return false
        }
        albums?.let { realAlbums ->
            for (album in realAlbums) {
                mPresenter.albumIdMap[album.itemId] = album
            }
            return prepareOrderListAndTitleIndexes()
        }
        return false
    }

    /**
     * 更新足迹专辑记录
     *
     * @param model          新数据
     * @param clearOldRecord 是否需要清除之前的记录
     * @return 是否发生了变化
     */
    fun prepareData(model: MyFootPrintModel?, clearOldRecord: Boolean): Boolean {
        if (null == model || ToolUtil.isEmptyCollects(model.albumInfoMobileResultList)) {
            if (clearOldRecord) {
                mPresenter.albumIdMap.clear()
                mPresenter.orderedAlbums.clear()
            }
            return false
        }
        var changed = clearOldRecord
        if (clearOldRecord) {
            mPresenter.albumIdMap.clear()
        }
        val xmRequestId = XmRequestIdManager.getInstance(ToolUtil.getCtx()).requestId
        for (album in model.albumInfoMobileResultList) {
            if (null == album) {
                continue
            }
            album.xmRequestId = xmRequestId
            mPresenter.albumIdMap[album.itemId] = album
        }
        if (prepareOrderListAndTitleIndexes()) {
            changed = true
        }
        return changed
    }

    fun prepareOrderListAndTitleIndexes(): Boolean {
        if (mPresenter.albumIdMap.isEmpty()) {
            return false
        }
        mPresenter.orderedAlbums.clear()
        mPresenter.orderedAlbums.addAll(mPresenter.albumIdMap.values)
        mPresenter.orderedAlbums = AlbumFootPrintUtil.reOrderAlbumList(mPresenter.orderedAlbums)
        val todayId = AlbumFootPrintUtil.findTodayAlbumIdV2(mPresenter.orderedAlbums)
        var changed = false
        if (todayId != mPresenter.todayFirstId) {
            mPresenter.todayFirstId = todayId
            changed = true
        }
        val yesterdayId = AlbumFootPrintUtil.findYesterdayAlbumIdV2(mPresenter.orderedAlbums)
        if (yesterdayId != mPresenter.yesterdayFirstId) {
            mPresenter.yesterdayFirstId = yesterdayId
            changed = true
        }
        val ancientId = AlbumFootPrintUtil.findAncientAlbumIdV2(mPresenter.orderedAlbums)
        if (ancientId != mPresenter.ancientFirstId) {
            mPresenter.ancientFirstId = ancientId
            changed = true
        }
        return changed
    }

    /**
     * 清除页面已有的所有数据
     * */
    fun clearData() {
        mPresenter.albumIdMap.clear()
        mPresenter.orderedAlbums.clear()
    }

    /**
     * 判断是否是没有要显示的记录
     * */
    fun isNoContent(): Boolean {
        return ToolUtil.isEmptyCollects(mPresenter.orderedAlbums)
    }

    override fun doOnDestroyFragment() {
    }

    override fun getFragment(): MyFootPrintFragmentV2? {
        return if (null == mFragmentReference.get()
                || !mFragmentReference.get()!!.canUpdateUi()) {
            null
        } else mFragmentReference.get()
    }
}