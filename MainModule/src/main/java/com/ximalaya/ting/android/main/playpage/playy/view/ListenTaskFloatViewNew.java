package com.ximalaya.ting.android.main.playpage.playy.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.airbnb.lottie.LottieDrawable;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.host.manager.play.ListenTimeManager;
import com.ximalaya.ting.android.host.model.listen.ListenTaskInfo;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.main.playpage.internalservice.IAdCoverHideService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IListenTimeListener;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * 收听时长任务悬浮组件（新版本）
 * 64dp*64dp 固定尺寸，支持两种状态切换
 */
public class ListenTaskFloatViewNew extends FrameLayout implements IListenTimeListener, IAdCoverHideService.IAdCoverStateChangeListener {

    private static final String TAG = "ListenTaskFloatViewNew";

    // 状态常量
    private static final int STATE_INIT = 0;    // 提示领取状态
    private static final int STATE_PROGRESS = 1; // 任务执行中状态
    private static final int STATE_CLAIM = 2;    // 提示领取状态

    // 动画时长
    private static final long EDGE_SNAP_DURATION = 300; // 吸边动画时长
    private static final long PROGRESS_BAR_ANIMATION_DURATION = 800; // 进度条位移动画时长
    private static final long TEXT_SCROLL_ANIMATION_DURATION = 400; // 文字滚动动画时长
    private static final long TEXT_SCROLL_ANIMATION_INTERVAL = 1500; // 文字滚动动画间隔

    // 拖拽相关常量
    private static final int TOUCH_SLOP = 5; // 触摸滑动阈值
    private static final int EDGE_MARGIN = 0; // 吸边时的边距（dp）

    // 进度条位置常量 (dp)
    private static final float PROGRESS_MARGIN_BOTTOM_PROGRESS = 22.5f; // 任务执行中状态进度条距底部22.5dp
    private static final float PROGRESS_MARGIN_BOTTOM_CLAIM = 15.5f; // 提示领取状态进度条距底部15.5dp

    // 定时检查间隔（毫秒）
    private int tipsInterval = 60 * 1000; // 定期提醒领金币的时间间隔，单位秒，默认60s

    // 轮播动画间隔（毫秒）
    private static final long CLAIM_CAROUSEL_INTERVAL = 1000; // 1秒轮播间隔

    // UI组件
    private TextView mCoinsText;
    private TextView mClaimText;
    private ProgressBar mSharedProgressBar;
    private View mClaimContentLayout;
    private View mCoinsContainer;
    private View mClaimTextContainer;
    private XmLottieAnimationView mLottieBackground;
    private ImageView mLottieBackImage;
    // 数据
    private ListenTaskInfo mTaskInfo;
    private int mTotalListenTime = 0; // 总收听时长（秒）
    private int mCurrentState = STATE_INIT; // 当前状态

    private OnFloatViewClickListener mClickListener;

    private ListenTaskInfo.StepInfo lastNewestClaimableStep;

    // 拖拽相关变量
    private float mDownX,mDownY; // 按下时的坐标
    private float mLastX, mLastY; // 上次移动的坐标
    private boolean mIsDragging = false; // 是否正在拖拽
    private boolean mIsMoving = false; // 是否已经开始移动
    private ObjectAnimator mSnapAnimator; // 吸边动画

    // 滑动冲突处理
    private OnDragStateChangeListener mDragStateChangeListener;

    // 定时检查相关
    private Handler mCheckHandler;
    private Runnable mCheckClaimableRunnable;

    // 循环滚动动画相关
    private Handler mScrollAnimationHandler;
    private Runnable mScrollAnimationRunnable;
    private boolean mIsScrollAnimationRunning = false;

    private boolean isAdCoverShowing = false;

    public interface OnFloatViewClickListener {
        void onFloatViewClick();
    }

    public interface OnDragStateChangeListener {
        void onDragStart();

        void onDragEnd();
    }

    public ListenTaskFloatViewNew(@NonNull Context context) {
        super(context);
        init();
    }

    public ListenTaskFloatViewNew(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ListenTaskFloatViewNew(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(R.layout.host_view_listen_task_float_new, this, true);

        mSharedProgressBar = findViewById(R.id.progress_bar_shared);
        mClaimContentLayout = findViewById(R.id.layout_claim_content);
        mCoinsContainer = findViewById(R.id.container_coins);
        mClaimTextContainer = findViewById(R.id.container_claim_text);
        mCoinsText = findViewById(R.id.tv_coins);
        mClaimText = findViewById(R.id.tv_claim_text);
        mLottieBackground = findViewById(R.id.lottie_background);
        mLottieBackImage = findViewById(R.id.iv_red_packet);

        // 移除原来的点击监听器，改用触摸事件处理
        // setOnClickListener 会被 onTouchEvent 覆盖

        // 获取当前总收听时长
        mTotalListenTime = ListenTimeManager.getTotalListenTime(getContext());
        // 注册收听时长监听
        ListenTimeManager.addListenTimeListener(getContext(), this);

        // 初始化Lottie动画
        initLottieAnimation();

        // 初始化定时检查
        initCheckTimer();

        // 初始化循环滚动动画
        initScrollAnimation();

        IAdCoverHideService adCoverHideService =
                PlayPageInternalServiceManager.getInstance().getService(IAdCoverHideService.class);
        if (adCoverHideService != null) {
            adCoverHideService.registerAdCoverStateChange(this);
        }
    }

    /**
     * 初始化Lottie动画
     */
    private void initLottieAnimation() {
        if (mLottieBackground != null) {
            // 默认播放任务执行中状态的动画
            playProgressLottieAnimation();
        }
    }

    /**
     * 初始化定时检查
     */
    private void initCheckTimer() {
        mCheckHandler = new Handler(Looper.getMainLooper());
        mCheckClaimableRunnable = new Runnable() {
            @Override
            public void run() {
                checkClaimableTask();
            }
        };
    }

    /**
     * 初始化循环滚动动画
     */
    private void initScrollAnimation() {
        mScrollAnimationHandler = new Handler(Looper.getMainLooper());
        mScrollAnimationRunnable = new Runnable() {
            @Override
            public void run() {
                if (mIsScrollAnimationRunning) {
                    performScrollAnimation();
                    // 1秒后执行下一次滚动
                    mScrollAnimationHandler.postDelayed(this, CLAIM_CAROUSEL_INTERVAL);
                }
            }
        };
    }

    /**
     * 设置任务信息
     */
    public void setTaskInfo(ListenTaskInfo taskInfo) {
        mTaskInfo = taskInfo;
        updateUI(false);
        if (taskInfo != null && taskInfo.tipsInterval > 0) {
            tipsInterval = taskInfo.tipsInterval * 1000;
        } else {
            tipsInterval = 60 * 1000;
        }
        if (mCurrentState == STATE_PROGRESS) {
            startCheckTimer();
        }
    }

    /**
     * 设置点击监听器
     */
    public void setOnFloatViewClickListener(OnFloatViewClickListener listener) {
        mClickListener = listener;
    }

    /**
     * 设置拖拽状态变化监听器
     */
    public void setOnDragStateChangeListener(OnDragStateChangeListener listener) {
        mDragStateChangeListener = listener;
    }

    @Override
    public void onListenTimeChange(int totalListenTime) {
        Logger.d(TAG, "onListenTimeChange: totalTime=" + totalListenTime);
        mTotalListenTime = totalListenTime;
        updateUI(true);
    }

    /**
     * 更新UI显示
     */
    private void updateUI(boolean needCheckVisible) {
        if (mTaskInfo == null) {
            return;
        }
        if (needCheckVisible && getVisibility() != VISIBLE) {
            return;
        }

        // 检查是否有可领取的奖励
        boolean claimableStepChanged = false;
        ListenTaskInfo.StepInfo firstClaimableStep = mTaskInfo.getClaimableStep(mTotalListenTime);
        ListenTaskInfo.StepInfo newestClaimableStep = mTaskInfo.getNewestClaimableStep(mTotalListenTime);
        if (lastNewestClaimableStep != null && newestClaimableStep != null && newestClaimableStep.index != lastNewestClaimableStep.index) {
            claimableStepChanged = true;
        }
        if (lastNewestClaimableStep == null && newestClaimableStep != null) {
            claimableStepChanged = true;
        }
        lastNewestClaimableStep = newestClaimableStep;

        // 更新领取内容布局的显示状态（与任务状态解耦）
        updateClaimContentLayoutVisibility(firstClaimableStep);
        // 更新进度条状态
        updateProgressState();

        if (mCurrentState == STATE_INIT) {
            // 初始化状态，默认直接进任务执行中状态
            mCurrentState = STATE_PROGRESS;
//            if (mTaskInfo != null && mTaskInfo.isAllStepClaimable(mTotalListenTime)) {
//                switchToClaimState(firstClaimableStep);
//            } else {
//                mCurrentState = STATE_PROGRESS;
//            }
        } else if (firstClaimableStep != null && mCurrentState == STATE_PROGRESS && claimableStepChanged) {
            // 有新的任务完成了，切换到提示领取状态,并且同步播放时长
            XmPlayerManager.getInstance(getContext()).syncTimeToServer(null);
            switchToClaimState(firstClaimableStep);
        } else if (firstClaimableStep == null && mCurrentState == STATE_CLAIM) {
            // 切换回任务执行中状态
            switchToProgressState();
        }
    }

    /**
     * 更新领取内容布局的显示状态（与任务状态解耦）
     */
    private void updateClaimContentLayoutVisibility(ListenTaskInfo.StepInfo claimableStep) {
        if (claimableStep == null) {
            // 无可领取任务，隐藏领取内容布局
            if (mClaimContentLayout.getVisibility() == VISIBLE) {
                animateProgressBarPosition(false);
                // 停止轮播动画
                stopScrollAnimation();
            }
        } else {
            // 更新金币显示
            updateClaimContentData(claimableStep);
            // 有可领取任务，显示领取内容布局并启动轮播
            if (mClaimContentLayout.getVisibility() != VISIBLE) {
                mCoinsContainer.setVisibility(VISIBLE);
                mCoinsContainer.setTranslationY(0);
                mClaimTextContainer.setVisibility(GONE);
                mClaimTextContainer.setTranslationY(0);
                animateProgressBarPosition(true);
            }
        }
    }

    /**
     * 更新领取内容的数据显示
     */
    private void updateClaimContentData(ListenTaskInfo.StepInfo claimableStep) {
        if (claimableStep != null) {
            String coinText = "+" + claimableStep.coins;
            mCoinsText.setText(coinText);
        }
    }

    /**
     * 切换到提示领取状态
     */
    private void switchToClaimState(ListenTaskInfo.StepInfo claimableStep) {
        Logger.d(TAG, "切换到提示领取状态，金币: " + claimableStep.coins);
        mCurrentState = STATE_CLAIM;

        // 停止定时检查
        stopCheckTimer();
        playTransitionAnimation(() -> {
            switchToProgressState();
        });
    }

    /**
     * 切换到任务执行中状态
     */
    private void switchToProgressState() {
        Logger.d(TAG, "切换到任务执行中状态");
        mCurrentState = STATE_PROGRESS;
        playProgressLottieAnimation();
        // 启动定时检查
        startCheckTimer();
    }

    /**
     * 启动定时检查
     */
    private void startCheckTimer() {
        stopCheckTimer();
        if (mCheckHandler != null && mCheckClaimableRunnable != null) {
            mCheckHandler.postDelayed(mCheckClaimableRunnable, tipsInterval);
        }
    }

    /**
     * 停止定时检查
     */
    private void stopCheckTimer() {
        if (mCheckHandler != null && mCheckClaimableRunnable != null) {
            mCheckHandler.removeCallbacks(mCheckClaimableRunnable);
        }
    }

    /**
     * 启动循环滚动动画
     */
    private void startScrollAnimation() {
        stopScrollAnimation();
        mIsScrollAnimationRunning = true;
        if (mScrollAnimationHandler != null && mScrollAnimationRunnable != null) {
            // 延迟开始第一次滚动
            mScrollAnimationHandler.postDelayed(mScrollAnimationRunnable, CLAIM_CAROUSEL_INTERVAL);
        }
    }

    /**
     * 停止循环滚动动画
     */
    private void stopScrollAnimation() {
        mIsScrollAnimationRunning = false;
        if (mScrollAnimationHandler != null && mScrollAnimationRunnable != null) {
            mScrollAnimationHandler.removeCallbacks(mScrollAnimationRunnable);
        }
    }

    /**
     * 执行一次滚动动画（金币和"立即领取"的切换）
     */
    private void performScrollAnimation() {
        if (mCoinsContainer == null || mClaimTextContainer == null) {
            return;
        }

        // 检查当前显示的是金币还是"立即领取"
        boolean isCoinsVisible = mCoinsContainer.getVisibility() == VISIBLE;

        if (isCoinsVisible) {
            // 当前显示金币，切换到"立即领取"
            animateCoinsToClaimTextInternal();
        } else {
            // 当前显示"立即领取"，切换到金币
            animateClaimTextToCoinsInternal();
        }
    }

    /**
     * 更新任务执行中状态的显示
     */
    private void updateProgressState() {
        // 更新共享进度条
        float progress = mTaskInfo.calculateCurrentProgress(mTotalListenTime);
        mSharedProgressBar.setProgress((int) progress);

        Logger.d(TAG, "任务执行中状态 - 进度: " + progress + "%");
    }

    /**
     * 检查是否有可领取的任务
     */
    private void checkClaimableTask() {
        if (mTaskInfo == null || mCurrentState != STATE_PROGRESS) {
            return;
        }
        // 检查是否有可领取的任务
        ListenTaskInfo.StepInfo claimableStep = mTaskInfo.getClaimableStep(mTotalListenTime);
        if (claimableStep != null) {
            Logger.d(TAG, "发现可领取任务，自动切换到提示领取状态，金币: " + claimableStep.coins);
            switchToClaimState(claimableStep);
        }
    }

    /**
     * 播放任务执行中状态的循环动画 (step1.json)
     */
    private void playProgressLottieAnimation() {
        Logger.d(TAG, "playProgressLottieAnimation");
        if (mLottieBackground != null) {
            mLottieBackImage.setVisibility(VISIBLE);
            mLottieBackground.setAnimation("lottie/host_listen_task_pendant/step1.json");
            mLottieBackground.setRepeatCount(LottieDrawable.INFINITE);
            mLottieBackground.addAnimatorListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mLottieBackImage.setVisibility(GONE);
                }

                @Override
                public void onAnimationResume(Animator animation) {
                    mLottieBackImage.setVisibility(GONE);
                }
            });
            if (!isAdCoverShowing) {
                mLottieBackground.playAnimation();
            }
        }
    }

    private void playTransitionAnimation(Runnable onAnimationEnd) {
        Logger.d(TAG, "playTransitionToClaimAnimation");
        if (mLottieBackground != null) {
            mLottieBackImage.setVisibility(VISIBLE);
            mLottieBackground.setAnimation("lottie/host_listen_task_pendant/complete.json");
            mLottieBackground.setRepeatCount(0);
            mLottieBackground.addAnimatorListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationStart(Animator animation) {
                    mLottieBackImage.setVisibility(GONE);
                }

                @Override
                public void onAnimationResume(Animator animation) {
                    mLottieBackImage.setVisibility(GONE);
                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    mLottieBackground.removeAnimatorListener(this);
                    if (onAnimationEnd != null) {
                        onAnimationEnd.run();
                    }
                }
            });
            if (!isAdCoverShowing) {
                mLottieBackground.playAnimation();
            }
        } else if (onAnimationEnd != null) {
            onAnimationEnd.run();
        }
    }

    /**
     * 动画进度条位置
     *
     * @param showCoin true表示切换到领取状态位置，false表示切换到执行状态位置
     */
    private void animateProgressBarPosition(boolean showCoin) {
        if (mSharedProgressBar == null) {
            return;
        }

        ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) mSharedProgressBar.getLayoutParams();
        int currentBottomMargin = params.bottomMargin;
        int targetBottomMargin = BaseUtil.dp2px(getContext(), showCoin ? PROGRESS_MARGIN_BOTTOM_CLAIM : PROGRESS_MARGIN_BOTTOM_PROGRESS);

        if (currentBottomMargin == targetBottomMargin) {
            return;
        }

        // 进度条位置动画
        ValueAnimator progressAnimator = ValueAnimator.ofInt(currentBottomMargin, targetBottomMargin);
        progressAnimator.setDuration(PROGRESS_BAR_ANIMATION_DURATION);
        progressAnimator.addUpdateListener(animation -> {
            int animatedValue = (int) animation.getAnimatedValue();
            params.bottomMargin = animatedValue;
            mSharedProgressBar.setLayoutParams(params);
        });

        // mClaimContentLayout的透明度动画
        ObjectAnimator alphaAnimator = null;
        if (showCoin) {
            // 切换到领取状态：渐现动画
            mClaimContentLayout.setAlpha(0f);
            mClaimContentLayout.setVisibility(VISIBLE);
            alphaAnimator = ObjectAnimator.ofFloat(mClaimContentLayout, "alpha", 0f, 1f);
            alphaAnimator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    // 启动轮播动画
                    startScrollAnimation();
                }
            });
        } else {
            // 切换到执行状态：渐隐动画
            alphaAnimator = ObjectAnimator.ofFloat(mClaimContentLayout, "alpha", 1f, 0f);
            alphaAnimator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    mClaimContentLayout.setVisibility(GONE);
                }
            });
        }
        alphaAnimator.setDuration(PROGRESS_BAR_ANIMATION_DURATION);

        // 组合动画
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(progressAnimator, alphaAnimator);
        animatorSet.start();
    }

    /**
     * 金币向上滑出，"立即领取"向上滑入（内部循环动画使用）
     */
    private void animateCoinsToClaimTextInternal() {
        if (mCoinsContainer == null || mClaimTextContainer == null) {
            return;
        }
        int containerHeight = BaseUtil.sp2px(getContext(), 8);

        // 金币向上滑出动画
        ObjectAnimator coinsSlideOut = ObjectAnimator.ofFloat(mCoinsContainer, "translationY", 0, -containerHeight);
        coinsSlideOut.setDuration(TEXT_SCROLL_ANIMATION_DURATION);

        // "立即领"从下方滑入动画
        mClaimTextContainer.setVisibility(VISIBLE);
        mClaimTextContainer.setTranslationY(containerHeight);
        ObjectAnimator claimSlideIn = ObjectAnimator.ofFloat(mClaimTextContainer, "translationY", containerHeight, 0);
        claimSlideIn.setDuration(TEXT_SCROLL_ANIMATION_DURATION);

        // 组合动画
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(coinsSlideOut, claimSlideIn);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 隐藏金币容器，重置位置
                mCoinsContainer.setVisibility(GONE);
                mCoinsContainer.setTranslationY(0);
            }
        });

        animatorSet.start();
    }

    /**
     * "立即领取"向上滑出，金币向上滑入（内部循环动画使用）
     */
    private void animateClaimTextToCoinsInternal() {
        if (mCoinsContainer == null || mClaimTextContainer == null) {
            return;
        }
        int containerHeight = BaseUtil.sp2px(getContext(), 8);

        // "立即领取"向上滑出动画
        ObjectAnimator claimSlideOut = ObjectAnimator.ofFloat(mClaimTextContainer, "translationY", 0, -containerHeight);
        claimSlideOut.setDuration(TEXT_SCROLL_ANIMATION_DURATION);

        // 金币从下方滑入动画
        mCoinsContainer.setVisibility(VISIBLE);
        mCoinsContainer.setTranslationY(containerHeight);
        ObjectAnimator coinsSlideIn = ObjectAnimator.ofFloat(mCoinsContainer, "translationY", containerHeight, 0);
        coinsSlideIn.setDuration(TEXT_SCROLL_ANIMATION_DURATION);

        // 组合动画
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(claimSlideOut, coinsSlideIn);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 隐藏"立即领取"容器，重置位置
                mClaimTextContainer.setVisibility(GONE);
                mClaimTextContainer.setTranslationY(0);
            }
        });

        animatorSet.start();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                return handleTouchDown(event);
            case MotionEvent.ACTION_MOVE:
                return handleTouchMove(event);
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                return handleTouchUp(event);
        }
        return super.onTouchEvent(event);
    }

    /**
     * 处理按下事件
     */
    private boolean handleTouchDown(MotionEvent event) {
        mDownX = event.getRawX();
        mDownY = event.getRawY();
        mLastX = mDownX;
        mLastY = mDownY;
        mIsDragging = false;
        mIsMoving = false;

        // 停止可能正在进行的吸边动画
        if (mSnapAnimator != null && mSnapAnimator.isRunning()) {
            mSnapAnimator.cancel();
        }

        return true;
    }

    /**
     * 处理移动事件
     */
    private boolean handleTouchMove(MotionEvent event) {
        float currentX = event.getRawX();
        float currentY = event.getRawY();

        // 计算移动距离
        float deltaX = currentX - mLastX;
        float deltaY = currentY - mLastY;

        // 判断是否开始拖拽
        if (!mIsMoving) {
            float totalDeltaX = Math.abs(currentX - mDownX);
            float totalDeltaY = Math.abs(currentY - mDownY);
            if (totalDeltaX > TOUCH_SLOP || totalDeltaY > TOUCH_SLOP) {
                mIsMoving = true;
                mIsDragging = true;
                // 开始拖拽时，请求父容器不要拦截触摸事件
//                requestParentDisallowInterceptTouchEvent(true);
                // 通知拖拽开始
                if (mDragStateChangeListener != null) {
                    mDragStateChangeListener.onDragStart();
                }
            }
        }

        if (mIsDragging) {
            // 更新位置
            moveFloatView(deltaX, deltaY);
            mLastX = currentX;
            mLastY = currentY;
        }

        return true;
    }

    /**
     * 处理抬起事件
     */
    private boolean handleTouchUp(MotionEvent event) {
        // 恢复父容器的触摸事件拦截
//        requestParentDisallowInterceptTouchEvent(false);

        if (mIsDragging) {
            // 执行吸边动画
            snapToEdge();
            mIsDragging = false;
            mIsMoving = false;
            // 通知拖拽结束
            if (mDragStateChangeListener != null) {
                mDragStateChangeListener.onDragEnd();
            }
        } else {
            // 如果没有拖拽，则当作点击处理
            if (mClickListener != null) {
                mClickListener.onFloatViewClick();
            }
        }
        return true;
    }

    /**
     * 请求父容器是否拦截触摸事件
     */
    private void requestParentDisallowInterceptTouchEvent(boolean disallow) {
        ViewGroup parent = (ViewGroup) getParent();
        if (parent != null) {
            parent.requestDisallowInterceptTouchEvent(disallow);
        }
    }

    /**
     * 移动悬浮窗
     */
    private void moveFloatView(float deltaX, float deltaY) {
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (!(layoutParams instanceof FrameLayout.LayoutParams)) {
            return;
        }

        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) layoutParams;
        ViewGroup parent = (ViewGroup) getParent();
        if (parent == null) {
            return;
        }

        // 计算新位置
        int newX = (int) (getX() + deltaX);
        int newY = (int) (getY() + deltaY);

        // 边界检测
        int parentWidth = parent.getWidth();
        int parentHeight = parent.getHeight();
        int viewWidth = getWidth();
        int viewHeight = getHeight();

        // 限制在父容器范围内
        newX = Math.max(0, Math.min(newX, parentWidth - viewWidth));
        newY = Math.max(0, Math.min(newY, parentHeight - viewHeight));

        // 更新位置
        setX(newX);
        setY(newY);
    }

    /**
     * 吸边动画
     */
    private void snapToEdge() {
        ViewGroup parent = (ViewGroup) getParent();
        if (parent == null) {
            return;
        }

        int parentWidth = parent.getWidth();
        int viewWidth = getWidth();
        int currentX = (int) getX();
        int currentY = (int) getY();

        // 计算应该吸附到左边还是右边
        int centerX = currentX + viewWidth / 2;
        boolean snapToLeft = centerX < parentWidth / 2;

        // 计算目标位置
        int targetX;
        int edgeMarginPx = 0;

        if (snapToLeft) {
            targetX = edgeMarginPx; // 吸附到左边
        } else {
            targetX = parentWidth - viewWidth - edgeMarginPx; // 吸附到右边
        }

        // 执行吸边动画
        if (targetX != currentX) {
            mSnapAnimator = ObjectAnimator.ofFloat(this, "x", currentX, targetX);
            mSnapAnimator.setDuration(EDGE_SNAP_DURATION);
            mSnapAnimator.start();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 停止吸边动画
        if (mSnapAnimator != null && mSnapAnimator.isRunning()) {
            mSnapAnimator.cancel();
        }

        // 停止Lottie动画
        if (mLottieBackground != null) {
            mLottieBackground.cancelAnimation();
        }

        // 停止定时检查
        stopCheckTimer();

        // 停止循环滚动动画
        stopScrollAnimation();

        // 移除监听器，避免内存泄漏
        ListenTimeManager.removeListenTimeListener(getContext(), this);
    }

    @Override
    public void onAdCoverHide() {
        Log.d(TAG, "onAdCoverHide: ");
        if (getVisibility() != View.VISIBLE) {
            return;
        }
        if (!isAdCoverShowing) {
            return;
        }
        isAdCoverShowing = false;
        mLottieBackground.playAnimation();
    }

    @Override
    public void noAdCover() {
        isAdCoverShowing = false;
    }

    @Override
    public void onAdCoverShow() {
        Log.d(TAG, "onAdCoverShow: ");
        if (getVisibility() != View.VISIBLE) {
            return;
        }
        long lastAdShowTime = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getLong(PreferenceConstantsInHost.KEY_LISTEN_TASK_FLOAT_SOUND_PATCH_SHOW_TIME, 0);
        // 仅每日的第一次贴片展示处理动画互斥
        if (lastAdShowTime != 0 && !DateTimeUtil.isAnotherDay(lastAdShowTime)) {
            return;
        }
        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveLong(PreferenceConstantsInHost.KEY_LISTEN_TASK_FLOAT_SOUND_PATCH_SHOW_TIME, System.currentTimeMillis());
        isAdCoverShowing = true;
        mLottieBackground.pauseAnimation();
    }
}
