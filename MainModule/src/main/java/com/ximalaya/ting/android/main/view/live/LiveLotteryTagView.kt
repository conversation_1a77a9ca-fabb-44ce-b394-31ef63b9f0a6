package com.ximalaya.ting.android.main.view.live

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.toColorInt
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.main.R

/**
 * 新首页首页 - 直播卡片 - 抽奖标签自定义 View
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13037236220
 * @wiki https://alidocs.dingtalk.com/i/nodes/mExel2BLV54Xxlk3cY5nOPO3Wgk9rpMq?utm_scene=team_space
 * @server 周乐乐
 * @since 2024/9/13
 */
class LiveLotteryTagView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : FrameLayout(context, attrs, defStyle) {

    private val tagImgView: ImageView = ImageView(context).apply {
        id = generateViewId()
        scaleType = ImageView.ScaleType.FIT_XY
        adjustViewBounds = true
    }

    private val tagTvView: TextView = TextView(context).apply {
        id = generateViewId()
        gravity = Gravity.CENTER_VERTICAL
        setPadding(11.dp, 0, 4.dp, 0)
        setBackgroundResource(R.drawable.main_4corner_stroke_4dff4477)
        setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10f)
        // 处理Android 15 拿不到同bundle下资源的问题
        val textColor = try {
            ResourcesCompat.getColor(resources, R.color.main_color_ff4477, null)
        } catch (e: Exception) {
            "#ff4477".toColorInt()
        }
        setTextColor(textColor)
        setTypeface(typeface, Typeface.BOLD)
    }

    init {
        addView(tagTvView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT).also {
            it.marginStart = 6.dp
        })
        addView(tagImgView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.MATCH_PARENT))

        if (isInEditMode) {
            setTagInfo(TagType.LUCKY_BAG)
        }
    }

    fun setTagInfo(tagType: TagType) {
        when (tagType) {
            TagType.GOD_CHOSEN_RED_PACKET -> {
                tagImgView.setImageResource(R.drawable.main_ic_god_redpacket_flag)
            }

            TagType.RED_PACKET -> {
                tagImgView.setImageResource(R.drawable.main_ic_redpacket_flag)
            }

            TagType.LUCKY_BAG -> {
                tagImgView.setImageResource(R.drawable.main_ic_lucky_bag_flag)
            }
        }
        tagTvView.text = tagType.desc
    }

    enum class TagType(val value: Int, val desc: String) {
        GOD_CHOSEN_RED_PACKET(1, "天选红包"),
        RED_PACKET(2, "红包"),
        LUCKY_BAG(3, "福袋");

        companion object {
            fun convert(type: Int?): TagType? {
                return when (type) {
                    GOD_CHOSEN_RED_PACKET.value -> GOD_CHOSEN_RED_PACKET

                    RED_PACKET.value -> RED_PACKET

                    LUCKY_BAG.value -> LUCKY_BAG

                    else -> null
                }
            }
        }
    }
}