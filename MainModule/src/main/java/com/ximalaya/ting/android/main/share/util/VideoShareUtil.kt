package com.ximalaya.ting.android.main.share.util

import android.Manifest
import android.content.ContentValues
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.webkit.MimeTypeMap
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.FileProviderUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction.IPermissionListener
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.other.PermissionManage
import com.ximalaya.ting.android.host.view.lrcview.LrcEntry
import com.ximalaya.ting.android.main.kachamodule.produce.fragment.KachaClipFragmentNew
import com.ximalaya.ting.android.main.kachamodule.utils.ShortContentUtil
import com.ximalaya.ting.android.main.manager.soundpatch.SoundPatchMainManager
import com.ximalaya.ting.android.main.share.fragment.VideoClipFragmentNew
import com.ximalaya.ting.android.main.share.fragment.VideoShareFragment
import com.ximalaya.ting.android.main.share.video.manager.ShortContentDirManager
import com.ximalaya.ting.android.main.share.video.model.ShareLrcEntry
import com.ximalaya.ting.android.main.share.video.model.ShortContentProductModel
import com.ximalaya.ting.android.main.share.video.synthesis.IVideoSaveListener
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager
import com.ximalaya.ting.android.opensdk.player.check.PauseReason
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.shareservice.base.IShareDstType
import com.ximalaya.ting.android.xmutil.Logger
import com.ximalaya.ting.android.xmutil.MD5.md5
import java.io.File
import java.io.IOException
import java.io.OutputStream
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2024/7/15
 */
object VideoShareUtil {
    private val clipTimeSdf = SimpleDateFormat("mm:ss", Locale.CHINA)
    fun convertToClipTime(time: Long): String {
        return clipTimeSdf.format(time)
    }

    fun startVideoShare(contentModel: ShareWrapContentModel) {
        val track = contentModel.soundInfo
        val mainActivity = BaseApplication.getMainActivity() as MainActivity? ?: return
        contentModel.isTrackPlayingWhenShareVideo = XmPlayerManager.getInstance(mainActivity).isPlaying
        val durationMs = XmPlayerManager.getInstance(mainActivity).getDuration()
        if (track == null || durationMs == 0) {
            CustomToast.showFailToast("请等待正片播放后再使用视频分享功能~")
            return
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
            return
        }
        if (XmPlayerManager.getInstance(mainActivity).isPlaying()) {
            XmPlayerManager.getInstance(mainActivity).pause(PauseReason.Business.VideoShareUtil)
        }
        if (SoundPatchMainManager.getInstance().isPlayingSoundPatch()) {
            SoundPatchMainManager.getInstance().stopSoundPatch()
        }
        val destFragment = VideoShareFragment.newInstance(contentModel, durationMs)
        destFragment.underThisHasPlayFragment = true
        mainActivity.startFragment(destFragment)
    }

    fun startVideoClip(contentModel: ShareWrapContentModel) {
        val track = contentModel.soundInfo
        val mainActivity = BaseApplication.getMainActivity() as MainActivity? ?: return
        contentModel.isTrackPlayingWhenShareClip = XmPlayerManager.getInstance(mainActivity).isPlaying
        val durationMs = XmPlayerManager.getInstance(mainActivity).getDuration()
        if (track == null || durationMs == 0) {
            CustomToast.showFailToast("请等待正片播放后再使用视频分享功能~")
            return
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(BaseApplication.getMyApplicationContext())
            return
        }
        if (XmPlayerManager.getInstance(mainActivity).isPlaying()) {
            XmPlayerManager.getInstance(mainActivity).pause(PauseReason.Business.VideoShareUtil)
        }
        if (SoundPatchMainManager.getInstance().isPlayingSoundPatch()) {
            SoundPatchMainManager.getInstance().stopSoundPatch()
        }
        val clipFragment = VideoClipFragmentNew.getInstanceForShare(track, durationMs, contentModel)
        clipFragment.underThisHasPlayFragment = true
        mainActivity.startFragment(clipFragment)
    }

    fun save2Gallery(model: ShortContentProductModel?, listener: IVideoSaveListener) {
        if (model == null || model.finalVideoStoragePath.isNullOrEmpty()) {
            CustomToast.showFailToast("保存失败,请重试")
            return
        }
        val runSave = Runnable {
            val destPath = doRealSave(model)
            if (destPath.isNotEmpty()) {
                listener.onSaveSuccess(destPath)
            } else {
                listener.onSaveFailed()
            }
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            PermissionManage.checkPermission(BaseApplication.getMainActivity()!!,
                (BaseApplication.getMainActivity() as MainActivity?)!!,
                object : LinkedHashMap<String?, Int?>() {
                    init {
                        put(
                            Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            R.string.host_sdcard_write_permission_to_save_video
                        )
                    }
                },
                object : IPermissionListener {
                    override fun havedPermissionOrUseAgree() {
                        MyAsyncTask.execute(runSave, false)
                    }

                    override fun userReject(noRejectPermiss: Map<String, Int>) {
                        CustomToast.showFailToast("权限被拒,保存失败")
                    }
                })
        } else {
            MyAsyncTask.execute(runSave, false)
        }
    }

    private fun doRealSave(model: ShortContentProductModel): String {
        try {
            val srcFilePath: String = model.finalVideoStoragePath
            val videoFileName =
                "${md5(model.albumName.toByteArray())}_${model.soundStartSecond}_${model.soundEndSecond}.mp4"
            if (srcFilePath.isEmpty() || !File(srcFilePath).exists()) {
                return ""
            }
            val destFile = File(ShortContentDirManager.ALBUM_VIDEO_DIR, videoFileName)
            if (destFile.exists()) {
                return destFile.absolutePath
            }
            ShortContentUtil.checkAndCreatePath(ShortContentDirManager.ALBUM_VIDEO_DIR)
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                ShortContentUtil.copyFile(
                    srcFilePath, ShortContentDirManager.ALBUM_VIDEO_DIR, videoFileName
                )
                MainApplication.getMyApplicationContext().sendBroadcast(
                    Intent(
                        Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                        Uri.parse("file://" + destFile.absolutePath)
                    )
                )
                return destFile.absolutePath
            } else {
                val contentResolver =
                    BaseApplication.getMyApplicationContext().contentResolver ?: return ""
                val mimeType = MimeTypeMap.getSingleton()
                    .getMimeTypeFromExtension(MimeTypeMap.getFileExtensionFromUrl(destFile.absolutePath))
                val contentValues = ContentValues()
                contentValues.put(MediaStore.Video.Media.DISPLAY_NAME, videoFileName)
                contentValues.put(MediaStore.Video.Media.MIME_TYPE, mimeType)
                val contentUri: Uri =
                    if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                        MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                    } else {
                        MediaStore.Video.Media.INTERNAL_CONTENT_URI
                    }
                contentValues.put(
                    MediaStore.Video.Media.RELATIVE_PATH,
                    Environment.DIRECTORY_MOVIES + File.separator + "喜马拉雅"
                )
                contentValues.put(MediaStore.MediaColumns.IS_PENDING, 1)
                val uri: Uri = contentResolver.insert(contentUri, contentValues) ?: return ""
                var os: OutputStream? = null
                try {
                    os = contentResolver.openOutputStream(uri)
                    if (os != null) {
                        os.write(File(srcFilePath).readBytes())
                        contentValues.clear()
                        contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0)
                        contentResolver.update(uri, contentValues, null, null)
                        return destFile.absolutePath
                    }
                } catch (e: Exception) {
                    contentResolver.delete(uri, null, null)
                    e.printStackTrace()
                } finally {
                    try {
                        os?.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
            return ""
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun openApp(dstType: String, path: String) {
        val context = BaseApplication.getMyApplicationContext() ?: return
        val tripe: Triple<String, String, String>? = when (dstType) {
            IShareDstType.SHARE_TYPE_WX_FRIEND -> {
                Triple("com.tencent.mm", "com.tencent.mm.ui.tools.ShareImgUI", "微信")
            }

            IShareDstType.SHARE_TYPE_QQ -> {
                Triple("com.tencent.mobileqq", "com.tencent.mobileqq.activity.JumpActivity", "QQ")
            }

            else -> null
        }
        if (tripe != null && !ToolUtil.isInstalledByPackageName(context, tripe.first)) {
            CustomToast.showFailToast("请先安装${tripe.third}")
            return
        }
        try {
            val shareIntent = Intent()
            shareIntent.setAction(Intent.ACTION_SEND)
            shareIntent.setType("video/*")
            val videoUri =
                FileProviderUtil.fromFile(context, File(path))
            shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri)
            shareIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            if (Build.VERSION.SDK_INT >= 24) {
                shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            if (tripe != null) {
                context.grantUriPermission(
                    tripe.first,
                    videoUri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                shareIntent.setClassName(tripe.first, tripe.second)
            }
            context.startActivity(shareIntent)
        } catch (e: Exception) {
            XDCSCollectUtil.statErrorToXDCS("share", "share_open_app_error,$e")
            CustomToast.showFailToast("打开失败")
        }
    }

    fun lrcCut2ShareLrc(
        lrcEntries: List<LrcEntry>, startTime: Long, endTime: Long
    ): MutableList<ShareLrcEntry> {
        val result = mutableListOf<ShareLrcEntry>()
        lrcEntries.map { ShareLrcEntry(it.text.trim(), it.time) }.forEachIndexed { index, entry ->
            if (entry.time in startTime..endTime) {
                result.add(entry)
            }
        }
        return result
    }

    fun log(msg: String) {
        if (!ConstantsOpenSdk.isDebug) {
            return
        }
        Logger.i("VideoShare", msg)
    }
}