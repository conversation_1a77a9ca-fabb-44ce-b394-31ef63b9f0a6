
package com.ximalaya.ting.android.main.playpage.playy.view;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Shader;
import android.graphics.SweepGradient;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;
import com.ximalaya.ting.android.framework.util.BaseUtil;

public class ListenTimePoolView extends View {
    private static final float DEFAULT_AMPLITUDE_RATIO = 0.05f;
    private static final float DEFAULT_WAVE_LENGTH_RATIO = 1.0f;
    private static final float DEFAULT_WAVE_SHIFT_RATIO = 0.0f;
    private static final float DEFAULT_WATER_LEVEL_RATIO = 0.5f;
    public static final int DEFAULT_FRONT_WAVE_COLOR =  Color.parseColor("#ffCFF9EA");
    public static final int DEFAULT_BEHIND_WAVE_COLOR = Color.parseColor("#66CFF9EA");
    public static final int DEFAULT_FRONT_WAVE_COLOR_DARK =  Color.parseColor("#ff26634C");
    public static final int DEFAULT_BEHIND_WAVE_COLOR_DARK = Color.parseColor("#6626634C");

    // shader containing repeated waves
    private BitmapShader mWaveShader;
    // shader matrix
    private Matrix mShaderMatrix;
    // paint to draw wave
    private Paint mViewPaint;
    // paint to draw border
    private Paint mBorderPaint;

    private float mDefaultAmplitude;
    private float mDefaultWaveLength;
    private float mDefaultWaterLevel;
    private double mDefaultAngularFrequency;

    private float mAmplitudeRatio = DEFAULT_AMPLITUDE_RATIO;
    private float mWaveLengthRatio = DEFAULT_WAVE_LENGTH_RATIO;
    private float mWaveShiftRatio = DEFAULT_WAVE_SHIFT_RATIO;
    private float mWaterLevelRatio = DEFAULT_WATER_LEVEL_RATIO;
    private int mBehindWaveColor = DEFAULT_BEHIND_WAVE_COLOR;
    private int mFrontWaveColor = DEFAULT_FRONT_WAVE_COLOR;

    private AnimatorSet mCurrentAnimatorSet;
    private AnimatorSet mNormalWaveAnimator;
    private AnimatorSet mAddTimeWaveAnimator;

    private ObjectAnimator waterLevelAnim;

    // 边框渐变颜色
    private final int[] mGradientColors = {0x086FD0AE, 0x1a25BC87, 0x086FD0AE, 0x3325BC87, 0x086FD0AE};

    public ListenTimePoolView(Context context) {
        super(context);
        init();
    }

    public ListenTimePoolView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ListenTimePoolView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        if (BaseFragmentActivity.sIsDarkMode) {
            mFrontWaveColor = DEFAULT_FRONT_WAVE_COLOR_DARK;
            mBehindWaveColor = DEFAULT_BEHIND_WAVE_COLOR_DARK;
        } else {
            mFrontWaveColor = DEFAULT_FRONT_WAVE_COLOR;
            mBehindWaveColor = DEFAULT_BEHIND_WAVE_COLOR;
        }
        mShaderMatrix = new Matrix();
        mViewPaint = new Paint();
        mViewPaint.setAntiAlias(true);
        mBorderPaint = new Paint();
        mBorderPaint.setAntiAlias(true);
        mBorderPaint.setStyle(Style.STROKE);
        mBorderPaint.setStrokeWidth(BaseUtil.dp2px(getContext(), 5));
        initAnimation();
    }

    /**
     * Set water level according to <code>waterLevelRatio</code>.
     *
     * @param waterLevelRatio Should be 0 ~ 1. Default to be 0.5.
     *                        Ratio of water level to WaveView height.
     */
    public void setWaterLevelRatio(float waterLevelRatio) {
        if (mWaterLevelRatio != waterLevelRatio) {
            mWaterLevelRatio = waterLevelRatio;
            invalidate();
        }
    }

    public float getWaterLevelRatio() {
        return mWaterLevelRatio;
    }

    /**
     * Shift the wave horizontally according to <code>waveShiftRatio</code>.
     *
     * @param waveShiftRatio Should be 0 ~ 1. Default to be 0.
     *                       Result of waveShiftRatio multiples width of WaveView is the length to shift.
     */
    public void setWaveShiftRatio(float waveShiftRatio) {
        if (mWaveShiftRatio != waveShiftRatio) {
            mWaveShiftRatio = waveShiftRatio;
            invalidate();
        }
    }

    public float getWaveShiftRatio() {
        return mWaveShiftRatio;
    }

    /**
     * Set vertical size of wave according to <code>amplitudeRatio</code>
     *
     * @param amplitudeRatio Default to be 0.05. Result of amplitudeRatio + waterLevelRatio should be less than 1.
     *                       Ratio of amplitude to height of WaveView.
     */
    public void setAmplitudeRatio(float amplitudeRatio) {
        if (mAmplitudeRatio != amplitudeRatio) {
            mAmplitudeRatio = amplitudeRatio;
            invalidate();
        }
    }

    public float getAmplitudeRatio() {
        return mAmplitudeRatio;
    }

    /**
     * Set horizontal size of wave according to <code>waveLengthRatio</code>
     *
     * @param waveLengthRatio Default to be 1.
     *                        Ratio of wave length to width of WaveView.
     */
    public void setWaveLengthRatio(float waveLengthRatio) {
        mWaveLengthRatio = waveLengthRatio;
    }

    public void startNormalAnimation() {
        mNormalWaveAnimator.start();
        mCurrentAnimatorSet = mNormalWaveAnimator;
    }

    public void startAddTimeAnimation(float startLevel, float endLevel) {
        waterLevelAnim.setFloatValues(startLevel, endLevel);
        waterLevelAnim.start();
    }

    private void initAnimation() {
        // 水量增加时的动画
        waterLevelAnim = ObjectAnimator.ofFloat(this, "waterLevelRatio", 0f, 0.5f);
        waterLevelAnim.setDuration(2500);
        waterLevelAnim.setInterpolator(new LinearInterpolator());

        // 正常水面波动动画
        ObjectAnimator waveShiftAnim2 = ObjectAnimator.ofFloat(this, "waveShiftRatio", 0f, 1f);
        waveShiftAnim2.setRepeatCount(ValueAnimator.INFINITE);
        waveShiftAnim2.setDuration(1000);
        waveShiftAnim2.setInterpolator(new LinearInterpolator());
        ObjectAnimator amplitudeAnim2 = ObjectAnimator.ofFloat(this, "amplitudeRatio", 0.01f, 0.04f);
        amplitudeAnim2.setRepeatCount(ValueAnimator.INFINITE);
        amplitudeAnim2.setRepeatMode(ValueAnimator.REVERSE);
        amplitudeAnim2.setDuration(3000);
        amplitudeAnim2.setInterpolator(new LinearInterpolator());
        mNormalWaveAnimator = new AnimatorSet();
        mNormalWaveAnimator.playTogether(waveShiftAnim2, amplitudeAnim2);
    }

    public void cancelAnimation() {
        if (mCurrentAnimatorSet != null) {
            mCurrentAnimatorSet.cancel();
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        createShader();
    }

    /**
     * Create the shader with default waves which repeat horizontally, and clamp vertically
     */
    private void createShader() {
        mDefaultAngularFrequency = 2.0f * Math.PI / DEFAULT_WAVE_LENGTH_RATIO / getWidth();
        mDefaultAmplitude = getHeight() * DEFAULT_AMPLITUDE_RATIO;
        mDefaultWaterLevel = getHeight() * DEFAULT_WATER_LEVEL_RATIO;
        mDefaultWaveLength = getWidth();

        Bitmap bitmap = Bitmap.createBitmap(getWidth(), getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);

        Paint wavePaint = new Paint();
        wavePaint.setStrokeWidth(2);
        wavePaint.setAntiAlias(true);

        // Draw default waves into the bitmap
        // y=Asin(ωx+φ)+h
        final int endX = getWidth() + 1;
        final int endY = getHeight() + 1;

        float[] waveY = new float[endX];

        wavePaint.setColor(mBehindWaveColor);
        for (int beginX = 0; beginX < endX; beginX++) {
            double wx = beginX * mDefaultAngularFrequency;
            float beginY = (float) (mDefaultWaterLevel + mDefaultAmplitude * Math.sin(wx));
            canvas.drawLine(beginX, beginY, beginX, endY, wavePaint);

            waveY[beginX] = beginY;
        }

        wavePaint.setColor(mFrontWaveColor);
        final int wave2Shift = (int) (mDefaultWaveLength / 4);
        for (int beginX = 0; beginX < endX; beginX++) {
            canvas.drawLine(beginX, waveY[(beginX + wave2Shift) % endX], beginX, endY, wavePaint);
        }

        // use the bitamp to create the shader
        mWaveShader = new BitmapShader(bitmap, Shader.TileMode.REPEAT, Shader.TileMode.CLAMP);
        mViewPaint.setShader(mWaveShader);
        SweepGradient sweepGradient = new SweepGradient(getWidth()/2f , getHeight()/2f,
                mGradientColors, null);
        mBorderPaint.setShader(sweepGradient);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        // modify paint shader according to mShowWave state
        if (mWaveShader != null) {
            // first call after mShowWave, assign it to our paint
            if (mViewPaint.getShader() == null) {
                mViewPaint.setShader(mWaveShader);
            }

            // sacle shader according to mWaveLengthRatio and mAmplitudeRatio
            // this decides the size(mWaveLengthRatio for width, mAmplitudeRatio for height) of waves
            mShaderMatrix.setScale(
                mWaveLengthRatio / DEFAULT_WAVE_LENGTH_RATIO,
                mAmplitudeRatio / DEFAULT_AMPLITUDE_RATIO,
                0,
                mDefaultWaterLevel);
            // translate shader according to mWaveShiftRatio and mWaterLevelRatio
            // this decides the start position(mWaveShiftRatio for x, mWaterLevelRatio for y) of waves
            mShaderMatrix.postTranslate(
                mWaveShiftRatio * getWidth(),
                (DEFAULT_WATER_LEVEL_RATIO - mWaterLevelRatio) * getHeight());

            // assign matrix to invalidate the shader
            mWaveShader.setLocalMatrix(mShaderMatrix);

            float borderWidth = mBorderPaint == null ? 0f : mBorderPaint.getStrokeWidth();
            if (borderWidth > 0) {
                canvas.drawCircle(getWidth() / 2f, getHeight() / 2f,
                        (getWidth() - borderWidth) / 2f - 1f, mBorderPaint);
            }
            float radius = getWidth() / 2f;
            canvas.drawCircle(getWidth() / 2f, getHeight() / 2f, radius, mViewPaint);
        } else {
            mViewPaint.setShader(null);
        }
    }
}
