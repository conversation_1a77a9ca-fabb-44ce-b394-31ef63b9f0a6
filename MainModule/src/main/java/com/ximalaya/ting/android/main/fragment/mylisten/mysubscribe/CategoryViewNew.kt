package com.ximalaya.ting.android.main.fragment.mylisten.mysubscribe

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.model.mylisten.WoTingSubscribeCategory
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.manager.mylisten.CategoryViewManager.FinishOrNotDimension
import com.ximalaya.ting.android.main.manager.mylisten.CategoryViewManagerNew
import com.ximalaya.ting.android.main.manager.mylisten.CategoryViewManagerNew.CATEGORY_DIMENSION_DEFAULT
import com.ximalaya.ting.android.main.manager.mylisten.CategoryViewManagerNew.CATEGORY_DIMENSION_EVENT
import com.ximalaya.ting.android.main.view.LinearItemDecoration
import com.ximalaya.ting.android.main.view.RecyclerViewCanDisallowIntercept
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by ZhuPeipei on 2020/10/21 14:09.
 */
class CategoryViewNew(private val type: Int, private val mCategoryManager: CategoryViewManagerNew) {
    private var mRootView: View? = null
    private var mCategoryRv: RecyclerViewCanDisallowIntercept? = null
    private var mAdapter: CategoryAdapterNew? = null
    private val mBoldFontFace = Typeface.create("sans-serif-light", Typeface.BOLD)
    private val mNormalFontFace = Typeface.defaultFromStyle(Typeface.NORMAL)
//    private val mHeadTvList = ArrayList<TextView>(3)
    private var mScrollX: Int = 0
    private var mFinishOrNotView: LinearLayout? = null
    private val mFinishOrNotTvList = ArrayList<TextView>(2)

    fun init(context: Context, parent: ViewGroup, slideView: ViewGroup?) {
        createViewIfNotExist(context, parent, slideView)
    }

    private fun createViewIfNotExist(context: Context, parent: ViewGroup, slideView: ViewGroup?) {
        val view: View? = LayoutInflater.from(context).inflate(
                R.layout.main_item_my_subscribe_category_view_new, parent, false)
        mRootView = view
        parent.addView(view)
//        val headerView: LinearLayout? = view?.findViewById(R.id.main_diff_dimension_header_ll)
//        initHeadViews(headerView)
        mCategoryRv = view?.findViewById(R.id.main_category_rv)
        mCategoryRv?.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mAdapter = CategoryAdapterNew(type, mCategoryManager)
        mCategoryRv?.adapter = mAdapter
        mCategoryRv?.addItemDecoration(LinearItemDecoration(0, 0))
        mCategoryRv?.setDisallowInterceptTouchEventView(slideView)
        mCategoryRv?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mScrollX += dx
            }
        })
        mFinishOrNotView = view?.findViewById(R.id.main_finish_or_not_sort_ll)
        initFinishOrNotViews(mFinishOrNotView)
    }

    fun setBackgroundColor(colorId: Int) {
        val color = mRootView?.context?.resources?.getColor(colorId)
        color?.let {
            mRootView?.setBackgroundColor(it)
        }
    }

//    private fun initHeadViews(headerView: LinearLayout?) {
//        if (headerView == null) {
//            return
//        }
//        for ((index, dimension) in CalDimension.values().withIndex()) {
//            val view: View = headerView.getChildAt(index) ?: continue
//            val categoryTv: TextView = view.findViewById(R.id.main_category_tv)
//            categoryTv.contentDescription = dimension.title
//            categoryTv.text = dimension.title
//            if (mCategoryManager.headCurDimension == dimension) {
//                categoryTv.isSelected = true
//                categoryTv.typeface = mBoldFontFace
//            } else {
//                categoryTv.isSelected = false
//                categoryTv.typeface = mNormalFontFace
//            }
//            mHeadTvList.add(categoryTv)
//            view.setOnClickListener(View.OnClickListener {
//                if (!OneClickHelper.getInstance().onClick(it)) {
//                    return@OnClickListener
//                }
//                mCategoryManager.onClickForLoadData(
//                        type,
//                        CalDimension.values()[index],
//                        mCategoryManager.categoryDimension,
//                        mCategoryManager.finishOrNotDimension)
//                // 我的-订阅tab-筛选-分类/完结状态/排序方式  点击事件
//                XMTraceApi.Trace()
//                        .click(37270)
//                        .put("Item", "${dimension.title}")
//                        .put("currPage", "mySpace9.0")
//                        .put("type", "sort")
//                        .createTrace()
//            })
//        }
//    }

//    private fun updateHeadViews() {
//        for ((index, dimension) in CalDimension.values().withIndex()) {
//            val categoryTv: TextView? = mHeadTvList[index]
//            if (mCategoryManager.headCurDimension == dimension) {
//                categoryTv?.isSelected = true
//                categoryTv?.typeface = mBoldFontFace
//            } else {
//                categoryTv?.isSelected = false
//                categoryTv?.typeface = mNormalFontFace
//            }
//        }
//    }

    private fun initFinishOrNotViews(finishOrNotView: LinearLayout?) {
        if (finishOrNotView == null) {
            return
        }
        for ((index, dimension) in FinishOrNotDimension.values().withIndex()) {
            val view: View = finishOrNotView.getChildAt(index) ?: continue
            val categoryTv: TextView = view.findViewById(R.id.main_category_tv)
            categoryTv.contentDescription = dimension.title
            categoryTv.text = dimension.title
            if (mCategoryManager.finishOrNotDimension == dimension) {
                categoryTv.isSelected = true
                categoryTv.typeface = mBoldFontFace
            } else {
                categoryTv.isSelected = false
                categoryTv.typeface = mNormalFontFace
            }
            mFinishOrNotTvList.add(categoryTv)
            view.setOnClickListener(View.OnClickListener {
                if (!OneClickHelper.getInstance().onClick(it)) {
                    return@OnClickListener
                }
                mCategoryManager.onClickForLoadData(
                        type,
                        mCategoryManager.headCurDimension,
                        mCategoryManager.categoryDimension,
                        FinishOrNotDimension.values()[index])
                // 我的-订阅tab-筛选-分类/完结状态/排序方式  点击事件
                XMTraceApi.Trace()
                        .click(37270)
                        .put("Item", "${dimension.title}")
                        .put("currPage", "mySpace9.0")
                        .put("type", "serialStatus")
                        .createTrace()
            })
        }
    }

    private fun getFinishAndNotListenNum(): IntArray {
        var finishNum = 0
        var unFinishNum = 0
        var notListenNum = 0
        val dataBean = mCategoryManager.categoryDataBean
        if (dataBean != null) {
            if (mCategoryManager.categoryDimension == CATEGORY_DIMENSION_DEFAULT) {
                if (dataBean.finishedIds != null) {
                    finishNum = dataBean.finishedIds.size
                }
                if (dataBean.unfinishedIds != null) {
                    unFinishNum = dataBean.unfinishedIds.size
                }
                if (dataBean.notListenedIds != null) {
                    notListenNum = dataBean.notListenedIds.size
                }
            } else {
                val model = mCategoryManager.categoryDimensionModel
                if (model != null) {
                    finishNum = model.finishCount
                    unFinishNum = model.unfinishCount
                    notListenNum = model.notListenCount
                }
            }
        }
        return intArrayOf(finishNum, unFinishNum, notListenNum)
    }

    private fun updateFinishOrNotViews(finishNum: Int, unFinishNum: Int, notListenNum: Int) {
        for ((index, dimension) in FinishOrNotDimension.values().withIndex()) {
            val categoryTv: TextView? = mFinishOrNotTvList[index]
            var num = 0
            num = when (dimension) {
                FinishOrNotDimension.ALL -> {
                    finishNum + unFinishNum
                }
                FinishOrNotDimension.FINISHED -> {
                    finishNum
                }
                FinishOrNotDimension.NOTListen -> {
                    notListenNum
                }
            }
            categoryTv?.text = "${dimension.title} $num"
            categoryTv?.contentDescription = "${dimension.title} $num"
            if (mCategoryManager.finishOrNotDimension == dimension) {
                categoryTv?.isSelected = true
                categoryTv?.typeface = mBoldFontFace
            } else {
                categoryTv?.isSelected = false
                categoryTv?.typeface = mNormalFontFace
            }
        }
    }

    fun setMetaData(dataBean: WoTingSubscribeCategory.DataBean?) {
        if (dataBean == null || !dataBean.isShowCategoryResults || ToolUtil.isEmptyCollects(dataBean.categoryResults)) {
            mAdapter?.clearData()
            mCategoryRv?.visibility = View.GONE
        } else {
            mAdapter?.setData(dataBean.categoryResults)
            mCategoryRv?.visibility = View.VISIBLE
        }
//        updateHeadViews()
        if (mCategoryManager.categoryDimension == CATEGORY_DIMENSION_EVENT) {
            mFinishOrNotView?.visibility = View.GONE
        } else {
            val nums = getFinishAndNotListenNum()
            if (nums[0] + nums[1] == 0) {
                mFinishOrNotView?.visibility = View.GONE
            } else {
                mFinishOrNotView?.visibility = View.VISIBLE
                updateFinishOrNotViews(nums[0], nums[1], nums[2])
            }
        }
    }

    fun updateView() {
        mAdapter?.notifyDataSetChanged()
//        updateHeadViews()
        if (mCategoryManager.categoryDimension == CATEGORY_DIMENSION_EVENT) {
            mFinishOrNotView?.visibility = View.GONE
        } else {
            val nums = getFinishAndNotListenNum()
            if (nums[0] + nums[1] == 0) {
                mFinishOrNotView?.visibility = View.GONE
            } else {
                mFinishOrNotView?.visibility = View.VISIBLE
                updateFinishOrNotViews(nums[0], nums[1], nums[2])
            }
        }
    }

    fun showOrHideView(show: Boolean) {
        mRootView?.visibility = if (show) View.VISIBLE else View.GONE
    }

    fun setScrollX(scrollX: Int) {
        mCategoryRv?.scrollBy(scrollX - mScrollX, 0)
    }

    fun getLastScrollX(): Int {
        return mScrollX
    }

    fun getViewHeight(): Int {
        return if (mRootView?.visibility == View.VISIBLE) mRootView?.height ?: 0 else 0
    }
}