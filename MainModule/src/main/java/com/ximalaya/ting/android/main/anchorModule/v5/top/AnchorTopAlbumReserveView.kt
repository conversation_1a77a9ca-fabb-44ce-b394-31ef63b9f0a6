package com.ximalaya.ting.android.main.anchorModule.v5.top

import android.view.View
import android.widget.TextView
import com.ximalaya.ting.android.host.manager.NewAlbumReserveManager
import com.ximalaya.ting.android.host.manager.NewAlbumReserveManager.IReserveChangeListener
import com.ximalaya.ting.android.host.util.view.setTextIfChanged
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.anchorModule.anchorSpace.view.IAnchorTopTraceView
import com.ximalaya.ting.android.main.anchorModule.v5.AnchorNewProductModel
import com.ximalaya.ting.android.main.anchorModule.v5.IAnchorSpaceView
import com.ximalaya.ting.android.main.anchorModule.v5.util.AnchorSpaceUtil
import com.ximalaya.ting.android.main.anchorModule.v5.util.AnchorTraceUtil
import com.ximalaya.ting.android.main.mine.extension.onSingleClick
import com.ximalaya.ting.android.xmtrace.XMTraceApi

class AnchorTopAlbumReserveView(anchorView: IAnchorSpaceView) : BaseAnchorTopView(anchorView),
    IAnchorTopTraceView, IReserveChangeListener {

    private val tvTitle: TextView by lazy { rootView.findViewById(R.id.main_tv_live_title) }
    private val tvTime: TextView by lazy { rootView.findViewById(R.id.main_tv_live_time) }
    private val tvReserve: TextView by lazy { rootView.findViewById(R.id.main_tv_goto_live) }

    override fun traceShow() {

    }

    override fun getChildLayoutId(): Int {
        return R.id.main_anchor_reserve_container
    }

    override fun refreshView() {
        val live = anchorModel?.wrap?.live
        val myclub = anchorModel?.wrap?.myclub
        val isLiveShowing = live?.isLiving() == true || live?.isNotice() == true || myclub?.isLiving() == true || myclub?.isNotice() == true
        val hasNewProduct = anchorModel?.wrap?.newProduct?.title.isNullOrEmpty().not()
        if (isLiveShowing || !hasNewProduct) {
            // 优先级低于直播
            rootView.visibility = View.GONE
            return
        }
        NewAlbumReserveManager.addListChangeListener(this)
        showAlbumReserveView()
        rootView.visibility = View.VISIBLE
    }

    private fun showAlbumReserveView() {
        val newProduct = anchorModel?.wrap?.newProduct ?: return
        tvTitle.setTextIfChanged(newProduct.title)
        rootView.setOnClickListener {
            it.onSingleClick {
                toAlbumPage(newProduct)
            }
        }
        tvTime.setTextIfChanged(newProduct.onlineTime)
        tvReserve.apply {
            isSelected = newProduct.reservationStatus
            text = if (isSelected) "已预约" else "预约"
            setOnClickListener {
                it.onSingleClick {
                    toReserveAlbum(newProduct)
                }
            }
        }
        eTraceReserve()
    }

    private fun toAlbumPage(newProduct: AnchorNewProductModel) {
        traceClick("title")
        runCatching {
            if (newProduct.url.isNullOrEmpty().not()) {
                AnchorSpaceUtil.startByLink(newProduct.url)
            }
        }
    }

    private fun toReserveAlbum(model: AnchorNewProductModel) {
        traceClick("button")
        NewAlbumReserveManager.reserveAlbum(model.reservationProductId!!, model.reservationStatus.not(),
            model.coverPath, "anchorSpace", null)
    }

    private fun eTraceReserve() {
        // 个人主页-新品预告  控件曝光
        XMTraceApi.Trace()
            .setMetaId(68947)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .apply {
                putCommonTrace(this)
            }
            .put("albumId", anchorModel?.wrap?.newProduct?.albumId?.toString())
            .createTrace()
    }

    private fun traceClick(btnName: String) {
        // 个人主页-新品预告  点击事件
        XMTraceApi.Trace()
            .click(68946) // 用户点击时上报
            .apply {
                putCommonTrace(this)
            }
            .put("albumId", anchorModel?.wrap?.newProduct?.albumId?.toString())
            .put("area", btnName) // title 表示点击条，button 表示点击预约按钮
            .createTrace()
    }

    private fun putCommonTrace(trace: XMTraceApi.Trace): XMTraceApi.Trace {
        return trace.apply {
            AnchorTraceUtil.putCommonTrace(this)
                .put("status", if (anchorModel?.wrap?.newProduct?.reservationStatus == true) "已预约" else "未预约")
        }
    }

    fun onDestroy() {
        NewAlbumReserveManager.removeListChangeListener(this)
    }

    override fun onChanged(reservationProductId: Long, isReserved: Boolean) {
        tvReserve.apply {
            anchorModel?.wrap?.newProduct?.reservationStatus = isReserved
            isSelected = isReserved
            text = if (isSelected) "已预约" else "预约"
        }
    }
}