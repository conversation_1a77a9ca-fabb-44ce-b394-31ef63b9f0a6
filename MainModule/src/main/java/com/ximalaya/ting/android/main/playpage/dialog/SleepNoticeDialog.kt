package com.ximalaya.ting.android.main.playpage.dialog

import android.graphics.Outline
import android.os.Bundle
import android.view.*
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.extension.dpFloat
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.playy.YPlayFragment
import com.ximalaya.ting.android.opensdk.player.manager.PlanTerminateManagerForPlay
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.XMTraceApi
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger


class SleepNoticeDialog: BaseDialogFragment<SleepNoticeDialog>() {

    companion object {
        private val TAG = "SleepNoticeDialog"
        private val keyLastPopup = "sleep_notice_popup_timestamps"

        //check has been show x times in N days
        private const val MAX_SHOW_TIMES = 3
        private const val MAX_SHOW_DAYS = 7

        fun checkDateLimit(): Boolean {
            val days = ConfigureCenter.getInstance().getInt("toc", "reminder_timed_popup", 3)
            val lastPopupTime = MMKVUtil.getInstance().getLong(keyLastPopup, 0L)
            val currentTime = System.currentTimeMillis()
            val daysInMillis = days * 24 * 60 * 60 * 1000L // 转换为毫秒

            // 如果从未弹窗过，或者距离上次弹窗已超过 N 天
            if (lastPopupTime == 0L || currentTime - lastPopupTime >= daysInMillis) {
                MMKVUtil.getInstance().saveLong(keyLastPopup, currentTime)
                return true // 可以显示弹窗
            }
            return false // 不显示弹窗
        }

        fun tryShow(playFragment: YPlayFragment, playingSoundInfo: PlayingSoundInfo) {
            //check current time is between 22:00 ~ 4:00
            val currentTime = System.currentTimeMillis()
            val calendar = java.util.Calendar.getInstance().apply {
                timeInMillis = currentTime
            }
            val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
            val min = calendar.get(java.util.Calendar.MINUTE)
            if (!(hour == 0 && min >= 5 || hour in 1..4)) {
                Logger.w(TAG, "tryShow: current time is not between 00:05 ~ 04:00")
                return
            }

            if (!playFragment.isRealVisable) {
                Logger.w(TAG, "tryShow: playFragment is not visible")
                return
            }

            if (!checkDateLimit()) {
                Logger.w(TAG, "tryShow: checkDateLimit failed")
                return
            }

            SleepNoticeDialog().apply {
                this.playFragment = playFragment
                this.playingSoundInfo = playingSoundInfo
            }.show(playFragment.childFragmentManager, "SleepNoticeDialog")
        }
    }

    private var playFragment: YPlayFragment? = null
    private var playingSoundInfo: PlayingSoundInfo? = null

    private var xmRequestId: String = ""

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        configureDialogStyle()
        return inflater.inflate(R.layout.main_dialog_play_sleep_notice, container, false)
    }

    override fun onResume() {
        super.onResume()

        // 新声音播放页-定时功能提醒弹窗  控件曝光
        // 新声音播放页-定时功能提醒弹窗  点击事件
        val trackId = playingSoundInfo?.trackInfo?.trackId?.toString()?: ""
        val albumId = playingSoundInfo?.trackInfo?.albumId?.toString()?: ""
        XMTraceApi.Trace()
            .setMetaId(67148)
            .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
            .put("currPage", "newPlay")
            .put("xmRequestId", xmRequestId)
            .put("contentType","alarm")
            .put(XmRequestIdManager.CONT_ID, trackId)
            .put("currTrackId", trackId)
            .put("currAlbumId", albumId)
            .put("currPageContentType", "track")
            .put("currPageContentId", trackId) // 传 trackId
            .createTrace()
    }

    private fun configureDialogStyle() {
        dialog?.apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.apply {
                decorView.setPadding(0, 0, 0, 0)
                val lp: WindowManager.LayoutParams = attributes
                lp.width = WindowManager.LayoutParams.MATCH_PARENT
                lp.height = WindowManager.LayoutParams.WRAP_CONTENT
                attributes = lp
                setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation)
                setBackgroundDrawableResource(R.color.main_transparent)
                setGravity(Gravity.BOTTOM)
                setDimAmount(0.7f)
            }
        }
    }


    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        xmRequestId = XmRequestIdManager.getInstance(getContext()).getRequestId()
        initUi()
    }

    private fun trace(item: String) {
        // 新声音播放页-定时功能提醒弹窗  点击事件
        val trackId = playingSoundInfo?.trackInfo?.trackId?.toString()?: ""
        val albumId = playingSoundInfo?.trackInfo?.albumId?.toString()?: ""

        XMTraceApi.Trace()
            .click(67147) // 用户点击时上报
            .put("currPage", "newPlay")
            .put("xmRequestId", xmRequestId)
            .put("contentType","alarm")
            .put(XmRequestIdManager.CONT_ID, trackId)
            .put("currTrackId", trackId)
            .put("currAlbumId", albumId)
            .put("currPageContentType", "track")
            .put("currPageContentId", albumId) // 传 trackId
            .put("Item", item)//"播完本集 | 关闭 | 30 分钟 | 60 分钟 |  自定义定时关闭设置")
            .createTrace()
    }

    private fun initUi() {
        val radius = 12.dpFloat
        findViewById(R.id.main_sleep_root_view).apply {
            setOutlineProvider(object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, radius)
                }
            })
            setClipToOutline(true)
        }
        findViewById(R.id.main_sleep_btn).setOnClickListener {
            trace("播完本集")
            PlanTerminalNewDialog.setTerminateWithType(PlanTerminateManagerForPlay.TIMER_CURRENT)
            dismiss()
        }

        findViewById(R.id.main_iv_close).setOnClickListener {
            trace("关闭")
            dismiss()
        }

        val showSetting = {
            playFragment?.openTimingOffPanel()
        }

        findViewById(R.id.main_custom_setting).setOnClickListener {
            trace("自定义定时关闭设置")
            dismiss()
            showSetting()
        }

        findViewById(R.id.main_tv_custom_setting).setOnClickListener {
            trace("自定义定时关闭设置")
            dismiss()
            showSetting()
        }
    }
}
