package com.ximalaya.ting.android.main.playpage.playy.manager

import androidx.collection.ArrayMap
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.model.game.GameModel
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmutil.Logger

/**
 * <AUTHOR>
 * @date 2023/10/12 19:47
 */
class GameCardShowABManager {
}

private const val TAG = "GameCardShowABManager"

fun isShowGameCardPlanANoLog(): <PERSON><PERSON>an {
    var planAB = ABTest.getStringWithoutLog("game_card_type", "0")
    Logger.i(TAG, "isShowGameCardPlanB=$planAB")
    return planAB == "0"
}

fun triggerLogForGameCard() {
    ABTest.triggerLog("game_card_type")
    Logger.i(TAG, "triggerLogForGameCard")
}

fun queryGameCardDataForPlanB() {
    val params = ArrayMap<String, String>()
    params[""] = ""
    CommonRequestM.userRecentPlayCardInfo(params, object : IDataCallBack<List<GameModel>> {
        override fun onSuccess(data: List<GameModel>?) {
        }

        override fun onError(code: Int, message: String?) {

        }
    })
}