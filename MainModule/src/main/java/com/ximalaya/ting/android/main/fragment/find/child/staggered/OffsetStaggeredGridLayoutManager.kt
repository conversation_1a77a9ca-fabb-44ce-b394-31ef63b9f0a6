package com.ximalaya.ting.android.main.fragment.find.child.staggered

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ximalaya.ting.android.main.view.PullToRefreshStaggeredRecyclerView
import kotlin.math.min

/**
 * Created by changle.fang on 2021/11/22.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
class OffsetStaggeredGridLayoutManager : StaggeredGridLayoutManager {

    private var mHeightMap = hashMapOf<Int, Int>()
    var mHeaderHeight = 0
    var mRecyclerView: PullToRefreshStaggeredRecyclerView? = null

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes) {}
    constructor(spanCount: Int, orientation: Int) : super(spanCount, orientation) {}

    override fun onLayoutCompleted(state: RecyclerView.State?) {
        super.onLayoutCompleted(state)
        for (i in 0..childCount) {
            val view = getChildAt(i)
            var firstVisiblePosition = mRecyclerView?.findFirstVisiblePosition() ?: 0
            mHeightMap[i + firstVisiblePosition] = view?.height ?: 0
        }
    }

    fun calculateHeaderHeight(firstBodyPos: Int): Int {
        mHeaderHeight = 0
        val endPos = min(firstBodyPos, childCount)
        for (i in 0 until endPos) {
            mHeaderHeight += mHeightMap.get(i) ?: 0
        }
        return mHeaderHeight
    }

    override fun computeVerticalScrollOffset(state: RecyclerView.State): Int {
        return super.computeVerticalScrollOffset(state)
    }
}