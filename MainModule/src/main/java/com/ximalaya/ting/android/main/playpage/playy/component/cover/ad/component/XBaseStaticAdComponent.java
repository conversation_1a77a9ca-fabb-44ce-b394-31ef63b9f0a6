package com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.component;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.RectF;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.ximalaya.ting.android.ad.manager.HostCommonRtbSortUtil;
import com.ximalaya.ting.android.ad.manager.RtbAdLoadManager;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.IAbstractAd;
import com.ximalaya.ting.android.ad.model.thirdad.XmNativeAd;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.manager.StatusBarManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.trace.SoundPatchAdTrace;
import com.ximalaya.ting.android.host.view.ad.XAdActionBtnView;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adModule.view.HightLightAdLayout;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.CreativeAdManager;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.IAdViewBehavior;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.interf.IPlayAdSDKRequestAdCallBack;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.manager.AdDynamicSourceLoadHelper;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.ad.manager.SourceType;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.adnew.AdShakeStyleUtil;
import com.ximalaya.ting.android.main.playpage.audioplaypage.components.cover.adnew.view.HorizontalLargeCoverFlowerAdViewNew;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.IXAdComponentProvider;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.PlayAdCachePool;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.PlayAdPreloadManager;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.YAdFlowerStyleUtil;
import com.ximalaya.ting.android.main.playpage.playy.component.cover.ad.view.XBaseStaticAdView;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.AdvertisList;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by le.xin on 2020/6/13.
 * 横版或者竖版图片类广告，基础渲染组件
 * <AUTHOR>
 * @email <EMAIL>
 */
public abstract class XBaseStaticAdComponent extends XDazzlingBaseAdComponent<XBaseStaticAdView, IAbstractAd> {

    public IAbstractAd mAbstractAd;

    public XBaseStaticAdView mHorizontalLargeCoverAdView;
    public List<HorizontalLargeCoverFlowerAdViewNew.FlutterItem> mFlutterItems = new ArrayList<>();
    public List<Bitmap> creativeBitmaps = new ArrayList<>();
    private long prepareResStartTime;

    @NonNull
    private AdDynamicSourceLoadHelper mSourceLoadHelper;

    public XBaseStaticAdComponent(@NonNull IXAdComponentProvider adComponentProvider, int viewKey, String complexTag) {
        super(adComponentProvider, viewKey, complexTag);
        mSourceLoadHelper = new AdDynamicSourceLoadHelper();
    }

    @Override
    public void onAdDataChange(@NonNull Advertis advertis, AdvertisList advertisList) {
        super.onAdDataChange(advertis, advertisList);

        mSourceLoadHelper.reset();

        boolean needRtb = false;
        if (advertisList != null) {
            if (advertis != null && advertis.isDoneRtb()){
                needRtb = false;
            } else {
                needRtb = RtbAdLoadManager.needJDRtb(advertisList.getAdvertisList()) ||
                        HostCommonRtbSortUtil.commonRtbAdEnable(advertisList.getAdvertisList());
            }
        }
        if (needRtb || AdManager.isThirdAd(advertis)) {
            mAdComponentProvider.loadThirdAd(advertis, advertisList, new IPlayAdSDKRequestAdCallBack() {
                @Override
                public void onSDKADBack(IAbstractAd abstractAd) {
                    if (abstractAd == null && advertis != null && !PlayAdPreloadManager.getInstance().checkAdRequestTimeOut()) {
                        int type = advertis.isDuringPlay() ? 2 : (advertis.isPausedRequestAd() ? 3 : 1);
                        Log.d("PlayAdCachePool", "onSDKADBack:  图片sdk获取失败");
                        abstractAd = PlayAdCachePool.get(type);
                        if (abstractAd instanceof AbstractThirdAd) {
//                            Log.d("PlayAdCachePool", "onSDKADBack: sdk 图片加载失败返回 空广告时，从缓存获取广告  广告是 == ID " + abstractAd.getAdvertis().getAdid() + " --- 这个场景是 == " + type);
                            mAdComponentProvider.onShowAdxCache((AbstractThirdAd)abstractAd);
                            SoundPatchAdTrace.reportGotCacheFinish(abstractAd.getAdvertis(), true, true);
                            return;
                        } else {
                            if (PlayAdPreloadManager.getInstance().getAdxRequestTimeoutMs() > 0) {
                                SoundPatchAdTrace.reportGotCacheFinish(null, false, true);
                            }
                        }
                    }
                    if (abstractAd == null && advertis != null) {
                        SoundPatchAdTrace.reportPrepareResourceFail(advertis);
                    }
                    onAdDataChangeHasThirdAd(advertis, advertisList, abstractAd);
                }
            });
        } else {
            SoundPatchAdTrace.requestSuccess(advertis);
            mAbstractAd = XmNativeAd.createXmNativeAdByAdvertis(advertis);

            boolean hasGif = (advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_GIF_AD
                    || advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_GIF_DANMU_AD)
                    && !TextUtils.isEmpty(advertis.getDynamicImage());

            YAdFlowerStyleUtil.prepareFlowerResource(advertis);
            prepareResource(mAbstractAd, hasGif);
        }
    }

    @Override
    public void onAdDataChangeHasThirdAd(@NonNull Advertis advertis, AdvertisList advertisList,
                                         IAbstractAd abstractAd) {
        super.onAdDataChangeHasThirdAd(advertis, advertisList, abstractAd);

        mAbstractAd = abstractAd;
        if(abstractAd != null){
            SoundPatchAdTrace.requestSuccess(advertis);
        }
        prepareResource(abstractAd, false);
    }

    private boolean sourceInCache(IAbstractAd adDataRef) {
        if (adDataRef == null) {
            return false;
        }
        String imageUr = adDataRef.getImgUrl();
        if (mHorizontalLargeCoverAdView != null) {
            return ImageManager.from(mHorizontalLargeCoverAdView.getContext()).getFromMemCache(imageUr) != null;
        }
        return false;
    }

    // 准备资源
    private void prepareResource(IAbstractAd adDataRef, boolean isGif) {
        if (adDataRef == null){
            return;
        }
        prepareResStartTime = System.currentTimeMillis();
        //当前广告是兜底缓存的物料且在缓存中时
        if (adDataRef != null && adDataRef.getAdvertis() != null && (adDataRef.getAdvertis().isAdxCacheMaterial() || adDataRef.getAdvertis().isAdxBottom())
                && sourceInCache(adDataRef)) {
            SoundPatchAdTrace.reportResourceParallel(adDataRef.getAdvertis());
            sourceReady(adDataRef);
            return;
        }
        if (PlayAdPreloadManager.getInstance().getAdxRequestTimeoutMs() > 0 && adDataRef != null && adDataRef.getAdvertis() != null) {
            Advertis advertis = adDataRef.getAdvertis();
            isGif = (advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_GIF_AD
                    || advertis.getSoundType() == Advertis.TYPE_HORIZONTAL_LARGE_GIF_DANMU_AD)
                    && !TextUtils.isEmpty(advertis.getDynamicImage());
        }
        if (adDataRef != null){
            SoundPatchAdTrace.reportResLoadStart(adDataRef.getAdvertis());
        }
        mSourceLoadHelper.sourceDownload(MainApplication.getMyApplicationContext(), adDataRef,
                isGif ? SourceType.DYNAMIC_SOURCE : SourceType.STATIC_SOURCE,
                new AdDynamicSourceLoadHelper.IPlayAdSourceState() {
                    @Override
                    public void sourceSuccess(IAbstractAd abstractAd) {
                        Advertis advertis = adDataRef.getAdvertis();
                        SoundPatchAdTrace.reportResLoadFinish(advertis,
                                System.currentTimeMillis() - prepareResStartTime, SoundPatchAdTrace.MSG_REQUEST_SUCCESS);
                        SoundPatchAdTrace.reportAllRequestEnd(advertis);
                        if(advertis != null
                                && advertis.getSoundType() == Advertis.TYPE_HIGHT_LIGHT_AD) {
                            loadHightCover(abstractAd, advertis);
                        } else if (AdManager.isFlowerAd(adDataRef)) {
                            YAdFlowerStyleUtil.loadFlowerAd(XBaseStaticAdComponent.this, adDataRef, mFlutterItems);
                            Logger.e("------msg", " ------222 mFlutterItems = " + mFlutterItems);
                        } else if (advertis != null && advertis.getAnimationType() != Advertis.ANIMATION_TYPE_NONE) {
                            loadCreativeAdPics(advertis, abstractAd);
                        } else {
                            sourceReady(abstractAd);
                        }
                    }

                    @Override
                    public void sourceFail(IAbstractAd abstractAd, int errorStatue) {
                        if (abstractAd != null) {
                            SoundPatchAdTrace.reportResLoadFinish(adDataRef.getAdvertis(),
                                    System.currentTimeMillis() - prepareResStartTime, String.valueOf(errorStatue));
                            SoundPatchAdTrace.reportAllRequestEnd(abstractAd.getAdvertis(), SoundPatchAdTrace.ERROR_MSG_SOURCE_ERROR);
                        }
                        sourceErr(abstractAd, errorStatue);
                    }
                });
    }

    private void loadCreativeAdPics(Advertis advertis, IAbstractAd abstractAd) {
        CreativeAdManager.loadCreativePics(advertis, new CreativeAdManager.PicLoadCallBack() {
            @Override
            public void loadSuccess(List<Bitmap> bitmaps) {
                if (adDataIsChanged(abstractAd)
                        || !mAdComponentProvider.getAdEngineProvider().canUpdateUi()) {
                    return;
                }
                creativeBitmaps = bitmaps;
                sourceReady(abstractAd);
            }
        });
    }

    private void loadHightCover(IAbstractAd abstractAd, Advertis advertis) {
        ImageManager.from(MainApplication.getMyApplicationContext()).downloadBitmap(advertis.getLogoUrl(),
                new ImageManager.DisplayCallback() {
                    @Override
                    public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                        if(bitmap == null) {
                            return;
                        }

                        if (adDataIsChanged(abstractAd)
                                || !mAdComponentProvider.getAdEngineProvider().canUpdateUi()
                                || mAdComponentProvider.getAdEngineProvider().getCheckCoverVisView() == null
                                || mAdComponentProvider.getAdEngineProvider().getBaseFragment2() == null) {
                            return;
                        }

                        View switchView =
                                mAdComponentProvider.getAdEngineProvider().getCheckCoverVisView();

                        Context context =
                                MainApplication.getMyApplicationContext();
                        // 滚动超过了可视范围
                        int listViewScrollY =
                                mAdComponentProvider.getAdEngineProvider().getListViewScrollY();
                        if (switchView.getY() - BaseUtil.dp2px(context, 20) <
                                listViewScrollY) {
                            return;
                        }

                        if (advertis.getGestureStartMs() < HightLightAdLayout.ANIMATION_DURATION) {
                            advertis.setGestureStartMs(HightLightAdLayout.ANIMATION_DURATION);
                        }

                        HightLightAdLayout hightLight = new HightLightAdLayout(context);
                        hightLight.setId(R.id.main_hight_light_lay);
                        hightLight.setLayoutParams(new ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT));

                        BaseFragment2 baseFragment2 =
                                mAdComponentProvider.getAdEngineProvider().getBaseFragment2();

                        if (baseFragment2.mContainerView instanceof ViewGroup) {
                            ((ViewGroup) baseFragment2.mContainerView).addView(hightLight);
                        }

                        float statusBarHeight = (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR
                                ? BaseUtil.getStatusBarHeight(context) : 0);

                        float drawY =
                                baseFragment2.getResourcesSafe().getDimension(com.ximalaya.ting.android.host.R.dimen.host_audio_play_title_bar_height)
                                        + baseFragment2.getResourcesSafe().getDimension(R.dimen.main_audio_play_cover_top_margin_sound_title)
                                        + statusBarHeight;

                        int coverHeight =
                                mAdComponentProvider.getAdEngineProvider().getAlbumCoverHeight();

                        float orightHeight = mAdComponentProvider.getAdEngineProvider().getAdContentWidth() * 1.0f / 16 * 9 ;
                        ((HightLightAdLayout) hightLight).setData(bitmap,
                                drawY + coverHeight * 1.0f / 2, orightHeight,
                                new HightLightAdLayout.IHightLightLife() {
                                    @Override
                                    public void animtorBegin() {

                                    }

                                    @Override
                                    public void showAnimtor(RectF rectF, float rx) {
                                        if (mHorizontalLargeCoverAdView != null) {
                                            mHorizontalLargeCoverAdView.setAnimatorPercent(rectF,
                                                    rx);
                                        }
                                    }

                                    @Override
                                    public void animtorOver() {
                                        if (mAdComponentProvider.getAdEngineProvider().canUpdateUi()
                                                && mAdComponentProvider.getAdEngineProvider().getBaseFragment2() != null
                                                && mAdComponentProvider.getAdEngineProvider().getBaseFragment2().mContainerView instanceof ViewGroup) {
                                            ((ViewGroup) (mAdComponentProvider.getAdEngineProvider().getBaseFragment2().mContainerView)).removeView(hightLight);
                                        }
                                    }
                                }, listViewScrollY
                                , statusBarHeight + baseFragment2.getResourcesSafe().getDimension(com.ximalaya.ting.android.host.R.dimen.host_title_bar_height));

                        sourceReady(abstractAd);
                    }
                });
    }

    @Override
    public void bindViewBefore(IAbstractAd abstractAd, XBaseStaticAdView view) {
        super.bindViewBefore(abstractAd, view);
        mHorizontalLargeCoverAdView = view;
        HorizontalLargeCoverFlowerAdViewNew flowerAdView = null;
        ViewGroup fragmentRootLayout = mAdComponentProvider.getAdEngineProvider().getFragmentRootLayout();
        if (fragmentRootLayout != null) {
            flowerAdView = YAdFlowerStyleUtil.getFlowerAdViewCover(abstractAd, fragmentRootLayout, mAdComponentProvider, view, mFlutterItems, true);
        }
        List<IAdViewBehavior> behaviors;
        if (view != null && flowerAdView != null) {
            behaviors = new ArrayList<>();
            behaviors.add(flowerAdView);
            Logger.e("msg_vertical_video", " ------ 撒花");
        } else {
            Logger.d("msg_vertical_video", "------ 摇一摇");
            behaviors = AdShakeStyleUtil.getAdShakeCover(abstractAd, view, true, false);
        }
        view.setOtherBindData(mSourceLoadHelper.isLoadedGif(), mSourceLoadHelper.isGifSource(), behaviors, creativeBitmaps);
    }

    @Override
    public void hide(boolean autoHide) {
        if(mHorizontalLargeCoverAdView != null
                && mHorizontalLargeCoverAdView.isTouchAding()
                && !adDataIsChanged(mAbstractAd)) {
            return;
        }

        super.hide(autoHide);

        mSourceLoadHelper.reset();
    }

    @Override
    XAdActionBtnView getAdActionBtnView() {
        if (mView != null) {
            return mView.getAdActionBtnView();
        }

        return null;
    }

    @Override
    public boolean showing() {

        BaseFragment2 baseFragment2 =
                mAdComponentProvider.getAdEngineProvider().getBaseFragment2();

        // 如果高光动画正在执行中也算展示中
        if (baseFragment2.mContainerView instanceof ViewGroup) {
            View view = ((ViewGroup) baseFragment2.mContainerView).findViewById(R.id.main_hight_light_lay);
            if(view != null) {
                return true;
            }
        }

        return super.showing();
    }

    public abstract XBaseStaticAdView getView(Context context, IAbstractAd abstractAd);

}