package com.ximalaya.ting.android.main.util.other;

import android.app.Activity;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.data.model.share.ShareVipGiftCardInfoModel;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.share.ICustomShareContentType;
import com.ximalaya.ting.android.host.manager.share.ShareConstants;
import com.ximalaya.ting.android.host.manager.share.ShareDialog;
import com.ximalaya.ting.android.host.manager.share.ShareManager;
import com.ximalaya.ting.android.host.manager.share.SharePanelDialog;
import com.ximalaya.ting.android.host.manager.share.ShareTraceUtilKt;
import com.ximalaya.ting.android.host.manager.share.ShareViewNew;
import com.ximalaya.ting.android.host.manager.share.ShareWrapContentModel;
import com.ximalaya.ting.android.host.manager.share.model.ChildShareDataModel;
import com.ximalaya.ting.android.host.manager.share.model.GoldenSentenceShareData;
import com.ximalaya.ting.android.host.manager.share.panel.SharePanelType;
import com.ximalaya.ting.android.host.model.ad.ShareAdRequestParams;
import com.ximalaya.ting.android.host.model.album.AlbumListenNote;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayPageBusinessInfo;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.share.ShareContentModel;
import com.ximalaya.ting.android.host.model.share.ShareUbtData;
import com.ximalaya.ting.android.host.model.share.SimpleShareData;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.share.manager.ShareDialogDataManager;
import com.ximalaya.ting.android.host.share.ui.IShareDialog;
import com.ximalaya.ting.android.host.util.AlbumColorUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.fragment.share.ai.ShareAiPosterFragment;
import com.ximalaya.ting.android.main.model.pay.RedEnvelope;
import com.ximalaya.ting.android.main.playpage.internalservice.IXPlayFragmentService;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageInternalServiceManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageMinorDataManager;
import com.ximalaya.ting.android.main.playpage.manager.PlayShareDialogAbManager;
import com.ximalaya.ting.android.main.share.manager.PLCShareManager;
import com.ximalaya.ting.android.main.share.manager.ShareManagerInMain;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.shareservice.AbstractShareType;
import com.ximalaya.ting.android.shareservice.base.IShareDstType;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * main module 中的分享集中从此分发 避免重复代码
 * Created by luhang on 2017/9/11.
 *
 * <AUTHOR>
 */

public class ShareUtilsInMain {

    private static void shareAlbum(
            Activity activity,
            ShareWrapContentModel contentModel,
            ShareManager.Callback callback) {
        if (contentModel.sharePanelType > 0 && !contentModel.currPage.isEmpty()) {
            if (contentModel.getAlbumModel() != null) {
                contentModel.panelColor = AlbumColorUtil.getColorByAlbumId(contentModel.getAlbumModel().getId());
            }
            new ShareManager(activity, contentModel, callback).showSharePanelDialog();
        }
    }

    /**
     * 售前页分享付费专辑
     *
     * @param activity
     * @param album
     * @param currPage
     */
    public static void shareAlbum(Activity activity, AlbumM album, String currPage, ShareManager.Callback callback) {
        if (album == null) {
            return;
        }
        if ((album.getStatus() == 2)) {
            CustomToast.showFailToast(R.string.main_album_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.FUNCTION_4, currPage,
                album.isCpsProductExist() ? ICustomShareContentType.SHARE_TYPE_ALBUM_EARN : ICustomShareContentType.SHARE_TYPE_ALBUM);
        contentModel.setAlbumModel(album);
        contentModel.isPaid = true;
        contentModel.showFamilyInfo = true;
        new ShareManager(activity, contentModel, callback).showSharePanelDialog();
    }

    /**
     * main bundle 分享免费带广告专辑
     */
    public static void shareAlbum(Activity activity, AlbumM album, int shareType) {
        shareAlbum(activity, album, shareType, 0);
    }

    /**
     * 分享免费专辑,额外提供subType
     */
    public static void shareAlbum(Activity activity, AlbumM album, int shareType, int subType) {
        if ((album != null && album.getStatus() == 2)) {
            CustomToast.showFailToast(R.string.main_album_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.setAlbumModel(album);
        contentModel.specialSubType = subType;
        if (shareType == ICustomShareContentType.SHARE_TYPE_MY_ALBUM) {
            if (album != null) {
                contentModel.ubtData = new ShareUbtData("myWorks", String.valueOf(album.getId()), "专辑", "");
            }
            contentModel.currPage = "myWorks";
            contentModel.sharePanelType = SharePanelType.FUNCTION_7;
            contentModel.isShowPosterHead = true;
        }
        if (album != null) {
            contentModel.mShareAdRequestParams =
                    new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_ALBUM, album.getId() + "");
        }
        shareAlbum(activity, contentModel, null);
    }

    /**
     * 分享专辑页免费专辑
     * AlbumTitleView/AlbumOperationPanelDialog
     */
    public static void shareAlbum(Activity activity, AlbumM album, int shareType, boolean hasFamilyInfo, ShareManager.Callback callback) {
        if ((album != null && album.getStatus() == 2)) {
            ShareTraceUtilKt.traceDebugAlbumClick("该专辑已下架");
            CustomToast.showFailToast(R.string.main_album_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.FUNCTION_4, "album", shareType);
        contentModel.setAlbumModel(album);
        contentModel.mShowSuccessDialog = false;
        contentModel.showFamilyInfo = true;
        contentModel.hasFamilyInfo = hasFamilyInfo;
        contentModel.isFromAlbumPage = true;
        contentModel.isShowPosterHead = true;
        if (album != null && album.getAlbumLabelType() == 3) {
            contentModel.specialSubType = 1147;
        }
        if (album != null) {
            contentModel.mShareAdRequestParams =
                    new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_ALBUM, album.getId() + "");
        }
        shareAlbum(activity, contentModel, callback);
    }

    /**
     * 分享专辑(听单)
     *
     * @param activity
     * @param album
     * @param shareType
     */
    public static ShareDialog shareAlbumListenNote(Activity activity, AlbumM album, AlbumListenNote albumListenNote, int shareType) {
        if ((album != null && album.getStatus() == 2)) {
            CustomToast.showFailToast(R.string.main_album_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.setAlbumModel(album);
        contentModel.albumListenNote = albumListenNote;
        return new ShareManager(activity, contentModel).showShareDialog();
    }

    public static View getShareAlbumView(Activity activity, AlbumM album, int shareType, ShareManager.Callback callback, int subType) {
        if (album == null) {
            return null;
        }
        if (album.getStatus() == 2 || !album.isPublic()) {
            return null;
        }
        String currPage;
        if (album.isPaid()) {
            if (album.isAuthorized()) {
                currPage = "afterSaleDetailsPage";
            } else {
                currPage = "preSaleDetailsPage";
            }
        } else {
            currPage = "album";
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.BASE_1, currPage, shareType);
        contentModel.setAlbumModel(album);
        contentModel.mShowSuccessDialog = true;
        contentModel.panelColor = AlbumColorUtil.getColorByAlbumId(contentModel.getAlbumModel().getId());
        contentModel.dstBgColor = ContextCompat.getColor(activity, R.color.host_color_eeeeee_0fFFFFFF);
        if (subType > 0) {
            contentModel.specialSubType = subType;
        }
        if (3 == album.getAlbumLabelType()) {
            contentModel.specialSubType = 1148;
        }
        return new ShareManager(activity, contentModel, callback).getShareView(activity);
    }

    public static View getShareTrackView(Activity activity, Track track, int shareType, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            return null;
        }

        if (!(track instanceof TrackM)) {
            return null;
        }

        if (!((TrackM) track).isPublic()) {
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.soundInfo = track;
        contentModel.mShowSuccessDialog = true;
        return new ShareManager(activity, contentModel, callback).getShareView(activity);
    }

    public static View getShareContentForFeedPlayFragment(Activity activity, Track track, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            return null;
        }

        if (track == null) {
            return null;
        }

        if (!(track).isPublic()) {
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_TRACK);
        contentModel.soundInfo = track;
        contentModel.mShowSuccessDialog = true;
        return new ShareManager(activity, contentModel, callback).getFeedPlayShareView(activity);
    }

    public static View getSharePanelContentForFeedPlayFragment(Activity activity, Track track, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            return null;
        }

        if (track == null) {
            return null;
        }

        if (!(track).isPublic()) {
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.BASE_1, "today_news", ICustomShareContentType.SHARE_TYPE_TRACK);
        contentModel.soundInfo = track;
        contentModel.mShowSuccessDialog = true;
        return new ShareManager(activity, contentModel, callback).getFeedPlaySharePanelView(activity);
    }

    /**
     * 分享播放页视频
     */
    public static SharePanelDialog shareVideoTrack(Activity activity, Track track, String currPage, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            ShareTraceUtilKt.traceDebugTrackClick("该声音已下架");
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.FUNCTION_5,
                currPage, ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY);
        contentModel.soundInfo = track;
        IXPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IXPlayFragmentService.class);
        if (service != null && !TextUtils.isEmpty(service.fullMode())) {
            contentModel.fullScreenMode = service.fullMode();
            contentModel.trackForm = service.isVideoMode() ? "video" : "track";
        }
        return new ShareManager(activity, contentModel, callback).showSharePanelDialog();
    }

    /**
     * 视频 ppt横屏分享
     */
    public static SharePanelDialog shareVideoPPTLandscapeTrack(boolean isVideoLandscape, Activity activity, Track track, String currPage, String trackForm, String fullScreenMode, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            ShareTraceUtilKt.traceDebugTrackClick("该声音已下架");
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.FUNCTION_5,
                currPage, ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY);
        contentModel.soundInfo = track;
        contentModel.trackForm = trackForm;
        contentModel.fullScreenMode = fullScreenMode;
        return new ShareManager(activity, contentModel, callback, isVideoLandscape).showSharePanelDialog();
    }

    /**
     * 分享声音
     *
     * @param activity
     * @param track
     * @param shareType
     */
    @Deprecated
    public static ShareDialog shareTrack(Activity activity, Track track, int shareType, int columnNumber) {
        return shareTrack(activity, track, shareType, columnNumber, null);
    }

    /**
     * 替代方法 {@link #shareTrackV2(Activity, Track, int, String, ShareManager.Callback)}
     */
    @Deprecated
    public static ShareDialog shareTrack(Activity activity, Track track, int shareType, int columnNumber, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.soundInfo = track;
        return new ShareManager(activity, contentModel, callback).showShareDialog(columnNumber);
    }

    public static void shareTrackV2(Activity activity, Track track, int shareType, String currPage, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(SharePanelType.FUNCTION_1, currPage, shareType);
        contentModel.soundInfo = track;
        new ShareManager(activity, contentModel, callback).showSharePanelDialog();
    }

    public static ShareDialog shareTrack(Activity activity, Track track, int shareType, String[] shareChannels, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.soundInfo = track;
        contentModel.shareDstNames = shareChannels;
        return new ShareManager(activity, contentModel, callback).showShareDialog(4);
    }

    public static SharePanelDialog shareTrackForPlayFragmentNewPanel(Activity activity, Track track, String currPage, ChildShareDataModel childShareData,
                                                                     int shareType, int sharePanelType, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(
                sharePanelType, currPage, shareType);
        contentModel.soundInfo = track;
        contentModel.mShowSuccessDialog = false;
        contentModel.showFamilyInfo = true;
        contentModel.isFromPlayPage = true;
//        contentModel.isShowPosterHead = true;
        if (childShareData != null) {
            contentModel.childShareData = childShareData;
        }
        if (track != null && PlayPageMinorDataManager.getInstance().canShowKachaEntry(track.getDataId())) {
            contentModel.fromPage = "PlayFragment";
        }

        if (track != null) {
            contentModel.mShareAdRequestParams =
                    new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_SOUND,
                            track.getDataId() + "");
        }
        return new ShareManager(activity, contentModel, callback).showSharePanelDialog();
    }

    private static ShareDialog shareTrackForPlayFragment(Activity activity, Track track, ChildShareDataModel childShareData,
                                                         int shareType, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.soundInfo = track;
        contentModel.mShowSuccessDialog = false;
        contentModel.showFamilyInfo = true;
        contentModel.isFromPlayPage = true;
        if (childShareData != null) {
            contentModel.childShareData = childShareData;
        }
        if (track != null && PlayPageMinorDataManager.getInstance().canShowKachaEntry(track.getDataId())) {
            contentModel.fromPage = "PlayFragment";
        }

        if (track != null) {
            contentModel.mShareAdRequestParams =
                    new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_SOUND,
                            track.getDataId() + "");
        }
        return new ShareManager(activity, contentModel, callback).showShareDialog(4);
    }

    public static ShareDialog shareTrackForPlayFragment(Activity activity, Track track, int shareType, ShareManager.Callback callback) {
        return shareTrackForPlayFragment(activity, track, null, shareType, callback);
    }

    public static IShareDialog shareTrackForPlayFragmentNew(Activity activity, Track track, int shareType, ShareManager.Callback callback, boolean isPoster) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.soundInfo = track;
        contentModel.mShowSuccessDialog = true;
        ABTest.triggerLog(CConstants.Group_toc.ITEM_SHARE_PANNEL);
        return new ShareManager(activity, contentModel, callback).showShareDialogNew(isPoster);
    }

    // 分享主播自己的声音
    public static IShareDialog shareAnchorTrackForPlayFragmentNew(Activity activity, Track track, int shareType, ShareManager.Callback callback, int shareEntrance) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.soundInfo = track;
        contentModel.showSuccessToastDirectly = true;
        return new ShareManagerInMain(activity, contentModel, callback).showSharePosterDialogForAnchor(shareEntrance);
    }

    public static void shareAnchorTrackUseH5(Track track, String link) {
        if (track == null || TextUtils.isEmpty(link)) {
            return;
        }

        String realLink = Uri.parse(link).buildUpon().appendQueryParameter("track_id", String.valueOf(track.getDataId())).build().toString();
        Activity activity = MainApplication.getMainActivity();
        if (activity instanceof MainActivity && !TextUtils.isEmpty(realLink)) {
            NativeHybridFragment.start((MainActivity) activity, realLink, true);
        }
    }

    public static ShareDialog shareTrack(Activity activity, Track track, ShareWrapContentModel model) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        return new ShareManager(activity, model).showShareDialog(4);
    }

    public static void shareVideo(Activity activity, Track track) {
        ShareWrapContentModel model = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY);
        model.trackId = track.getDataId();
        model.soundInfo = track;
        new ShareManager(activity, model).showShareDialog(4);
    }

    public static void shareDub(Activity activity, Track track) {
        ShareWrapContentModel model = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_TRACK_DUB);
        model.trackId = track.getDataId();
        model.soundInfo = track;
        model.picUrl = track.getCoverUrlMiddle();
        model.isPictureVideo = true;
        new ShareManager(activity, model).showShareDialog(4);
    }

    public static ShareDialog shareTrack(Activity activity, Track track, ShareWrapContentModel model, ShareManager.Callback callback) {
        return shareTrack(activity, track, true, model, callback);
    }

    public static ShareDialog shareTrack(Activity activity, Track track,
                                         boolean needSortItem, ShareWrapContentModel model, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return null;
        }
        ShareManager shareManager = new ShareManager(activity, model, callback);
        shareManager.setNeedSortItem(needSortItem);
        return shareManager.showShareDialog(4);
    }

    public static ShareDialog shareTrack(Activity activity, Track track, int shareType) {
        return shareTrack(activity, track, shareType, 4, null);
    }

    public static void showShareDubbingDialog(Activity activity, Track track,
                                              boolean needSortItem, ShareWrapContentModel model, ShareManager.Callback callback) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return;
        }
        ShareManager shareManager = new ShareManager(activity, model, callback);
        shareManager.setNeedSortItem(needSortItem);
        shareManager.showSharePanelDialog();
    }

    /**
     * 知道分享目的地 不需要弹框萱蕚分享目的地
     *
     * @param activity
     * @param track
     * @param dstType
     * @param shareType
     */
    public static void shareTrack(Activity activity, Track track, String dstType, int shareType) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType, dstType);
        contentModel.soundInfo = track;
        new ShareManager(activity, contentModel).share();
    }

    /**
     * 方法功能同shareTrack(Activity activity, Track track, String dstType, int shareType)，
     * 只是内部不发送埋点信息
     */
    public static void shareTrackWithoutXdcs(Activity activity, Track track, String dstType, int shareType) {
        if (track != null && track.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType, dstType);
        contentModel.soundInfo = track;
        new ShareManager(activity, contentModel, false).share();
    }

    /**
     * 分享 红包
     *
     * @param activity
     * @param envelope
     * @param ShareType
     */
    public static void shareRedEnvelope(Activity activity, RedEnvelope envelope, int ShareType) {
        ShareWrapContentModel contentModel = new ShareWrapContentModel(ShareType);
        contentModel.redEnvelopeId = envelope.getRedEnvelopeId();
        new ShareManager(activity, contentModel).showShareDialog();
    }

    /**
     * 分享 H5
     *
     * @param activity
     * @param simpleShareData
     * @param shareType
     */
    public static void shareH5(Activity activity, SimpleShareData simpleShareData, int shareType) {
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.simpleShareData = simpleShareData;
        new ShareManager(activity, contentModel).showShareDialog();
    }

    public static void shareH5(Activity activity, SimpleShareData simpleShareData, String dstName, int shareType) {
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        contentModel.simpleShareData = simpleShareData;
        contentModel.shareDstName = dstName;
        //new ShareManager(activity, contentModel).showShareDialog();
        new ShareManager(activity, contentModel).getShareContent(contentModel);
    }

    /**
     * 已知分享目的地的分享
     *
     * @param activity
     * @param dstName
     * @param shareType
     */
    public static void shareH5(Activity activity, ShareContentModel shareContentModel, String dstName, int shareType) {
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType, dstName);
        shareContentModel.shareFrom = shareType;
        shareContentModel.thirdPartyName = dstName;
        new ShareManager(activity, contentModel).resultShareContent(shareContentModel, contentModel);
    }

    public static void shareImageToQQ(Activity activity, String path, int shareType) {
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType, IShareDstType.SHARE_TYPE_QQ);
        contentModel.picUrl = path;
        ShareContentModel shareContentModel = new ShareContentModel();
        shareContentModel.thirdPartyName = contentModel.shareDstName;
        shareContentModel.shareFrom = contentModel.customShareType;
        shareContentModel.picUrl = path;
        new ShareManager(activity, contentModel).shareContentToQQDirectly(shareContentModel);
    }

    public static void shareImageToWx(Activity activity, String dstName, Bitmap bitmap, int shareType) {
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType, dstName);
        contentModel.bitmap = bitmap;
        ShareContentModel shareContentModel = new ShareContentModel();
        shareContentModel.thirdPartyName = dstName;
        shareContentModel.shareFrom = shareType;
        new ShareManager(activity, contentModel).shareBitmapToWxDirectly(shareContentModel);
    }

    public static void shareImageToWx(Activity activity, ShareContentModel shareContentModel
            , ShareWrapContentModel shareWrapContentModel) {
        new ShareManager(activity, shareWrapContentModel).shareBitmapToWxDirectly(shareContentModel);
    }

    public static void shareImageToDouYin(Activity activity, ShareContentModel shareContentModel
            , ShareWrapContentModel shareWrapContentModel) {
        new ShareManager(activity, shareWrapContentModel).shareBitmapToDouYinDirectly(shareContentModel);
    }

    public static void shareImageToXhs(Activity activity, ShareContentModel shareContentModel
            , ShareWrapContentModel shareWrapContentModel) {
        new ShareManager(activity, shareWrapContentModel).shareBitmapToXhsDirectly(shareContentModel);
    }


    /**
     * @param track   待分享的有视频的声音
     * @param dstName 查看 IShareDstType
     */
    public static void shareVideoToDst(Activity activity, Track track, String dstName) {
        ShareWrapContentModel shareWrapContentModel = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_VIDEO_PLAY, dstName);
        shareWrapContentModel.trackId = track.getDataId();
        shareWrapContentModel.soundInfo = track;

        new ShareManager(activity, shareWrapContentModel).getShareContent(shareWrapContentModel);
    }

    /**
     * @param track   待分享的配音秀声音
     * @param dstName 查看 IShareDstType
     */
    public static void shareDubToDst(Activity activity, Track track, String dstName) {
        ShareWrapContentModel shareWrapContentModel = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_TRACK_DUB, dstName);
        shareWrapContentModel.trackId = track.getDataId();
        shareWrapContentModel.soundInfo = track;

        new ShareManager(activity, shareWrapContentModel).getShareContent(shareWrapContentModel);
    }

    /**
     * 获取群组分享的文案
     */
    public static void getXMGroupShareContent(final Activity activity,
                                              final int shareType,
                                              long resId,
                                              final ShareManager.IGetShareContentCallback callback) {
        if (!NetworkType.isConnectTONetWork(activity)) {
            CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_network_error);
            return;
        }
        if (resId <= 0) {
            return;
        }

        final String dstName = ShareConstants.SHARE_TYPE_XM_GROUP;
        Map<String, String> apiParams = new HashMap<>();
        apiParams.put("tpName", dstName);

        switch (shareType) {
            case ICustomShareContentType.SHARE_TYPE_TRACK:
            case ICustomShareContentType.SHARE_TYPE_TRACK_SALE:
                apiParams.put("srcId", resId + "");
                apiParams.put("srcType", "7");
                apiParams.put("subType", "1015");
                break;
            case ICustomShareContentType.SHARE_TYPE_ALBUM:
                apiParams.put("srcId", resId + "");
                apiParams.put("srcType", "6");
                apiParams.put("subType", "1010");
                break;
            default:
                break;
        }
        CommonRequestM.getShareContent(apiParams, new IDataCallBack<ShareContentModel>() {
            @Override
            public void onSuccess(@Nullable ShareContentModel object) {
                if (activity == null || activity.isFinishing()) {
                    return;
                }

                if (callback != null) {
                    callback.onSuccess(object);
                }
            }

            @Override
            public void onError(int code, String message) {
                if (callback != null) {
                    callback.onFail(code, message);
                }
                Logger.d("getXMGroupShareContent", "ErrCode: " + code + ", ErrMsg:" + message);
            }
        });
    }

    public static View getShareViewNew(Activity activity, int shareType,
                                       ShareViewNew.Builder builder,
                                       boolean needLoadShareContent,
                                       ShareManager.Callback callback) {
        if (activity == null) {
            return null;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
        ArrayList<AbstractShareType> shareDstTypes = ShareDialogDataManager.INSTANCE.getCommonShareTypeListForPoster();
        return new ShareManager(activity, contentModel, callback).getShareViewNew(shareDstTypes, builder, needLoadShareContent);
    }

    public static void shareMyTrack(Activity activity, Track track, int shareType, int subType) {
        if (track == null) {
            return;
        }
        if (track.getTrackStatus() == 2) {
            CustomToast.showFailToast("该声音已下架");
            return;
        }
        if (PlayShareDialogAbManager.INSTANCE.usePLCShareDialog(track.getUid())) {
            if (PlayShareDialogAbManager.INSTANCE.checkShowPLCDialog()) {
                ShareUtilsInMain.shareAnchorTrackUseH5(track, PlayShareDialogAbManager.INSTANCE.getMPLCLink());
            } else {
                ShareUtilsInMain.shareAnchorTrackForPlayFragmentNew(activity, track, shareType, null, PLCShareManager.SHARE_ENTRANCE_MY_WORKS);
            }
        } else {
            ShareWrapContentModel contentModel = new ShareWrapContentModel(shareType);
            if (shareType == ICustomShareContentType.SHARE_TYPE_MY_TRACK) {
                contentModel.currPage = "myWorks";
                contentModel.ubtData = new ShareUbtData("myWorks", String.valueOf(track.getDataId()), "声音", "");
                contentModel.sharePanelType = SharePanelType.FUNCTION_6;
                contentModel.disableDouYinXHS = true;
                if (subType == 1108) {
                    contentModel.isShowPosterHead = true;
                }
            }
            contentModel.soundInfo = track;
            contentModel.paramSubType = subType;
            new ShareManager(activity, contentModel, null).showSharePanelDialog();
        }
    }

    public static void openPLCShareDialogFromITing(Activity activity, long trackId) {
        if (activity == null || trackId <= 0) {
            return;
        }

        Track track = new Track();
        track.setDataId(trackId);
        ShareUtilsInMain.shareAnchorTrackForPlayFragmentNew(activity, track, ICustomShareContentType.SHARE_TYPE_TRACK, null, PLCShareManager.SHARE_ENTRANCE_PLAY);
    }

    public static void shareRadio(Activity activity, long radioId,
                                  String radioName, String coverUrl, ShareManager.Callback clickCallback) {

        if (activity == null || radioId <= 0) {
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(ICustomShareContentType.SHARE_TYPE_RADIO);
        contentModel.radioId = radioId;
        contentModel.title = radioName;
        contentModel.picUrl = coverUrl;
        new ShareManager(activity, contentModel, clickCallback).showShareDialog();
    }

    public static void shareGoldenSentenceForPlayPage(PlayingSoundInfo soundInfo, GoldenSentenceShareData goldenSentence) {
        if (soundInfo == null || !(BaseApplication.getMainActivity() instanceof MainActivity)
                || goldenSentence == null || !goldenSentence.valid()) {
            CustomToast.showFailToast("分享失败");
            return;
        }
        TrackM trackM = soundInfo.trackInfo2TrackM();
        if (trackM == null) {
            return;
        }
        ShareAiPosterFragment aiPosterFragment = ShareAiPosterFragment.Companion.newInstance(
                IMainFunctionAction.FragmentConstants.TYPE_FREE_TRACK,
                trackM.getDataId(),
                trackM.getTrackTitle(),
                trackM.getValidCover(),
                1015, "", goldenSentence, null);
        ((MainActivity) BaseApplication.getMainActivity()).startFragment(aiPosterFragment);
    }

    public static void shareMilestoneForPlayPage(PlayingSoundInfo soundInfo,
                                                 PlayPageBusinessInfo.EmotionValueShare emotionValueShare) {
        if (soundInfo == null || !(BaseApplication.getMainActivity() instanceof MainActivity)) {
            CustomToast.showFailToast("分享失败");
            return;
        }
        TrackM trackM = soundInfo.trackInfo2TrackM();
        if (trackM == null || emotionValueShare == null) {
            return;
        }
        ShareVipGiftCardInfoModel shareVipGiftCardInfoModel = new ShareVipGiftCardInfoModel(
                emotionValueShare.redeemGiftCount,
                emotionValueShare.totalGiftCount,
                emotionValueShare.vipLevel,
                "",
                emotionValueShare.sign,
                emotionValueShare.purchaseUserId,
                emotionValueShare.tradeOrderNo);

        ShareAiPosterFragment aiPosterFragment = ShareAiPosterFragment.Companion.newInstance(
                trackM.isPaid() ? IMainFunctionAction.FragmentConstants.TYPE_PAID_TRACK : IMainFunctionAction.FragmentConstants.TYPE_FREE_TRACK,
                trackM.getDataId(),
                trackM.getTrackTitle(),
                trackM.getValidCover(),
                1015, "", null, shareVipGiftCardInfoModel, emotionValueShare);
        ((MainActivity) BaseApplication.getMainActivity()).startFragment(aiPosterFragment);
    }

    public static void shareMilestoneForAlbumPage(AlbumM albumM,
                                                 PlayPageBusinessInfo.EmotionValueShare emotionValueShare) {
        if (emotionValueShare == null || albumM == null || !(BaseApplication.getMainActivity() instanceof MainActivity)) {
            CustomToast.showFailToast("分享失败");
            return;
        }
        ShareVipGiftCardInfoModel shareVipGiftCardInfoModel = new ShareVipGiftCardInfoModel(
                emotionValueShare.redeemGiftCount,
                emotionValueShare.totalGiftCount,
                emotionValueShare.vipLevel,
                "",
                emotionValueShare.sign,
                emotionValueShare.purchaseUserId,
                emotionValueShare.tradeOrderNo);

        ShareAiPosterFragment aiPosterFragment = ShareAiPosterFragment.Companion.newInstance(
                albumM.isPaid() ? IMainFunctionAction.FragmentConstants.TYPE_PAID_ALBUM : IMainFunctionAction.FragmentConstants.TYPE_FREE_ALBUM,
                albumM.getId(),
                albumM.getAlbumTitle(),
                albumM.getValidCover(),
                1015, "", null, shareVipGiftCardInfoModel, emotionValueShare);
        ((MainActivity) BaseApplication.getMainActivity()).startFragment(aiPosterFragment);
    }

    /**
     * 半屏播放页专用统一分享
     */
    public static void showPlayPageShareDialog(Activity mActivity,
                                               PlayingSoundInfo soundInfo,
                                               ShareManager.Callback callback) {
        if (soundInfo == null || mActivity == null) {
            return;
        }
        TrackM trackM = soundInfo.trackInfo2TrackM();
        if (trackM == null) {
            return;
        }

        //知识红包
//        boolean showKnowledgeGiftShareForDebug = ToolUtil.getDebugSystemProperty("debug.shixin.share", "-1").equals("1");
//        boolean showKnowledgeSharePackage = showKnowledgeGiftShareForDebug ||
//                soundInfo.otherInfo != null && soundInfo.otherInfo.showKnowledgeSharePackage;

        boolean isSaleTrack = false;
        if (soundInfo.albumInfo != null && soundInfo.albumInfo.isCpsProductExist) {
            trackM.setCpsProductCommission(soundInfo.albumInfo.cpsProductCommission);
            trackM.setCpsProductExist(soundInfo.albumInfo.isCpsProductExist);
            trackM.setCpsPromotionRate(soundInfo.albumInfo.cpsPromotionRate);
            trackM.setCpsTitle(soundInfo.albumInfo.cpsTitle);
            trackM.setCpsContent(soundInfo.albumInfo.cpsContent);
            trackM.setCpsRuleDesc(soundInfo.albumInfo.cpsRuleDesc);
            trackM.setCpsRuleLink(soundInfo.albumInfo.cpsRuleLink);
            isSaleTrack = true;
        }
        if (!trackM.isPublic()) {
            CustomToast.showFailToast("私密声音不支持分享");
            ShareTraceUtilKt.traceDebugTrackClick("私密声音不支持分享");
            return;
        }

        int type = ICustomShareContentType.SHARE_TYPE_TRACK;
        if (isSaleTrack) {
            type = ICustomShareContentType.SHARE_TYPE_TRACK_SALE;
        }

        int sharePanelType = SharePanelType.FUNCTION_3;
        if (trackM.getVisibleCrowdType() == 1) {
            sharePanelType = SharePanelType.BASE_1;
        }
        if (trackM.getTrackStatus() == 2) {
            CustomToast.showFailToast(R.string.main_track_offsale_tip);
            return;
        }
        ShareWrapContentModel contentModel = new ShareWrapContentModel(
                sharePanelType, "newPlay", type);
        contentModel.soundInfo = trackM;
        contentModel.mShowSuccessDialog = false;
        contentModel.showFamilyInfo = true;
        contentModel.isShowPosterHead = true;
        contentModel.panelColor = PlayPageDataManager.getInstance().getBackgroundColor();
        contentModel.isFromPlayPage = true;
        // 播客专用subType,特殊区分
        if (trackM.isPodcastAlbum()) {
            contentModel.paramSubType = 1149;
        }
//        contentModel.showKnowledgeSharePackage = showKnowledgeSharePackage;
        if (PlayPageMinorDataManager.getInstance().canShowKachaEntry(trackM.getDataId())) {
            contentModel.fromPage = "PlayFragment";
        }
        contentModel.mShareAdRequestParams =
                new ShareAdRequestParams(AdManager.SHARE_AD_SOURCE_PAGE_SOUND,
                        trackM.getDataId() + "");

        IXPlayFragmentService service = PlayPageInternalServiceManager.getInstance().getService(IXPlayFragmentService.class);
        if (service != null && !TextUtils.isEmpty(service.fullMode())) {
            contentModel.fullScreenMode = service.fullMode();
            contentModel.trackForm = service.isVideoMode() ? "video" : "track";
        }

        contentModel.playPageColor = PlayPageDataManager.getInstance().getBackgroundColor();
        new ShareManager(mActivity, contentModel, callback).showSharePanelDialog();

//        if (!showKnowledgeSharePackage) {
//            new ShareManager(mActivity, contentModel, callback).showSharePanelDialog();
//            return;
//        }
//
//        //知识红包，先请求接口再拉起面板
//        long albumId = -1;
//        if (trackM != null && trackM.getAlbum() != null) {
//            albumId = trackM.getAlbum().getAlbumId();
//        }
//
//        CommonRequestM.getKnowledgeRedPackageInfo(albumId, new IDataCallBack<KnowledgeGiftShareInfo>() {
//            @Override
//            public void onSuccess(@Nullable KnowledgeGiftShareInfo data) {
//                if (data == null || !data.valid()) {
//                    new ShareManager(mActivity, contentModel, callback).showSharePanelDialog();
//                    return;
//                }
//                contentModel.knowledgeGiftShareInfo = data;
////                contentModel.backupCustomShareType = contentModel.customShareType;
////                contentModel.customShareType = ICustomShareContentType.SHARE_TYPE_LINK_NEW;
//
////                SimpleShareData simpleShareData = new SimpleShareData();
////                simpleShareData.setUrl(data.shareLinkUrl);
////                simpleShareData.setPicUrl(data.shareImageUrl);
////                simpleShareData.setContent(data.shareSubTitle);
////                simpleShareData.setTitle(data.shareTitle);
////
////                contentModel.simpleShareData = simpleShareData;
//
//                new ShareManager(mActivity, contentModel, callback).showSharePanelDialog();
//            }
//
//            @Override
//            public void onError(int code, String message) {
////                ToastManager.showToast("数据获取失败，请稍后再试 " + code + ", " + message);
//                new ShareManager(mActivity, contentModel, callback).showSharePanelDialog();
//            }
//        });
    }
}
