package com.ximalaya.ting.android.main.fragment.myspace;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.model.OutSiteWebLoginModel;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2024/1/31
 * Description：站外-->站内授权-->站外登录
 * 大致通过浏览器匹配的包名进行跳转回原app的操作，可能会有问题，在产品可接受范围内
 */
public class OutSiteWebLoginFragment extends BaseFragment2 implements View.OnClickListener {
    private boolean isFirstResume;
    private String mLoginCode;
    private String mLoginTitle;
    private String mLoginScheme;

    public static OutSiteWebLoginFragment newInstance(OutSiteWebLoginModel outSiteWebLoginModel) {
        if (outSiteWebLoginModel == null) {
            return null;
        }
        OutSiteWebLoginFragment fra = new OutSiteWebLoginFragment();
        fra.mLoginCode = outSiteWebLoginModel.getLoginCode();
        fra.mLoginTitle = outSiteWebLoginModel.getLoginTitle();
        fra.mLoginScheme = outSiteWebLoginModel.getLoginScheme();
        return fra;
    }

    public OutSiteWebLoginFragment() {
        super(true, null);
    }


    @Override
    protected String getPageLogicName() {
        return "授权登录";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        isFirstResume = true;
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(mContext);
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle("授权登录");
        findViewById(R.id.main_qrcodeLogin_login).setOnClickListener(this);
        findViewById(R.id.main_qrcodeLogin_cancel).setOnClickListener(this);
        TextView whichLogin = findViewById(R.id.main_qrcode_login_which_login);
        whichLogin.setText(mLoginTitle);
    }

    @Override
    protected void loadData() {

    }

    @Override
    public void onMyResume() {
        super.onMyResume();
        if (!isFirstResume && !UserInfoMannage.hasLogined()) {
            finishFragment();
        }
        isFirstResume = false;
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_fra_out_site_web_login;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_qrcodeLogin_title;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_qrcodeLogin_login) {
            Map<String, String> params = new HashMap();
            params.put("code", mLoginCode);
            MainCommonRequest.authCodeAuthLogin(params, new IDataCallBack<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean data) {
                    if (data != null && data) {
                        jumpBack2OriginalApp();
                    } else {
                        CustomToast.showFailToast("授权失败");
                    }
                }
                @Override
                public void onError(int code, String message) {
                    CustomToast.showFailToast(message);
                }
            });
        } else if (id == R.id.main_qrcodeLogin_cancel) {
            jumpBack2OriginalApp();
        }
    }

    private void jumpBack2OriginalApp() {
        if (TextUtils.isEmpty(mLoginScheme)) {
            finishFragment();
            return;
        }
        try {
            PackageManager packageManager = mContext.getPackageManager();
            if (mLoginScheme.equals("baidu")) {
                if (packageManager.getLaunchIntentForPackage("com.baidu.searchbox") != null) {
                    mLoginScheme = "com.baidu.searchbox";
                } else if (packageManager.getLaunchIntentForPackage("com.baidu.browser.apps") != null) {
                    mLoginScheme = "com.baidu.browser.apps";
                } else if (packageManager.getLaunchIntentForPackage("com.baidu.searchbox.lite") != null) {
                    mLoginScheme = "com.baidu.searchbox.lite";
                }
            } else if (mLoginScheme.equals("uc")) {
                if (packageManager.getLaunchIntentForPackage("com.UCMobile") != null) {
                    mLoginScheme = "com.UCMobile";
                } else if (packageManager.getLaunchIntentForPackage("com.ucmobile.lite") != null) {
                    mLoginScheme = "com.ucmobile.lite";
                }
            }
            Intent intent = packageManager.getLaunchIntentForPackage(mLoginScheme);
            if (intent == null) {
                CustomToast.showDebugFailToast("请自行回到浏览器");
            } else {
                startActivity(intent);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            finishFragment();
        }
    }
}