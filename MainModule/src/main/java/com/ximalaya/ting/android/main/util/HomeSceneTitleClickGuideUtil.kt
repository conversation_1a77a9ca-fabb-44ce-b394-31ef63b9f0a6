package com.ximalaya.ting.android.main.util

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Rect
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.TouchDelegate
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.ViewUtil
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.view.RecommendPullToRefreshStaggeredRecyclerView
import com.ximalaya.ting.android.read.utils.LogUtils
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmtrace.XMTraceApi


object HomeSceneTitleClickGuideUtil {

    private const val TAG = "HomeSceneTitleClickGuideUtil"

    private var hiddenRun: Runnable? = null

    private var isShowingGuide = false

    private var isBottomPullDownTipViewAnimal = false

    private var curHashCode = 0

    fun isBottomPullDownTipViewAnimal(isAnimal: Boolean) {
        isBottomPullDownTipViewAnimal = isAnimal
    }

    fun getShowingGuideStr(): String {
        return if (isShowingGuide) {
            "1"
        } else {
            "0"
        }
    }

    fun clickGuide(
        rootView: ViewGroup?,
        recommendItemNew: RecommendItemNew?
    ) {
        if (recommendItemNew?.item !is RecommendCommonItem) {
            return
        }
        if (hiddenRun != null) {
            HandlerManager.removeCallbacks(hiddenRun)
        }
        val commonItem = recommendItemNew.item as? RecommendCommonItem
        dismissGuide(rootView, commonItem)
        HomeSceneTitleClickGuideDataUtil.saveClick()
    }

    private fun isAllowCheck(): Boolean {
        // 出龙珠  则不显示标题和标题引导
        return !SceneDragonBallUtil.isAllowShowDragonBall()
    }

    private var isInit = false

    fun init(
        refreshView: RecommendPullToRefreshStaggeredRecyclerView?,
        adapter: RecommendFragmentStaggeredAdapter?,
        fragment: RecommendFragmentStaggered
    ) {
        if (!isAllowCheck()) {
            return
        }
//        LogUtils.d(TAG, "init()")
        if (fragment.mRecommendData == null || fragment.mRecommendData?.isLocalCache == true) {
//            LogUtils.d(TAG, "no data isLocalCache:${fragment.mRecommendData?.isLocalCache}")
            return
        }

        if (isInit) {
//            LogUtils.d(TAG, "isInit true")
            return
        }

        try {
            val localCode = fragment.mRecommendData?.hashCode() ?: 0
            if (localCode != 0 && localCode == curHashCode) {
                LogUtils.d(TAG, "hashcode equal")
                return
            }
            curHashCode = localCode
        } catch (e: Exception) {
            e.printStackTrace()
            LogUtils.d(TAG, "hashcode exception ${e.message}")
        }

//        LogUtils.d(TAG, "set isInit true")
        isInit = true

        HandlerManager.postOnUIThread {
            scrollIdle(refreshView, adapter, fragment, "init")
        }
    }

    fun scrollIdle(
        refreshView: RecommendPullToRefreshStaggeredRecyclerView?,
        adapter: RecommendFragmentStaggeredAdapter?,
        fragment: RecommendFragmentStaggered,
        from: String = "scrollIdle"
    ) {
        if (!isAllowCheck()) {
            return
        }
//        LogUtils.d(TAG, "$from -> scrollIdle()")
        if (fragment.mIsRefreshing || refreshView?.isRefreshing == true) {
            LogUtils.d(TAG, "refreshing")
            return
        }

        val activity = BaseApplication.getMainActivity() as? FragmentActivity
        if (ViewUtil.haveDialogIsShowing(activity)) {
            LogUtils.d(TAG, "dialog is showing")
            return
        }

        val layoutManager = refreshView?.refreshableView?.layoutManager
        if (layoutManager == null || adapter == null) {
            LogUtils.d(TAG, "layoutManager null or adapter null")
            return
        }

        if (!refreshView.context.checkActivity()) {
            return
        }

        val first = refreshView.findFirstVisiblePosition()
        val last = refreshView.findLastVisiblePosition()

//        LogUtils.d(TAG, "first:$first last:$last")

        for (modulePosition in first..last) {
            val listItemView = layoutManager.findViewByPosition(modulePosition) ?: continue

            if (ViewStatusUtil.getViewVisibleAreaPercent(listItemView) < 99) {
                continue
            }

            val itemData = adapter.getItem(modulePosition)

            if (itemData !is RecommendItemNew) {
                continue
            }

            val llSatisfyRootView =
                listItemView.findViewById<FrameLayout?>(R.id.main_fl_title_click_guide) ?: continue

            val itemType = RecommendFragmentTypeManager.getItemViewType(itemData)

            if (itemType == RecommendFragmentTypeManager.VIEW_TYPE_SCENE_LISTEN_CARD
                || itemType == RecommendFragmentTypeManager.VIEW_TYPE_SCENE_LISTEN_CARD_STYLE1
                || itemType == RecommendFragmentTypeManager.VIEW_TYPE_SCENE_LISTEN_CARD_STYLE2
            ) {
                checkShowGuide(llSatisfyRootView, itemData, false)
                break
            }
        }
    }

    fun checkShowGuide(
        rootView: ViewGroup?,
        recommendItemNew: RecommendItemNew?,
        isBind: Boolean = true
    ) {
        if (!isAllowCheck()) {
            return
        }

        if (rootView == null || !rootView.context.checkActivity()) {
            return
        }

        if (recommendItemNew?.item !is RecommendCommonItem) {
            return
        }
        val commonItem = recommendItemNew.item as? RecommendCommonItem

        val leftTitle = commonItem?.ext?.leftTitle
        if (leftTitle == null) {
            LogUtils.d(TAG, "leftTitle null")
            dismissGuide(rootView, commonItem)
            return
        }

        if (!leftTitle.isAllowShowGuide()) {
            LogUtils.d(TAG, " leftTitle isAllowShowGuide false")
            dismissGuide(rootView, commonItem)
            return
        }

        if (isBottomPullDownTipViewAnimal) {
            LogUtils.d(TAG, "isBottomPullDownTipViewAnimal true")
            return
        }

        val duration = HomeSceneTitleClickGuideDataUtil.getDuration()
        if (leftTitle.isShowingGuide && leftTitle.showingStartTime > 0) {
            val diffTime = SystemClock.elapsedRealtime() - leftTitle.showingStartTime
            if (diffTime >= duration) {
                dismissGuide(rootView, commonItem)
                return
            }

            showGuide(rootView, recommendItemNew, false, duration - diffTime)
            return
        }

        if (isBind) { // 绑定只走恢复逻辑
            return
        }

        if (!HomeSceneTitleClickGuideDataUtil.isAllowShowGuide()) {
            dismissGuide(rootView, commonItem)
            return
        }

        showGuide(rootView, recommendItemNew, true, duration)
    }

    private fun startShowAnimation(view: View) {
        val scaleXAnimator1 = ObjectAnimator.ofFloat(view, "scaleX", 0f, 1.1f)
        val scaleYAnimator1 = ObjectAnimator.ofFloat(view, "scaleY", 0f, 1.1f)
        val scaleXAnimator2 = ObjectAnimator.ofFloat(view, "scaleX", 1.1f, 1f)
        val scaleYAnimator2 = ObjectAnimator.ofFloat(view, "scaleY", 1.1f, 1f)
        val alphaAnimator = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        scaleXAnimator1.duration = 160
        scaleYAnimator1.duration = 160
        alphaAnimator.duration = 160

        scaleXAnimator2.duration = 80
        scaleYAnimator2.duration = 80

        view.pivotX = 0f
        view.pivotY = 0f

        val animatorSet = AnimatorSet()
        animatorSet.playSequentially(
            AnimatorSet().apply {
                playTogether(scaleXAnimator1, scaleYAnimator1, alphaAnimator)
            },
            AnimatorSet().apply {
                playTogether(scaleXAnimator2, scaleYAnimator2)
            }
        )
        animatorSet.start()
    }

    private fun startDismissAnimation(view: View, onEnd: () -> Unit) {
        val scaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0f)
        val scaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0f)
        val alphaAnimator = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)

        scaleXAnimator.duration = 160
        scaleYAnimator.duration = 160
        alphaAnimator.duration = 160

        view.pivotX = 0f
        view.pivotY = 0f

        val animatorSet = AnimatorSet()
        animatorSet.playTogether(scaleXAnimator, scaleYAnimator, alphaAnimator)
        animatorSet.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator?) {
                super.onAnimationEnd(animation)
                onEnd.invoke()
            }
        })
        animatorSet.start()
    }

    private fun showGuide(
        rootView: ViewGroup?,
        recommendItem: RecommendItemNew,
        isAnimal: Boolean,
        duration: Long
    ) {
        val commonItem = recommendItem.item as? RecommendCommonItem
        val leftTitle = commonItem?.ext?.leftTitle

        var tvTitle = rootView?.findViewById<TextView?>(R.id.tv_content)
        if (tvTitle != null) {
            tvTitle.text = HomeSceneTitleClickGuideDataUtil.getTips()
            return
        }

        try {
            val childView = LayoutInflater.from(rootView?.context)
                .inflate(R.layout.main_scene_listen_click_guide_layout, rootView, false)

            tvTitle = childView.findViewById(R.id.tv_content)
            tvTitle.text = HomeSceneTitleClickGuideDataUtil.getTips()

            val ivClose = childView.findViewById<View>(R.id.iv_close)
            ivClose.setOnClickListener {

                startDismissAnimation(childView) {
                    dismissGuide(rootView, commonItem)
                }

                // 新首页-场景化卡片-标题-气泡点击关闭  点击事件
                val trace = XMTraceApi.Trace()
                    .click(66690) // 用户点击时上报
                    .put("currPage", "newHomePage")
                    .put("xmRequestId", recommendItem.xmRequestId)
                    .put("dialogName", tvTitle.text.toString()) // 客户端传，传气泡文案
                    .put("contentType", "习惯听气泡提示") // 客户端传。去重使用
                    .put("contentId", "-1") // 客户端传。去重使用
                RecommendNewUbtV2Manager.addUbtV2Data(trace, leftTitle?.ubtV2)
                trace.createTrace()
            }
            ivClose.post {
                expandTouchDelegate(ivClose, 17)
            }

            rootView?.visibility = ViewGroup.VISIBLE
            rootView?.addView(childView)
            if (isAnimal) {
                startShowAnimation(childView)
            }

            HomeSceneTitleClickGuideDataUtil.saveShowGuide()

            HandlerManager.removeCallbacks(hiddenRun)
            hiddenRun = Runnable {
                hiddenRun = null
                dismissGuide(rootView, commonItem)
            }
            HandlerManager.postOnUIThreadDelay(hiddenRun, duration)


            // 新首页-场景化卡片-标题-气泡  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(66691)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("xmRequestId", recommendItem.xmRequestId)
                .put("dialogName", tvTitle.text.toString()) // 客户端传，传气泡文案
                .put("contentType", "习惯听气泡提示") // 客户端传。去重使用
                .put("contentId", "-1") // 客户端传。去重使用
            RecommendNewUbtV2Manager.addUbtV2Data(trace, leftTitle?.ubtV2)
            trace.createTrace()

            leftTitle?.isShowingGuide = true
            leftTitle?.showingStartTime = SystemClock.elapsedRealtime()
            isShowingGuide = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun dismissGuide(rootView: ViewGroup?, commonItem: RecommendCommonItem?) {
        isShowingGuide = false
        if (rootView?.context?.checkActivity() == true) {
            rootView.removeAllViews()
            rootView.visibility = ViewGroup.GONE
        }

        commonItem?.ext?.leftTitle?.let {
            it.showingStartTime = 0
            it.isShowingGuide = false
        }
    }

    private fun expandTouchDelegate(view: View, extraPadding: Int) {
        val parentView = view.parent as? View ?: return

        val rect = Rect()
        view.getHitRect(rect) // 获取当前视图的点击区域

        // 扩展点击区域
        rect.top -= extraPadding
        rect.bottom += extraPadding
        rect.left -= extraPadding
        rect.right += extraPadding

        parentView.touchDelegate = TouchDelegate(rect, view)
    }


    fun onDestroy() {
        if (hiddenRun != null) {
            HandlerManager.removeCallbacks(hiddenRun)
            hiddenRun = null
        }
    }
}