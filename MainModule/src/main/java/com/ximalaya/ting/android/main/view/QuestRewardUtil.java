package com.ximalaya.ting.android.main.view;

import android.os.Bundle;

import com.google.gson.Gson;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.exception.NonException;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.TimeLimitManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.main.albumModule.album.album2.AlbumFragmentNew2;
import com.ximalaya.ting.android.main.albumModule.album.album3.AlbumFragmentNew3;
import com.ximalaya.ting.android.main.fragment.dialog.QuestRewardFragment;
import com.ximalaya.ting.android.main.fragment.dialog.TaskShareRewardDialog;
import com.ximalaya.ting.android.main.model.QuestReward;
import com.ximalaya.ting.android.main.model.TaskShareReward;
import com.ximalaya.ting.android.xmutil.Logger;


/**
 * 积分Toast
 *
 * <AUTHOR>
 */

public class QuestRewardUtil {
    public static final String TAG = "QuestReward";
    public static final int TYPE_COMMENT = 0;
    public static final int TYPE_SHARE = 1;
    public static int FROM_PLAY_PAGE = 0;
    public static int FROM_ALBUM_PAGE = 1;
    public static int FROM_MULTI_COMMENT_PAGE = 2;
    public static int FROM_ITING_COMMENT_PAGE = 3;

    public static final String KEY_ALBUM_ID = "key_album_id";
    public static final String KEY_AUTHOR_ID = "key_author_id";
    public static final String KEY_TRACK_ID = "key_track_id";

    public static boolean showFirstShareToast(BaseFragment2 fragment, int type) {
        if (!UserInfoMannage.hasLogined()) {
            return false;
        }

        int from = FROM_PLAY_PAGE;
        if (fragment instanceof AlbumFragmentNew2) {
            from = FROM_ALBUM_PAGE;
        } else if (fragment instanceof AlbumFragmentNew3) {
            from = FROM_ALBUM_PAGE;
        }
        if (TimeLimitManager.getInstance().check(TimeLimitManager.EVENT_GET_POINT_SHARE_V2)) {
            Logger.i(TAG, "每日首次分享触发任务奖励弹窗");
            return showShareRewardDialog(fragment, type, from);
        }
        return false;
    }

    public static boolean showToast(BaseFragment2 f, int type) {
        boolean isCoin = ConfigureCenter.getInstance().getBool("tob", "coinSwitch", false);
        if (isCoin) {
            return false;
        }
        if (!UserInfoMannage.hasLogined()) {
            return false;
        }

        int from = FROM_PLAY_PAGE;
        if (f instanceof AlbumFragmentNew2) {
            from = FROM_ALBUM_PAGE;
        } else if (f instanceof AlbumFragmentNew3) {
            from = FROM_ALBUM_PAGE;
        }

        if (TimeLimitManager.getInstance().check(TimeLimitManager.EVENT_GET_POINT_WITH_PRODUCT)) {
//            if (!UserInfoMannage.hasLogined()) {
//                Logger.i(TAG, "未登录不触发弹窗");
//                TimeLimitManager.getInstance().decreaseCount(TimeLimitManager
//                        .EVENT_GET_POINT_WITH_PRODUCT, 1);
//            } else {
//                Logger.i(TAG, "触发任务奖励商品弹窗");
            return showToastWithProduct(f, type, from);
//            }
        }

        if (type == TYPE_COMMENT) {
            if (TimeLimitManager.getInstance().check(TimeLimitManager
                    .EVENT_GET_POINT_COMMENT)) {
                Logger.i(TAG, "评论触发任务奖励弹窗");
                return showToastPoint(f, type, from);
            }
        } else if (type == TYPE_SHARE) {
            if (TimeLimitManager.getInstance().check(TimeLimitManager.EVENT_GET_POINT_SHARE)) {
                Logger.i(TAG, "分享触发任务奖励弹窗");
                return showToastPoint(f, type, from);
            }
        }
        return false;
    }

    private static boolean showShareRewardDialog(BaseFragment2 fragment2, int type, int from) {
        final TaskShareRewardDialog fragment = new TaskShareRewardDialog();
        Bundle bundle = new Bundle();
        try {
            String json = ConfigureCenter.getInstance().getJsonString("toc", "taskCenter_share","");
            json = json.replace("\n", "").replace(" ", "");
            Logger.i(TAG, "奖励文本: " + json);
            TaskShareReward reward = new Gson().fromJson(json, TaskShareReward.class);
            if (reward != null) {
                if (!reward.getStatus()) {
                    return false;
                }
                bundle.putParcelable("reward", reward);
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        bundle.putInt("from", from);
        fragment.setArguments(bundle);
        fragment.show(fragment2.getChildFragmentManager(), "toast_point");
        HandlerManager.obtainMainHandler().postDelayed(() -> {
            if (fragment.canUpdateUi()) {
                fragment.dismiss();
            }
        }, 5000);
        XDCSCollectUtil.statErrorToXDCS("share", "触发当日首次分享领积分");
        return true;
    }

    private static boolean showToastPoint(BaseFragment2 fragment2, int type, int from) {
        final QuestRewardFragment fragment = new QuestRewardFragment();
        Bundle bundle = new Bundle();
        try {
            String json = ConfigureCenter.getInstance().getString("toc",
                    type == TYPE_COMMENT ? "commentSuccessToast" : "shareSuccessToast");
            json = json.replace("\n", "").replace(" ", "");
            Logger.i(TAG, "奖励文本: " + json);
            QuestReward reward = new Gson().fromJson(json, QuestReward.class);
            if (reward != null) {
                if (!reward.getStatus()) {
                    return false;
                }
                bundle.putParcelable("reward", reward);
            } else {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        bundle.putInt("type", 0);
        bundle.putInt("actionType", type);
        bundle.putInt("from", from);
        fragment.setArguments(bundle);
        fragment.show(fragment2.getChildFragmentManager(), "toast_point");
        HandlerManager.obtainMainHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (fragment.canUpdateUi()) {
                    fragment.dismiss();
                }
            }
        }, 5000);
        return true;
    }

    private static boolean showToastWithProduct(BaseFragment2 fragment2, int type, int from) {
        final QuestRewardFragment fragment = new QuestRewardFragment();
        Bundle bundle = new Bundle();
        try {
            String json = ConfigureCenter.getInstance().getString("toc",
                    type == TYPE_COMMENT ? "commentSuccessToast" : "shareSuccessToast");
            Logger.i(TAG, "奖励文本: " + json);
            QuestReward reward = new Gson().fromJson(json, QuestReward.class);
            if (reward != null) {
                if (!reward.getStatus()) {
                    return false;
                }
                bundle.putParcelable("reward", reward);
            } else {
                return false;
            }
        } catch (NonException e) {
            e.printStackTrace();
            return false;
        }
        bundle.putInt("type", 1);
        bundle.putInt("actionType", type);
        bundle.putInt("from", from);
        fragment.setArguments(bundle);
        fragment.show(fragment2.getChildFragmentManager(), "toast_point");
        return true;
    }
}
