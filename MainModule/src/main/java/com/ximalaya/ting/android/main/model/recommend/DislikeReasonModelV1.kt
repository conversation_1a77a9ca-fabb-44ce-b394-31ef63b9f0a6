package com.ximalaya.ting.android.main.model.recommend

import com.ximalaya.ting.android.main.R

/**
 * Created by changle.fang on 2022/2/18.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 */
data class DislikeReasonModelV1(
        var reasonRowType: Int,
        var negative: DislikeReasonNegativeModelV1? = null
) {
    companion object {
        const val TYPE_DISLIKE_ITEM = 0
        const val TYPE_REDUCE_SIMILAR = 1
        const val TYPE_DISLIKE_ANCHOR = 2
        const val TYPE_FEEDBACK = 3
    }

    fun getIcon(): Int {
        return when (reasonRowType) {
            TYPE_DISLIKE_ITEM -> com.ximalaya.ting.android.host.R.drawable.host_ic_recommend_dislike
            TYPE_REDUCE_SIMILAR -> R.drawable.main_ic_recommend_shield
            TYPE_DISLIKE_ANCHOR -> R.drawable.main_ic_recommend_dislike_anchor
            else -> R.drawable.main_ic_recommend_feedback
        }
    }

    fun getTitle(): String {
        return negative?.key?.name ?: ""
    }

    fun getCodeType(): String {
        return negative?.key?.codeType ?: ""
    }
    fun getSubReasons(): List<DislikeReasonNew>? {
        return negative?.subReasons
    }

    fun getMainReason(): DislikeReasonNew? {
        return negative?.key
    }
}

data class DislikeReasonNegativeModelV1(
        var key: DislikeReasonNew? = null,
        var subReasons: List<DislikeReasonNew>? = null
)