package com.ximalaya.ting.android.main.adapter.find.recommendStaggered

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ximalaya.ting.android.framework.util.RpAdaptUtil
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.manager.HomeRecommendPageLoadingOptimizationManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.XmFeedInnerModel
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.SpmTraceUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.startup.PerformanceMonitor
import com.ximalaya.ting.android.host.util.startup.ViewPool
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.adapter.find.util.RecommendCornerUtils
import com.ximalaya.ting.android.main.adapter.find.util.RecommendNewUbtV2Manager
import com.ximalaya.ting.android.main.adapter.find.util.RpAdaptUtil.getOffset
import com.ximalaya.ting.android.main.adapter.find.util.ScenePlayDataUtil
import com.ximalaya.ting.android.main.adapter.find.util.getBRTagUrl
import com.ximalaya.ting.android.main.adapter.find.view.FreeListenerTimeView
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered
import com.ximalaya.ting.android.main.adapter.recyclerview.HorizontalMoreBaseAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager
import com.ximalaya.ting.android.main.model.rec.CommonSubElement
import com.ximalaya.ting.android.main.model.rec.RecommendCommonItem
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew
import com.ximalaya.ting.android.main.request.MainCommonRequest
import com.ximalaya.ting.android.main.view.recommend.StartSnapHelper
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.read.utils.checkActivity
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * 免费畅听模块 横滑样式
 */
class NewRecommendFreeListenerAdapterProviderStaggered(
    private var fragment: BaseFragment2,
    private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?
) : IMulitViewTypeViewAndDataStaggered<NewRecommendFreeListenerAdapterProviderStaggered.ListCardViewHolder, RecommendItemNew>,
    IMulitViewTypeViewAndDataTraceStaggered<NewRecommendFreeListenerAdapterProviderStaggered.ListCardViewHolder, RecommendItemNew> {

    override fun getView(
        layoutInflater: LayoutInflater?, position: Int, parent: ViewGroup?
    ): View? {
        return layoutInflater?.inflate(
            R.layout.main_item_recommend_free_listener_card_new,
            parent,
            false
        )
    }

    class ListCardViewHolder(convertView: View) : RecyclerView.ViewHolder(convertView) {
        val rootView = convertView
        val recyclerView: RecyclerView = convertView.findViewById(R.id.main_rcv_album_list)
        var tvModuleTitle: TextView = convertView.findViewById(R.id.main_tv_module_title)
        val tvMore: TextView = convertView.findViewById(R.id.main_tv_more)
        val freeListenView: FreeListenerTimeView =
            convertView.findViewById(R.id.free_listen_time_view)

        var startSnapHelper: StartSnapHelper? = null
    }

    override fun createViewHolder(convertView: View?): ListCardViewHolder? {
        PerformanceMonitor.traceBegin("free_listener_createViewHolder")
        if (convertView == null) {
            return null
        }
        val viewHolder = ListCardViewHolder(convertView)
        PerformanceMonitor.traceEnd("free_listener_createViewHolder", 21)
        return viewHolder
    }

    override fun bindViewHolder(
        holder: ListCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?,
        convertView: View?
    ) {
        if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
            HandlerManager.postOnUIThread {
                onBindViewHolderInner(holder, position, recommendItemNew)
            }
        } else {
            onBindViewHolderInner(holder, position, recommendItemNew)
        }
    }

    fun onBindViewHolderInner(
        holder: ListCardViewHolder?,
        position: Int,
        recommendItemNew: RecommendItemNew?
    ) {
        if (holder == null || recommendItemNew == null) {
            return
        }

        RecommendStaggeredTraceManager.checkXmRequestId(recommendItemNew)
        val recommendCommonItem = recommendItemNew.item
        if (recommendCommonItem !is RecommendCommonItem) {
            return
        }
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }
        PerformanceMonitor.traceBegin("free_listener_bindViewHolder_" + recommendCommonItem.title)
        val context = holder.rootView.context

        holder.tvModuleTitle.text = recommendCommonItem.title

        var time = 0L
        recommendCommonItem.ext?.extraInfo?.permissionExpireTime?.let {
            try {
                time = it.toLong()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        holder.freeListenView.setData(recommendItemNew.xmRequestId, position, time, object :
            FreeListenerTimeView.IFinishTimeCallBack {
            override fun onTimeFinish() {
                if (!holder.itemView.context.checkActivity()) {
                    return
                }

                try {
                    dataAction?.remove(position)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

        })

        val exportMore = { action: String ->
            // 新首页-社会化标注-更多  点击事件
            val trace = XMTraceApi.Trace()
                .click(61824) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("moduleName", recommendCommonItem.title) // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.moduleId?.toString() ?: "") // 例如：1000000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("xmRequestId", recommendItemNew.xmRequestId ?: "")
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("action", action)
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString(),
                "更多",
                "d01"
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()
        }

        val performMoreClick = {
            ScenePlayDataUtil.saveDataForRn(recommendItemNew)
            ToolUtil.clickUrlAction(
                fragment, recommendCommonItem.landingPage ?: "", holder.tvMore
            )
        }

        holder.tvModuleTitle.setOnClickListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            trace1.createTrace()
            exportMore("click")
            performMoreClick()
        }

        holder.tvMore.setOnClickListener {
            exportMore("click")
            performMoreClick()
        }

        val spanCount = if (RecommendFragmentTypeManager.isNewSceneCard() && position == 1) {
            3
        } else if (RecommendFragmentNetManager.instance.secondFloorShow2LinesEnable() && position == 1) {
            2
        } else {
            3
        }
        val cardAlbumListAdapter = ListAlbumItemAdapter(
            dataAction,
            fragment,
            recommendCommonItem,
            recommendItemNew,
            recommendCommonItem.subElements!!,
            position,
            holder.recyclerView,
            spanCount,
            holder.rootView,
            recommendCommonItem.landingPage.isNullOrEmpty().not()
        )
        cardAlbumListAdapter.mEnableMoreItem = cardAlbumListAdapter.enableJumpMore
        cardAlbumListAdapter.setRelaseJumpActivityListener {
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew.xmRequestId) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace1, recommendCommonItem.ubtV2)
            trace1.createTrace()
            exportMore("slide")
            performMoreClick()
        }
        holder.recyclerView.adapter = cardAlbumListAdapter
        RecommendStaggeredTraceManager.traceOnAdShowSizeOpt(
            this.javaClass.simpleName,
            spanCount,
            position,
            recommendItemNew
        )
        val layoutManager =
            GridLayoutManager(context, spanCount, GridLayoutManager.HORIZONTAL, false)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (cardAlbumListAdapter.getItemViewType(position) == HorizontalMoreBaseAdapter.MOREDATAVIEWTYPE) spanCount else 1
            }
        }
        holder.recyclerView.layoutManager = layoutManager

        if (holder.startSnapHelper == null) {
            holder.startSnapHelper = StartSnapHelper()
            holder.startSnapHelper?.attachToRecyclerView(holder.recyclerView)
            holder.startSnapHelper?.setContainerView(holder.recyclerView)
        }

        holder.recyclerView.clearOnScrollListeners()
        holder.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            private var mOldState = RecyclerView.SCROLL_STATE_IDLE

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == mOldState) {
                    return
                }
                mOldState = newState
                if (mOldState == RecyclerView.SCROLL_STATE_IDLE) {
                    traceOnItemShow(recommendItemNew, position, holder)
                    recommendItemNew.firstVisiblePosition =
                        layoutManager.findFirstVisibleItemPosition()
                }
            }
        })
        if (recommendItemNew.firstVisiblePosition != layoutManager.findFirstVisibleItemPosition()) {
            holder.recyclerView.scrollToPosition(recommendItemNew.firstVisiblePosition)
        }

        PerformanceMonitor.traceEnd("free_listener_bindViewHolder_" + recommendCommonItem.title, 21)
    }

    override fun traceOnItemShow(
        data: RecommendItemNew?,
        position: Int,
        holder: ListCardViewHolder?
    ) {
        if (data == null || holder == null) {
            return
        }
        val recommendCommonItem = data.item as? RecommendCommonItem ?: return
        if (recommendCommonItem.subElements.isNullOrEmpty()) {
            return
        }

        fragment.postOnUiThread {
            if (!fragment.canUpdateUi()) {
                return@postOnUiThread
            }

            if (!ViewStatusUtil.viewIsRealShowing(holder.itemView)) {
                return@postOnUiThread
            }

            // 新首页-首页大卡模块  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(62177)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("modulePosition", (position + 1).toString())
                .put("xmRequestId", data.xmRequestId) // 客户端传
                .put("contentType", data.itemType) // 客户端传
                .put("contentId", recommendCommonItem.id.toString()) // 客户端传
                .put("card_adTopn", recommendCommonItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (position + 1).toString()
            )
            if (data.isLocalCache) {
                trace.isLocalCache
            }
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            trace.createTrace()

            val childCount = holder.recyclerView.childCount
            for (i in 0 until childCount) {
                val view = holder.recyclerView.getChildAt(i)
                if (!ViewStatusUtil.viewIsRealShowing(view)) {
                    continue
                }

                val subElement =
                    view.getTag(R.id.main_id_item_data) as? CommonSubElement ?: continue
                val index = view.getTag(R.id.main_id_data_index) as? Int ?: continue
                traceAlbumInner(
                    subElement, recommendCommonItem, data, index, position,
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view)
                )
            }
        }
    }

    class ListAlbumItemAdapter(
        private val dataAction: RecommendFragmentStaggeredAdapter.IDataAction?,
        private val fragment: BaseFragment2,
        // 卡片数据
        private val moduleItem: RecommendCommonItem,
        private val recommendItemNew: RecommendItemNew?,
        list: List<CommonSubElement>,
        var modulePosition: Int,
        val recyclerView: RecyclerView,
        var spanCount: Int,
        val rootView: View,
        val enableJumpMore: Boolean
    ) : HorizontalMoreBaseAdapter() {

        private val commonSubElementList = mutableListOf<CommonSubElement>()

        init {
            commonSubElementList.addAll(list)
        }

        override fun horizontalMoreOnCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
        ): RecyclerView.ViewHolder {
            val view: View = ViewPool.getInstance().getView(
                HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent(),
                R.layout.main_item_recommend_free_listener_list_item_new,
                parent,
                false,
                "FreeListenListNew"
            )
            return SocialListAlbumViewHolder(view)
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindViewHolderWrapper(holder, position)
                }
            } else {
                onBindViewHolderWrapper(holder, position)
            }
        }

        private fun onBindViewHolderWrapper(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is SocialListAlbumViewHolder) {
                onBindViewHolderInner(holder, position)
            } else if (holder is HorizontalMoreDataViewViewHolder) {
                val layoutParams = horizontalView?.layoutParams
                layoutParams?.width = 37.dp
                layoutParams?.height = ViewGroup.LayoutParams.MATCH_PARENT
                if (layoutParams != null) {
                    horizontalView?.layoutParams = layoutParams
                }
                if (enableJumpMore) {
                    horizontalView?.visibility = View.VISIBLE
                } else {
                    horizontalView?.visibility = View.GONE
                }
            }
        }

        private fun onBindAlbumView(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder,
            textViewContainerWith: Int
        ) {
            if (HomeRecommendPageLoadingOptimizationManager.canNotInitLessImportantComponent()) {
                HandlerManager.postOnUIThread {
                    onBindAlbumViewInner(commonSubElement, holder, textViewContainerWith)
                }
            } else {
                onBindAlbumViewInner(commonSubElement, holder, textViewContainerWith)
            }
        }

        private fun onBindAlbumViewInner(
            commonSubElement: CommonSubElement,
            holder: SocialListAlbumViewHolder,
            textViewContainerWith: Int
        ) {
            holder.cslContainerView.visibility = View.VISIBLE
            holder.itemTitleTv.text = commonSubElement.title
            RecommendCornerUtils.updateTitleColor(holder.itemTitleTv)
            if (commonSubElement.ext?.reasonContent.isNullOrEmpty()) {
                holder.itemSubtitle1Tv.visibility = View.GONE
            } else {
                holder.itemSubtitle1Tv.visibility = View.VISIBLE
                holder.itemSubtitle1Tv.text = commonSubElement.ext!!.reasonContent
                holder.itemSubtitle1Tv.maxLines = 1
                RecommendCornerUtils.updateSubTitleMargin(holder.itemSubtitle1Tv)
            }
            val adTagShow = commonSubElement.ext?.subRefInfo?.businessExtraInfo?.showAdMark ?: false
            ViewStatusUtil.setVisible(if (adTagShow) View.VISIBLE else View.GONE, holder.ivAdTag)
            val adWidth = if (adTagShow) 24 else 0
            val otherWidth =
                16 + holder.albumCoverLayoutView.getWholeViewWidthInDp() + 12 + 16 + adWidth //  else 16 + 70 + 12 + 16
            RecommendShowTagsUtilNew.bindTagsView(
                holder.layoutShowTags,
                commonSubElement.ext?.showTags,
                textViewContainerWith - otherWidth.dp,
                commonSubElement.ext?.subTitle1,
                commonSubElement.ext?.subTitle2
            )
            commonSubElement.wrap?.ltSubscriptTag?.tag?.let {
                holder.albumCoverLayoutView.setAlbumTag(
                    it
                )
            }
            if (holder.layoutRightTxtArea.layoutParams is ConstraintLayout.LayoutParams) {
                (holder.layoutRightTxtArea.layoutParams as ConstraintLayout.LayoutParams).startToEnd =
                    R.id.main_album_cover_layout
            }
            commonSubElement.cover?.let { holder.albumCoverLayoutView.setAlbumCover(it) }

            holder.albumCoverLayoutView.setPodCastTagAutoSize(
                commonSubElement.ext?.other?.getBRTagUrl()
            )
        }

        private fun updateSize(holder: SocialListAlbumViewHolder) {
            val padding = RecommendCornerUtils.getPaddingSize()
            holder.cslContainerView.run {
                setPadding(paddingLeft, padding, paddingRight, padding)
            }
            RecommendCornerUtils.updateAlbumCorner(holder.albumCoverLayoutView)
            holder.albumCoverLayoutView.updateSize(RecommendCornerUtils.getSocialCoverSize())

            holder.showTagParentAlbum?.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }

            holder.showTagParentTrack?.run {
                val params = this.layoutParams as MarginLayoutParams
                params.topMargin = RecommendCornerUtils.getSocialShowTagGapSize()
                this.layoutParams = params
            }
        }

        fun onBindViewHolderInner(holder: SocialListAlbumViewHolder, position: Int) {
            val commonSubElement = commonSubElementList[position]
            holder.itemView.setTag(R.id.main_id_item_data, commonSubElement)
            holder.itemView.setTag(R.id.main_id_data_index, position)
            val remainder: Int = commonSubElementList.size % spanCount
            val start = if (remainder == 0) {
                commonSubElementList.size - spanCount
            } else {
                commonSubElementList.size - remainder
            }
            updateSize(holder)

            holder.itemView.setOnLongClickListener {
                val requestMap = mutableMapOf<String, String>()
                val traceMap = mutableMapOf<String, String>()
                traceMap["currPage"] = "newHomePage"
                traceMap["cardPosition"] = (modulePosition + 1).toString()
                traceMap["positionNew"] = (position + 1).toString()
                if (!moduleItem.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(moduleItem.ubtV2)
                }
                if (!commonSubElement.ubtV2.isNullOrEmpty()) {
                    traceMap.putAll(commonSubElement.ubtV2)
                }
                traceMap["xmRequestId"] = recommendItemNew?.xmRequestId ?: ""
                traceMap["contentType"] = commonSubElement.bizType ?: ""
                traceMap["contentId"] = commonSubElement.refId?.toString() ?: ""
                requestMap[HttpParamsConstants.PARAM_ALBUM_ID] =
                    commonSubElement.refId.toString()
                requestMap[HttpParamsConstants.PARAM_SOURCE] = "newHomePage"
                requestMap[HttpParamsConstants.PARAM_ANCHOR_ID] =
                    commonSubElement.anchor?.uid.toString()
                requestMap["card_contentType"] = moduleItem.contentType ?: ""
                requestMap["card_bizType"] = moduleItem.bizType ?: ""
                requestMap["card_id"] = moduleItem.id.toString()

                val leve2Build = DisLikeLeve2Build()
                leve2Build.isFromAd = false
                leve2Build.anchorName = commonSubElement.anchor?.nickName
                leve2Build.requestMap = requestMap
                leve2Build.traceMap = traceMap
                leve2Build.onFeedBackListener =
                    object : NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        override fun onDialogShow(showSuccess: Boolean) {
                        }

                        override fun onFeedBack(list: List<XmFeedInnerModel>) {
                            MainCommonRequest.getSingleSocialListenListItem(
                                position, null, moduleItem,
                                object : IDataCallBack<CommonSubElement> {
                                    override fun onSuccess(subElement: CommonSubElement?) {
                                        if (subElement == null) {
                                            removeElement(
                                                commonSubElementList,
                                                moduleItem.subElements,
                                                position,
                                                modulePosition
                                            )
                                            return
                                        }
                                        commonSubElementList[position] = subElement
                                        var firstPos =
                                            (recyclerView.layoutManager as GridLayoutManager).findFirstCompletelyVisibleItemPosition()
                                        if (firstPos < 0) {
                                            firstPos =
                                                (recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
                                        }
                                        notifyItemChanged(position)  // 使用这个  当前在最后一列时  系统定位不准 会滚动到前一列
                                        recyclerView.scrollToPosition(firstPos)
                                        traceAlbumInner(
                                            subElement,
                                            moduleItem,
                                            recommendItemNew,
                                            position,
                                            modulePosition,
                                            ViewStatusUtil.getViewVisibleAreaRealPercent(holder.itemView)
                                        )
                                    }

                                    override fun onError(code: Int, message: String?) {
                                        removeElement(
                                            commonSubElementList,
                                            moduleItem.subElements,
                                            position,
                                            modulePosition
                                        )
                                    }
                                })
                        }
                    }

                val typeStr = MoreFuncBuild.TYPE_ALBUM
                val refId = commonSubElement.refId
                val build: MoreFuncBuild = MoreFuncBuild.createCommonLongClickModel(
                    fragment, typeStr, refId, null, true, leve2Build
                )
                val trackMap = mutableMapOf<String, String?>().apply {
                    put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                    put("contentType", commonSubElement.bizType ?: "")
                    put("contentId", commonSubElement.refId?.toString() ?: "")
                    put("modulePosition", (modulePosition + 1).toString())
                    put("positionNew", (position + 1).toString())
                    moduleItem.ubtV2?.let { it1 -> putAll(it1) }
                    commonSubElement.ubtV2?.let { it1 -> putAll(it1) }
                }
                build.trackMap = trackMap

                XmMoreFuncManager.checkShowMorePage(build)
                true
            }

            val layoutParams = holder.cslContainerView.layoutParams
            val textViewContainerWithInPx: Int
            if (position >= start) { // 最后一列
                val moreSize = if (enableJumpMore) {
                    37.dp
                } else {
                    0
                }
                layoutParams.width = getRpAdaptSize(375) - moreSize
                textViewContainerWithInPx = getRpAdaptSize(375 - 3) - moreSize
            } else {
                layoutParams.width = getRpAdaptSize(337 - getOffset())
                textViewContainerWithInPx = getRpAdaptSize(337 - getOffset() - 3)
            }

            onBindAlbumView(commonSubElement, holder, textViewContainerWithInPx)

            holder.itemView.setOnClickListener {
                onItemClickInner(
                    commonSubElement,
                    position,
                    it
                )
            }
        }

        private fun removeElement(
            commonSubElementList: MutableList<CommonSubElement>,
            subElements: MutableList<CommonSubElement>?,
            position: Int,
            modulePosition: Int
        ) {
            val size = commonSubElementList.size
            if (size <= 3) { // 删到只有两个item时 直接移除整个模块
                dataAction?.remove(modulePosition)
                return
            }

            var firstPos =
                (recyclerView.layoutManager as GridLayoutManager).findFirstCompletelyVisibleItemPosition()
            if (firstPos < 0) {
                firstPos =
                    (recyclerView.layoutManager as GridLayoutManager).findFirstVisibleItemPosition()
            }

            commonSubElementList.removeAt(position)
            subElements?.removeAt(position)

            val newSize = commonSubElementList.size
            notifyItemRemoved(position)
            if (position != newSize) {
                notifyItemRangeChanged(position, newSize - position)
            }

            recyclerView.scrollToPosition(firstPos)
        }

        private fun onItemClickInner(
            commonSubElement: CommonSubElement,
            position: Int,
            view: View?
        ) {
            if (commonSubElement.landingPage.isNullOrEmpty()) {
                return
            }
            val albumId = commonSubElement.refId ?: 0
            val trackId = 0L
            val contentId = albumId.toString()
            val rewardName = ""
            val rankName = ""
            val tarTypeId = commonSubElement.ext?.tagType ?: 0
            val trace1 = XMTraceApi.Trace()
                .click(62176) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("modulePosition", (modulePosition + 1).toString()) // 客户端传
                .put("xmRequestId", recommendItemNew?.xmRequestId) // 客户端传
                .put("card_adTopn", moduleItem.cardAdCount.toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2,
                (modulePosition + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(
                trace1,
                (recommendItemNew?.item as? RecommendCommonItem)?.ubtV2
            )
            trace1.createTrace()
            // 新首页-社会化标注-专辑卡片  点击事件
            val trace = XMTraceApi.Trace()
                .click(60896) // 用户点击时上报
                .put("currPage", "newHomePage")
                .put("albumId", albumId.toString())
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", moduleItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", moduleItem.id.toString()) // 例如：100000000
                .put("rec_src", moduleItem.ubt?.recSrc ?: "")
                .put("rec_track", moduleItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", moduleItem.ubt?.traceId ?: "")
                .put("contentId", contentId)
                .put("contentType", commonSubElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", recommendItemNew?.xmRequestId ?: "")
                .put("trackId", trackId.toString()) // 当是声音时传
                .put("titleId", moduleItem.ext?.recWrap?.id.toString()) // 传 recWrap 中的 id
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("socialTagId", commonSubElement.ext?.reasonId ?: "") // 传 reasonId
                .put("trigger", moduleItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", moduleItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
                .put(
                    "exploreArea",
                    ViewStatusUtil.getViewVisibleAreaRealPercent(view).toString()
                )
                .put("isAd", "false")
                .put("area", "item")
                .put("rewardName", rewardName) // 直播 奖励名称
                .put("rankName", rankName) // 直播 榜单名称
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                moduleItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = commonSubElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, moduleItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, commonSubElement.ubtV2)
            trace.createTrace()

            ToolUtil.clickUrlAction(fragment, commonSubElement.landingPage!!, view)
        }

        override fun getItemCount(): Int {
            if (enableJumpMore) {
                return commonSubElementList.size + 1
            }
            return commonSubElementList.size
        }

        override fun horizontalMoreGetItemViewType(postion: Int): Int {
            return -1
        }

        class SocialListAlbumViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            var cslContainerView: View = view.findViewById(R.id.main_csl_item_root_view)
            var itemTitleTv: TextView = view.findViewById(R.id.main_tv_album_title)

            var albumCoverLayoutView: AlbumCoverLayoutView =
                view.findViewById(R.id.main_album_cover_layout)
            var layoutRightTxtArea: View = view.findViewById(R.id.main_layout_right_txt_area)
            var itemSubtitle1Tv: TextView = view.findViewById(R.id.main_tv_sub_title)
            var layoutShowTags: LinearLayout = view.findViewById(R.id.main_layout_show_tag)
            var ivAdTag: ImageView = view.findViewById(R.id.main_iv_ad_tag)
            var showTagParentAlbum: ViewGroup? =
                view.findViewById(R.id.main_layout_show_tag_parent_album)
            var showTagParentTrack: ViewGroup? =
                view.findViewById(R.id.main_layout_show_tag_parent_track)
        }
    }

    companion object {

        // 尺寸大小适配
        fun getRpAdaptSize(size: Int): Int {
            return RpAdaptUtil.rp2PxIn375(size)
        }

        fun traceAlbumInner(
            subElement: CommonSubElement,
            recommendCommonItem: RecommendCommonItem,
            data: RecommendItemNew?,
            position: Int,
            modulePosition: Int,
            exploreArea: Int
        ) {
            val albumId = subElement.refId ?: 0
            val tarTypeId = subElement.ext?.tagType ?: 0
            // 新首页-社会化标注-专辑卡片  控件曝光
            val trace = XMTraceApi.Trace()
                .setMetaId(60897)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newHomePage")
                .put("tagType", tarTypeId.toString()) // 0 表示空，1 表示近7日XX播放 2 表示XX人订阅 3 表示XX人评论
                .put("albumId", albumId.toString())
                .put("exploreArea", exploreArea.toString()) // 可见区域占屏幕的比例
                .put("positionNew", (position + 1).toString()) // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                .put("moduleName", recommendCommonItem.title ?: "") // 例如：保重身体，健康的身体是我们革命本钱
                .put("moduleId", recommendCommonItem.id.toString()) // 例如：100000000
                .put("rec_src", recommendCommonItem.ubt?.recSrc ?: "")
                .put("rec_track", recommendCommonItem.ubt?.recTrack ?: "")
                .put("ubtTraceId", recommendCommonItem.ubt?.traceId ?: "")
                .put("contentId", albumId.toString())
                .put("contentType", subElement.bizType ?: "") // 传接口返回的 bizType
                .put("xmRequestId", data?.xmRequestId)
                .put("trackId", "") // 当是声音时传
                .put("titleId", recommendCommonItem.ext?.recWrap?.id.toString())
                .put("socialTagId", subElement.ext?.reasonId ?: "")
                .put("trigger", recommendCommonItem.ext?.extraInfo?.triggerId ?: "")
                .put("moduleType", recommendCommonItem.contentType ?: "")
                .put("modulePosition", (modulePosition + 1).toString())
            SpmTraceUtil.addSpmTraceInfo(
                trace,
                recommendCommonItem.ubtV2,
                (modulePosition + 1).toString(),
                contentTitle = subElement.title,
                contentPosition = (position + 1).toString()
            )
            RecommendNewUbtV2Manager.addUbtV2Data(trace, recommendCommonItem.ubtV2)
            RecommendNewUbtV2Manager.addUbtV2Data(trace, subElement.ubtV2)
            if (data?.isLocalCache == true) {
                trace.isLocalCache
            }
            trace.createTrace()
        }
    }
}