package com.ximalaya.ting.android.main.model.rec;

import com.ximalaya.ting.android.host.model.track.TrackM;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2019/5/22 10:02
 */
public class RecommendHeadLineModel {
    private int channelId;
    private String channelName;
    private TrackM trackM;

    public RecommendHeadLineModel(String json) {
        try {
            JSONObject jsonObject = new JSONObject(json);
            setChannelId(jsonObject.optInt("channelId"));
            setChannelName(jsonObject.optString("channelName"));
            setTrackM(new TrackM(jsonObject.optString("track")));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public TrackM getTrackM() {
        return trackM;
    }

    public void setTrackM(TrackM trackM) {
        this.trackM = trackM;
    }
}
