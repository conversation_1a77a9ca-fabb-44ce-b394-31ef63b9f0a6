package com.ximalaya.ting.android.main.playpage.view;

import android.app.Activity;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.listener.ICollectStatusCallback;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.manager.track.LikeTrackManage;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.other.VideoPlayCommonUtil;
import com.ximalaya.ting.android.host.util.view.TextViewExtensitionKt;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.fragment.subscribeRecommend.SubscribeRecommendFragment;
import com.ximalaya.ting.android.main.model.recommend.SubscribeRecommendAlbumMListWithDescription;
import com.ximalaya.ting.android.main.playModule.presenter.VideoPlayListPresenter;
import com.ximalaya.ting.android.main.playpage.listener.IPlayFunctionNew;
import com.ximalaya.ting.android.main.playpage.listener.IPlayVideoTabListener;
import com.ximalaya.ting.android.main.playpage.manager.PlayPageDataManager;
import com.ximalaya.ting.android.main.playpage.util.PlayCommentUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

import org.jetbrains.annotations.NotNull;

/**
 * 播放页-声音基础信息模块
 *
 * <AUTHOR> on 2017/4/8.
 */

public class TrackInfoViewNew implements IPlayVideoTabListener.ITrackInfoView,
        AlbumEventManage.CollectListener, View.OnClickListener {

    private IPlayFunctionNew mFunction;
    @Nullable
    private AlbumM mAlbum;
    private TrackM mTrack;
    private View mVSpaceAlbumInfo;
    private VideoListViewNew mVideoListView;
    private View mVideoListDivider;
    @Nullable
    private IPlayVideoTabListener.ITrackInfoViewNewEventListener mEventListener;
    private ImageView mIvAlbumCover;
    private TextView mTvAlbumTitle;
    private TextView mTvSubscribeCount;
    private TextView mTrackSubTv;
    private View mVAlbumLayout;
    private View mVAnchorLayout;
    private ImageView mIvAnchorCover;
    private TextView mTvAnchorTitle;
    private TextView mTvAnchorFollowCount;
    private TextView mFollowTv;

    private long mSubscribeCount;
    private ViewStub mViewStub;
    private TextView mTvTrackTitle;
    private TextView mTvPlayInfo;
    private ImageView mIvDanmaBtn;
    private TextView mTvSendDanma;
    private TextView mTvDescription;
    private TextView mTvLike;
    private TextView mTvDownload;
    private TextView mTvShare;
    private PlayingSoundInfo mPlayingSoundInfo;

    public TrackInfoViewNew(IPlayFunctionNew function, @Nullable IPlayVideoTabListener.ITrackInfoViewNewEventListener listener) {
        mFunction = function;
        mEventListener = listener;
    }

    public void init(BaseFragment2 fragment) {
        mViewStub = fragment.findViewById(R.id.main_view_stub_trackInfo);
        mViewStub.inflate();
        mIvAlbumCover = fragment.findViewById(R.id.main_header_owner_icon);
        mTvAlbumTitle = fragment.findViewById(R.id.main_header_owner_name);
        mTvSubscribeCount = fragment.findViewById(R.id.main_header_sub_num);
        mTrackSubTv = fragment.findViewById(R.id.main_header_owner_subscribe);
        mTvTrackTitle = fragment.findViewById(R.id.main_play_track_title);
        mVAlbumLayout = fragment.findViewById(R.id.main_layout_album);
        mVAnchorLayout = fragment.findViewById(R.id.main_layout_anchor);
        mIvAnchorCover = fragment.findViewById(R.id.main_anchor_header_owner_icon);
        mTvAnchorTitle = fragment.findViewById(R.id.main_anchor_header_owner_name);
        mTvAnchorFollowCount = fragment.findViewById(R.id.main_anchor_header_sub_num);
        mFollowTv = fragment.findViewById(R.id.main_anchor_header_owner_follow);
        mVideoListDivider = fragment.findViewById(R.id.main_v_video_list_divider);

        mTvPlayInfo = fragment.findViewById(R.id.main_tv_play_info);
        FrameLayout flDanmaBtn = fragment.findViewById(R.id.main_fl_danmaku_btn);
        FrameLayout flSendDanma = fragment.findViewById(R.id.main_fl_send_danmaku);
        mIvDanmaBtn = fragment.findViewById(R.id.main_iv_danmaku_btn);
        mTvSendDanma = fragment.findViewById(R.id.main_tv_send_danmaku);
        mTvDescription = fragment.findViewById(R.id.main_tv_description);
        mTvLike = fragment.findViewById(R.id.main_tv_like);
        mTvDownload = fragment.findViewById(R.id.main_tv_download);
        mTvShare = fragment.findViewById(R.id.main_tv_track_view_share);
        mVSpaceAlbumInfo = fragment.findViewById(R.id.main_space_album_info);

        if (fragment instanceof View.OnClickListener) {
            mVSpaceAlbumInfo.setOnClickListener((View.OnClickListener) fragment);
            mVAnchorLayout.setOnClickListener((View.OnClickListener) fragment);
            flSendDanma.setOnClickListener((View.OnClickListener) fragment);
            mTvDescription.setOnClickListener((View.OnClickListener) fragment);
            mTvDownload.setOnClickListener((View.OnClickListener) fragment);
            mTvShare.setOnClickListener((View.OnClickListener) fragment);
        }
        flDanmaBtn.setOnClickListener(this);
        mTvLike.setOnClickListener(this);
        mTrackSubTv.setOnClickListener(this);
        mFollowTv.setOnClickListener(this);
        mVideoListView = new VideoListViewNew(mFunction.getContext(), mFunction);
        mVideoListView.init(fragment);
    }

    @Override
    public void onClick(View view) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return;
        }
        int id = view.getId();
        if (id == R.id.main_fl_danmaku_btn) {
            onDanmakuBtnClick();
            if (mAlbum!=null && mTrack!=null){
                new XMTraceApi.Trace()
                        .click(49562) // 用户点击时上报
                        .put("currPage", "newVideoPlay")
                        .put("albumId", getAlbumIdStr())
                        .put("trackId", getTrackIdStr())
                        .put("anchorId", getAnchorUidStr())
                        .put("Item", "弹幕开关")
                        .put("status", "半屏") // 全屏/半屏
                        .createTrace();
            }
        } else if (id == R.id.main_tv_like) {
            onLikeClick();

                new XMTraceApi.Trace()
                        .click(49569) // 用户点击时上报
                        .put("currPage", "newVideoPlay")
                        .put("trackId", getTrackIdStr())
                        .put("albumId", getAlbumIdStr())
                        .put("anchorId", getAnchorUidStr())
                        .put("Item", "点赞") // 点赞｜下载｜分享
                        .createTrace();

        } else if (id == R.id.main_header_owner_subscribe) {
            // 播放页视频tab-专辑条-订阅/取消订阅  点击事件
            if (mAlbum!=null){
                new XMTraceApi.Trace()
                        .click(17743) // 用户点击时上报
                        .put("trackId", getTrackIdStr())
                        .put("categoryId",getCategoryIdStr())
                        .put("albumId", getAlbumIdStr())
                        .put("anchorId", getAnchorUidStr())
                        .put("Item", mAlbum.isFavorite() ? "已订阅":"免费订阅")
                        .put("currPage", "newVideoPlay")
                        .createTrace();
            }
            onSubscribeClick();
        } else if (id == R.id.main_anchor_header_owner_follow) {
            followAnchor();
        }
    }

    private String getAlbumIdStr(){
        if (mAlbum==null){
            return "0";
        }
        return mAlbum.getId()+"";
    }

    private String getTrackIdStr(){
        if (mTrack==null){
            return "0";
        }
        return mTrack.getDataId()+"";
    }

    private String getCategoryIdStr(){
        if (mTrack == null){
            return "0";
        }
        return mTrack.getCategoryId() + "";
    }

    private String getAnchorUidStr(){
        if (mTrack==null){
            return "0";
        }
        return mTrack.getAnchorUid() + "";
    }

    public void gone() {
        if (!mFunction.canUpdateUi()) {
            return;
        }
        if (mViewStub != null) {
            mViewStub.setVisibility(View.GONE);
        }
        mIvAlbumCover.setVisibility(View.GONE);
        mTvAlbumTitle.setVisibility(View.GONE);
        mTvSubscribeCount.setVisibility(View.GONE);
        mTrackSubTv.setVisibility(View.GONE);
        AlbumEventManage.removeListener(this);
    }

    public void visible() {
        if (!mFunction.canUpdateUi()) {
            return;
        }
        if (mViewStub != null) {
            mViewStub.setVisibility(View.VISIBLE);
        }
        mIvAlbumCover.setVisibility(View.VISIBLE);
        mTvAlbumTitle.setVisibility(View.VISIBLE);
        mTvSubscribeCount.setVisibility(View.VISIBLE);
        mTrackSubTv.setVisibility(View.VISIBLE);
        AlbumEventManage.addListener(this);
    }

    @Override
    public void setTrackInfo(@NotNull PlayingSoundInfo playingSoundInfo, AlbumM album, boolean autoStart) {
        mPlayingSoundInfo = playingSoundInfo;
        if (album != null) {
            mSubscribeCount = album.getSubscribeCount();
        }
        mAlbum = album;
        mTrack = playingSoundInfo.trackInfo2TrackM();
        if (mFunction == null || !mFunction.canUpdateUi() || mTrack == null || mAlbum == null) {
            gone();
            return;
        }
        visible();

        // 判断是否展示简介
        PlayPageDataManager.getInstance().loadTrackIntro(mTrack, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                if (mFunction.canUpdateUi()) {
                    mTvDescription.setVisibility(TextUtils.isEmpty(object) ? View.INVISIBLE : View.VISIBLE);
                }
            }

            @Override
            public void onError(int code, String message) {
            }
        });

        mTvLike.setText(StringUtil.getFriendlyNumStrSearch(mTrack.getFavoriteCount()));
        mTvLike.setSelected(mTrack.isLike());

        String text = StringUtil.getFriendlyDataStr(mTrack.getCreatedAt()) + "发布" + "  |  " +
                StringUtil.getFriendlyNumStrAndCheckIsZero(mTrack.getPlayCount(),
                        mFunction.getFragment().getStringSafe(R.string.main_num_play));
        mTvPlayInfo.setText(text);
        mTvTrackTitle.setText(mTrack.getTrackTitle());

        boolean isDanmakuOpen = VideoPlayCommonUtil.getVideoDanmakuOpenState(mFunction.getContext());
        if (autoStart) {
            setDanmakuViewState(isDanmakuOpen);
        } else {
            setDanmakuViewState(isDanmakuOpen);
        }

        if (PlayCommentUtil.isDecoupleTrack(playingSoundInfo)) {
            updateAnchorFollowStatus();
            if (playingSoundInfo.userInfo != null) {
                ImageManager.from(mFunction.getContext()).displayImage(mFunction.getFragment(),
                        mIvAnchorCover, playingSoundInfo.userInfo.smallLogo, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);
                mTvAnchorTitle.setText(playingSoundInfo.userInfo.nickname);
                mTvAnchorFollowCount.setText(String.format("已被%s人关注", StringUtil.getFriendlyNumStr(playingSoundInfo.userInfo.followers)));
            }
            if (mVideoListView != null) {
                mVideoListView.gone();
            }
            mVideoListDivider.setVisibility(View.GONE);
            mVAlbumLayout.setVisibility(View.GONE);
            mVAnchorLayout.setVisibility(View.VISIBLE);
        } else {
            if (mAlbum != null) {
                setSubscribeStatus(mAlbum.isFavorite());
                ImageManager.from(mFunction.getContext()).displayImage(mFunction.getFragment(),
                        mIvAlbumCover, mAlbum.getValidCover(), com.ximalaya.ting.android.host.R.drawable.host_default_album);
                mTvAlbumTitle.setText(mAlbum.getAlbumTitle());
                mTvSubscribeCount.setText(StringUtil.getFriendlyNumStrAndCheckIsZero(mSubscribeCount,
                        mFunction.getFragment().getStringSafe(R.string.main_num_people_sub)));
                if (mVideoListView != null) {
                    mVideoListView.visible();
                }
                mVideoListDivider.setVisibility(View.VISIBLE);
                mVAlbumLayout.setVisibility(View.VISIBLE);
                mVAnchorLayout.setVisibility(View.GONE);
            }
        }

        AutoTraceHelper.bindData(mVSpaceAlbumInfo, "播放页", mAlbum);
        AutoTraceHelper.bindData(mTrackSubTv, "播放页", mAlbum);
    }

    /**
     * 专辑条
     */
    public View getAlbumItView(){
        return mVAlbumLayout;
    }

    /**
     * 简介textView
     */
    public View getTvDescription(){
        return mTvDescription;
    }

    /**
     * 选集View
     */
    public View getVideoListView(){
        if (mVideoListView == null) return null;
        return mVideoListView.getRootView();
    }

    @Override
    public void release() {
        AlbumEventManage.removeListener(this);
        if (mVideoListView != null) {
            mVideoListView.release();
        }
    }

    private void updateAnchorFollowStatus() {
        if (mFollowTv == null) {
            return;
        }
        if (mPlayingSoundInfo == null || mPlayingSoundInfo.otherInfo == null) {
            return;
        }
        if (ChildProtectManager.isChildProtectOpen(mFunction.getContext())) {
            mFollowTv.setVisibility(View.INVISIBLE);
            return;
        }
        boolean isFollow = mPlayingSoundInfo.otherInfo.isFollowed;
        if (isFollow) {
            TextViewExtensitionKt.setTextIfChanged(mFollowTv, "已关注");
            mFollowTv.setTextColor(Color.WHITE);
        } else {
            TextViewExtensitionKt.setTextIfChanged(mFollowTv, "关注");
            mFollowTv.setTextColor(mFunction.getContext().getResources().getColor(com.ximalaya.ting.android.host.R.color.host_color_ff4c2e));
        }
        mFollowTv.setSelected(!isFollow);
        mFollowTv.setContentDescription(isFollow ? "取消关注" : "关注");
    }

    private void followAnchor() {
        if (mPlayingSoundInfo == null
                || mPlayingSoundInfo.otherInfo == null
                || mPlayingSoundInfo.userInfo == null) {
            return;
        }
        boolean isCurrentFollowed = mPlayingSoundInfo.otherInfo.isFollowed;
        AnchorFollowManage.followV2(mFunction.getActivity(), isCurrentFollowed,
                mPlayingSoundInfo.userInfo.uid,
                AnchorFollowManage.FOLLOW_BIZ_TYPE_MAIN_AUDIO_PLAY_PAGE,
                new IDataCallBack<Boolean>() {
                    @Override
                    public void onSuccess(Boolean object) {
                        if (mFunction == null || !mFunction.canUpdateUi()) {
                            return;
                        }
                        if (object == null || mPlayingSoundInfo == null
                                || mPlayingSoundInfo.otherInfo == null) {
                            return;
                        }
                        mPlayingSoundInfo.otherInfo.isFollowed = object;
                        if (object) {
                            CustomToast.showSuccessToast("关注成功");
                        }
                        updateAnchorFollowStatus();
                    }

                    @Override
                    public void onError(int code, String message) {
                    }
                }, mFollowTv);
    }

    /**
     * @param isFavorite true 表示已订阅
     */
    public void setSubscribeStatus(boolean isFavorite) {
        mTrackSubTv.setSelected(isFavorite);
        mTrackSubTv.setText(isFavorite ? "已订阅" : "免费订阅");
    }

    private void onDanmakuBtnClick() {
        setDanmakuViewStateAndNotify(!VideoPlayCommonUtil.getVideoDanmakuOpenState(mFunction.getContext()));
    }

    public void setDanmakuViewStateAndNotify(boolean isDanmakuOpen) {
        setDanmakuViewState(isDanmakuOpen);
        notifyDanmakuViewState(isDanmakuOpen);
    }

    private void setDanmakuViewState(boolean isDanmakuOpen) {
        VideoPlayCommonUtil.setVideoDanmakuOpenState(mFunction.getContext(), isDanmakuOpen);
        mIvDanmaBtn.setSelected(isDanmakuOpen);
        if (isDanmakuOpen) {
            mTvSendDanma.setVisibility(View.VISIBLE);
            mIvDanmaBtn.setBackground(ContextCompat.getDrawable(mFunction.getContext(), R.drawable.main_bg_solid_f3f3f3_right_radius_12));
        } else {
            mTvSendDanma.setVisibility(View.GONE);
            mIvDanmaBtn.setBackground(ContextCompat.getDrawable(mFunction.getContext(), R.drawable.main_bg_solid_f3f3f3_radius_12));
        }
    }

    private void notifyDanmakuViewState(boolean isDanmakuOpen) {
        if (mEventListener != null) {
            mEventListener.onDanmakuOpenState(isDanmakuOpen);
        }
    }

    private void onLikeClick() {
        if (mTrack == null) {
            return;
        }
        Activity activity = BaseApplication.getTopActivity();
        if (activity == null) {
            return;
        }
        if (!UserInfoMannage.hasLogined()) {
            UserInfoMannage.gotoLogin(activity);
            return;
        }
        final Track track = mTrack;
        LikeTrackManage.toLikeOrUnLike(track, null, activity, new IDataCallBack<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean object) {
                if (object == null || !object) {
                    return;
                }
                mTrack.setLike(!mTrack.isLike());
                int likeCount = mTrack.getFavoriteCount();
                mTrack.setFavoriteCount(mTrack.isLike() ? likeCount + 1 : likeCount - 1);
                mTvLike.setText(StringUtil.getFriendlyNumStrSearch(mTrack.getFavoriteCount()));
                mTvLike.setSelected(mTrack.isLike());
            }

            @Override
            public void onError(int code, String message) {
                CustomToast.showFailToast(message);
            }
        });
    }

    private void onSubscribeClick() {
        if (mAlbum == null) {
            return;
        }
        AlbumEventManage.doCollectActionV2(mAlbum, mFunction.getFragment(), new ICollectStatusCallback() {
            @Override
            public void onCollectSuccess(int code, final boolean isCollected) {
                if (!mFunction.canUpdateUi()) {
                    return;
                }
                mAlbum.setFavorite(isCollected);
                setSubscribeStatus(isCollected);
                mSubscribeCount += isCollected ? 1 : -1;
                mAlbum.setSubscribeCount(mSubscribeCount);
                mTvSubscribeCount.setText(StringUtil.getFriendlyNumStrAndCheckIsZero(mSubscribeCount, mFunction.getFragment().getStringSafe(R.string.main_num_people_sub)));
                showRecommendSubscribeView();
            }

            @Override
            public void onError() {

            }
        });
    }

    private void showRecommendSubscribeView() {
        if (mAlbum == null) {
            return;
        }
        long startTime = System.currentTimeMillis();
        SubscribeRecommendFragment.doSubscribeRecommendRequestWithoutLocalSubscribes(
                mAlbum.getId(),
                mFunction.getContext(),
                new IDataCallBack<SubscribeRecommendAlbumMListWithDescription>() {
                    @Override
                    public void onSuccess(@Nullable SubscribeRecommendAlbumMListWithDescription object) {
                        if (!mFunction.canUpdateUi()) {
                            return;
                        }
                        long timeSpend = System.currentTimeMillis() - startTime;
                        if (timeSpend > 1000 || object == null || !mAlbum.isFavorite()) {
                            return;
                        }
                        if (ToolUtil.isEmptyCollects(object.getAlbumMList())) {
                            CustomToast.showFailToast("已全部订阅完了");
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        CustomToast.showFailToast(message);
                    }
                }
        );
    }

    @Override
    public void onCollectChanged(boolean collect, long id) {
        if (mFunction.canUpdateUi() && id == mAlbum.getId()) {
            if (mAlbum == null) {
                return;
            }
            if (mAlbum.getId() == id) {
                mAlbum.setFavorite(collect);
            }
            if (mFunction != null && mFunction.canUpdateUi()) {
                setSubscribeStatus(mAlbum.isFavorite());
            }
        }
    }

    public void showDownloadView(boolean show) {
        mTvDownload.setVisibility(show ? View.VISIBLE : View.INVISIBLE);
    }

    public void setDownloadViewState(boolean isDownloaded) {
        mTvDownload.setCompoundDrawablesWithIntrinsicBounds(
                0, isDownloaded ? R.drawable.main_ic_video_downloaded : R.drawable.main_ic_video_download, 0, 0);
    }

    public void setVideoPlayListPresenter(VideoPlayListPresenter presenter) {
        if (mVideoListView != null) {
            mVideoListView.setPresenter(presenter);
        }
    }

}
