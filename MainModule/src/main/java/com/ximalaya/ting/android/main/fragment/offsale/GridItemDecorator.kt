package com.ximalaya.ting.android.main.fragment.offsale

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyItemSpacingDecorator
import com.ximalaya.ting.android.host.util.extension.dp

class GridItemDecorator(
    private val pxForEdge: Int
): RecyclerView.ItemDecoration() {

    private var verticallyScrolling = false
    private var horizontallyScrolling = false
    private var firstItem = false
    private var lastItem = false
    private var grid = false
    private var isFirstItemInRow = false
    private var isLastItemInRow = false
    private var spanIndex = 0
    private var fillsLastSpan = false
    private var isInFirstRow = false
    private var isInLastRow = false

    private var spanCount = 3
    private var fillHorizontal: Boolean = false

    private fun useRightPadding(): Boolean {
        return if (this.grid) {
            this.fillHorizontal ||this.horizontallyScrolling && !this.isInLastRow || this.verticallyScrolling && this.isLastItemInRow
        } else {
            this.horizontallyScrolling && !this.lastItem
        }
    }

    private fun useLeftPadding(): Boolean {
        return if (this.grid) {
            this.fillHorizontal || this.horizontallyScrolling && !this.isInFirstRow || this.verticallyScrolling && this.isFirstItemInRow
        } else {
            this.horizontallyScrolling && !this.firstItem
        }
    }

    private fun isInFirstRow(
        position: Int,
        spanSizeLookup: GridLayoutManager.SpanSizeLookup,
        spanCount: Int
    ): Boolean {
        var totalSpan = 0
        for (i in 0..position) {
            totalSpan += spanSizeLookup.getSpanSize(i)
            if (totalSpan > spanCount) {
                return false
            }
        }
        return true
    }

    private fun isInLastRow(
        position: Int,
        itemCount: Int,
        spanSizeLookup: GridLayoutManager.SpanSizeLookup,
        spanCount: Int
    ): Boolean {
        var totalSpan = 0
        for (i in itemCount - 1 downTo position) {
            totalSpan += spanSizeLookup.getSpanSize(i)
            if (totalSpan > spanCount) {
                return false
            }
        }
        return true
    }

    private fun calculatePositionDetails(
        parent: RecyclerView,
        position: Int,
        layout: RecyclerView.LayoutManager
    ) {
        val itemCount = parent.adapter!!.itemCount
        this.firstItem = position == 0
        this.lastItem = position == itemCount - 1
        this.horizontallyScrolling = layout.canScrollHorizontally()
        this.verticallyScrolling = layout.canScrollVertically()
        this.grid = layout is GridLayoutManager
        if (this.grid) {
            val grid = layout as GridLayoutManager
            val spanSizeLookup = grid.spanSizeLookup
            val spanSize = spanSizeLookup.getSpanSize(position)
            this.spanCount = grid.spanCount
            this.fillHorizontal = spanCount == spanSize
            this.spanIndex = spanSizeLookup.getSpanIndex(position, spanCount)
            this.isFirstItemInRow = spanIndex == 0
            this.isLastItemInRow = spanIndex == spanCount - 1
            this.fillsLastSpan = spanIndex + spanSize == spanCount
            this.isInFirstRow = isInFirstRow(position, spanSizeLookup, spanCount)
            this.isInLastRow = !this.isInFirstRow && isInLastRow(
                position,
                itemCount,
                spanSizeLookup,
                spanCount
            )
        }
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)

        outRect.setEmpty()
        val position = parent.getChildAdapterPosition(view)
        if (position != -1) {
            //ignore first
            if (position == 0) return

            val layout = parent.layoutManager?: return
            this.calculatePositionDetails(parent, position, layout)
            val left: Boolean = this.useLeftPadding()
            val right: Boolean = this.useRightPadding()
//            var top: Boolean = this.useTopPadding()
//            var bottom: Boolean = this.useBottomPadding()
            val padding: Int = pxForEdge
            outRect.right = if (right) padding else 0
            outRect.left = if (left) padding else 0

            if(spanIndex > 0 && spanIndex < spanCount -1) {
                outRect.right = 8.dp
                outRect.left = 8.dp
            }
        }
    }

}