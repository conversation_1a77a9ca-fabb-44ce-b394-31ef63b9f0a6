package com.ximalaya.ting.android.main.dialog;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.track.AlbumEventManage;
import com.ximalaya.ting.android.host.model.play.ShareToFreeListenPlayModel;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

public class ShareToFreeListenDialog extends BaseDialogFragment
        implements View.OnClickListener {
    public static final String TAG = "ShareToFreeListenDialog";
    public static final String ARGS_MODEL = "argsModel";
    public static final String ARGS_ALBUM_ID = "argsAlbumId";
    public static final String ARGS_TRACK_ID = "argsTrackId";


    private ShareToFreeListenPlayModel mModel;
    private long mAlbumId;
    private long mTrackId;



    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle args = getArguments();
        if (args != null) {
            mModel = (ShareToFreeListenPlayModel) args.getSerializable(ARGS_MODEL);
            mAlbumId = args.getLong(ARGS_ALBUM_ID);
            mTrackId = args.getLong(ARGS_TRACK_ID);
        }
        if (mModel == null || mAlbumId == 0) {
            dismiss();
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = getDialog().getWindow();
        if(window != null){
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            window.setLayout(
                    BaseUtil.getScreenWidth(getContext()) - BaseUtil.dp2px(getContext(), 45) * 2,
                    ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        View mView = inflater.inflate(R.layout.main_dialog_share_free_listen_time_over, ((ViewGroup) window.findViewById(android.R.id.content)), false);
        ImageView vCloseIcon = mView.findViewById(R.id.main_close);
        TextView vSubTitle = mView.findViewById(R.id.main_subtitle);
        TextView vCouponButton = mView.findViewById(R.id.main_coupon_btn);
        TextView vMoreAlbumButton = mView.findViewById(R.id.main_more_free_listen);

        vSubTitle.setText(mModel.description);
        vCouponButton.setText(mModel.discountContent);
        vMoreAlbumButton.setVisibility(mModel.inActivity ? View.VISIBLE : View.GONE);

        vCloseIcon.setOnClickListener(this);
        vCouponButton.setOnClickListener(this);
        vMoreAlbumButton.setOnClickListener(this);

        return mView;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.main_close) {
            dismiss();
        } else if (id == R.id.main_coupon_btn) {
            dismiss();
            AlbumEventManage.startMatchAlbumFragment(mAlbumId, AlbumEventManage.FROM_OTHER, ConstantsOpenSdk.PLAY_FROM_OTHER,
                    null, null, -1, getActivity());
            new UserTracking("track","button").setTrackId(mTrackId).setSrcModule("18123免费听结束弹窗").setItemId(mModel.discountContent)
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,"trackPageClick");

        } else if (id == R.id.main_more_free_listen) {
            dismiss();
            if (getActivity() instanceof MainActivity) {
                Bundle bundle = new Bundle();
                bundle.putString(BundleKeyConstants.KEY_EXTRA_URL, mModel.moreAlbumsUrl);
                ((MainActivity) getActivity()).startFragment(NativeHybridFragment.newInstance(bundle));
            }
            new UserTracking("track","button").setTrackId(mTrackId).setSrcModule("18123免费听结束弹窗").setItemId("更多专辑免费听")
                    .statIting(XDCSCollectUtil.APP_NAME_EVENT,"trackPageClick");
        }
    }
}
