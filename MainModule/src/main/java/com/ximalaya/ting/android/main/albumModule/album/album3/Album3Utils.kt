package com.ximalaya.ting.android.main.albumModule.album.album3

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.text.TextUtils
import android.widget.TextView
import androidx.core.graphics.ColorUtils
import androidx.palette.graphics.Palette
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.host.async.AsyncImage
import com.ximalaya.ting.android.host.model.album.Album3Type
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.util.view.PaletteCache
import com.ximalaya.ting.android.host.util.view.PaletteCache.get
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.playpage.manager.YDomainColorUtil
import com.ximalaya.ting.android.xmutil.Logger
import kotlin.math.max
import kotlin.math.min

/**
 * Author: <PERSON>
 * Email: <EMAIL>
 * Date: 2024/7/15
 * Description：
 */
object Album3Utils {
    private val TAG = "Album3Utils"

    fun isNovelStyleAlbum(contentType: String? = "default"): Boolean {
        return contentType == Album3Type.NOVEL.contentType()
    }

    fun isNovelStyleAlbum(album: AlbumM? = null): Boolean {
        album?:return false
        return album.albumStyle == Album3Type.NOVEL.contentType()
    }

    fun isPodCastStyleAlbum(contentType: String? = "default"): Boolean {
        return contentType == Album3Type.PODCAST.contentType()
    }

    fun isPodCastStyleAlbum(album: AlbumM? = null): Boolean {
        album?:return false
        return album.albumStyle == Album3Type.PODCAST.contentType()
    }

    fun isBigIpStyleAlbum(contentType: String? = "default"): Boolean {
        return contentType == Album3Type.BIG_IP.contentType()
    }

    fun isBigIpStyleAlbum(album: AlbumM? = null): Boolean {
        album?:return false
        return album.albumStyle == Album3Type.BIG_IP.contentType()
    }

    fun getColorWithAlpha(alpha: Float, baseColor: Int): Int {
        val a = min(255, max(0, (alpha * 255).toInt())) shl 24
        val rgb = 0x00ffffff and baseColor
        return a + rgb
    }


    /**
     * @param albumM 专辑
     * @param level 1: 模块间标题 2: 模块内标题 3: 模块内副标题
     * */
    fun getNormalColorByLevelAndType(context: Context?, albumM: AlbumM?, level: Int): Int {
        if (context == null) {
            return 0
        }
        if (albumM == null) {
            return context.resources.getColor(R.color.main_color_131313_ffffff)
        }
        when (albumM.albumStyle) {
            Album3Type.NOVEL.contentType() -> {
                if (level == 2) {
                    return getColorWithAlpha(0.7f, context.resources.getColor(R.color.main_color_131313_ffffff))
                } else if (level == 3) {
                    return getColorWithAlpha(0.3f, context.resources.getColor(R.color.main_color_131313_ffffff))
                } else {
                    return context.resources.getColor(R.color.main_color_131313_ffffff)
                }
            }
            Album3Type.PODCAST.contentType() -> {
                when (level) {
                    2 -> {
                        return getColorWithAlpha(0.7f, context.resources.getColor(R.color.main_color_131313_ffffff))
                    }
                    3 -> {
                        return getColorWithAlpha(0.3f, context.resources.getColor(R.color.main_color_131313_ffffff))
                    }
                    else -> {
                        return context.resources.getColor(R.color.main_color_131313_ffffff)
                    }
                }
            }
            Album3Type.BIG_IP.contentType() -> {
                when (level) {
                    2 -> {
                        return getColorWithAlpha(0.7f, context.resources.getColor(R.color.main_color_ffffff))
                    }
                    3 -> {
                        return getColorWithAlpha(0.3f, context.resources.getColor(R.color.main_color_ffffff))
                    }
                    else -> {
                        return context.resources.getColor(R.color.main_color_ffffff)
                    }
                }
            }
            else -> 0
        }
        return context.resources.getColor(R.color.main_color_131313_ffffff)
    }


    @JvmStatic
    fun getDomainColor(url: String?, bitmap: Bitmap?, defColor: Int, callback: LocalImageUtil.Callback?) {
        val color = PaletteCache.get(url)
        if (color != null && callback != null) {
            callback.onMainColorGot(getColorFromMainColor(color))
            return
        }

        if (bitmap != null && !bitmap.isRecycled && callback != null) {
            Palette.Builder(bitmap).maximumColorCount(16).generate { palette: Palette? ->
                try {
                    val rgb = getColorFromPalette(url, defColor, palette)
                    callback.onMainColorGot(getColorFromMainColor(rgb))
                } catch (e: Exception) {
                    callback.onMainColorGot(defColor)
                }
            }
        } else {
            callback?.onMainColorGot(defColor)
        }
    }

    private fun getColorFromPalette(url: String?, defColor: Int, palette: Palette?): Int {
        if (palette == null) return defColor
        val color = palette.getVibrantColor(defColor)
        if (color == defColor) {
            return palette.getDominantColor(defColor)
        }

        PaletteCache.put(url, color)

        return color
    }

    private fun getColorFromMainColor(rgb: Int): Int {
        val hsl = FloatArray(3)
        val r = Color.red(rgb)
        val g = Color.green(rgb)
        val b = Color.blue(rgb)
        ColorUtils.RGBToHSL(r, g, b, hsl)
        val color = adjustHsl(hsl[0], hsl[1], hsl[2])
        if (color == 0xffffff.toInt()) {
            return 0xB7BAD5.toInt()
        }
        return if (color == 0x000000) {
            0xB7BAD5.toInt()
        } else color
    }

    private fun adjustHsl(h: Float, s: Float, l: Float): Int {
        var newS = s
        var newH = h
        var newL = l
        Logger.d(Album3Utils.TAG, "adjustHsl: begin $h:$s:$l")
        if (s <= 0.06f) {
            return 0xffb7bad5.toInt()
        }

        if (s in 0.05f .. 0.15f) {
            newS = s + 0.05f
            newL = 0.88f
            Logger.d(Album3Utils.TAG, "adjustHsl: end $newH:$newS:$newL")
            return ColorUtils.HSLToColor(floatArrayOf(newH, newS, newL))
        }

        when(h.toInt()) {
            in 0..10 -> {
                newS = 0.48f
                newL = 0.9f
            }
            in 320..360 -> {
                newS = 0.48f
                newL = 0.9f
            }
            in 11..49 -> {
                newS = 0.8f
                newL = 0.9f
            }
            in 50..60 -> {
                newS = 0.6f
                newL = 0.9f
            }
            in 61..80 -> {
                newS = 0.40f
                newL = 0.88f
            }
            in 81..94 -> {
                newS = 0.4f
                newL = 0.88f
            }
            in 95..140 -> {
                newS = 0.4f
                newL = 0.88f
            }
            in 141..174 -> {
                newS = 0.40f
                newL = 0.88f
            }
            in 175..235 -> {
                newS = 0.26f
                newL = 0.78f
            }
            in 236..270 -> {
                newS = 0.25f
                newL = 0.8f
            }
            in 271..319 -> {
                newS = 0.25f
                newL = 0.8f
            }
        }
        Logger.d(Album3Utils.TAG, "adjustHsl: end $newH:$newS:$newL")
        return ColorUtils.HSLToColor(floatArrayOf(newH, newS, newL))
    }

    fun appendDarkModeParams(originalUrl: String): String? {
        if (TextUtils.isEmpty(originalUrl)) {
            return originalUrl
        }
        return if (originalUrl.contains("?")) {
            originalUrl + "&isDarkMode=" + BaseFragmentActivity.sIsDarkMode
        } else {
            originalUrl + "?isDarkMode=" + BaseFragmentActivity.sIsDarkMode
        }
    }

    fun getTvRealWidth(textView: TextView, textString: String?): Float {
        if (TextUtils.isEmpty(textString)) {
            return 0f
        }
        val paint = textView.paint
        val paddingLeft = textView.paddingLeft
        val paddingRight = textView.paddingRight
        return paint.measureText(textString)+ paddingLeft + paddingRight
    }

    fun getItingParamEncode(itingUrl: String?, paramName: String?): String? {
        if (TextUtils.isEmpty(itingUrl) || TextUtils.isEmpty(paramName)) {
            return null
        }
        try {
            return Uri.encode(Uri.parse(itingUrl).getQueryParameter(paramName))
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }


    interface Callback {
        fun onMainColorGot(colorBottomRound: Int, colorMiddleRound: Int)
    }

    /**
     * @param type:0 底部圆  1中间圆
     * */
    @JvmStatic
    fun getPodCastAlbumDomainColor(url: String?, bitmap: Bitmap?, defColor: Int, callback: Callback?) {
        val color = get(url)
        if (color != null && callback != null) {
            callback.onMainColorGot(getPodCastColorFromMainColor(color, 0), getPodCastColorFromMainColor(color, 1))
            return
        }

        if (bitmap != null && !bitmap.isRecycled && callback != null) {
            Palette.Builder(bitmap).maximumColorCount(16).generate { palette: Palette? ->
                try {
                    val rgb = getColorFromPalette(url, defColor, palette)
                    callback.onMainColorGot(getPodCastColorFromMainColor(rgb, 0), getPodCastColorFromMainColor(rgb, 1))
                } catch (e: Exception) {
                    callback.onMainColorGot(defColor, defColor)
                }
            }
        } else {
            callback?.onMainColorGot(defColor, defColor)
        }
    }

    private fun getPodCastColorFromMainColor(rgb: Int, type: Int): Int {
        val hsl = FloatArray(3)
        val r = Color.red(rgb)
        val g = Color.green(rgb)
        val b = Color.blue(rgb)
        ColorUtils.RGBToHSL(r, g, b, hsl)
        val color = if (type == 0) adjustPodCastHslBottom(hsl[0], hsl[1], hsl[2]) else adjustPodCastHslMiddle(hsl[0], hsl[1], hsl[2])
        if (color == 0xffffff.toInt()) {
            return 0xB7BAD5.toInt()
        }
        return if (color == 0x000000) {
            0xB7BAD5.toInt()
        } else color
    }

    private fun adjustPodCastHslBottom(h: Float, s: Float, l: Float): Int {
        var newS = s
        var newH = h
        var newL = l
        Logger.d(Album3Utils.TAG, "adjustPodCastHslBottom: begin $h:$s:$l")
        when(h.toInt()) {
            in 0..10 -> {
                newS = if (s <= 0.2f) {
                    return Color.parseColor("#A88A8B")
                } else if (s in 0.07f..0.4f) {
                    s + 0.2f
                } else {
                    0.6f
                }
                newL = 0.7f
            }
            in 11..30 -> {
                newS = if (s <= 0.06f) {
                    return Color.parseColor("#B1A29A")
                } else if (s in 0.1f..0.3f) {
                    s + 0.2f
                } else if (s in 0.3f..0.7f) {
                    s
                } else {
                    0.7f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else if (s in 0.625f..0.675f) {
                    0.65f
                } else {
                    0.7f
                }
            }
            in 31..49 -> {
                newS = if (s <= 0.06f) {
                    return Color.parseColor("#B1A29A")
                } else if (s in 0.1f..0.3f) {
                    s + 0.2f
                } else if (s in 0.3f..0.7f) {
                    s
                } else {
                    0.7f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else if (s in 0.625f..0.675f) {
                    0.65f
                } else {
                    0.7f
                }
            }
            in 320..340 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#B996AA")
                } else if (s in 0.11f..0.2f) {
                    s + 0.15f
                } else if (s in 0.21f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else {
                    0.65f
                }
            }
            in 341..360 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#AD858B")
                } else if (s in 0.11f..0.35f) {
                    0.35f
                } else if (s in 0.35f..0.55f) {
                    s
                } else {
                    0.55f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else {
                    0.65f
                }
            }
            in 50..60 -> {
                newS = if (s <= 0.06f) {
                    return Color.parseColor("#969592")
                } else if (s in 0.07f..0.4f) {
                    s + 0.2f
                } else {
                    0.6f
                }
                newL = if (l <= 0.675f) {
                    0.65f
                } else {
                    0.7f
                }
            }
            in 61..80 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8C738C")
                } else if (s in 0.11f..0.3f) {
                    s + 0.15f
                } else if (s in 0.31f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.575f) {
                    0.55f
                } else {
                    0.6f
                }
            }
            in 81..140 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8E958C")
                } else if (s in 0.11f..0.2f) {
                    s + 0.1f
                } else if (s in 0.21f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.575f) {
                    0.55f
                } else {
                    0.6f
                }
            }
            in 141..174 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#627977")
                } else if (s in 0.11f..0.2f) {
                    s + 0.1f
                } else if (s in 0.21f..0.50f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.475f) {
                    0.45f
                } else if (s in 0.475f..0.525f) {
                    0.5f
                } else {
                    0.55f
                }
            }
            in 175..194 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#A4AAA1")
                } else if (s in 0.11f..0.2f) {
                    s + 0.1f
                } else if (s in 0.21f..0.50f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.475f) {
                    0.45f
                } else if (s in 0.475f..0.525f) {
                    0.5f
                } else {
                    0.55f
                }
            }
            in 195..214 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#597B9D")
                } else if (s in 0.11f..0.2f) {
                    s + 0.3f
                } else if (s in 0.21f..0.60f) {
                    s
                } else {
                    0.6f
                }
                newL = 0.65f
            }
            in 215..234 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#68759A")
                } else if (s in 0.11f..0.2f) {
                    s + 0.2f
                } else if (s in 0.21f..0.60f) {
                    s
                } else {
                    0.6f
                }
                newL = if (l <= 0.575f) {
                    0.55f
                } else {
                    0.6f
                }
            }
            in 235..270 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8585AD")
                } else if (s in 0.11f..0.2f) {
                    s + 0.15f
                } else if (s in 0.21f..0.50f) {
                    s
                } else {
                    0.5f
                }
                newL = 0.6f
            }
            in 271..319 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8C738C")
                } else if (s in 0.11f..0.3f) {
                    s + 0.1f
                } else if (s in 0.31f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.525f) {
                    0.5f
                } else if (s in 0.525f..0.6f) {
                    0.55f
                } else {
                    0.65f
                }
            }
        }
        Logger.d(Album3Utils.TAG, "adjustPodCastHslBottom: end $newH:$newS:$newL")
        return ColorUtils.HSLToColor(floatArrayOf(newH, newS, newL))
    }

    private fun adjustPodCastHslMiddle(h: Float, s: Float, l: Float): Int {
        var newS = s
        var newH = h
        var newL = l
        Logger.d(Album3Utils.TAG, "adjustPodCastHslMiddle: begin $h:$s:$l")
        when(h.toInt()) {
            in 0..10 -> {
                newH = h + 10
                newS = if (s <= 0.2f) {
                    return Color.parseColor("#A88A8B")
                } else if (s in 0.07f..0.4f) {
                    s + 0.2f
                } else {
                    0.6f
                }
                newL = 0.7f
            }
            in 11..30 -> {
                newH = h + 10
                newS = if (s <= 0.06f) {
                    return Color.parseColor("#B1A29A")
                } else if (s in 0.1f..0.3f) {
                    s + 0.2f
                } else if (s in 0.3f..0.7f) {
                    s
                } else {
                    0.7f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else if (s in 0.625f..0.675f) {
                    0.65f
                } else {
                    0.7f
                }
            }
            in 31..49 -> {
                newH = h + 10
                newS = if (s <= 0.06f) {
                    return Color.parseColor("#B1A29A")
                } else if (s in 0.1f..0.3f) {
                    s + 0.2f
                } else if (s in 0.3f..0.7f) {
                    s
                } else {
                    0.7f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else if (s in 0.625f..0.675f) {
                    0.65f
                } else {
                    0.7f
                }
            }
            in 320..340 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#B996AA")
                } else if (s in 0.11f..0.2f) {
                    s + 0.15f
                } else if (s in 0.21f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else {
                    0.65f
                }
            }
            in 341..360 -> {
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#AD858B")
                } else if (s in 0.11f..0.35f) {
                    0.35f
                } else if (s in 0.35f..0.55f) {
                    s
                } else {
                    0.55f
                }
                newL = if (l <= 0.625f) {
                    0.6f
                } else {
                    0.65f
                }
            }
            in 50..60 -> {
                newS = if (s <= 0.06f) {
                    return Color.parseColor("#969592")
                } else if (s in 0.07f..0.4f) {
                    s + 0.2f
                } else {
                    0.6f
                }
                newL = if (l <= 0.675f) {
                    0.65f
                } else {
                    0.7f
                }
            }
            in 61..80 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8C738C")
                } else if (s in 0.11f..0.3f) {
                    s + 0.15f
                } else if (s in 0.31f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.575f) {
                    0.55f
                } else {
                    0.6f
                }
            }
            in 81..140 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8E958C")
                } else if (s in 0.11f..0.2f) {
                    s + 0.1f
                } else if (s in 0.21f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.575f) {
                    0.55f
                } else {
                    0.6f
                }
            }
            in 141..174 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#627977")
                } else if (s in 0.11f..0.2f) {
                    s + 0.1f
                } else if (s in 0.21f..0.50f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.475f) {
                    0.45f
                } else if (s in 0.475f..0.525f) {
                    0.5f
                } else {
                    0.55f
                }
            }
            in 175..194 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#A4AAA1")
                } else if (s in 0.11f..0.2f) {
                    s + 0.1f
                } else if (s in 0.21f..0.50f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.475f) {
                    0.45f
                } else if (s in 0.475f..0.525f) {
                    0.5f
                } else {
                    0.55f
                }
            }
            in 195..214 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#597B9D")
                } else if (s in 0.11f..0.2f) {
                    s + 0.3f
                } else if (s in 0.21f..0.60f) {
                    s
                } else {
                    0.6f
                }
                newL = 0.65f
            }
            in 215..234 -> {
                newH = h + 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#68759A")
                } else if (s in 0.11f..0.2f) {
                    s + 0.2f
                } else if (s in 0.21f..0.60f) {
                    s
                } else {
                    0.6f
                }
                newL = if (l <= 0.575f) {
                    0.55f
                } else {
                    0.6f
                }
            }
            in 235..270 -> {
                newH = h - 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8585AD")
                } else if (s in 0.11f..0.2f) {
                    s + 0.15f
                } else if (s in 0.21f..0.50f) {
                    s
                } else {
                    0.5f
                }
                newL = 0.6f
            }
            in 271..319 -> {
                newH = h - 10
                newS = if (s <= 0.1f) {
                    return Color.parseColor("#8C738C")
                } else if (s in 0.11f..0.3f) {
                    s + 0.1f
                } else if (s in 0.31f..0.5f) {
                    s
                } else {
                    0.5f
                }
                newL = if (l <= 0.525f) {
                    0.5f
                } else if (s in 0.525f..0.6f) {
                    0.55f
                } else {
                    0.65f
                }
            }
        }
        Logger.d(Album3Utils.TAG, "adjustPodCastHslMiddle: end $newH:$newS:$newL")
        return ColorUtils.HSLToColor(floatArrayOf(newH, newS, newL))
    }

    @JvmStatic
    fun getTencentVideoDomainColor(url: String?, bitmap: Bitmap?, defColor: Int, callback: LocalImageUtil.Callback?) {
        val color = PaletteCache.get(url)
        if (color != null && callback != null) {
            callback.onMainColorGot(YDomainColorUtil.getColorFromMainColor(color))
            return
        }

        if (bitmap != null && !bitmap.isRecycled && callback != null) {
            Palette.Builder(bitmap).maximumColorCount(16).generate { palette: Palette? ->
                try {
                    val rgb = AsyncImage.getColorFromPalette(defColor, palette)
                    callback.onMainColorGot(YDomainColorUtil.getColorFromMainColor(rgb))
                } catch (e: Exception) {
                    callback.onMainColorGot(defColor)
                }
            }
        } else {
            callback?.onMainColorGot(defColor)
        }
    }

}