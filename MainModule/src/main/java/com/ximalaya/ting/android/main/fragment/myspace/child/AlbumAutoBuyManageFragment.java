package com.ximalaya.ting.android.main.fragment.myspace.child;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.view.ViewStub;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.handmark.pulltorefresh.library.PullToRefreshBase;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.pay.AutoRechargeABManager;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.myspace.AlbumAutoBuyManageAdapter;
import com.ximalaya.ting.android.main.manager.autoBuy.AutoBuyEventManager;
import com.ximalaya.ting.android.main.manager.autoBuy.AutoBuyManagePresenter;
import com.ximalaya.ting.android.main.model.album.AlbumAutoBuy;
import com.ximalaya.ting.android.main.model.pay.AutoRechargeModel;
import com.ximalaya.ting.android.main.request.MainCommonRequest;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Created by Lennon on 2018/4/27.
 * <AUTHOR>
 */

public class AlbumAutoBuyManageFragment extends BaseFragment2 {
    // 初始化界面
    public static final int MSG_UPDATE_UI_ON_FIRST_TIME = 1;
    // 刷新自动充值管理栏
    public static final int MSG_UPDATE_UI_ON_UPDATE_AUTO_RECHARGE_BAR = 2;

    @NonNull
    public static AlbumAutoBuyManageFragment newInstance() {
        Bundle args = new Bundle();
        AlbumAutoBuyManageFragment fragment = new AlbumAutoBuyManageFragment();
        fragment.setArguments(args);
        return fragment;
    }

    RefreshLoadMoreListView vListView;
    AlbumAutoBuyManageAdapter mAdapter;

    private boolean mContainsAutoRecharge;
    private ViewStub mAutoRechargeStub;
    private View mAutoRechargeArea;

    @Nullable
    private UiHandler mHandler;
    @Nullable
    private AutoBuyManagePresenter mPresenter;
    @Nullable
    private AutoBuyEventManager mEventManager;

    public AlbumAutoBuyManageFragment() {
        super(true, null);
        mContainsAutoRecharge = AutoRechargeABManager.getInstance().containsRechargeManage();
    }


    @Override
    protected String getPageLogicName() {
        return "管理自动购买";
    }


    @Override
    protected void initUi(Bundle savedInstanceState) {
        setTitle(R.string.main_text_manage_auto_buy);
        vListView = (RefreshLoadMoreListView) findViewById(R.id.main_listview);
        vListView.setMode(PullToRefreshBase.Mode.DISABLED);
        setNoContentTitle("没有专辑开启自动购买");

        initAutoRechargeContent();
    }

    private void initAutoRechargeContent() {
        if (mContainsAutoRecharge) {
            mAutoRechargeStub = findViewById(R.id.main_auto_recharge);

            mHandler = new UiHandler(this);
            mPresenter = new AutoBuyManagePresenter(this);
            mEventManager = new AutoBuyEventManager(mPresenter, this);
        }
    }

    @Override
    protected void loadData() {
        // 请求的接口不同
        if (mContainsAutoRecharge) {
            if (null != mPresenter) {
                onPageLoadingCompleted(LoadCompleteType.LOADING);
                mPresenter.requestOnFirstTime();
            }
        } else {
            // 以前的逻辑，不变动
            requestAutoBuyAlbumList();
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.main_manage_album_auto_buy;
    }

    @Override
    public int getTitleBarResourceId() {
        return R.id.main_title_bar;
    }

    @Override
    protected boolean isShowPlayButton() {
        return false;
    }

    private void requestAutoBuyAlbumList() {
        onPageLoadingCompleted(LoadCompleteType.LOADING);
        MainCommonRequest.albumAutoBuyList(new IDataCallBack<List<AlbumAutoBuy>>() {
            @Override
            public void onSuccess(@Nullable final List<AlbumAutoBuy> object) {
                if (!canUpdateUi()) return;
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        if (!ToolUtil.isEmptyCollects(object)) {
                            mAdapter = new AlbumAutoBuyManageAdapter(mContext, object);
                            vListView.setAdapter(mAdapter);
                            onPageLoadingCompleted(LoadCompleteType.OK);
                        } else {
                            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
                        }
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR);
            }
        });
    }

    public void updateUi(int msg) {
        if (null != mHandler) {
            mHandler.sendEmptyMessage(msg);
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    //////////////////////////      提供刷新ui的方法    //////////////////////////////
    //////////////////////////          start         //////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////

    private void updateUiOnFirstTime() {
        if (null == mPresenter || null == mPresenter.getData()) {
            return;
        }
        if (!ToolUtil.isEmptyCollects(mPresenter.getData().autoBuyAlbumVos)) {
            mAdapter = new AlbumAutoBuyManageAdapter(mContext, mPresenter.getData().autoBuyAlbumVos);
            vListView.setAdapter(mAdapter);
            onPageLoadingCompleted(LoadCompleteType.OK);
        } else {
            onPageLoadingCompleted(LoadCompleteType.NOCONTENT);
        }

        updateUiOnUpdateAutoRechargeBar();
    }

    private void updateUiOnUpdateAutoRechargeBar() {
        if (null == mPresenter || null == mPresenter.getData()) {
            return;
        }
        if (null != mPresenter.getData().autoPayVo) {
            if (AutoRechargeModel.STATUS_AUTO_RECHARGE_NONE == mPresenter.getData().autoPayVo.status
                    || AutoRechargeModel.STATUS_AUTO_RECHARGE_UNSIGNED == mPresenter.getData().autoPayVo.status) {
                // 解约或待签约时不显示
                ViewStatusUtil.setVisible(View.GONE, mAutoRechargeArea);
                return;
            }
            if (null == mAutoRechargeArea) {
                if (null == mAutoRechargeStub) {
                    return;
                }
                mAutoRechargeArea = mAutoRechargeStub.inflate();
            }
            if (null == mAutoRechargeArea) {
                // 从ViewStub创建失败
                return;
            }
            TextView rechargeValueTv = mAutoRechargeArea.findViewById(R.id.main_auto_recharge_value);
            View alterBtn = mAutoRechargeArea.findViewById(R.id.main_auto_recharge_alter_value);
            View cancelBtn = mAutoRechargeArea.findViewById(R.id.main_auto_recharge_cancel);

            String valueString = StringUtil.convertDouble(mPresenter.getData().autoPayVo.amount, 2);
            SpannableString value = new SpannableString(valueString);
            if (valueString.contains(".")) {
                int start = valueString.indexOf(".");
                int end = valueString.length();
                value.setSpan(new AbsoluteSizeSpan(BaseUtil.sp2px(getContext(), 13)), start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }

            ViewStatusUtil.setText(rechargeValueTv, value);

            if (null != mEventManager) {
                ViewStatusUtil.setOnClickListener(alterBtn, mEventManager.getClickListener());
                ViewStatusUtil.setOnClickListener(cancelBtn, mEventManager.getClickListener());
            }
        } else {
            // 无自动充值内容
            ViewStatusUtil.setVisible(View.GONE, mAutoRechargeArea);
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    //////////////////////////      提供刷新ui的方法    //////////////////////////////
    //////////////////////////           end          //////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////


    private static class UiHandler extends Handler {
        private WeakReference<AlbumAutoBuyManageFragment> mFragmentReference;

        public UiHandler(AlbumAutoBuyManageFragment fragment) {
            mFragmentReference = new WeakReference<>(fragment);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (null == getFragment()) {
                return;
            }
            switch (msg.what) {
                case MSG_UPDATE_UI_ON_FIRST_TIME:
                    getFragment().updateUiOnFirstTime();
                    break;
                case MSG_UPDATE_UI_ON_UPDATE_AUTO_RECHARGE_BAR:
                    getFragment().updateUiOnUpdateAutoRechargeBar();
                    break;
                default:
                    break;
            }
        }

        private AlbumAutoBuyManageFragment getFragment() {
            if (null == mFragmentReference
                    || null == mFragmentReference.get()
                    || !mFragmentReference.get().canUpdateUi()) {
                return null;
            }
            return mFragmentReference.get();
        }
    }
}
