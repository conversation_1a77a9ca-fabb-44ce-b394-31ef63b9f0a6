package com.ximalaya.ting.android.main.albumModule.album.reserve;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.autosize.AutoSize;
import com.ximalaya.ting.android.framework.autosize.AutoSizeConfig;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.play.XmSubPlayManager;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.play.SubPlayableModel;
import com.ximalaya.ting.android.host.model.track.TrackM;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.albumModule.album.album3.Album3Utils;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.widget.AbRecyclerViewAdapter;

import java.util.ArrayList;
import java.util.List;


public class ReserveTrialTracksAdapter extends AbRecyclerViewAdapter<RecyclerView.ViewHolder> {
    private final List<TrackM> mListData = new ArrayList<>();
    private AlbumM mData;
    private Context mContext;


    public ReserveTrialTracksAdapter(Context context, AlbumM album) {
        mContext = context;
        mData = album;
    }


    public void setListData(List<TrackM> models) {
        mListData.clear();
        mListData.addAll(models);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        Activity activity = BaseApplication.getMainActivity();
        if (activity instanceof MainActivity) {
            if (AutoSizeConfig.getInstance().getApplication() != null && !AutoSizeConfig.getInstance().isStop()) {
                AutoSize.autoConvertDensityOfGlobal(activity);
            }
        }
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.main_item_reserve_trial_track, parent, false);
        return new NormalCardViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        if (position >= 0 && position < mListData.size() && viewHolder instanceof NormalCardViewHolder) {
            NormalCardViewHolder holder = (NormalCardViewHolder) viewHolder;
            TrackM track = mListData.get(position);
            if (track == null) {
                return;
            }
            SubPlayableModel subPlayableModel =  SubPlayableModel.createNetModel(track.getPlayUrl64());
            boolean isCurrentTrack = subPlayableModel.getUrl() != null && subPlayableModel.getUrl().equals(XmSubPlayManager.getInstance(mContext).getCurPlayUrl());
            boolean isCurrentTrackPlaying = isCurrentTrack && XmSubPlayManager.getInstance(mContext).isPlaying();
            holder.mPlayIv.setImageResource(isCurrentTrackPlaying ? R.drawable.host_btn_pause_btn_n_fill_n_24 : R.drawable.host_btn_play_btn_inside_fill_n_24);
            if (Album3Utils.INSTANCE.isBigIpStyleAlbum(mData)) {
                holder.mRootView.setBackgroundResource(isCurrentTrackPlaying ? R.drawable.main_bg_14ffffff_radius_6 : R.drawable.main_bg_0affffff_radius_6);
                int smallIconColor = ContextCompat.getColor(mContext, R.color.main_color_e6ffffff);
                holder.mPlayIv.setColorFilter(smallIconColor);
                holder.mPlayLayout.setBackgroundResource(R.drawable.host_round_29ffffff);
                holder.mTitleTv.setTextColor(Album3Utils.INSTANCE.getColorWithAlpha(
                        isCurrentTrackPlaying ? 0.9f : 0.7f,
                        mContext.getResources().getColor(R.color.main_color_ffffff)));
            } else {
                holder.mRootView.setBackgroundResource(isCurrentTrackPlaying ? R.drawable.main_bg_ffffff_14ffffff_radius_6 : R.drawable.main_bg_99ffffff_0affffff_radius_6);
                int smallIconColor = ContextCompat.getColor(mContext, R.color.main_color_2c2c3c_dcdcdc);
                holder.mPlayIv.setColorFilter(smallIconColor);
                holder.mPlayLayout.setBackgroundResource(R.drawable.host_round_142c2c3c_298d8d91);
                holder.mTitleTv.setTextColor(Album3Utils.INSTANCE.getColorWithAlpha(
                        isCurrentTrackPlaying ? 1f : 0.8f,
                        mContext.getResources().getColor(R.color.main_color_393942_dcdcdc)));
            }
            holder.mTitleTv.setText(track.getTrackTitle());
            holder.mPlayIv.setOnClickListener(v -> {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }
                String item = "开始播放";
                if (isCurrentTrackPlaying) {
                    XmSubPlayManager.getInstance(mContext).pausePlay();
                    item = "暂停播放";
                } else {
                    XmSubPlayManager.getInstance(mContext).play(subPlayableModel);
                }
                // 专辑预约页-试听音频播放按钮  点击事件
                new XMTraceApi.Trace()
                        .click(68939) // 用户点击时上报
                        .put("currPage", " albumSchedulePage")
                        .put("xmRequestId", mData.getRequestId())
                        .put("currAlbumId", mData.getId() + "")
                        .put("trackName", track.getTrackTitle())
                        .put("Item", item) // 开始播放 | 暂停播放
                        .put("positionNew", (position + 1) + "") // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                        .put("trackId", track.getDataId() + "")
                        .createTrace();
            });

            if (holder.mRootView.getLayoutParams() != null) {
                ViewGroup.LayoutParams params = holder.mRootView.getLayoutParams();
                if (mListData.size() == 1 && BaseUtil.getScreenWidth(mContext) <= BaseUtil.dp2px(mContext, 600)) {
                    params.width = BaseUtil.getScreenWidth(mContext) - BaseUtil.dp2px(mContext, 32);
                } else {
                    params.width = BaseUtil.dp2px(mContext, 280);
                }
                holder.mRootView.setLayoutParams(params);
            }

            // 专辑预约页-试听音频播放按钮  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(68940)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", " albumSchedulePage")
                    .put("xmRequestId", mData.getRequestId())
                    .put("contentId", track.getDataId() + "")
                    .put("contentType", "track")
                    .put("trackName", track.getTrackTitle())
                    .put("currAlbumId", mData.getId() + "")
                    .put("positionNew", (position + 1) + "") // 双端统一从1开始，计算模块内的相对位置(遇到多列时先左右，后上下)
                    .put("trackId", track.getDataId() + "")
                    .createTrace();
        }
    }

    @Override
    public Object getItem(int position) {
        if (mListData != null && mListData.size() > 0 && mListData.size() > position && position >= 0) {
            return mListData.get(position);
        }
        return null;
    }

    @Override
    public int getItemCount() {
        return mListData.size();
    }

    public class NormalCardViewHolder extends RecyclerView.ViewHolder {
        public View mRootView;
        public View mPlayLayout;
        public ImageView mPlayIv;
        public TextView mTitleTv;

        public NormalCardViewHolder(View itemView) {
            super(itemView);
            mRootView = itemView.findViewById(R.id.main_card_item_root_ll);
            mTitleTv = itemView.findViewById(R.id.main_title_tv);
            mPlayIv = itemView.findViewById(R.id.main_play_iv);
            mPlayLayout = itemView.findViewById(R.id.main_play_iv_bg);
        }
    }
}