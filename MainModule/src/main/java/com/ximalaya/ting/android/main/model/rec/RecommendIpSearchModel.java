package com.ximalaya.ting.android.main.model.rec;

import com.google.gson.Gson;
import com.ximalaya.ting.android.host.model.album.AlbumM;
import com.ximalaya.ting.android.host.model.anchor.Anchor;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by changle.fang on 2020-05-15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15050162674
 * ip维度检索
 */
public class RecommendIpSearchModel {

    private Anchor anchor;
    private List<AlbumM> albums;

    public RecommendIpSearchModel(String content) {
        try {
            JSONObject jsonObject = new JSONObject(content);
            if (jsonObject.has("anchor")) {
                anchor = new Gson().fromJson(jsonObject.optString("anchor"), Anchor.class);
            }
            albums = new ArrayList<>();
            JSONArray jsonArray = jsonObject.optJSONArray("list");
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    albums.add(new AlbumM(jsonArray.optString(i)));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public Anchor getAnchor() {
        return anchor;
    }

    public void setAnchor(Anchor anchor) {
        this.anchor = anchor;
    }

    public List<AlbumM> getAlbums() {
        return albums;
    }

    public void setAlbums(List<AlbumM> albums) {
        this.albums = albums;
    }

}
