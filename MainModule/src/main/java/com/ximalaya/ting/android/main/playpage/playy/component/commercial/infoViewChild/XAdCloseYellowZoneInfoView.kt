package com.ximalaya.ting.android.main.playpage.playy.component.commercial.infoViewChild

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.model.play.CommonGuidanceInfo
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.model.play.YellowZoneModel
import com.ximalaya.ting.android.host.util.other.FrequencyUtil
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.manager.playPage.PlayPageMarkPointManager
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.infoViewChild.basic.AbstractInfoViewChild
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.infoViewChild.basic.IInfoViewComponentActionProvider
import com.ximalaya.ting.android.main.playpage.playy.component.commercial.infoViewChild.basic.IInfoViewDataProvider
import com.ximalaya.ting.android.main.util.CommonUtil
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil
import com.ximalaya.ting.android.xmutil.Logger
import java.lang.ref.WeakReference
import java.util.concurrent.TimeUnit

class XAdCloseYellowZoneInfoView(
    context: Context,
    dataProvider: IInfoViewDataProvider,
    actionProvider: IInfoViewComponentActionProvider
) : AbstractInfoViewChild(context, dataProvider, actionProvider), View.OnClickListener {
    companion object {
        private const val TAG = "XAdCloseYellowZoneInfoView"
        private const val COUNT_DOWN_TIME = 10 * 1000L
    }

    private var guideInfo: CommonGuidanceInfo? = null
    private var vInfoBarView: View? = null
    private var vHintText: TextView? = null
    private var vBtnText: TextView? = null
    private var vArrowImage: ImageView? = null
    private var countDown: CountDown? = null

    override fun show(): Boolean {
        val playingSoundInfo: PlayingSoundInfo? = mDataProvider.getPlayingSoundInfoFromComponent()
        playingSoundInfo ?: return false
        Logger.i(TAG, "show step 1")
        CommonGuidanceInfo.findTarget(playingSoundInfo, CommonGuidanceInfo.TYPE_AD_OFF_TIPS)?.let {
            Logger.i(TAG, "show step 2")
            if (!checkReadyToShow(it)) {
                return false
            }
            setView(playingSoundInfo, it)
            return true
        }
        Logger.i(TAG, "show step 3")
        ViewStatusUtil.removeViewParent(vInfoBarView)
        clearData()
        return false
    }

    private fun checkReadyToShow(data: CommonGuidanceInfo): Boolean {
        Logger.i(TAG, "show step 1.1")
        val time =
            FrequencyUtil.EachDay.getRepeatTime(getSavedKey())
        Logger.i(TAG, "show step 1.2:time:$time")
        val limitTimes = if (data.displayTimes > 0) data.displayTimes else 3
        val readyToShow = time < limitTimes
        if (!readyToShow) {
            YellowZoneModel.addHideBarType("${YellowZoneModel.YELLOW_ZONE_TYPE_AD_OFF_PLAY_ZONE}")
        }
        return readyToShow
    }

    override fun onDestroy() {
        resetCountDown()
    }

    override fun clearData() {
        playingSoundInfo = null
        guideInfo = null
        resetCountDown()
    }

    private fun resetCountDown() {
        try {
            countDown?.cancel()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initView() {
        if (null == vInfoBarView) {
            val layoutRes: Int = R.layout.main_play_page_info_view_yellow_zone_y
            vInfoBarView = LayoutInflater.from(mContext).inflate(layoutRes, null)
            val height = INFO_VIEW_HEIGHT_28
            val llp: LinearLayout.LayoutParams = LinearLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                BaseUtil.dp2px(mContext, height)
            )
            vInfoBarView?.layoutParams = llp
            vHintText = vInfoBarView?.findViewById(R.id.main_yellow_zone_text)
            vBtnText = vInfoBarView?.findViewById(R.id.main_yellow_zone_btn)
            vArrowImage = vInfoBarView?.findViewById(R.id.main_yellow_zone_arrow)
        }
    }

    private fun setView(playingSoundInfo: PlayingSoundInfo, guideInfo: CommonGuidanceInfo) {
        this.playingSoundInfo = playingSoundInfo
        this.guideInfo = guideInfo
        initView()
        ViewStatusUtil.setText(vHintText, guideInfo.text)
        val btnInfo: YellowZoneModel.ButtonInfo? = CommonUtil.safelyGetItem(guideInfo.buttonList, 0)
        if (null == btnInfo) {
            ViewStatusUtil.setVisible(View.GONE, vBtnText, vArrowImage)
        } else {
            decorateButton(guideInfo, vBtnText, vArrowImage)
            ViewStatusUtil.setText(vBtnText, btnInfo.text)
            ViewStatusUtil.setOnClickListener(R.id.main_id_tag_click_model, btnInfo, this, vBtnText)
            ViewStatusUtil.setOnClickListener(
                R.id.main_id_tag_click_model,
                btnInfo,
                this,
                vArrowImage
            )
            ViewStatusUtil.setVisible(View.VISIBLE, vBtnText, vArrowImage)
        }

        markPointOnShow(this.playingSoundInfo)
        mActionProvider.addView(vInfoBarView)
        countDown = CountDown(this, COUNT_DOWN_TIME)
        countDown?.start()
    }

    private fun decorateButton(guideInfo: CommonGuidanceInfo, btn: TextView?, arrow: ImageView?) {
        val defaultColor: Int = Color.parseColor("#F6BCA4")
        ViewStatusUtil.setTextColor(btn, defaultColor)
        arrow?.imageTintList = ColorStateList.valueOf(defaultColor)
//        if (CommonGuidanceInfo.TYPE_TIME_LIMIT_LISTEN_PLAY_ZONE == guideInfo.guidanceType) {
//            val color: Int = Color.parseColor("#6CC7A7")
//            ViewStatusUtil.setTextColor(btn, color)
//            arrow?.imageTintList = ColorStateList.valueOf(color)
//        }
    }

    override fun onClick(v: View?) {
        if (!OneClickHelper.getInstance().onClick(v)) {
            return
        }
        val tag: Any? = v?.getTag(R.id.main_id_tag_click_model)
        if (tag is YellowZoneModel.ButtonInfo) {
            PlayPageMarkPointManager.UnderCoverCommercial.markPointOnClickAdCloseByUserCommercialView(
                playingSoundInfo
            )
            realDoClickOperation(v, playingSoundInfo, tag)
        }
    }

    var lastMarkedShowId: Long = -1
    private fun markPointOnShow(
        soundInfo: PlayingSoundInfo?
    ) {
        val thisTrackId: Long = mDataProvider.getCurrentTrackIdFromComponent()
        if (lastMarkedShowId == thisTrackId) {
            return
        }
        lastMarkedShowId = thisTrackId
        PlayPageMarkPointManager.UnderCoverCommercial.markPointOnShowAdCloseByUserCommercialView(
            soundInfo
        )
        FrequencyUtil.EachDay.increaseRepeatTime(getSavedKey())
    }

    private fun getSavedKey(): String {
        if (UserInfoMannage.hasLogined()) {
            return "${FrequencyUtil.EachDay.KEY_X_INFO_ZONE_AD_CLOSE_VIP_PURCHASE}_${UserInfoMannage.getUid()}"
        }
        return FrequencyUtil.EachDay.KEY_X_INFO_ZONE_AD_CLOSE_VIP_PURCHASE
    }

    private class CountDown(infoView: XAdCloseYellowZoneInfoView?, millisInFuture: Long) :
        CountDownTimer(millisInFuture, TimeUnit.SECONDS.toMillis(1)) {
        private val infoViewReference: WeakReference<XAdCloseYellowZoneInfoView> =
            WeakReference(infoView)

        override fun onTick(millisUntilFinished: Long) {
        }

        override fun onFinish() {
            infoViewReference.get() ?: return
            infoViewReference.get()?.mActionProvider?.refreshView()
        }
    }
}