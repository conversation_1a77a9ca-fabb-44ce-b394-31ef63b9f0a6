package com.ximalaya.ting.android.main.playpage.playy.component.ad;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.ActivityManagerDetacher;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.listen.ListenTaskInfo;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.playpage.playy.view.ListenTaskFloatViewNew;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.service.IListenTimeListener;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Map;

/**
 * YPlayFragment 中的收听任务组件
 */
public class YPlayListenTaskComponent {
    
    private static final String TAG = "YPlayListenTaskComponent";
    
    private BaseFragment2 mFragment;
    private Context mContext;
    private ListenTaskFloatViewNew mFloatView;
    private ViewGroup mParentContainer;
    private boolean isFirstResume = true;
    private boolean isInitialPositionSet = false; // 标记是否已设置初始位置

    private ListenTaskFloatViewNew.OnDragStateChangeListener mDragStateChangeListener;

    private String xmRequestId;

    private IListenTimeListener mListenTimeListener = new IListenTimeListener() {
        @Override
        public void onListenTimeChange(int totalListenTime) {
            // 第二天收听时长从0开始，重新查询任务信息
            if (totalListenTime == 0) {
                queryListenTask();
            }
        }
    };

    private boolean isAppGoToBackground = false;
    private ActivityManagerDetacher.AppStatusListener  mAppStatusListener =  new ActivityManagerDetacher.AppStatusListener() {
        @Override
        public void onAppGoToForeground(Activity startedActivity) {
        }

        @Override
        public void onAppGoToBackground(Activity stoppedActivity) {
            isAppGoToBackground = true;
        }
    };


    public YPlayListenTaskComponent(BaseFragment2 fragment) {
        mFragment = fragment;
        mContext = fragment.getContext();
    }
    
    /**
     * 初始化组件
     */
    public void onCreate(ListenTaskFloatViewNew.OnDragStateChangeListener dragStateChangeListener) {
        Logger.d(TAG, "onCreate");
        mDragStateChangeListener = dragStateChangeListener;
        if (BaseApplication.sInstance != null) {
            BaseApplication.sInstance.addAppStatusListener(mAppStatusListener);
        }
    }
    
    /**
     * 初始化UI
     */
    public void initUI(ViewGroup rootView) {
        if (rootView == null) {
            return;
        }
        mParentContainer = rootView;
        // 创建悬浮组件
        createFloatView();
        // 查询收听任务
        queryListenTask();
    }
    
    /**
     * 创建悬浮组件
     */
    private void createFloatView() {
        if (mContext == null || mParentContainer == null) {
            return;
        }
        
        mFloatView = new ListenTaskFloatViewNew(mContext);
        mFloatView.setOnFloatViewClickListener(() -> {
            // 点击跳转到福利页
            jumpToWelfarePage();
        });

        // 设置拖拽状态监听器，处理与页面滑动的冲突
        mFloatView.setOnDragStateChangeListener(new ListenTaskFloatViewNew.OnDragStateChangeListener() {
            @Override
            public void onDragStart() {
                // 拖拽开始时，禁用页面的下滑收起功能
                if (mDragStateChangeListener != null) {
                    mDragStateChangeListener.onDragStart();
                }
            }

            @Override
            public void onDragEnd() {
                // 拖拽结束时，恢复页面的下滑收起功能
                if (mDragStateChangeListener != null) {
                    mDragStateChangeListener.onDragEnd();
                }
            }
        });
        
        // 设置悬浮组件的布局参数
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
        );

        mFloatView.setLayoutParams(params);
        mFloatView.setVisibility(View.GONE); // 初始隐藏

        // 添加到父容器
        mParentContainer.addView(mFloatView);
    }

    public void queryListenTask() {
        Map<String, String> params = new ArrayMap<>();
        // 可以添加一些必要的参数，比如用户ID等
        CommonRequestM.welfareRequestQueryListenTask(params, new IDataCallBack<ListenTaskInfo>() {
            @Override
            public void onSuccess(@Nullable ListenTaskInfo data) {
                Logger.d(TAG, "查询收听任务成功: " + data);
                if (data != null && data.success) {
                    updateFloatView(data);
                } else {
                    hideFloatView();
                }
            }

            @Override
            public void onError(int code, String message) {
                Logger.e(TAG, "查询收听任务失败: code=" + code + ", message=" + message);
                hideFloatView();
            }
        });
    }
    
    /**
     * 更新悬浮组件
     */
    private void updateFloatView(ListenTaskInfo taskInfo) {
        if (mFloatView == null || taskInfo == null) {
            return;
        }
        if (ToolUtil.isEmptyCollects(taskInfo.stepInfo)) {
            hideFloatView();
            return;
        }
        for (int i = 0; i < taskInfo.stepInfo.size(); i++) {
            ListenTaskInfo.StepInfo step = taskInfo.stepInfo.get(i);
            step.index = i;
        }
        // 如果有未完成的任务步骤，则显示悬浮组件
        if (taskInfo.hasNoFinishStep()) {
            mFloatView.setTaskInfo(taskInfo);
            showFloatView();
        } else {
            hideFloatView();
        }
    }
    
    /**
     * 显示悬浮组件
     */
    private void showFloatView() {
        if (mFloatView != null && mFloatView.getVisibility() != View.VISIBLE) {
            // 首次显示时设置初始位置
            if (!isInitialPositionSet) {
                setInitialPosition();
                mFloatView.setVisibility(View.VISIBLE);
                isInitialPositionSet = true;
            } else {
                mFloatView.setVisibility(View.VISIBLE);
            }
        }
        traceOnShow();
    }

    private void traceOnShow() {
        xmRequestId = XmRequestIdManager.getInstance(BaseApplication.getMyApplicationContext()).getRequestId();
        // 新声音播放页-福利中心挂件入口  控件曝光
        long trackId = 0;
        long albumId = 0;
        PlayableModel playableModel = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrSound();
        if (playableModel != null && playableModel instanceof Track) {
            trackId = playableModel.getDataId();
            if (((Track) playableModel).getAlbum() != null) {
                albumId = ((Track) playableModel).getAlbum().getAlbumId();
            }
        }
        final long finalTrackId = trackId;
        final long finalAlbumId = albumId;
        new XMTraceApi.Trace()
                .setMetaId(68889)
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "newPlay")
                .put("xmRequestId", xmRequestId)
                .put("currAlbumId", finalAlbumId + "")
                .put("currTrackId", finalTrackId + "")
                .put(XmRequestIdManager.CONT_ID, finalTrackId + "")
                .put(XmRequestIdManager.IS_DUPLICATE_VIEW, "0")
                .createTrace();
    }

    /**
     * 设置悬浮组件的初始位置
     */
    private void setInitialPosition() {
        if (mFloatView == null || mParentContainer == null) {
            return;
        }

        // 使用 post 确保视图已经完成测量
        int rightMargin = BaseUtil.dp2px(mContext, 0);
        int bottomMargin = BaseUtil.dp2px(mContext, 370);

        int viewWidth = BaseUtil.dp2px(mContext, 64);
        int viewHeight = BaseUtil.dp2px(mContext, 64);
        int parentWidth = mParentContainer.getWidth();
        int parentHeight = mParentContainer.getHeight();

        Logger.d(TAG, "setInitialPosition viewWidth: " + viewWidth + ", viewHeight: " + viewHeight
                + ", parentWidth: " + parentWidth + ", parentHeight: " + parentHeight);

        // 计算初始位置：距离屏幕底部406dp
        int initialX = parentWidth - viewWidth - rightMargin;
        int initialY = parentHeight - viewHeight - bottomMargin;

        // 确保不超出边界
        initialX = Math.max(0, Math.min(initialX, parentWidth - viewWidth));
        initialY = Math.max(0, Math.min(initialY, parentHeight - viewHeight));

        Logger.d(TAG, "设置初始位置: x=" + initialX + ", y=" + initialY);

        mFloatView.setX(initialX);
        mFloatView.setY(initialY);
        mFloatView.setVisibility(View.VISIBLE);
    }
    
    /**
     * 隐藏悬浮组件
     */
    private void hideFloatView() {
        if (mFloatView != null && mFloatView.getVisibility() == View.VISIBLE) {
            mFloatView.setVisibility(View.GONE);
        }
    }
    
    /**
     * 跳转到福利页
     */
    private void jumpToWelfarePage() {
        // 新声音播放页-福利中心挂件入口  控件曝光
        performJumpToWelfarePage();
        long trackId = 0;
        long albumId = 0;
        PlayableModel playableModel = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrSound();
        if (playableModel != null && playableModel instanceof Track) {
            trackId = playableModel.getDataId();
            if (((Track) playableModel).getAlbum() != null) {
                albumId = ((Track) playableModel).getAlbum().getAlbumId();
            }
        }
        final long finalTrackId = trackId;
        final long finalAlbumId = albumId;
        // 新声音播放页-福利中心挂件入口  点击事件
        new XMTraceApi.Trace()
                .click(68888) // 用户点击时上报
                .put("currPage", "newPlay")
                .put("xmRequestId", xmRequestId)
                .put("currAlbumId", finalAlbumId + "")
                .put("currTrackId", finalTrackId + "")
                .createTrace();
//        if (!XmPlayerManager.getInstance(mContext).isPlaying()) {
//            // 如果当前没有播放，则直接跳转
//            performJumpToWelfarePage();
//            return;
//        }
//        try {
//            XmPlayerManager.getInstance(mContext).syncTimeToServer(new SyncTimeCallbackAdapter.ISyncTimeCallback() {
//                @Override
//                public void onSyncSuccess(int totalTime) {
//                    Logger.d(TAG, "时长同步成功，总时长: " + totalTime + "秒，开始跳转福利页");
//                    performJumpToWelfarePage();
//                }
//
//                @Override
//                public void onSyncFailed() {
//                    Logger.w(TAG, "时长同步失败，仍然跳转福利页");
//                    performJumpToWelfarePage();
//                }
//            });
//        } catch (Exception e) {
//            Logger.e(TAG, "同步时长失败，直接跳转: " + e.getMessage());
//            e.printStackTrace();
//            // 同步失败时直接跳转
//            performJumpToWelfarePage();
//        }
    }

    /**
     * 执行跳转到福利页
     */
    private void performJumpToWelfarePage() {
        try {
            String jumpUrl = "iting://open?msg_type=94&bundle=rn_credit_center&coin=1&location=listen_task&srcChannel=play_page";
            NativeHybridFragment.start((MainActivity) BaseApplication.getMainActivity(), jumpUrl, false);
        } catch (Exception e) {
            Logger.e(TAG, "跳转福利页失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Fragment Resume 时调用
     */
    public void onResume() {
        Logger.d(TAG, "onResume isAppGoToBackground: " + isAppGoToBackground);
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).addListenTimeListener(mListenTimeListener);
        if (isFirstResume) {
            isFirstResume = false;
            return;
        }
        // 重新查询任务状态, 前后台切换时不需要重新查询
        if (isAppGoToBackground) {
            isAppGoToBackground = false;
            return;
        }
        queryListenTask();
    }
    
    /**
     * Fragment Pause 时调用
     */
    public void onPause() {
        Logger.d(TAG, "onPause");
        XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).removeListenTimeListener(mListenTimeListener);
    }
    
    /**
     * Fragment Destroy 时调用
     */
    public void onDestroy() {
        Logger.d(TAG, "onDestroy");
        // 清理资源
        if (mFloatView != null && mParentContainer != null) {
            mParentContainer.removeView(mFloatView);
            mFloatView = null;
        }

        mParentContainer = null;
        mFragment = null;
        mContext = null;
        isInitialPositionSet = false; // 重置位置设置标志
        if (BaseApplication.sInstance != null) {
            BaseApplication.sInstance.removeAppStatusListener(mAppStatusListener);
        }
    }
}
