package com.ximalaya.ting.android.main.adapter.find.recommendStaggered;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.adsdk.external.INativeAd;
import com.ximalaya.ting.android.adsdk.external.XmNativeAdContainer;
import com.ximalaya.ting.android.adsdk.external.feedad.IExpressFeedAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.feedback.NewXmFeedBackPopDialog;
import com.ximalaya.ting.android.host.feedback.XmAdFeedbackUtil;
import com.ximalaya.ting.android.host.feedback.XmMoreFuncManager;
import com.ximalaya.ting.android.host.feedback.inter.IMoreFuncListener;
import com.ximalaya.ting.android.host.feedback.model.DisLikeLeve2Build;
import com.ximalaya.ting.android.host.feedback.model.MoreFuncBuild;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.ad.WebViewPreloadManager;
import com.ximalaya.ting.android.host.manager.ad.util.ADABTestUtil;
import com.ximalaya.ting.android.host.manager.ad.util.AdUbtReportUtil;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.model.XmFeedInnerModel;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.main.R;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataStaggered;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.IMulitViewTypeViewAndDataTraceStaggered;
import com.ximalaya.ting.android.main.adapter.mulitviewtype.ItemModel;
import com.ximalaya.ting.android.main.fragment.find.child.recommendad.RecommendCenterBigFeedAdManager;
import com.ximalaya.ting.android.main.fragment.find.child.recommendad.RecommendCenterBigPicAdManager;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentNetManager;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggered;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentStaggeredAdapter;
import com.ximalaya.ting.android.main.fragment.find.child.staggered.RecommendFragmentTypeManager;
import com.ximalaya.ting.android.main.model.rec.RecommendItemNew;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class RecommendCenterBigPicAdProvider implements
        IMulitViewTypeViewAndDataStaggered<RecommendCenterBigPicAdProvider.CenterBigPicAdViewHolder, ItemModel<RecommendItemNew>>,
        IMulitViewTypeViewAndDataTraceStaggered<RecommendCenterBigPicAdProvider.CenterBigPicAdViewHolder, ItemModel<RecommendItemNew>> {
    private BaseFragment2 mBaseFragment2;
    private RecommendFragmentStaggeredAdapter.IDataAction mRemover;
    private RecommendCenterBigFeedAdManager mFeedAdManager;
    private RecommendCenterBigPicAdManager mCenterBigPicAdManager;

    private INativeAd mShowAdModel;
    private IExpressFeedAd mFeedAd;
    private String mItemRequestId;
    private int mPosition = -1;

    public RecommendCenterBigPicAdProvider(BaseFragment2 fragment2,
                                           RecommendFragmentStaggeredAdapter.IDataAction remover,
                                           RecommendCenterBigFeedAdManager feedAdManager, RecommendCenterBigPicAdManager adManager) {
        mBaseFragment2 = fragment2;
        mRemover = remover;
        mFeedAdManager = feedAdManager;
        mCenterBigPicAdManager = adManager;
    }

    @Override
    public CenterBigPicAdViewHolder createViewHolder(View convertView) {
        return new CenterBigPicAdViewHolder(convertView);
    }

    @Override
    public void bindViewHolder(CenterBigPicAdViewHolder holder, int position, ItemModel<RecommendItemNew> data, View convertView) {
        if (holder == null || data == null || data.getObject() == null || mCenterBigPicAdManager == null) {
            return;
        }
        Logger.i("CenterBigPicAdProvider", "bindViewHolder ：--------------------------------- \n" +
                "bindViewHolder ：中插广告是否正在请求 = " + mCenterBigPicAdManager.isAdLoading()
                + ", 是否收到巨幕广告回调 = " + mCenterBigPicAdManager.hasReceiveBroadcast()
                + ", 巨幕广告能否展示 = " + mCenterBigPicAdManager.bigScreenAdEnable());
        if (mCenterBigPicAdManager.isAdLoading()) {
            return;
        }
        if (!mCenterBigPicAdManager.hasReceiveBroadcast() || mCenterBigPicAdManager.bigScreenAdEnable()) {
            // 更新列表数据至最新，用于判断列表是否被刷新
            mItemRequestId = data.getObject().getXmRequestId();
            hideAdContainer(holder.adContainer);
            mFeedAd = null;
            mShowAdModel = null;
            return;
        }
        Logger.i("CenterBigPicAdProvider", "bindViewHolder ：mItemRequestId = " + mItemRequestId + " --- data.requestId = "
                + data.getObject().getXmRequestId() + "， mFeedAd = " + mFeedAd);
        Logger.i("CenterBigPicAdProvider", "bindViewHolder ：isMainHotStart = " + RecommendCenterBigPicAdManager.isMainHotStart());
        boolean hasNewSecondExposureAd = hasNewSecondExposureAd();
        // 如果 mRecommendItemNew变了说明首页列表数据被刷新
        boolean isListRefreshed = !Objects.equals(mItemRequestId, data.getObject().getXmRequestId());
        // 1、二曝请求到新物料时，刷新广告
        // 2、首页列表刷新时，更新广告，ps：主站6小时热启触发二曝的case不刷新，否则二曝广告先曝光了，再刷新会触发频控，广告又被关掉
        boolean needUpdateAd = hasNewSecondExposureAd || (isListRefreshed && !RecommendCenterBigPicAdManager.isMainHotStart());
        if (needUpdateAd) {
            if (isListRefreshed) {
                Logger.i("CenterBigPicAdProvider", "bindViewHolder ：列表被刷新");
            }
            if (hasNewSecondExposureAd){
                Logger.i("CenterBigPicAdProvider", "bindViewHolder ：二曝请求了新物料");
            }
            if (ADABTestUtil.isCenterBigAdNeedMutex() && mFeedAdManager != null && mFeedAdManager.isCenterBigFeedAdShowing()) {
                // 兜底处理：如果中插信息流先展示了，中插大图就不展示了
                Logger.i("CenterBigPicAdProvider", "CenterBigMutex 中插信息流已经展示了，中插大图不展示");
                mFeedAd = null;
            } else {
                if (mCenterBigPicAdManager.centerBigPicAdEnable()) {
                    mFeedAd = mCenterBigPicAdManager.getExpressFeedAd();
                } else {
                    Logger.i("CenterBigPicAdProvider", "bindViewHolder ：中插广告频控中");
                    mFeedAd = null;
                }
            }
            mItemRequestId = data.getObject().getXmRequestId();
            mShowAdModel = null;
        } else if (isListRefreshed && RecommendCenterBigPicAdManager.isMainHotStart()){
            Logger.i("CenterBigPicAdProvider", "bindViewHolder ：列表刷新，是主站6小时热启，不刷新物料");
            // 主站热启状态用完就重置
            RecommendCenterBigPicAdManager.setMainHotStart(false);
            mItemRequestId = data.getObject().getXmRequestId();
        }
//        else if (mPosition == position && mShowAdModel != null) {
//            // 如果 mRecommendItemNew 没变 而且下标没变不刷新
//            Logger.i("CenterBigPicAdNew", "bindViewHolder position = " + position + " --- return");
//            return;
//        }
        mPosition = position;
        IExpressFeedAd feedAd = mFeedAd;
        if (feedAd == null) {
            Logger.i("CenterBigPicAdProvider", "bindViewHolder ：中插广告为空");
            tryNotifySecondFloorExpand(holder.adContainer);
            hideAdContainer(holder.adContainer);
            return;
        }
        mCenterBigPicAdManager.setCenterBigAdShowing(true);
        tryNotifySecondFloorFold(holder.adContainer);
        showAdContainer(holder.adContainer);
        convertView.setTag(R.id.main_staggered_default_holder, false);
        long startBindViewMillis = System.currentTimeMillis();
        Logger.i("CenterBigPicAdProvider", "bindViewHolder ：绑定广告数据");
        feedAd.bindExpressAdToView(holder.adContainer,
                RecommendFragmentTypeManager.INSTANCE.isNewSceneCard() ?
                        IExpressFeedAd.AD_STYLE_CENTER_BIG_PICTURE_EFFICIENCY : IExpressFeedAd.AD_STYLE_CENTER_BIG_PICTURE,
                new IExpressFeedAd.IExpressAdInteractionListener() {
            @Override
            public void onAdClose(int closeEvent, int i, int listSize) {
                if (!XmAdFeedbackUtil.feedbackEnable(mShowAdModel)) {
                    removeAd();
                    return;
                }
                if (closeEvent == INativeAd.CLOSE_EVENT_INTERCEPT_MIX_AD_LIST) {
                    String adTitle = "广告内容";
                    if (mShowAdModel != null && !TextUtils.isEmpty(mShowAdModel.getTitle())) {
                        adTitle = mShowAdModel.getTitle();
                    }
                    MoreFuncBuild build = new MoreFuncBuild();
                    build.setFragment2(mBaseFragment2);
                    build.setShowLevel1Dislike(true);
                    build.setLevel1DisLikeTitle("减少推荐：" + adTitle);
                    build.setListener(new IMoreFuncListener() {
                        @Override
                        public void onLevel1DisLikeClick(@Nullable String btnText) {
                            removeAd();
                            feedAd.reportFeedback("", 1);
                        }
                    });
                    DisLikeLeve2Build leve2Build = new DisLikeLeve2Build();
                    leve2Build.setVibratorEnable(false);
                    build.setDisLikeLeve2Build(leve2Build);
                    XmMoreFuncManager.checkShowMorePage(build);
                } else {
                    MoreFuncBuild build = new MoreFuncBuild();
                    build.setFragment2(mBaseFragment2);
                    build.setShowLevel2Dislike(true);
                    DisLikeLeve2Build leve2Build = new DisLikeLeve2Build();
                    leve2Build.setFromAd(true);
                    leve2Build.setOnFeedBackListener(new NewXmFeedBackPopDialog.IOnFeedBackListener() {
                        @Override
                        public void onDialogShow(boolean showSuccess) {
                            if (!showSuccess && i < 0) {
                                removeAd();
                            }
                        }

                        @Override
                        public void onFeedBack(List<XmFeedInnerModel> list) {
                            String name = "";
                            String type = "";
                            if (list != null && !list.isEmpty()) {
                                name = list.get(0).getName();
                                type = list.get(0).getCodeType();
                            }
                            try {
                                if (i >= 0) {
                                    feedAd.reportSubAdFeedback(name, Integer.parseInt(type), i);
                                    if (i == 0 && listSize == 1) {
                                        removeAd();
                                    }
                                } else {
                                    removeAd();
                                    feedAd.reportFeedback(name, Integer.parseInt(type));
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
                    build.setDisLikeLeve2Build(leve2Build);
                    XmMoreFuncManager.checkShowMorePage(build);
                }
            }

            @Override
            public void onAdRenderFail() {
                Logger.i("CenterBigPicAdProvider", "bindViewHolder ：广告渲染失败");
                tryNotifySecondFloorExpand(holder.adContainer);
                convertView.setTag(R.id.main_staggered_default_holder, true);
                hideAdContainer(holder.adContainer);
            }

            @Override
            public void onAdClicked(@Nullable View view, INativeAd iNativeAd, boolean b) {
                CustomToast.showDebugFailToast("广告点击了-debug环境下显示");
                String positionNew = position + "";
                Map<String, String> otherInfo = feedAd.getOtherInfo();
                // 新首页-广告（双列）  点击事件
                XMTraceApi.Trace traceBuilder = new XMTraceApi.Trace()
                        .click(38751)
                        .put("adId", otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                        .put("materialId", otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                        .put("adType", "商业广告")
                        .put("style", "新版中插大图")
                        .put("positionNew", position + 1 + "")
                        .put("currPage", "newHomePage")
                        .put(XmRequestIdManager.XM_REQUEST_ID, data.getObject() != null ? data.getObject().getXmRequestId() : "")
                        .put(XmRequestIdManager.CONT_ID, otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                        .put("contentType", "图文");

                Logger.d(
                        "CenterBigPicAdProvider",
                        "bindViewHolder ：中插广告点击");
                AdUbtReportUtil.appendUbtReportMap(traceBuilder, iNativeAd);
                traceBuilder.createTrace();
            }

            @Override
            public void onAdShow(INativeAd iNativeAd) {
                if (mFeedAd == null) return;
                if (iNativeAd != mShowAdModel) {
                    Logger.i("CenterBigPicAdProvider", "bindViewHolder ：中插广告曝光 --- bindViewCost = " +
                            (System.currentTimeMillis() - startBindViewMillis) + "， Date = " +
                            DateTimeUtil.long2String(System.currentTimeMillis(), "MM-dd HH:mm:ss"));
                    mCenterBigPicAdManager.saveCenterBigPicAdShowTime();
                }
                mShowAdModel = iNativeAd;
                if (WebViewPreloadManager.canPreloadByDevice()) {
                    WebViewPreloadManager.getInstance().preloadWhenAdExposure(iNativeAd);
                }
                CustomToast.showDebugFailToast("广告显示了-debug环境下显示");
            }

            private void removeAd() {
                mFeedAd = null;
                mCenterBigPicAdManager.clearCacheFeedAd();
                if (mRemover != null) {
                    mRemover.remove(mPosition, false);
                }
                // 这里延迟是为了防止影响列表删除动画
                holder.adContainer.postDelayed(() -> {
                    hideAdContainer(holder.adContainer);
                }, 500);
            }
        });

        //待执行完bindExpressAdToView之后获取，子view中的XmNativeAdContainer 设置radio
        if (holder.adContainer != null && holder.adContainer.getChildCount() > 0) {
            View view = holder.adContainer.getChildAt(0);
            if (view != null && view instanceof XmNativeAdContainer) {
                int radio = ConfigureCenter.getInstance().getInt(
                        CConstants.Group_ad.GROUP_NAME,
                        CConstants.Group_ad.ITEM_AD_MAIN_FEED_SHOW_EXPOSE_RADIO, 0);
                if (radio > 0) {
                    ((XmNativeAdContainer) view).setSubPercent(radio);
                }
            }
        }
    }

    private boolean hasNewSecondExposureAd() {
        if (mCenterBigPicAdManager.centerBigPicAdEnable()) {
            IExpressFeedAd expressFeedAd = mCenterBigPicAdManager.getExpressFeedAd();
            if (expressFeedAd != null && expressFeedAd != mFeedAd){
                Map<String, String> otherInfo = expressFeedAd.getOtherInfo();
                return otherInfo != null && "1".equals(otherInfo.get(IExpressFeedAd.OtherInfoKey.IS_SECOND_EXPOSURE));
            }
        }
        return false;
    }

    private void tryNotifySecondFloorExpand(ViewGroup adContainer) {
        if (adContainer == null || mPosition == 1) return;
        // 如果本来就是没有折叠，则不处理
        if (!RecommendFragmentNetManager.Companion.getInstance().getSecondFloorLastFoldStatus()){
            Logger.i("CenterBigPicAdProvider", "tryNotifySecondFloorExpand ：二楼现在展开状态");
            return;
        }
        if (!isAdContainerHide(adContainer)) {
            Logger.i("CenterBigPicAdProvider", "tryNotifySecondFloorExpand ：通知 2 楼不折叠");
            adContainer.post(() -> {
                if (mRemover != null) {
                    RecommendRankListAdapterProviderStaggered.Companion.setForceUpdateFromAd();
                    mRemover.refresh(1);
                }
            });
        }
    }

    private void tryNotifySecondFloorFold(ViewGroup adContainer) {
        if (adContainer == null || mPosition == 1) return;
        // 如果本来就是折叠，则不处理
        if (RecommendFragmentNetManager.Companion.getInstance().getSecondFloorLastFoldStatus()){
            Logger.i("CenterBigPicAdProvider", "tryNotifySecondFloorFold ：二楼现在折叠状态，不通知");
            return;
        }
        if (RecommendCenterBigPicAdManager.notifyCenterBigPicAdShowEnable(mFeedAd, true)) {
            adContainer.post(() -> {
                if (mRemover != null) {
                    RecommendRankListAdapterProviderStaggered.Companion.setForceUpdateFromAd();
                    mRemover.refresh(1);
                }
                Logger.i("CenterBigPicAdProvider", "tryNotifySecondFloorFold ：通知 2 楼折叠");
            });
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.main_item_recommend_ad_center_big_pic;
    }

    @Override
    public void traceOnItemShow(ItemModel<RecommendItemNew> data, int position, CenterBigPicAdViewHolder holder) {
        if (data == null) {
            return;
        }
        if (holder == null || holder.adContainer == null || isAdContainerHide(holder.adContainer)
                || holder.adContainer.getVisibility() != View.VISIBLE) {
            return;
        }
        // 新首页-广告（双列）  控件曝光
        IExpressFeedAd feedAd = mFeedAd;
        if (feedAd == null) {
            return;
        }
        RecommendStaggeredTraceManager.INSTANCE.checkXmRequestId(data.getObject());
        Map<String, String> otherInfo = feedAd.getOtherInfo();
        XMTraceApi.Trace traceBuilder = new XMTraceApi.Trace()
                .setMetaId(38752)
                .setServiceId("slipPage")
                .put("adId", otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                .put("materialId", otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                .put("adType", "商业广告")
                .put("style", "新版中插大图")
                .put("positionNew", position + 1 + "")
                .put("currPage", "newHomePage")
                .put("contentType", "图文")
                .put("xmContentType", "recommendBannerAd")
                .put(XmRequestIdManager.XM_REQUEST_ID, data.getObject() != null ? data.getObject().getXmRequestId() : "")
                .put(XmRequestIdManager.CONT_ID, otherInfo != null ? otherInfo.get(IExpressFeedAd.OtherInfoKey.AD_ID) : "")
                .put("exploreType", RecommendFragmentStaggered.Companion.getMExploreType() + "");

        Logger.d(
                "msg_ubt",
                "RecommendBannerAdRealTimeProvider -- 333 -- show --  ");
        AdUbtReportUtil.appendUbtReportMap(traceBuilder, mShowAdModel);
        if (data.getObject() instanceof RecommendItemNew && ((RecommendItemNew) data.getObject()).isLocalCache()) {
            traceBuilder.isLocalCache();
        }
        traceBuilder.createTrace();
    }

    private void hideAdContainer(ViewGroup adContainer) {
        if (adContainer == null) return;
        adContainer.removeAllViews();
        // 这里设 visibility为gone 可能会有问题，当provider在列表第一行时 会出现列表无法下拉的问题
        ViewGroup.LayoutParams layoutParams = adContainer.getLayoutParams();
        if (layoutParams.height != 1) {
            layoutParams.height = 1;
            adContainer.setLayoutParams(layoutParams);
        }
        Logger.i("CenterBigPicAdProvider", "hideAdContainer ：中插广告隐藏");
        mCenterBigPicAdManager.setCenterBigAdShowing(false);
    }

    private void showAdContainer(ViewGroup adContainer) {
        if (adContainer == null) return;
        ViewGroup.LayoutParams layoutParams = adContainer.getLayoutParams();
        if (layoutParams.height != ViewGroup.LayoutParams.WRAP_CONTENT) {
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            adContainer.setLayoutParams(layoutParams);
        }
    }

    private boolean isAdContainerHide(ViewGroup adContainer) {
        if (adContainer == null) return true;
        return adContainer.getLayoutParams().height == 1;
    }

    public static class CenterBigPicAdViewHolder extends RecyclerView.ViewHolder {
        ViewGroup adContainer;

        public CenterBigPicAdViewHolder(View convertView) {
            super(convertView);
            this.adContainer = convertView.findViewById(R.id.main_center_big_ad_container);
        }
    }
}