package com.ximalaya.ting.android.main.albumModule.album.reserve

import android.content.Context
import android.graphics.Bitmap
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.LinearLayout
import android.widget.Space
import androidx.recyclerview.widget.LinearLayoutManager
import com.ximalaya.ting.android.framework.manager.StatusBarManager
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.manager.play.XmSubPlayManager
import com.ximalaya.ting.android.host.model.album.Album3PlantGrassModel
import com.ximalaya.ting.android.host.model.album.Album3Type
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.host.view.RecyclerViewCanDisallowInterceptInHost
import com.ximalaya.ting.android.host.view.recyclerview.LinearItemLeftRightMarginDifferentDecoration
import com.ximalaya.ting.android.main.R
import com.ximalaya.ting.android.main.albumModule.album.album3.Album3Utils
import com.ximalaya.ting.android.main.albumModule.album.album3.header.ReserveHeaderBigIp
import com.ximalaya.ting.android.main.albumModule.album.album3.header.ReserveHeaderNovel
import com.ximalaya.ting.android.main.albumModule.album.album3.header.ReserveHeaderPodCast
import com.ximalaya.ting.android.main.albumModule.album.album3.view.AlbumTagsView3
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.IAlbumSubViewLifecycleListener
import com.ximalaya.ting.android.main.albumModule.album.singleAlbum.IBaseAlbumHeaderInterface
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.player.service.IXmPlayerStatusListener
import com.ximalaya.ting.android.opensdk.player.service.XmPlayerException


/**
 * Author: Harry Shi
 * Email: <EMAIL>
 * Date: 2024/7/12
 * Description：
 */
class ReserveDetailView(val mFragment: ReserveAlbumFragment,
                        val mPresenter: ReserveAlbumFragmentPresenter
): IAlbumSubViewLifecycleListener, IXmPlayerStatusListener {

    private var mContext: Context? = null
    private var mRootView: View? = null
    private var mViewStubNovelNew: ViewStub? = null
    private var mViewStubPodCast: ViewStub? = null
    private var mViewStubBigIp: ViewStub? = null
    private var mViewStubPodCastBackGround: ViewStub? = null
    private var mViewStubBigIpBackGround: ViewStub? = null
    private var mTopBgView: View? = null
    private var mTopBgViewBottom: View? = null
    private var mReserveTrialTracksAdapter: ReserveTrialTracksAdapter? = null

    private var mRealViewHeader: View? = null   // 备用，顶部headerView，一般不用这个，用mHeaderViewInterface
    private var mHeaderViewInterface: IBaseAlbumHeaderInterface? = null // 顶部header接口

    private var mSpaceTopView: Space? = null // 根据不同的header，变化不同的高度，调整底部mDetailContainer的位置
    private var mDetailContainer: LinearLayout? = null // 存放详情卡片内容
    private var mHasPlayTrailTrack = false

    fun initHeaderAlbumInfoViews(rootView: View) {
        mContext = mFragment.context
        mRootView = rootView
        mSpaceTopView = rootView.findViewById(R.id.main_album3_space_before_head)
        mViewStubBigIpBackGround = rootView.findViewById(R.id.main_album3_top_bg_big_ip_vs)
        mViewStubPodCastBackGround = rootView.findViewById(R.id.main_album3_top_bg_pod_cast_vs)
        mViewStubBigIp = rootView.findViewById(R.id.main_album3_header_big_ip_vs)
        mViewStubNovelNew = rootView.findViewById(R.id.main_album3_header_novel_vs_new)
        mViewStubPodCast = rootView.findViewById(R.id.main_album3_header_pod_cast_vs)
        mDetailContainer = rootView.findViewById(R.id.main_album3_detail_container)
        var spaceTop = 50.dp
        if ((Album3Utils.isNovelStyleAlbum(mPresenter.album)) || Album3Utils.isPodCastStyleAlbum(mPresenter.album)) {
            spaceTop = 46.dp
        }
        if (StatusBarManager.CAN_CHANGE_STATUSBAR_COLOR) {
            mSpaceTopView?.layoutParams?.height = BaseUtil.getStatusBarHeight(mContext) + spaceTop
        } else {
            mSpaceTopView?.layoutParams?.height = spaceTop
        }
        mRootView!!.setBackgroundColor(mContext!!.resources.getColor(R.color.host_color_f7f9fc_181818))
    }

    fun setDataForView() {
        mPresenter.album ?: return
        // 初始化头部header
        when (mPresenter.album!!.albumStyle) {
            Album3Type.NOVEL.contentType() -> {
                if (mHeaderViewInterface == null && mRealViewHeader == null) {
                    mRealViewHeader = mViewStubNovelNew?.inflate()
                    if (mTopBgViewBottom == null) {
                        mTopBgViewBottom = mViewStubPodCastBackGround?.inflate()
                    }
                    // 专辑头部信息
                    mHeaderViewInterface = ReserveHeaderNovel(mFragment, mPresenter, mRealViewHeader!!, mTopBgViewBottom!!)
                    mHeaderViewInterface?.setDataForView()
                }
            }
            Album3Type.PODCAST.contentType() -> {
                if (mHeaderViewInterface == null && mRealViewHeader == null) {
                    mRealViewHeader = mViewStubPodCast?.inflate()
                    if (mTopBgView == null) {
                        mTopBgView = mViewStubPodCastBackGround?.inflate()
                        mTopBgViewBottom = mTopBgView
                    }
                    // 专辑头部信息
                    mHeaderViewInterface = ReserveHeaderPodCast(mFragment, mPresenter, mRealViewHeader!!, mTopBgView!!)
                    mHeaderViewInterface?.setDataForView()
                }
            }
            Album3Type.BIG_IP.contentType() -> {
                if (mHeaderViewInterface == null && mRealViewHeader == null) {
                    mRealViewHeader = mViewStubBigIp?.inflate()
                    if (mTopBgView == null) {
                        mTopBgView = mViewStubBigIpBackGround?.inflate()
                    }
                    // 专辑头部信息
                    mHeaderViewInterface = ReserveHeaderBigIp(mFragment, mPresenter, mRealViewHeader!!, mTopBgView!!)
                    mHeaderViewInterface?.setDataForView()
                }
            }
        }

        var containerShow = false
        mPresenter.album.album3PlantGrass?.forEach { plantGrassModel ->
            if (plantGrassModel.cardType == Album3PlantGrassModel.Companion.PLANT_GRASS_CARD_TYPE_ALBUM_INTRO) {
                // 专辑简介
                val tagView = mDetailContainer?.findViewById<AlbumTagsView3>(R.id.main_album3_tags)
                val tagShow = tagView!!.handleDataView(mPresenter.album, mFragment.slideView, plantGrassModel!!)
                tagView.visibility = if (tagShow) View.VISIBLE else View.GONE
                containerShow = tagShow
            }
        }

        val recyclerView = mRootView!!.findViewById<RecyclerViewCanDisallowInterceptInHost>(R.id.main_reserve_track_recycler)
        if (mPresenter.album.commonTrackList?.tracks.isNullOrEmpty().not()) {
            ViewStatusUtil.setVisible(View.VISIBLE, recyclerView)
            mReserveTrialTracksAdapter = ReserveTrialTracksAdapter(mContext, mPresenter.album)
            mReserveTrialTracksAdapter!!.setListData(mPresenter.album.commonTrackList.tracks)
            recyclerView.adapter = mReserveTrialTracksAdapter
            val itemDecoration = LinearItemLeftRightMarginDifferentDecoration(8, 16, 16, true)
            recyclerView.addItemDecoration(itemDecoration)
            XmSubPlayManager.getInstance(mContext).addPlayerStatusListener(this)
            recyclerView.setDisallowInterceptTouchEventView(mFragment!!.slideView)
            recyclerView.layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
            containerShow = true
        } else {
            ViewStatusUtil.setVisible(View.GONE, recyclerView)
        }
        if (containerShow.not()) {
            ViewStatusUtil.setVisible(View.GONE, mDetailContainer)
        }
    }

    fun getAlbumCoverImage(): Bitmap? {
        return mHeaderViewInterface?.getAlbumCoverImage()
    }

    private fun createContainerParams(topMargin: Float = 0f, bottomMargin: Float = 0f): LinearLayout.LayoutParams {
        val params = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        if (topMargin > 0 || bottomMargin > 0) {
            params.topMargin = topMargin.toInt()
            params.bottomMargin = bottomMargin.toInt()
        }
        return params
    }

    override fun onPageItemExplore(topY: Int, bottomY: Int) {
        mHeaderViewInterface?.onPageItemExplore()
    }

    override fun onAlbumPageScrollStop(contentTopY: Int, isTopHidden: Boolean) {
    }

    override fun onAlbumPageMyResume() {
        mHeaderViewInterface?.onAlbumPageMyResume()
    }

    override fun onAlbumPagePause() {
        mHeaderViewInterface?.onAlbumPagePause()
    }

    override fun onAlbumPageDestroy() {
        if (mHasPlayTrailTrack) {
            XmSubPlayManager.getInstance(mContext).stopPlay()
        }
        mHeaderViewInterface?.onAlbumPageDestroy()
        XmSubPlayManager.getInstance(mContext).removePlayerStatusListener(this)
    }

    override fun onFoldScreenConfigurationChanged() {
        mHeaderViewInterface?.onFoldScreenConfigurationChanged()
    }

    override fun bgColorChange(color: Int) {
        mHeaderViewInterface?.onBgColorChange(color)
        if (Album3Utils.isBigIpStyleAlbum(mPresenter.album)) {
            mRootView?.setBackgroundColor(color)
        }
    }

    override fun onPlayStart() {
        mHasPlayTrailTrack = true
        mReserveTrialTracksAdapter?.notifyDataSetChanged()
    }

    override fun onPlayPause() {
        mReserveTrialTracksAdapter?.notifyDataSetChanged()
    }

    override fun onPlayStop() {
        mReserveTrialTracksAdapter?.notifyDataSetChanged()
    }

    override fun onSoundPlayComplete() {
        mReserveTrialTracksAdapter?.notifyDataSetChanged()
    }

    override fun onSoundPrepared() {
    }

    override fun onSoundSwitch(lastModel: PlayableModel?, curModel: PlayableModel?) {
        mReserveTrialTracksAdapter?.notifyDataSetChanged()
    }

    override fun onBufferingStart() {
    }

    override fun onBufferingStop() {
    }

    override fun onBufferProgress(percent: Int) {
    }

    override fun onPlayProgress(currPos: Int, duration: Int) {
    }

    override fun onError(exception: XmPlayerException?): Boolean {
        mReserveTrialTracksAdapter?.notifyDataSetChanged()
        return true
    }
}