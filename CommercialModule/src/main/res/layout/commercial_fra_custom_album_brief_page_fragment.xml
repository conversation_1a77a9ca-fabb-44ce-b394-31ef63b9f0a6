<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/commercial_id_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="#3D4C66"/>

    <LinearLayout
        android:id="@+id/commercial_id_title_bar_area"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/commercial_id_button_close"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_below="@id/commercial_id_title_bar_area_top_padding"
            android:layout_marginLeft="3dp"
            android:padding="13dp"
            android:scaleType="fitXY"
            android:src="@drawable/host_ic_standard_back_arrow_left"
            android:tint="@color/host_color_ffffff" />

        <TextView
            android:id="@+id/commercial_id_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textSize="14sp"
            android:textColor="@color/host_color_ffffff"
            android:textStyle="bold"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"
            android:paddingRight="37dp"
            android:layout_marginLeft="-18dp"
            tools:text="猎罪者 | 阴间神探 | 有声的紫荆独家演播 | 会员…"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/commercial_id_title_bar_area"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <FrameLayout
            android:id="@+id/commercial_id_external_view_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/host_transparent"/>

        <View
            android:id="@+id/commercial_id_background_color"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:alpha="0.1"
            android:background="@color/host_color_ffffff"/>

        <LinearLayout
            android:id="@+id/commercial_id_function_button_area"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="-64dp"
            android:layout_marginBottom="16dp"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/commercial_id_button_play_area"
                android:paddingStart="19dp"
                android:paddingEnd="13dp"
                android:orientation="horizontal"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center"
                android:layout_marginRight="4dp"
                android:background="@drawable/commercial_bg_rect_ffffffff_radius_8"
                android:layout_gravity="center">

                <ImageView
                    android:id="@+id/commercial_id_button_play_icon"
                    android:layout_gravity="center"
                    android:src="@drawable/host_album_new_control_play_icon"
                    android:layout_width="20dp"
                    android:layout_height="20dp" />

                <TextView
                    android:id="@+id/commercial_id_button_play_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center_vertical"
                    android:layout_marginStart="3dp"
                    android:includeFontPadding="false"
                    android:textColor="@color/host_color_ff4444"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-light"
                    android:maxLines="1"
                    tools:text="续播" />

                <View
                    android:id="@+id/commercial_id_divider"
                    android:layout_marginStart="4dp"
                    android:background="#66ff4444"
                    android:layout_width="1px"
                    android:layout_height="12dp"/>

                <TextView
                    android:id="@+id/commercial_id_button_play_sub_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="1px"
                    android:fontFamily="sans-serif-light"
                    android:gravity="center_vertical"
                    android:layout_marginStart="4dp"
                    android:includeFontPadding="false"
                    android:textColor="@color/host_color_ff4444"
                    android:textSize="13sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="白夜行 第111白夜行白夜行白夜行白夜行" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/commercial_id_button_subscribe_area"
                android:paddingStart="5dp"
                android:paddingEnd="9dp"
                android:orientation="horizontal"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="3"
                android:gravity="center"
                android:layout_marginLeft="4dp"
                android:background="@drawable/commercial_bg_rect_ffffffff_radius_8"
                android:layout_gravity="center">

                <ImageView
                    android:id="@+id/commercial_id_button_subscribe_icon"
                    android:layout_gravity="center"
                    android:src="@drawable/commercial_album_new_control_subscribe_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/commercial_id_button_subscribe_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="sans-serif-light"
                    android:layout_marginStart="4dp"
                    android:gravity="center_vertical"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="@color/host_color_333333"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:ellipsize="end"
                    tools:text="免费订阅" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>