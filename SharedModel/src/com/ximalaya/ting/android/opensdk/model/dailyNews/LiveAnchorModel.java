package com.ximalaya.ting.android.opensdk.model.dailyNews;

import android.os.Parcel;
import android.os.Parcelable;

public class LiveAnchorModel implements Parcelable {
    private long uid;
    private String logo;
    private String nickName;
    private String summary;


    protected LiveAnchorModel(Parcel in) {
        uid = in.readLong();
        logo = in.readString();
        nickName = in.readString();
        summary = in.readString();
    }

    public static final Creator<LiveAnchorModel> CREATOR = new Creator<LiveAnchorModel>() {
        @Override
        public LiveAnchorModel createFromParcel(Parcel in) {
            return new LiveAnchorModel(in);
        }

        @Override
        public LiveAnchorModel[] newArray(int size) {
            return new LiveAnchorModel[size];
        }
    };


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.uid);
        dest.writeString(this.logo);
        dest.writeString(this.nickName);
        dest.writeString(this.summary);
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }
}
