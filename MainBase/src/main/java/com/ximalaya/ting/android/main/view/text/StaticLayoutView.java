package com.ximalaya.ting.android.main.view.text;

import android.content.Context;
import android.graphics.Canvas;
import android.text.Layout;
import android.text.Selection;
import android.text.Spannable;
import android.text.style.ClickableSpan;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 */
public class StaticLayoutView extends View {
    private int width;
    private int height;

    @Nullable
    private Layout layout = null;
    @Nullable
    private CharSequence mText;

    public StaticLayoutView(Context context) {
        super(context);
    }

    public StaticLayoutView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public StaticLayoutView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setLayout(Layout layout) {
        if (layout == null)
            return;

        this.layout = layout;
        mText = this.layout.getText();
        if (this.layout.getWidth() != width || this.layout.getHeight() != height) {
            width = this.layout.getWidth();
            height = this.layout.getHeight();
            requestLayout();
        }
//        if (!TextUtils.isEmpty(layout.getText())) {
//            TextRemoteImageManager.checkAddViewToLoadingCache(this, layout.getText());
//        }
    }

    public @Nullable Layout getLayout() {
        return layout;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        canvas.save();
        canvas.translate(getPaddingLeft(), getPaddingTop());
        if (layout != null) {
            try {
                layout.draw(canvas, null, null, 0);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        canvas.restore();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (layout == null)
            return true;

        int action = event.getAction();
        boolean updateSelection = (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_DOWN) && mText instanceof Spannable;
        if (updateSelection) {
            int x = (int) event.getX();
            int y = (int) event.getY();


            int line = layout.getLineForVertical(y);
            int off = layout.getOffsetForHorizontal(line, x);

            ClickableSpan[] link = ((Spannable) mText).getSpans(off, off, ClickableSpan.class);

            if (link.length != 0) {
                if (action == MotionEvent.ACTION_UP) {
                    link[0].onClick(this);
                } else {
                    Selection.setSelection(((Spannable) mText), ((Spannable) mText).getSpanStart(link[0]), ((Spannable) mText).getSpanEnd(link[0]));
                }

                return true;
            } else {
                Selection.removeSelection(((Spannable) mText));
            }
        }
        return super.onTouchEvent(event);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (layout != null) {
            setMeasuredDimension(layout.getWidth() + getPaddingLeft() + getPaddingRight(), layout.getHeight() + getPaddingTop() + getPaddingBottom());
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }
    }

    @Nullable
    public CharSequence getText() {
        return mText;
    }
}
