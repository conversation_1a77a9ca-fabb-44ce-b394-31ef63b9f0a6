package com.ximalaya.android.universalcomponentsdk.loader.operationPart

import android.view.View
import android.view.ViewGroup

/**
 * Created by 5Greatest on 2021.11.01
 *
 * <AUTHOR>
 *   On 2021/11/1
 */
interface IBlockAddViewOperation {
    fun blockAddChildViewToFragmentView(type: String?, childView: View?, fragmentViewGroup: ViewGroup?): Boolean

    fun blockChildViewToTargetArea(type: String?, childView: View?, viewGroup: ViewGroup): Bo<PERSON><PERSON>
}