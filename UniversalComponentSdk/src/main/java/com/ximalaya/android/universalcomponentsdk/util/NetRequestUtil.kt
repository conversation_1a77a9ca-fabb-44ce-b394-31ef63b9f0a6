package com.ximalaya.android.universalcomponentsdk.util

import com.ximalaya.android.universalcomponentsdk.ComponentConfigurationCenter
import com.ximalaya.android.universalcomponentsdk.callBack.IComponentDataCallBack
import com.ximalaya.android.universalcomponentsdk.callBack.IComponentRequestCallBack
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.util.common.SceneInfoUtil

/**
 * Created by 5Greatest on 2021.10.28
 *
 * <AUTHOR>
 *   On 2021/10/28
 */
class NetRequestUtil {
    companion object {
        private const val ERROR_CODE_NO_IMP = -99900

        fun <T> baseGetRequest(
            url: String?,
            specificParams: Map<String, String>?,
            callback: IComponentDataCallBack<T>?,
            successCallBack: IComponentRequestCallBack<T>?
        ) {
            SceneInfoUtil.addSceneInfoToParams(specificParams)
            SceneInfoUtil.addNewFreeCrowdToParams(specificParams)
            ComponentConfigurationCenter.getCommonRequestFunction()?.let {
                it.baseGetRequest(url, specificParams, callback, successCallBack)
                return
            }
            callback?.onError(ERROR_CODE_NO_IMP, "")
        }
    }
}