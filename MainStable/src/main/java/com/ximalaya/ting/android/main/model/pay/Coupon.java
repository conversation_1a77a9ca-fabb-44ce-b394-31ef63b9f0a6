package com.ximalaya.ting.android.main.model.pay;

import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.RelativeSizeSpan;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.util.common.StringUtil;
import com.ximalaya.ting.android.main.stable.R;

import java.io.Serializable;


public class Coupon {
    public static final String DISCOUNT_TYPE_RATE = "RATE";
    public static final String DISCOUNT_TYPE_VALUE = "VALUE";

    @SerializedName(value = "couponId", alternate = "id")
    private long couponId;          //优惠券id
    @SerializedName("promoCode")
    private long promoCode;         //优惠券码
    @SerializedName("activityId")
    private long activityId;        //优惠券对应活动id

    @SerializedName("hasGet")
    private boolean hasGet;         //优惠券是否被领取
    @SerializedName("isAvailable")
    private boolean isAvailable;    //优惠券当前是否可用
    @SerializedName("isLimitType")
    private boolean isLimitType;
    @SerializedName("isPaid")
    private boolean isPaid;         // 优惠券自身是否收费(是优惠券还是限购券)
    @SerializedName("isVipCounpon")
    private boolean isVipCounpon;   //优惠券是否是会员券
    private boolean checked;

    @SerializedName("plusRate")
    private double plusRate;        // 优惠券面值，折扣加类型，单位百分比，显示为8折
    @SerializedName("couponValue")
    private double couponValue;     //优惠金额
    @SerializedName("promotionPrice")
    private double promotionPrice;  //促销价
    @SerializedName("minLimitValue")
    private double minLimitValue;   //受限前提下的最低门槛
    @SerializedName("couponRate")
    private double couponRate;      //优惠券折扣（数字格式统一为0.**） 这里没有会传null

    @SerializedName("name")
    private String name;                    //优惠券名称
    @SerializedName("discountType")
    private String discountType;            // 价格类型，RATE折扣价，VALUE固定价
    @SerializedName("couponDetailUrl")
    private String couponDetailUrl;         // 已获取优惠券详情页地址
    @SerializedName("couponUrl")
    private String couponUrl;               //优惠券h5url
    @SerializedName("unavailableAlertText")
    private String unavailableAlertText;    //领券成功但是不可用提示文案，没有不提示
    @SerializedName("successAlertText")
    private String successAlertText;        //领券成功并且可用提示文案，没有不提示
    @SerializedName("couponType")
    private String couponType;              //优惠券类型:UNIVERSAL(通用券)|PAYED_VIP(付费会员券)|PAYED_SUBSCRIBE(付费订阅券)|SINGLE_PHASE(单期购买券)
    @SerializedName("couponTypeName")
    private String couponTypeName;
    @SerializedName("description")
    private String description;             //优惠券简介
    @SerializedName("startTime")
    private String startTime;
    @SerializedName("endTime")
    private String endTime;
    @SerializedName("receiveCouponUrl")
    private String receiveCouponUrl;
    @SerializedName("validDateDesc")
    private String validDateDesc;


    @SerializedName("validDate")
    private ValidDate validDate;
    @SerializedName("properties")
    private Property property;
    @SerializedName("paidInfo")
    private LimitTicketPaidInfo paidInfo; // 付费优惠券信息，免费不存在，since：v2

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Coupon coupon = (Coupon) o;

        return couponId == coupon.couponId;

    }

    @Override
    public int hashCode() {
        return (int) (couponId ^ (couponId >>> 32));
    }

    /****************************************************************/

    public String getValidDateDesc() {
        return validDateDesc;
    }

    public void setValidDateDesc(String validDateDesc) {
        this.validDateDesc = validDateDesc;
    }

    public double getCouponRate() {
        return couponRate;
    }

    public void setCouponRate(double couponRate) {
        this.couponRate = couponRate;
    }

    public boolean isVipCounpon() {
        return isVipCounpon;
    }


    public long getCouponId() {
        return couponId;
    }

    public void setCouponId(long couponId) {
        this.couponId = couponId;
    }

    public long getPromoCode() {
        return promoCode;
    }


    public double getCouponValue() {
        return couponValue;
    }

    public String getCouponValueStr() {
        return StringUtil.subZeroAndDot(couponValue);
    }

    public void setCouponValue(double couponValue) {
        this.couponValue = couponValue;
    }

    public String getName() {
        return name;
    }


    public boolean isLimitType() {
        return isLimitType;
    }


    public double getMinLimitValue() {
        return minLimitValue;
    }


    public String getCouponType() {
        return couponType;
    }


    public String getCouponTypeName() {
        return couponTypeName;
    }


    public String getStartTime() {
        return startTime;
    }


    public String getEndTime() {
        return endTime;
    }


    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public long getActivityId() {
        return activityId;
    }


    public boolean isHasGet() {
        return hasGet;
    }

    public void setHasGet(boolean hasGet) {
        this.hasGet = hasGet;
    }

    public String getDescription() {
        return description;
    }


    public String getDiscountType() {
        return discountType;
    }


    public double getPlusRate() {
        return plusRate;
    }


    /**
     * 得到折扣
     * "plusRate": 80,           //优惠券面值，折扣加类型，单位百分比，显示为8折
     *
     * @return
     */
    public String getRateValue() {
        double v = plusRate / 10;
        String s = String.valueOf(v);
        if (s.contains(".")) {
            String substring = s.substring(s.indexOf(".") + 1);
            if ("0".equals(substring)) {
                return s.substring(0, s.indexOf("."));
            }
        }
        return s;
    }

    public boolean isPaid() {
        return isPaid;
    }


    public LimitTicketPaidInfo getPaidInfo() {
        return paidInfo;
    }


    public String getCouponDetailUrl() {
        return couponDetailUrl;
    }


    public String getReceiveCouponUrl() {
        return receiveCouponUrl;
    }

    public String getCouponUrl() {
        return couponUrl;
    }


    public ValidDate getValidDate() {
        return validDate;
    }

    public Property getProperty() {
        return property;
    }

    public double getPromotionPrice() {
        return promotionPrice;
    }

    public boolean isAvailable() {
        return isAvailable;
    }

    public String getUnavailableAlertText() {
        return unavailableAlertText;
    }

    public String getSuccessAlertText() {
        return successAlertText;
    }

    public String getPeriodText(Context context) {
        if (context == null) return "";
        if (!TextUtils.isEmpty(startTime) && !TextUtils.isEmpty(endTime)) {
            return context.getResources().getString(R.string.main_coupon_period, startTime, endTime);
        } else if (validDate != null && validDate.startTime > 0 && validDate.endTime > 0) {
            String startTime = StringUtil.getTimeWithFormatYYMMDD(validDate.startTime);
            String endTime = StringUtil.getTimeWithFormatYYMMDD(validDate.endTime);
            return context.getResources().getString(R.string.main_coupon_period, startTime, endTime);
        }
        return "";
    }

    public static class ValidDate implements Serializable {
        public static final String TYPE_VALID_DAY = "VALID_DAY";//有效期类型:VALID_DAY(自领取日起有效天数)
        public static final String TYPE_LIMIT_DATE = "LIMIT_DATE";//LIMIT_DATE(截止日期)
        public static final String TYPE_FIXED_TIME_PERIOD = "FIXED_TIME_PERIOD";//FIXED_TIME_PERIOD(日期区间)

        @SerializedName("type")
        private String type;
        @SerializedName("startTime")
        private long startTime;   //日期区间类型时有值
        @SerializedName("endTime")
        private long endTime;     //截止日期和日期区间类型时有值
        @SerializedName("validDays")
        private int validDays;      //截止日期和日期区间类型时有值


        public String getType() {
            return type;
        }

        public long getStartTime() {
            return startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public int getValidDays() {
            return validDays;
        }
    }

    public static class Property {
        @SerializedName("background")
        public String background;//优惠券样式背景图片，没有就用默认的
        @SerializedName("fontColor")
        public String fontColor;
        @SerializedName("buttonStyle")
        public NormalButtonStyle normalButtonStyle;
        @SerializedName("receivedButtonStyle")
        public ReceivedButtonStyle receivedButtonStyle;

    }

    //可领取按钮样式
    public static class NormalButtonStyle {
        @SerializedName("backgroundColor")
        public String backgroundColor;  //按钮背景颜色(#333333)
        @SerializedName("text")
        public String text;             //按钮文案
        @SerializedName("fontColor")
        public String fontColor;        //字体颜色(#333333)
    }

    //已领取按钮样式
    public static class ReceivedButtonStyle {
        @SerializedName("backgroundColor")
        public String backgroundColor;  //按钮背景颜色(#333333)
        @SerializedName("text")
        public String text;             //按钮文案
        @SerializedName("fontColor")
        public String fontColor;        //字体颜色(#333333)
    }


    public SpannableString getValueText() {
        String couponValue = "";
        String couponUnit = "";
        String discountType = getDiscountType();
        if (Coupon.DISCOUNT_TYPE_VALUE.equals(discountType)) {
            couponUnit = " 喜点";
            couponValue = getCouponValueStr();
        } else if (Coupon.DISCOUNT_TYPE_RATE.equals(discountType)) {
            couponUnit = " 折";
            couponValue = getRateValue();
        }

        StringBuilder priceContentBuilder = new StringBuilder();
        priceContentBuilder.append(couponValue);
        priceContentBuilder.append(couponUnit);
        SpannableString spannableString = new SpannableString(priceContentBuilder);
        spannableString.setSpan(new RelativeSizeSpan(0.3f),
                priceContentBuilder.indexOf(couponUnit), priceContentBuilder.length(),
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }
}
