package com.ximalaya.ting.android.main.manager.trainingcamp.beforeSale;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.TextView;

import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.fragment.trainingcamp.TrainingCampFragment;
import com.ximalaya.ting.android.main.manager.trainingcamp.ITrainingCampManager;
import com.ximalaya.ting.android.main.manager.trainingcamp.ITrainingCampPresenter;
import com.ximalaya.ting.android.main.model.album.TrainingAlbum;
import com.ximalaya.ting.android.main.util.ui.ViewStatusUtil;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by 5Greatest on 2020.05.27
 *
 * <AUTHOR>
 * On 2020-05-27
 */
public class TrainingCampSaleLooperManager implements ITrainingCampManager {
    private static final long LOOP_INTERVAL = 1150;

    private WeakReference<TrainingCampFragment> mFragmentReference;
    private TrainingCampBeforeSalePresenter mPresenter;

    private WeakReference<TextView> mTextViewReference;

    private Handler mHandler = new Handler(Looper.getMainLooper());
    private LooperRunnable mLooperRunnable = new LooperRunnable();

    private int mIndex = 0;
    private List<String> mSaleList = new ArrayList<>();
    private boolean mIsStop = false;

    public TrainingCampSaleLooperManager(TrainingCampFragment fragment, TrainingCampBeforeSalePresenter presenter) {
        this.mFragmentReference = new WeakReference<>(fragment);
        this.mPresenter = presenter;
    }

    ////////////////////////////////////////////////////////
    ////////////////// setter and getter ///////////////////
    //////////////////       start       ///////////////////
    ////////////////////////////////////////////////////////

    public void setTextView(TextView textView) {
        mTextViewReference = new WeakReference(textView);
    }

    ////////////////////////////////////////////////////////
    ////////////////// setter and getter ///////////////////
    //////////////////        end        ///////////////////
    ////////////////////////////////////////////////////////

    /**
     * 开始循环
     */
    public void startLoop() {
        stopLoop();
        if (null != mPresenter.getAlbum()
                && mPresenter.getAlbum() instanceof TrainingAlbum
                && !ToolUtil.isEmptyCollects(((TrainingAlbum) mPresenter.getAlbum()).saleList)) {
            updateSaleList(((TrainingAlbum) mPresenter.getAlbum()).saleList);
            mIsStop = false;
            mLooperRunnable.run();
        }
    }

    /**
     * 终止循环
     */
    public void stopLoop() {
        mIsStop = true;
        mIndex = 0;
        mSaleList.clear();
        mHandler.removeCallbacks(mLooperRunnable);
        hideLoopText();
    }

    /**
     * 终止循环
     */
    public void resumeLoop() {
        if (ToolUtil.isEmptyCollects(mSaleList)) {
            return;
        }
        mIsStop = false;
        mHandler.removeCallbacks(mLooperRunnable);
        mLooperRunnable.run();
    }

    /**
     * 暂停循环
     */
    public void pauseLoop() {
        mIsStop = true;
        mHandler.removeCallbacks(mLooperRunnable);
        hideLoopText();
    }

    /**
     * 循环
     */
    private void loopAnimate() {
        if (mIsStop) {
            return;
        }
        mIndex++;
        if (mIndex >= mSaleList.size()) {
            mIndex = 0;
        }
        mHandler.postDelayed(mLooperRunnable, LOOP_INTERVAL);
    }

    /**
     * 更新售卖信息
     */
    private void updateSaleList(List<String> list) {
        mIndex = 0;
        mSaleList.clear();
        if (!ToolUtil.isEmptyCollects(list)) {
            mSaleList.addAll(list);
        }
    }

    private void hideLoopText() {
        if (null == getTextView()) {
            return;
        }
        ViewStatusUtil.setVisible(View.INVISIBLE, getTextView());
    }

    private TextView getTextView() {
        if (null == getFragment()
                || null == mTextViewReference
                || null == mTextViewReference.get()) {
            return null;
        }
        return mTextViewReference.get();
    }

    @Override
    public TrainingCampFragment getFragment() {
        if (null == mFragmentReference
                || null == mFragmentReference.get()
                || !mFragmentReference.get().canUpdateUi()) {
            return null;
        }
        return mFragmentReference.get();
    }

    @Override
    public void onFragmentDestroy() {
        mSaleList.clear();
        mPresenter = null;
    }

    @Override
    public ITrainingCampPresenter getDataProvider() {
        return mPresenter;
    }

    private class LooperRunnable implements Runnable {
        private ObjectAnimator alphaAnimator = null;
        private ObjectAnimator translationAnimator = null;

        @Override
        public void run() {
            if (!mIsStop
                    && null != getFragment()
                    && null != mTextViewReference
                    && null != mTextViewReference.get()
                    && !ToolUtil.isEmptyCollects(mSaleList)) {
                String saleOrderStr = ViewStatusUtil.getSafe(mSaleList, mIndex);
                doSaleLoopAnimation(mTextViewReference.get(), saleOrderStr, new LooperAnimatorListener());
            }
        }

        /**
         * 配置并开启循环
         */
        private void doSaleLoopAnimation(TextView textView, String oldString, Animator.AnimatorListener listener) {
            if (null == oldString) {
                return;
            }
            if (null == translationAnimator) {
                float transactionX;
                if (null != textView.getLayoutParams()
                        && textView.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
                    transactionX = textView.getMeasuredWidth() + ((ViewGroup.MarginLayoutParams) textView.getLayoutParams()).leftMargin;
                } else {
                    transactionX = textView.getMeasuredWidth();
                }
                translationAnimator = ObjectAnimator.ofFloat(textView, "translationX", transactionX);
                translationAnimator.setDuration(250);
                translationAnimator.setInterpolator(new AccelerateInterpolator());
            }

            if (null == alphaAnimator) {
                alphaAnimator = ObjectAnimator.ofFloat(textView, "alpha", 1F, 0F);
                alphaAnimator.setDuration(300);
                alphaAnimator.setStartDelay(1550);
                alphaAnimator.setInterpolator(new DecelerateInterpolator());
                alphaAnimator.addListener(listener);
            }

            textView.setText(oldString);
            textView.setAlpha(1F);
            textView.setVisibility(View.VISIBLE);
            translationAnimator.start();
            alphaAnimator.start();
        }
    }

    private class LooperAnimatorListener implements Animator.AnimatorListener {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            loopAnimate();
        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    }
}
