package com.ximalaya.ting.android.main.manager.familyAlbum

import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.xmtrace.XMTraceApi

/**
 * Created by 5Greatest on 2021.07.06
 *
 * <AUTHOR>
 *   On 2021/7/6
 */
class FamilyVipMarkPointManager {
    companion object {
        /**
         * 曝光 身份标记弹窗
         * */
        fun markPointOnShowMarkMemberDialog() {
            XMTraceApi.Trace()
                .setMetaId(33318)
                .setServiceId("dialogView")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .createTrace()
        }

        /**
         * 点击 身份标记弹窗-身份标签
         * */
        fun markPointOnClickMemberItem(item: String?) {
            XMTraceApi.Trace()
                .setMetaId(33319)
                .setServiceId("dialogClick")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .put("Item", item ?: "")
                .createTrace()
        }

        /**
         * 点击 身份标记弹窗-底部按钮
         * */
        fun markPointOnClickMarkMemberConfirm(item: String?) {
            XMTraceApi.Trace()
                .setMetaId(33320)
                .setServiceId("dialogClick")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .put("Item", item ?: "")
                .createTrace()
        }

        /**
         * 曝光 身份标记完成弹窗
         * */
        fun markPointOnShowMarkMemberCompleteDialog() {
            XMTraceApi.Trace()
                .setMetaId(33321)
                .setServiceId("dialogView")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .createTrace()
        }

        /**
         * 点击 身份标记完成弹窗-知道了
         * */
        fun markPointOnClickMarkMemberClose() {
            XMTraceApi.Trace()
                .setMetaId(33322)
                .setServiceId("dialogClick")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .createTrace()
        }

        /**
         * 曝光 送亲友一起听弹窗
         * */
        fun markPointOnShowShareBenefitDialog(albumId: Long) {
            XMTraceApi.Trace()
                .setMetaId(33316)
                .setServiceId("dialogView")
                .put("currAlbumId", "$albumId")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .createTrace()
        }

        /**
         * 点击 送亲友一起听弹窗-分享按钮
         * */
        fun markPointOnClickShareBenefit(albumId: Long) {
            XMTraceApi.Trace()
                .setMetaId(33317)
                .setServiceId("dialogClick")
                .put("userType", "inviter")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .put("currAlbumId", "$albumId")
                .createTrace()
        }

        /**
         * 点击 送亲友一起听弹窗-查看进度
         * */
        fun markPointOnClickCheckProgress(albumId: Long) {
            XMTraceApi.Trace()
                .setMetaId(33623)
                .setServiceId("dialogClick")
                .put("currAlbumId", "$albumId")
                .put("isVIP", "" + UserInfoMannage.isVipUser())
                .createTrace()
        }
    }
}