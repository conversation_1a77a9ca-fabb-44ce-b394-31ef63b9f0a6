package com.ximalaya.ting.android.main.fragment.other.child

import android.app.DatePickerDialog
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.framework.util.CustomToast
import com.ximalaya.ting.android.framework.util.OneClickHelper
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder
import com.ximalaya.ting.android.framework.view.drawable.FlexibleRoundDrawable
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.fragment.BaseFragment2
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment
import com.ximalaya.ting.android.host.listener.IFragmentFinish
import com.ximalaya.ting.android.host.util.extension.dp
import com.ximalaya.ting.android.host.util.view.LocalImageUtil
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil
import com.ximalaya.ting.android.main.model.ChildInfoModel
import com.ximalaya.ting.android.main.model.recommend.RecommendBayInfoModel
import com.ximalaya.ting.android.main.request.MainStableRequest
import com.ximalaya.ting.android.main.stable.R
import com.ximalaya.ting.android.main.view.album.DateUtils
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.xmutil.SystemServiceManager
import java.text.ParsePosition
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList

class EditChildInfoFragment : BaseFragment2(false, null), View.OnClickListener, IFragmentFinish {

    companion object {
        private const val KEY_TYPE = "key_type"
        private const val KEY_BUTTON_TEXT = "key_button_text"
        private const val KEY_URL = "key_url"

        private const val GENDER_NOT_DEFINED = -1
        private const val GENDER_PREGNANT = 0
        private const val GENDER_BOY = 1
        private const val GENDER_GIRL = 2

        private const val STYLE_COMMON = 0 //正常样式
        private const val STYLE_DEFINED = 1 //预产期样式

        fun newInstance(model: RecommendBayInfoModel): EditChildInfoFragment {
            val fragment = EditChildInfoFragment()
            fragment.mDefaultChildInfo = model
            return fragment
        }

        fun newInstance(type: Int, text: String?, url: String?): EditChildInfoFragment {
            val bundle = Bundle()
            bundle.putInt(KEY_TYPE, type)
            bundle.putString(KEY_BUTTON_TEXT, text)
            bundle.putString(KEY_URL, url)
            val fragment = EditChildInfoFragment()
            fragment.arguments = bundle
            return fragment
        }
    }

    private var mRecyclerView: RecyclerView? = null
    private var mMask: View? = null
    private var mAddView: View? = null
    private var mTvName: TextView? = null
    private var mRgGender: RadioGroup? = null
    private var mRbBoy: RadioButton? = null
    private var mRbGirl: RadioButton? = null
    private var mRbPregnant: RadioButton? = null
    private var mVDate: View? = null
    private var mTvDate: TextView? = null
    private var mTvChooseDate: TextView? = null
    private var mTvComputeDueDateHint: TextView? = null
    private var mVComputeDueDate: View? = null
    private var mTvChooseLastMenstruation: TextView? = null
    private var mTvComputeDueDate: TextView? = null
    private var mTvGetDueDate: TextView? = null
    private var mTvSave: TextView? = null

    private var mSelectedBoyBg: Drawable? = null
    private var mSelectedGirlBg: Drawable? = null

    private var mType = 0
    private var mButtonText: String? = null
    private var mUrl: String? = null

    private var mLayoutManager: ChildHeadLayoutManager? = null
    private var mSnapHelper: PagerSnapHelper? = null
    private var mAdapter: ChildHeadAdapter? = null
    private var mChildInfoList = ArrayList<ChildInfoModel>()
    private var mCurChildInfo: ChildInfoModel? = null
    private var mDefaultChildInfo: RecommendBayInfoModel? = null
    private var mCurIndex = 1
    private var mLastMenstruationTime: String? = null
    private var mIsComputeDueDate = false

    override fun getPageLogicName(): String {
        return javaClass.simpleName
    }

    override fun getContainerLayoutId(): Int {
        return R.layout.main_fra_edit_child_info
    }

    override fun getTitleBarResourceId(): Int {
        return R.id.main_title_bar
    }

    override fun isShowPlayButton(): Boolean {
        return false
    }

    override fun initUi(savedInstanceState: Bundle?) {
        initView()
        initListeners()
    }

    private fun initView() {
        arguments?.apply {
            mType = getInt(KEY_TYPE)
            mButtonText = getString(KEY_BUTTON_TEXT)
            mUrl = getString(KEY_URL)
        }

        setTitle("宝贝信息")
        mRecyclerView = findViewById(R.id.main_rv_child_head)
        mMask = findViewById(R.id.main_v_mask)
        mAddView = findViewById(R.id.main_v_add_child)
        mTvName = findViewById(R.id.main_tv_name)
        mRgGender = findViewById(R.id.main_rg_gender)
        mRbBoy = findViewById(R.id.main_rb_gender_boy)
        mRbGirl = findViewById(R.id.main_rb_gender_girl)
        mRbPregnant = findViewById(R.id.main_rb_gender_pregnant)
        mVDate = findViewById(R.id.main_v_date)
        mTvDate = findViewById(R.id.main_tv_birthday)
        mTvChooseDate = findViewById(R.id.main_tv_choose_date)
        mTvComputeDueDateHint = findViewById(R.id.main_tv_compute_date_hint)
        mVComputeDueDate = findViewById(R.id.main_v_compute_due_date)
        mTvChooseLastMenstruation = findViewById(R.id.main_tv_choose_time_of_last_menstruation)
        mTvComputeDueDate = findViewById(R.id.main_tv_compute_due_date)
        mTvGetDueDate = findViewById(R.id.main_tv_get_due_date)
        mTvSave = findViewById(R.id.main_tv_save)
        if (!mButtonText.isNullOrEmpty()) {
            mTvSave?.text = mButtonText
        }

        mSelectedBoyBg = FlexibleRoundDrawable.createFlexibleRoundDrawable(mContext,
                R.drawable.main_ic_gender_boy, 50.dp, FlexibleRoundDrawable.CORNER_ALL)
        mSelectedGirlBg = FlexibleRoundDrawable.createFlexibleRoundDrawable(mContext,
                R.drawable.main_ic_gender_girl, 50.dp, FlexibleRoundDrawable.CORNER_ALL)
    }

    private fun initListeners() {
        mMask?.apply {
            val params = layoutParams
            params.width = BaseUtil.getScreenWidth(mContext) / 2 - 65.dp
            layoutParams = params
        }
        mLayoutManager = ChildHeadLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
        mAdapter = ChildHeadAdapter(mChildInfoList, object : ChildHeadAdapter.IOnItemClickListener {
            override fun onItemClicked(childInfoModel: ChildInfoModel) {
                val position = mChildInfoList.indexOf(childInfoModel)
                if (position < 0 || position == mCurIndex) {
                    return
                }
                updateRemoveBtnStatus(position, true)
                mRecyclerView?.smoothScrollToPosition(position)
                changeChildView(position)
            }

            override fun onItemRemoved(childInfoModel: ChildInfoModel) {
                showDeleteChildInfoDialog(childInfoModel)
            }
        })
        mRecyclerView?.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager
            setPadding(110.dp, paddingTop, 110.dp, paddingBottom)
            mSnapHelper = PagerSnapHelper()
            mSnapHelper?.attachToRecyclerView(this)
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                var position = 0

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) { //如果滚动结束
                        val snapView = mSnapHelper?.findSnapView(mLayoutManager) ?: return
                        val curPosition = mLayoutManager?.getPosition(snapView) ?: 0
                        if (position != curPosition) {
                            position = curPosition
                            mRecyclerView?.smoothScrollToPosition(position)
                            updateRemoveBtnStatus(position, true)
                            changeChildView(position)
                        }
                    }
                }
            })
        }
        mRgGender?.setOnCheckedChangeListener { _, checkedId ->
            val button = findViewById<RadioButton?>(checkedId)
            if (checkedId == -1 || (button != null && button.isChecked)) {
                val lastGender = mCurChildInfo?.gender ?: GENDER_PREGNANT
                setRadioButtonBg(checkedId)
                val gender = mCurChildInfo?.gender ?: GENDER_PREGNANT
                mTvDate?.text = getDateText(gender)
                if (lastGender != gender && (lastGender == GENDER_PREGNANT || gender == GENDER_PREGNANT)) {
                    resetChooseDateView()
                }
                refreshDueDateView(false)
                mTvComputeDueDateHint?.visibility = if (gender == GENDER_PREGNANT) View.VISIBLE else View.GONE
                updateCompleteBtn()
                mAdapter?.notifyItemChanged(mCurIndex, ChildHeadAdapter.UPDATE_HEAD_IMG_TAG)
            }
        }
        mAddView?.setOnClickListener(this)
        mTvName?.setOnClickListener(this)
        mTvChooseDate?.setOnClickListener(this)
        mTvComputeDueDateHint?.setOnClickListener(this)
        mTvChooseLastMenstruation?.setOnClickListener(this)
        mTvGetDueDate?.setOnClickListener(this)
        mTvSave?.setOnClickListener(this)
    }

    private fun updateRemoveBtnStatus(selectPos: Int, needRefresh: Boolean) {
        mAdapter?.apply {
            setSelectedPos(selectPos)
            setCanRemove(mChildInfoList.size > 1)
            if (needRefresh) {
                notifyItemRangeChanged(0, itemCount, ChildHeadAdapter.UPDATE_REMOVE_IMG_TAG)
            }
        }
    }

    private fun showDeleteChildInfoDialog(childInfoModel: ChildInfoModel) {
        DialogBuilder<DialogBuilder<*>>(activity)
                .setMessage(R.string.main_delete_child_info)
                .setOkBtn("删除", DialogBuilder.DialogCallback {
                    val position = mChildInfoList.indexOf(childInfoModel)
                    if (position < 0) {
                        return@DialogCallback
                    }
                    mChildInfoList.remove(childInfoModel)
                    mMask?.visibility = if (mChildInfoList.size < 3 && mType == STYLE_COMMON) View.VISIBLE else View.GONE
                    mAdapter?.notifyItemRemoved(position)
                    updateCompleteBtn()
                    CustomToast.showToast("删除成功")
                    postOnUiThreadDelayed({
                        val snapView = mSnapHelper?.findSnapView(mLayoutManager)
                        if (snapView != null) {
                            val curPosition = mLayoutManager?.getPosition(snapView) ?: 0
                            updateRemoveBtnStatus(curPosition, true)
                            mRecyclerView?.smoothScrollToPosition(curPosition)
                            mLayoutManager?.scaleView()
                            changeChildView(curPosition)
                        }
                    }, 100)
                })
                .setCancelBtn("取消")
                .showConfirm()
    }

    private fun changeChildView(position: Int) {
        mCurIndex = position
        mCurChildInfo = mChildInfoList[mCurIndex]
        showChildInfo(mCurChildInfo)
    }

    private fun showChildInfo(childInfoModel: ChildInfoModel?) {
        childInfoModel?.apply {
            mTvName?.text = nickName
            getCheckIdButton(gender)?.isChecked = true
            mTvDate?.text = getDateText(gender)
            refreshDueDateView(false)
            mTvChooseDate?.let {
                if (birthday.isNullOrEmpty()) {
                    it.text = "请选择"
                    it.setCompoundDrawables(null, null, LocalImageUtil
                            .getDrawable(mContext, com.ximalaya.ting.android.host.R.drawable.host_arrow_gray_right), null)
                } else {
                    it.text = birthday
                    it.setCompoundDrawables(null, null, null, null)
                }
            }
            mTvComputeDueDateHint?.visibility = if (gender == GENDER_PREGNANT) View.VISIBLE else View.GONE
        }
    }

    private fun getDateText(gender: Int): String {
        return if (gender == RecommendBayInfoModel.STATUS_PREGNANCY) "预产期" else "宝贝生日"
    }

    private fun getDateTextHint(gender: Int): String {
        return if (gender == RecommendBayInfoModel.STATUS_PREGNANCY) "预产期" else "生日"
    }

    private fun getCheckIdButton(gender: Int): RadioButton? {
        return when (gender) {
            GENDER_BOY -> mRbBoy
            GENDER_GIRL -> mRbGirl
            else -> mRbPregnant
        }
    }

    private fun setRadioButtonBg(checkedId: Int) {
        when (checkedId) {
            R.id.main_rb_gender_boy -> {
                mCurChildInfo?.gender = GENDER_BOY
                mRbBoy?.background = mSelectedBoyBg
                mRbGirl?.setBackgroundResource(R.drawable.main_gender_unselected_bg)
            }
            R.id.main_rb_gender_girl -> {
                mCurChildInfo?.gender = GENDER_GIRL
                mRbBoy?.setBackgroundResource(R.drawable.main_gender_unselected_bg)
                mRbGirl?.background = mSelectedGirlBg
            }
            R.id.main_rb_gender_pregnant -> {
                mCurChildInfo?.gender = GENDER_PREGNANT
                mRbBoy?.setBackgroundResource(R.drawable.main_gender_unselected_bg)
                mRbGirl?.setBackgroundResource(R.drawable.main_gender_unselected_bg)
            }
            else -> {
                mCurChildInfo?.gender = GENDER_NOT_DEFINED
                mRbBoy?.setBackgroundResource(R.drawable.main_gender_unselected_bg)
                mRbGirl?.setBackgroundResource(R.drawable.main_gender_unselected_bg)
            }
        }
    }

    override fun loadData() {
        MainStableRequest.queryChildInfo(object : IDataCallBack<List<ChildInfoModel>> {
            override fun onSuccess(result: List<ChildInfoModel>?) {
                doAfterAnimation {
                    if (!canUpdateUi()) {
                        return@doAfterAnimation
                    }
                    if (result.isNullOrEmpty()) {
                        val childInfoModel = ChildInfoModel()
                        childInfoModel.gender = mDefaultChildInfo?.status ?: GENDER_PREGNANT
                        childInfoModel.nickName = "宝贝1"
                        mChildInfoList.add(childInfoModel)
                    } else {
                        mType = STYLE_COMMON
                        mChildInfoList.addAll(result)
                    }
                    mMask?.visibility = if (mChildInfoList.size < 3 && mType == STYLE_COMMON) View.VISIBLE else View.GONE
                    val index = getIndex(mChildInfoList, mDefaultChildInfo)
                    updateRemoveBtnStatus(index, false)
                    mAdapter?.notifyDataSetChanged()
                    mRecyclerView?.smoothScrollToPosition(index)
                    changeChildView(index)
                    setAllViewVisibility(View.VISIBLE)
                }
            }

            override fun onError(code: Int, message: String?) {
                if (!canUpdateUi()) {
                    return
                }
                setAllViewVisibility(View.INVISIBLE)
                onPageLoadingCompleted(LoadCompleteType.NETWOEKERROR)
            }
        })
    }

    private fun getIndex(childInfoList: ArrayList<ChildInfoModel>,
                         babyInfo: RecommendBayInfoModel?): Int {
        if (babyInfo != null && !babyInfo.nickName.isNullOrEmpty()) {
            for (i in 0 until childInfoList.size) {
                if (childInfoList[i].nickName == babyInfo.nickName) {
                    return i
                }
            }
        }
        return if (childInfoList.size > 1) 1 else 0
    }

    private fun updateCompleteBtn() {
        mTvSave?.isSelected = isSaveBtnEnable(false)
    }

    override fun onPause() {
        super.onPause()
        hideSoftInput()
    }

    private fun hideSoftInput() {
        if (mActivity == null || mActivity.currentFocus == null) {
            return
        }
        SystemServiceManager.hideSoftInputFromWindow(mActivity, mActivity.currentFocus!!.windowToken, 0)
    }

    private fun setAllViewVisibility(visibility: Int) {
        ViewStatusUtil.setVisible(visibility, mTvName, mVDate, mTvSave)
        mRgGender?.visibility = if (mType == STYLE_COMMON) visibility else View.INVISIBLE
    }

    override fun onClick(view: View?) {
        if (!OneClickHelper.getInstance().onClick(view)) {
            return
        }
        when (view?.id) {
            R.id.main_v_add_child -> {
                addNewChildInfo()
            }
            R.id.main_tv_name -> {
                val fragment = EditChildNameFragment.newInstance(mCurChildInfo?.nickName)
                fragment.setCallbackFinish(this)
                startFragment(fragment)
            }
            R.id.main_tv_choose_date -> {
                mCurChildInfo?.apply {
                    showChooseBirthDialog(gender, birthday)
                }
            }
            R.id.main_tv_compute_date_hint -> {
                refreshDueDateView(true)
                resetComputeDueDateView()
                updateCompleteBtn()
            }
            R.id.main_tv_get_due_date -> {
                refreshDueDateView(false)
                resetChooseDateView()
                updateCompleteBtn()
            }
            R.id.main_tv_choose_time_of_last_menstruation -> {
                showChooseLastMenstruationDialog(mLastMenstruationTime)
            }
            R.id.main_tv_save -> {
                doSave()
            }
        }
    }

    private fun addNewChildInfo() {
        if (!isSaveBtnEnable(true)) {
            return
        }
        val childInfoModel = ChildInfoModel()
        childInfoModel.gender = GENDER_PREGNANT
        childInfoModel.nickName = "宝贝${mChildInfoList.size + 1}"
        mChildInfoList.add(childInfoModel)
        if (mChildInfoList.size >= 3) {
            mMask?.visibility = View.GONE
        }
        mAdapter?.notifyItemInserted(mChildInfoList.size - 1)
        updateRemoveBtnStatus(mChildInfoList.size - 1, true)
        mRecyclerView?.smoothScrollToPosition(mChildInfoList.size - 1)
        mRecyclerView?.post {
            mLayoutManager?.scaleView()
        }
        updateCompleteBtn()
    }

    private fun isChildInfoComplete(): Boolean {
        var pregnantChildCount = 0
        for (childInfoModel in mChildInfoList) {
            var hint: String
            val nickName = childInfoModel.nickName
            val birthday = childInfoModel.birthday
            if (childInfoModel.gender == GENDER_PREGNANT) {
                pregnantChildCount++
            }
            hint = when {
                nickName.isNullOrEmpty() -> "请选择昵称"
                birthday.isNullOrEmpty() -> "请选择${nickName}的${getDateTextHint(childInfoModel.gender)}"
                pregnantChildCount > 1 -> "暂时只支持一个怀孕中的宝贝"
                else -> getInCorrectDueOrBirthdayHint(nickName, birthday, childInfoModel.gender)
            }
            if (hint.isNotEmpty()) {
                CustomToast.showToast(hint)
                return false
            }
        }
        return true
    }

    private fun isSaveBtnEnable(showToast: Boolean): Boolean {
        for (childInfoModel in mChildInfoList) {
            var hint: String
            val nickName = childInfoModel.nickName
            val birthday = childInfoModel.birthday
            hint = when {
                nickName.isNullOrEmpty() -> "请选择昵称"
                birthday.isNullOrEmpty() -> "请选择${nickName}的${getDateTextHint(childInfoModel.gender)}"
                else -> getInCorrectDueOrBirthdayHint(nickName, birthday, childInfoModel.gender)
            }
            if (hint.isNotEmpty()) {
                if (showToast) {
                    CustomToast.showToast(hint)
                }
                return false
            }
        }
        return true
    }

    private fun getInCorrectDueOrBirthdayHint(nickName: String, birthday: String, gender: Int): String {
        val type = DateUtils.compareToToday(birthday)
        if (gender == GENDER_PREGNANT && type < 0) {
            return "${nickName}的预产期不能早于当前时间"
        } else if (gender != GENDER_PREGNANT && type > 0) {
            return "${nickName}的出生日期不能晚于当前时间"
        }
        return ""
    }

    private fun showChooseBirthDialog(gender: Int, birthday: String?) {
        var year = -1
        var month = -1
        var day = -1
        if (!birthday.isNullOrEmpty()) {
            val array = birthday.split("-")
            if (array.size == 3) {
                year = array[0].toInt()
                month = array[1].toInt() - 1
                day = array[2].toInt()
            }
        }
        val calendar = Calendar.getInstance(Locale.CHINA)
        calendar[1900, 0, 0, 0, 0] = 0
        var minDate = calendar.timeInMillis
        var maxDate = System.currentTimeMillis()
        calendar[year, month] = day
        val myDate = Date()
        calendar.time = myDate
        if (year <= 0 || month < 0 || day < 0) {
            year = calendar[Calendar.YEAR]
            month = calendar[Calendar.MONTH]
            day = calendar[Calendar.DAY_OF_MONTH]
        }
        if (gender == GENDER_PREGNANT) {
            calendar.add(Calendar.DATE, 280)
            minDate = System.currentTimeMillis()
            maxDate = calendar.timeInMillis
        }
        if (mActivity != null) {
            val datePickerDialog = DatePickerDialog(mActivity, com.ximalaya.ting.android.host.R.style.datePickerDialog,
                    { _, yearOfCentury, monthOfYear, dayOfMonth ->
                        if (yearOfCentury > 0 && monthOfYear >= 0 && dayOfMonth > 0) {
                            val newBirthDay = "$yearOfCentury-${
                                String.format("%02d",
                                        monthOfYear + 1)
                            }-${String.format("%02d", dayOfMonth)}"
                            mCurChildInfo?.birthday = newBirthDay
                            mTvChooseDate?.let {
                                it.text = newBirthDay
                                it.setCompoundDrawables(null, null, null, null)
                            }
                            updateCompleteBtn()
                        }
                    }, year, month, day)
            datePickerDialog.datePicker.minDate = minDate
            datePickerDialog.datePicker.maxDate = maxDate
            datePickerDialog.show()
        }
    }

    private fun showChooseLastMenstruationDialog(lastMenstruationTime: String?) {
        var year = -1
        var month = -1
        var day = -1
        if (!lastMenstruationTime.isNullOrEmpty()) {
            val array = lastMenstruationTime.split("-")
            if (array.size == 3) {
                year = array[0].toInt()
                month = array[1].toInt() - 1
                day = array[2].toInt()
            }
        }
        val calendar = Calendar.getInstance(Locale.CHINA)
        val myDate = Date()
        calendar.time = myDate
        if (year <= 0 || month < 0 || day < 0) {
            year = calendar[Calendar.YEAR]
            month = calendar[Calendar.MONTH]
            day = calendar[Calendar.DAY_OF_MONTH]
        }
        calendar.add(Calendar.DATE, -280)
        val minDate = calendar.timeInMillis
        val maxDate = System.currentTimeMillis()
        if (mActivity != null) {
            val datePickerDialog = DatePickerDialog(mActivity, com.ximalaya.ting.android.host.R.style.datePickerDialog,
                    { _, yearOfCentury, monthOfYear, dayOfMonth ->
                        if (yearOfCentury > 0 && monthOfYear >= 0 && dayOfMonth > 0) {
                            val newMenstruationTime = "$yearOfCentury-${
                                String.format("%02d",
                                        monthOfYear + 1)
                            }-${String.format("%02d", dayOfMonth)}"
                            mLastMenstruationTime = newMenstruationTime
                            mTvChooseLastMenstruation?.let {
                                it.text = newMenstruationTime
                                it.setCompoundDrawables(null, null, null, null)
                            }
                            val dueDate = getDueDate(newMenstruationTime)
                            mCurChildInfo?.birthday = dueDate
                            mTvComputeDueDate?.text = dueDate
                            updateCompleteBtn()
                        }
                    }, year, month, day)
            datePickerDialog.datePicker.minDate = minDate
            datePickerDialog.datePicker.maxDate = maxDate
            datePickerDialog.show()
        }
    }

    private fun getDueDate(lastMenstruationTime: String): String? {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val date = sdf.parse(lastMenstruationTime, ParsePosition(0)) ?: return null
        val calendar = Calendar.getInstance(Locale.CHINA)
        calendar.time = date
        calendar.add(Calendar.DATE, 280)
        val dueDate = calendar.time
        return sdf.format(dueDate)
    }

    private fun resetChooseDateView() {
        mCurChildInfo?.birthday = null
        mTvChooseDate?.apply {
            text = "请选择"
            setCompoundDrawables(null, null, LocalImageUtil
                    .getDrawable(mContext, com.ximalaya.ting.android.host.R.drawable.host_arrow_gray_right), null)
        }
    }

    private fun resetComputeDueDateView() {
        mCurChildInfo?.birthday = null
        mTvChooseLastMenstruation?.apply {
            text = "请选择"
            setCompoundDrawables(null, null, LocalImageUtil
                    .getDrawable(mContext, com.ximalaya.ting.android.host.R.drawable.host_arrow_gray_right), null)
        }
        mTvComputeDueDate?.text = "自动计算"
        mLastMenstruationTime = null
    }

    private fun refreshDueDateView(isShowComputeDueDate: Boolean) {
        if (isShowComputeDueDate) {
            mVDate?.visibility = View.GONE
            mVComputeDueDate?.visibility = View.VISIBLE
            mIsComputeDueDate = true
        } else {
            mVDate?.visibility = View.VISIBLE
            mVComputeDueDate?.visibility = View.GONE
            mIsComputeDueDate = false
        }
    }

    private fun doSave() {
        if (!isChildInfoComplete()) {
            return
        }
        val postData = Gson().toJson(mChildInfoList)
        MainStableRequest.addChildInfo(postData, object : IDataCallBack<Boolean> {
            override fun onSuccess(result: Boolean?) {
                if (result != null && result) {
                    CustomToast.showToast("保存成功")
                    setFinishCallBackData(true)
                    finish()
                    if (!mUrl.isNullOrEmpty() && mActivity is MainActivity) {
                        NativeHybridFragment.start(mActivity as MainActivity, mUrl, true)
                    }
                } else {
                    CustomToast.showFailToast("保存失败，请稍后重试")
                }
            }

            override fun onError(code: Int, message: String?) {
                val msg = if (message.isNullOrEmpty()) "网络错误" else message
                CustomToast.showFailToast(msg)
            }
        })
    }

    override fun onFinishCallback(cls: Class<*>?, fid: Int, params: Array<Any?>?) {
        if (canUpdateUi() && cls != null && cls == EditChildNameFragment::class.java
                && !params.isNullOrEmpty() && params[0] is String) {
            val nickName = params[0] as String
            mTvName?.text = nickName
            mCurChildInfo?.nickName = nickName
        }
    }
}