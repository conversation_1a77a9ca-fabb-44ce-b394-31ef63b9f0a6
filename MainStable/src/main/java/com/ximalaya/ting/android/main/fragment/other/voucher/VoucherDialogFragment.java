package com.ximalaya.ting.android.main.fragment.other.voucher;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.StringUtil;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.pay.PaySignatureUtil;
import com.ximalaya.ting.android.host.util.constant.BundleKeyConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.main.model.album.Voucher;
import com.ximalaya.ting.android.main.request.MainStableRequest;
import com.ximalaya.ting.android.main.stable.R;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * $desc$
 * <p>
 * Created by Chang.Xiao on 2017/10/20.
 * <AUTHOR>
 *
 * @version 1.0
 */
public class VoucherDialogFragment extends BaseDialogFragment implements View.OnClickListener {

    public static final String TAG = VoucherDialogFragment.class.getSimpleName();

    public static final int RET_UNDERSTOCK = 5; // 库存不足

    private TextView mVoucherValue;
    private TextView mVoucherTitle;
    private ImageView mVoucherLogo;
    private TextView mVoucherDesc;
    private ImageView mVoucherReceiveBtn;
    private TextView mVoucherTipText;
    private TextView mVoucherRule;

    private Voucher mVoucher;
    private List<String> voucherRules;

    public static VoucherDialogFragment newInstance(Voucher object, ArrayList<String> voucherRules) {
        VoucherDialogFragment voucherDialogFragment = new VoucherDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleKeyConstants.KEY_VOUCHER, object);
        bundle.putStringArrayList(BundleKeyConstants.KEY_VOUCHER_RULE, voucherRules);
        voucherDialogFragment.setArguments(bundle);
        return voucherDialogFragment;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        Window window = getDialog().getWindow();
        if (null == window)
            return null;

        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        View mView = inflater.inflate(R.layout.main_dialog_album_voucher,
                ((ViewGroup) window.findViewById(android.R.id.content)), false); //此处必须是android.R.id.content
        window.setLayout(BaseUtil.dp2px(window.getContext(), 280), ViewGroup.LayoutParams.WRAP_CONTENT);

        parseBundle();
        initView(mView);
        updateUi();

        return mView;
    }

    private void parseBundle() {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mVoucher = (Voucher) bundle.getSerializable(BundleKeyConstants.KEY_VOUCHER);
            voucherRules = bundle.getStringArrayList(BundleKeyConstants.KEY_VOUCHER_RULE);
        }
    }

    private void initView(View mView) {
        mView.findViewById(R.id.main_iv_close).setOnClickListener(this);
        AutoTraceHelper.bindData(mView.findViewById(R.id.main_iv_close),"");
        mVoucherValue = (TextView) mView.findViewById(R.id.main_tv_voucher_value);
        mVoucherTitle = (TextView) mView.findViewById(R.id.main_tv_voucher_name);
        mVoucherLogo = (ImageView) mView.findViewById(R.id.main_iv_voucher_logo);
        mVoucherDesc = (TextView) mView.findViewById(R.id.main_tv_voucher_desc);
        mVoucherReceiveBtn = (ImageView) mView.findViewById(R.id.main_btn_voucher_receive);
        mVoucherTipText = (TextView) mView.findViewById(R.id.main_tv_voucher_tip_text);
        mVoucherRule = (TextView) mView.findViewById(R.id.main_tv_voucher_rule);
        mVoucherReceiveBtn.setOnClickListener(this);
        AutoTraceHelper.bindData(mVoucherReceiveBtn,"");

    }

    private void updateUi() {
        if (null != mVoucher) {
            String value = StringUtil.toSpecFormatNumber(mVoucher.getAmount());
            SpannableStringBuilder style = new SpannableStringBuilder(value + "喜点");
            style.setSpan(new AbsoluteSizeSpan(BaseUtil.sp2px(getContext(), 32)), 0, value.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            mVoucherValue.setText(style);

            mVoucherTitle.setText(String.valueOf(mVoucher.getTitle()));
            ImageManager.from(getContext()).displayImage(mVoucherLogo, mVoucher.getLogo(), -1);
            mVoucherDesc.setText(mVoucher.getDescription());
            updateReceiveBtnStatus(mVoucher.isReceived());
        }
        if (null != voucherRules) {
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < voucherRules.size(); i++) {
                builder.append(i + 1).append(".").append(voucherRules.get(i));
                if ((i + 1) != voucherRules.size()) {
                    builder.append("\n");
                }
            }
            mVoucherRule.setText(builder.toString());
        }
    }

    private void updateReceiveBtnStatus(boolean isReceived) {
        if (null != mVoucher ) {
            if (!isReceived) {
                if (mVoucher.hasRemainInventory()) {
                    mVoucherReceiveBtn.setImageResource(R.drawable.main_bg_voucher_receive_selector);
                    mVoucherReceiveBtn.setEnabled(true);
                } else {
                    showVoucherTipText();
                }
            } else {
                mVoucherReceiveBtn.setImageResource(R.drawable.main_bg_voucher_received);
                mVoucherReceiveBtn.setEnabled(false);
            }
        }
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        if (vId == R.id.main_iv_close) {
            dismissAllowingStateLoss();
        } else if (vId == R.id.main_btn_voucher_receive) {
            if (null != mVoucher ) {
                if (!mVoucher.isReceived()) {
                    if (mVoucher.hasRemainInventory()) {
                        // 领取条件判断
                        receiveVoucher();
                    }
                }
            }
        }
    }

    private void receiveVoucher() {
        Map<String, String> params = new HashMap<>();
        params.put("productItemId", String.valueOf(null != mVoucher ? mVoucher.getItemId() : ""));
        params.put(HttpParamsConstants.PARAM_SIGNATURE,
                PaySignatureUtil.getSignature(getContext(), params));
        MainStableRequest.receiveVoucher(params, new IDataCallBack<JSONObject>() {
            @Override
            public void onSuccess(@Nullable JSONObject object) {
                if (null != object) {
                    int ret = object.optInt("ret");
                    if (ret == 0) {
                        // 领取成功
                        CustomToast.showToast("领取成功");
                        updateReceiveBtnStatus(true);
                        if (null != mOnReceiveCallback) {
                            mOnReceiveCallback.onReceiveSuccess(true);
                        }
                    } else if (ret == RET_UNDERSTOCK) {
                        // 库存不足已领完
                        showVoucherTipText();
                        CustomToast.showToast("代金券太火爆了，已被领完啦");
                        if (null != mOnReceiveCallback) {
                            mOnReceiveCallback.onReceiveFail(ret);
                        }
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (code == RET_UNDERSTOCK) {
                    // 库存不足已领完
                    showVoucherTipText();
                    CustomToast.showToast("代金券太火爆了，已被领完啦");
                    if (null != mOnReceiveCallback) {
                        mOnReceiveCallback.onReceiveFail(code);
                    }
                } else {
                    String msg = TextUtils.isEmpty(message)?"领取失败":message;
                    CustomToast.showFailToast(msg);
                }
            }
        });
    }

    private void showVoucherTipText() {
        mVoucherReceiveBtn.setVisibility(View.GONE);
        mVoucherTipText.setVisibility(View.VISIBLE);
    }

    private IonReceiveCallback mOnReceiveCallback;

    public void setOnReceiveCallback(IonReceiveCallback mOnReceiveCallback) {
        this.mOnReceiveCallback = mOnReceiveCallback;
    }

    public interface IonReceiveCallback {
        void onReceiveSuccess(boolean receive);
        void onReceiveFail(int code);
    }
}
