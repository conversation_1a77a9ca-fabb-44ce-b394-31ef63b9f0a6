package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item;

import android.widget.CompoundButton;

import com.ximalaya.ting.android.host.fragment.other.web.WebClient;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugItemAdapter;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugType;

/**
 * <AUTHOR>
 */
public class InterceptResourceToggle extends BaseDebugItem {

    @Override
    int getIconResId() {
        return 0;
    }

    @Override
    boolean showToggle() {
        return true;
    }

    @Override
    boolean showArrow() {
        return false;
    }

    @Override
    public String getName() {
        return "拦截资源请求";
    }

    @Override
    public DebugType getCategory() {
        return DebugType.CATEGORY_WEBVIEW;
    }

    @Override
    public void bindView(DebugItemAdapter.BaseDebugItemViewHolder holder) {
        super.bindView(holder);
        holder.debugToggle.setChecked(WebClient.allowRequestInterceptor);
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        super.onCheckedChanged(buttonView, isChecked);
        WebClient.allowRequestInterceptor = isChecked;
    }
}
