package com.ximalaya.ting.android.main.setting.permissionSetting.permission

import com.ximalaya.ting.android.host.fragment.BaseFragment2

/**
 * Created by z<PERSON>kai<PERSON> on 2021/11/22.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 * @Description
 */
abstract class BaseBackgroundRunningPermission(fragment: BaseFragment2) :
    BasePermissionItem(fragment) {
    override fun getTitle() = "后台运行策略"

    override fun getSubtitle() = "由于系统会对后台运行的应用自动采取一些限制措施，为保证后台收听不受影响，请按照指引配置后台运行策略"

    override fun getPermissionStatus(): Bo<PERSON>an = false

    override fun getAlreadySetText(): String = "已允许"
}