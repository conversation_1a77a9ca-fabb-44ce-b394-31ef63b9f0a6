package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item;

import android.widget.CheckBox;
import android.widget.CompoundButton;

import com.ximalaya.aiagentconnect.sdk.connection.network.AgentCharlesUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugType;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;

/**
 * Created by le.xin on 2021/2/2.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class HttpsRecordToggle extends BaseDebugItem {
    @Override
    protected int getIconResId() {
        return 0;
    }

    @Override
    public boolean showToggle() {
        return true;
    }

    @Override
    boolean showArrow() {
        return false;
    }

    @Override
    public String getName() {
        return "https是否可以抓包";
    }

    @Override
    protected void bindToggle(CheckBox toggle) {
        super.bindToggle(toggle);

        toggle.setChecked(!BaseCall.ifHostVerifier);
    }

    @Override
    public DebugType getCategory() {
        return DebugType.CATEGORY_NETWORK;
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
            // 关闭ssl验证,放到这里只是为了线上有个可以设置的入口
            MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveBoolean(PreferenceConstantsInOpenSdk.KEY_OPEN_SSL_CHECK, false);
            BaseCall.getInstanse().setIfHostVerifier(false);
            AgentCharlesUtil.INSTANCE.setAllow(true);
        } else {
            MmkvCommonUtil.getInstance(ToolUtil.getCtx()).removeKey(PreferenceConstantsInOpenSdk.KEY_OPEN_SSL_CHECK);
            BaseCall.getInstanse().setIfHostVerifier(true);
            AgentCharlesUtil.INSTANCE.setAllow(false);
        }
    }
}
