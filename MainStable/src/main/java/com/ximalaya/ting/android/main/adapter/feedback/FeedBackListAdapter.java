package com.ximalaya.ting.android.main.adapter.feedback;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.adapter.HolderAdapter;
import com.ximalaya.ting.android.main.model.feedback.FeedBackCategoryModel;
import com.ximalaya.ting.android.main.model.feedback.FeedBackDetailModel;
import com.ximalaya.ting.android.main.stable.R;

import java.util.List;

/**
 * <AUTHOR> on 2017/4/24.
 */

public class FeedBackListAdapter extends HolderAdapter {

    private int mType;//问题条种类
    public final static int CHOOSE_TYPE = 1;

    public FeedBackListAdapter(Context context, List listData) {
        super(context, listData);
    }

    @Override
    public void onClick(View view, Object o, int position, BaseViewHolder holder) {
    }

    public void setType(int type) {
        this.mType = type;
    }

    @Override
    public int getConvertViewId() {
        return R.layout.main_item_feed_back_question1;
    }

    @Override
    public BaseViewHolder buildHolder(View convertView) {
        return new ViewHolder(convertView);
    }

    @Override
    public void bindViewDatas(BaseViewHolder holder, Object o, int position) {
        if (holder == null || !(holder instanceof ViewHolder) || o == null) {
            return;
        }
        ViewHolder viewHolder = (ViewHolder) holder;
        if (o instanceof FeedBackCategoryModel) {
            viewHolder.title.setText(((FeedBackCategoryModel) o).getTitle());
            if (!TextUtils.isEmpty(((FeedBackCategoryModel) o).getTips()) && mType != CHOOSE_TYPE) {
                viewHolder.tips.setText(((FeedBackCategoryModel) o).getTips());
            }
        } else if (o instanceof FeedBackDetailModel) {
            viewHolder.title.setText(((FeedBackDetailModel) o).getTitle());
        }
    }

    public static class ViewHolder extends BaseViewHolder {
        private TextView title;
        private View divider;
        private TextView tips;

        public ViewHolder(View convertView) {
            title = (TextView) convertView.findViewById(R.id.main_item_title);
            tips = (TextView) convertView.findViewById(R.id.main_tips);
            divider = convertView.findViewById(R.id.main_divider);
        }
    }
}
