package com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.item;

import android.widget.CompoundButton;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugItemAdapter;
import com.ximalaya.ting.android.main.fragment.myspace.other.setting.debug.DebugType;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

/**
 * <AUTHOR>
 * @date 2023/8/16 19:05
 */
public class BundleDownloadDelayMockToggle extends BaseDebugItem {

    @Override
    public String getName() {
        return "插件下载很慢模拟";
    }

    @Override
    public DebugType getCategory() {
        return DebugType.CATEGORY_APP_INFO;
    }

    @Override
    boolean showToggle() {
        return true;
    }

    @Override
    boolean showArrow() {
        return false;
    }

    private boolean isOpen() {
        return MMKVUtil.getInstance().getBoolean("BundleDownloadDelayMockToggle_0527", false);
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        super.onCheckedChanged(buttonView, isChecked);

        MMKVUtil.getInstance().saveBoolean("BundleDownloadDelayMockToggle_0527", isChecked);

        if (isChecked) {
            CustomToast.showToast("打开成功，重启生效");
        } else {
            CustomToast.showToast("关闭成功，重启生效");
        }
    }

    @Override
    public void bindView(DebugItemAdapter.BaseDebugItemViewHolder holder) {
        super.bindView(holder);
        holder.debugToggle.setChecked(isOpen());
    }

    @Override
    int getIconResId() {
        return 0;
    }
}
