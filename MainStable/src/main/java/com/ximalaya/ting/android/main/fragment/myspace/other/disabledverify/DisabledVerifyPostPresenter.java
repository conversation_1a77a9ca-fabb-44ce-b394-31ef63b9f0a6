package com.ximalaya.ting.android.main.fragment.myspace.other.disabledverify;

import android.net.Uri;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.View;

import com.ximalaya.ting.android.encryptservice.EncryptUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileProviderUtil;
import com.ximalaya.ting.android.framework.view.dialog.MyProgressDialog;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.loginservice.LoginEncryptUtil;
import com.ximalaya.ting.android.main.model.disabledverify.DisabledVerifyBean;
import com.ximalaya.ting.android.main.request.MainStableRequest;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;
import java.util.Map;

import androidx.annotation.Nullable;

/**
 * Created by zhifu.zhang on 2019-08-09.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber ***********
 * @desc
 */
public class DisabledVerifyPostPresenter implements IDisabledVerifyPost.IPresenter {

    private BaseFragment2 mFragment;
    private IDisabledVerifyPost.IView mView;
    private int mType;
    private long mUid;
    private SparseArray<String> mPhotoContainer = new SparseArray<>(3);//保存3张图片的绝对路径
    DisabledVerifyPostPresenter(BaseFragment2 mFragment, IDisabledVerifyPost.IView view,
                                int mType, long uid) {
        this.mFragment = mFragment;
        this.mType = mType;
        mView = view;
        mUid = uid;
    }

    @Override
    public void clickPhoto(View view, int position) {
        selectPhoto(position);
    }

    private void selectPhoto(final int position) {
        if (isFragmentValid()) {
            PicGetor.getPic(mFragment, 640, 640, new PicGetor.OnImgCallback() {
                @Override
                public void onGet(@Nullable Uri fileUri, String oriFilePath) {
                    String result = FileProviderUtil.getFilePathFromUri(fileUri);
                    if (TextUtils.isEmpty(result)) {
                        result = oriFilePath;
                    }
                    if (!TextUtils.isEmpty(result)) {
                        mPhotoContainer.put(position, result);
                        if (mView != null) {
                            mView.updatePhoto(position, result);
                        }
                    }
                }
            });
        }
    }

    private boolean isFragmentValid() {
        return mFragment != null && mFragment.canUpdateUi();
    }

    @Override
    public void clickPhotoDel(View view, int position) {
        mPhotoContainer.put(position, null);
        if (mView != null) {
            mView.updatePhoto(position, null);
        }
    }

    @Override
    public void onDestroy() {
        mView = null;
        mFragment = null;
    }

    private MyProgressDialog mLoadingDialog;

    @Override
    public void submitByManual(final String name, final String verifyCode,
                               final String disabledCode) {
        if (!isFragmentValid()){
            return;
        }
        if (mLoadingDialog == null){
            mLoadingDialog = new MyProgressDialog(mFragment.getActivity());
            mLoadingDialog.setTitle(com.ximalaya.ting.android.host.R.string.host_sending_please_waiting);
            mLoadingDialog.setMessage(mFragment.getResources().getString(com.ximalaya.ting.android.host.R.string.host_loading_data));
        }
        if (mLoadingDialog != null){
            mLoadingDialog.show();
        }
        MainStableRequest.getDisabledVerifyNonce(new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String nonce) {
                if (!TextUtils.isEmpty(nonce) && isFragmentValid()) {
                    try {
                        Map<String, String> params = new HashMap<>();
                        params.put("idCardNumber", encrypt(verifyCode));
                        params.put("handicappedCardNumber", encrypt(disabledCode));
                        params.put("name", name);
                        params.put("nonce", nonce);
                        String signature = LoginEncryptUtil.getInstance().createLoginParamSign(mFragment.getContext(),
                                        BaseConstants.ENVIRONMENT_ON_LINE != BaseConstants.environmentId, params);
                        params.put("signature", signature);
                        postDisabledVerify(params);
                    } catch (Exception e){
                        e.printStackTrace();
                    }


                }
            }

            @Override
            public void onError(int code, String message) {
                if (isFragmentValid()) {
                    CustomToast.showToast(message);
                    if (mLoadingDialog != null && mLoadingDialog.isShowing()){
                        mLoadingDialog.dismiss();
                    }
                }
            }
        });
    }

    private String encrypt(String inData) {
        return EncryptUtil.getInstance(mFragment.getContext()).rsaEncryptByPublicKey(
                EncryptUtil.getInstance(mFragment.getContext()).getPrivacyStr(mFragment.getContext(),
                        "disabled_verify"), inData);
    }

    private void postDisabledVerify(Map<String, String> params) {
        MainStableRequest.postDisabledVerify(params, new IDataCallBack<DisabledVerifyBean>() {
            @Override
            public void onSuccess(@Nullable DisabledVerifyBean object) {
                if (!isFragmentValid()){
                    return;
                }
                if (mLoadingDialog != null && mLoadingDialog.isShowing()){
                    mLoadingDialog.dismiss();
                }
                if (object != null && isFragmentValid()) {
                    mFragment.startFragment(DisabledVerifyResultFragment.newInstance(mUid,object));
                    if (mView != null && mView.getManagerFragment() != null){
                        mView.getManagerFragment().removeFragmentFromStacks(mFragment,false);
                    }
                }
            }

            @Override
            public void onError(int code, String message) {
                if (isFragmentValid()) {
                    if (mLoadingDialog != null && mLoadingDialog.isShowing()){
                        mLoadingDialog.dismiss();
                    }
                    if (code == 50){
                        UserInfoMannage.gotoLogin(mFragment.getContext());
                    } else {
                        CustomToast.showToast(message);
                    }
                }
            }
        });
    }

    @Override
    public void submitByPhoto() {
    }
}
