package com.ximalaya.ting.android.main.model.myspace;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

import org.json.JSONObject;

import java.io.Serializable;
import java.util.List;

/**
 * Created by 5Greatest on 2021.04.01
 *
 * <AUTHOR>
 * On 2021/4/1
 */
public class FamilyRecommendModel {
    public static final int TYPE_TRACK = 1;
    public static final int TYPE_ALBUM = 2;

    /**
     * 概览
     */
    public static class FamilyRecommendOverView implements Serializable {
        @SerializedName("simpleFamilyMemberInfos")
        public List<SimpleFamilyMemberInfo> simpleFamilyMemberInfo;     // 家人信息
        @SerializedName("defaultRecommendationPage")
        public RecommendationPage recommendationPage;                   // 家人为我推荐默认内容页, 默认返回20条

        public static FamilyRecommendOverView parse(String content) {
            if (null == content) {
                return null;
            }
            try {
                JSONObject object = new JSONObject(content);
                if (object.has("data")) {
                    return new Gson().fromJson(object.optJSONObject("data").toString(), FamilyRecommendOverView.class);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
    }

    /**
     * 家人信息
     */
    public static class SimpleFamilyMemberInfo implements Serializable {
        @SerializedName("memberUserId")
        public long memberUserId;               // 家人用户id
        @SerializedName("memberTag")
        public String memberTag;                // 家庭成员标签，若请求者是owner，对应家庭成员角色名，否则为用户昵称
    }

    /**
     * 推荐内容页
     */
    public static class RecommendationPage implements Serializable {
        @SerializedName("recommendations")
        public List<FamilyRecommendationItem> recommendations;          // 具体数据
        @SerializedName("hasMore")
        public boolean hasMore;                                         // 是否还有剩余数据
        @SerializedName("lastRecommendationId")
        public long lastRecommendationId;                               // 最后一条推荐数据id，用于帮助获取后续分页内容

        public static RecommendationPage parse(String content) {
            if (null == content) {
                return null;
            }
            try {
                JSONObject object = new JSONObject(content);
                if (object.has("data")) {
                    return new Gson().fromJson(object.optJSONObject("data").toString(), RecommendationPage.class);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
    }

    /**
     * 推荐具体数据
     */
    public static class FamilyRecommendationItem implements Serializable {
        @SerializedName("referrerUserId")
        public long referrerUserId;             // 推荐人用户id
        @SerializedName("resourceTypeId")
        public int resourceTypeId;              // 推荐资源类型，1-声音，2-专辑
        @SerializedName("resourceId")
        public long resourceId;                 // 推荐资源id，若资源类型是声音，对应声音id，若资源类型是专辑，对应专辑id
        @SerializedName("recommendationTime")
        public String recommendTime;            // 推荐时间，服务端按格式化后字符串给客户端
        @SerializedName("recommendationTips")
        public String recommendationTips;       // 推荐小贴士，对应 “xx 推荐给我”
        @SerializedName("resourceTitle")
        public String resourceTitle;            // 推荐资源标题，资源是专辑时对应专辑标题，资源是声音时对应声音标题
        @SerializedName("resourceSubTitle")
        public String resourceSubTitle;         // 推荐资源副标题，资源是专辑时对应最新更新声音标题，资源是声音时对应关联专辑标题
        @SerializedName("resourceCover")
        public String resourceCover;            // 被推荐资源封面，专辑和声音类资源都对应相关专辑封面
        @SerializedName("resourceTag")
        public String resourceTag;              // 被推荐资源标识
    }


}
