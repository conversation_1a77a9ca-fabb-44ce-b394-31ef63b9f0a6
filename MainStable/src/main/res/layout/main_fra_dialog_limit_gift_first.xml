<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_root_limit_first"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#B3000000">

    <LinearLayout
        android:id="@+id/main_ll_limit_first"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginHorizontal="50dp"
        android:background="@drawable/main_bg_rect_ffecea_radius_10"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingHorizontal="16dp">

        <TextView
            android:id="@+id/main_tv_limit_title_first"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            android:drawableLeft="@drawable/main_icon_limit_gift_zs_left"
            android:drawableRight="@drawable/main_icon_limit_gift_zs_right"
            android:drawablePadding="7dp"
            android:text="新人专属福利"
            android:textColor="#99660000"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/main_tv_limit_subtitle_first"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="每天收听即可获得礼包"
            android:textColor="#660000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/main_tv_limit_dsp_first"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:gravity="center"
            android:text="次日可以开启"
            android:textColor="#660000"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/main_iv_limit_task_first"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="14dp"/>

        <TextView
            android:id="@+id/main_tv_limit_confirm_first"
            android:layout_width="200dp"
            android:layout_height="40dp"
            android:layout_marginTop="18dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/host_bg_ff4444_radius_50"
            android:gravity="center"
            android:text="我知道了"
            android:textColor="#ffffff"
            android:textSize="14sp"
            android:textStyle="bold" />
    </LinearLayout>

    <ImageView
        android:id="@+id/main_iv_limit_close_first"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_alignTop="@id/main_ll_limit_first"
        android:layout_alignRight="@id/main_ll_limit_first"
        android:layout_marginTop="2dp"
        android:layout_marginRight="2dp"
        android:padding="10dp"
        android:src="@drawable/main_icon_limit_gift_close" />

    <com.ximalaya.ting.android.host.view.ShadowView
        android:id="@+id/main_shadow_limit_first"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

</RelativeLayout>