<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="24dp"
    android:paddingBottom="16dp"
    android:paddingHorizontal="16dp">

    <TextView
        android:id="@+id/main_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:layout_goneMarginTop="0dp"
        app:layout_constraintTop_toTopOf="parent"
        android:textColor="@color/host_color_textColor"
        android:textSize="17sp"
        android:fontFamily="sans-serif-light"
        android:textStyle="bold"
        tools:text="AI恋人真的来了吗，一起聊聊character.ai在我们工作中的应用"
        android:accessibilityTraversalBefore="@id/main_tv_update_num"
        />

    <TextView
        android:id="@+id/main_tv_update_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/main_tv_title"
        android:layout_marginTop="8dp"
        android:textSize="13sp"
        android:textColor="@color/host_color_ThinTextColor"
        android:accessibilityTraversalAfter="@id/main_tv_title"
        tools:text="2022-12更新"
        />

</androidx.constraintlayout.widget.ConstraintLayout>