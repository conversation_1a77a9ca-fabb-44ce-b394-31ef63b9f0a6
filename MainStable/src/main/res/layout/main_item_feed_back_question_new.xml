<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/host_bg_list_selector_white"
    >

    <ImageView
        android:id="@+id/main_iv_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="16dp"
        android:contentDescription="@string/main_content_description_more"
        android:src="@drawable/host_ic_more"
        android:layout_centerVertical="true"/>

    <TextView
        android:id="@+id/main_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10dp"
        android:layout_centerVertical="true"
        android:layout_toLeftOf="@+id/main_iv_arrow"
        android:ellipsize="end"
        android:gravity="center_vertical|right"
        android:maxEms="8"
        android:minWidth="1dp"
        android:singleLine="true"
        android:textColor="@color/main_color_f86442"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/main_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="17dp"
        android:layout_marginBottom="17dp"
        android:layout_toLeftOf="@+id/main_tips"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:layout_centerVertical="true"
        android:singleLine="true"
        android:textColor="@color/main_color_black"
        android:textSize="16sp" />


    <View
        android:id="@+id/main_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5px"
        android:layout_marginLeft="15dp"
        android:layout_below="@+id/main_item_title"
        android:background="@color/main_color_e8e8e8_2a2a2a" />


</RelativeLayout>