package com.ximalaya.ting.android.ad.model.thirdad.RewardVideo;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bytedance.sdk.openadsdk.TTDrawFeedAd;
import com.bytedance.sdk.openadsdk.TTNativeExpressAd;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.xmutil.Logger;

public class CsjDrawVideoAd extends AbstractRewardVideoAd<TTDrawFeedAd>{
    public CsjDrawVideoAd(@Nullable Advertis advertis, TTDrawFeedAd ttDrawFeedAd, String dspPositionId) {
        super(advertis, ttDrawFeedAd, dspPositionId);
    }

    @Override
    public int getType() {
        return AbstractThirdAd.THIRD_AD_CSJ_DRAW_VIDEO;
    }

    @Override
    public void showRewardVideoAd(@NonNull Activity activity, @NonNull RewardExtraParams extraParams, @NonNull IVideoAdStatueCallBack adStatueCallBack) {

    }

    @Override
    public double getRtbPrice() {
        try{
            if (getAdvertis() != null && !getAdvertis().isMobileRtb() && getAdvertis().getPrice() > 0) {
                Logger.i("------msg_rtb", " ------- csj 激励视频广告， 不是实时竞价物料，使用固价 0000 - getPrice --》 " + getAdvertis().getPrice());
                return getAdvertis().getPrice();
            }
            if (getAdData() != null && getAdData().getMediaExtraInfo() != null) {
                String price = getAdData().getMediaExtraInfo().get("price") + "";
                return  Double.parseDouble(price) / 100d;
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        return -1;
    }
}
