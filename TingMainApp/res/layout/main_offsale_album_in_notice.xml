<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="4dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="ResourceName">
、
    <com.ximalaya.ting.android.host.view.AlbumCoverLayoutView
        android:id="@+id/main_album_cover_layout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:albumCoverSize="68dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/main_offSale_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="@color/host_color_464652_dcdcdc"
        tools:text="我的世界观，爱因斯爱因斯爱因斯"
        app:layout_constrainedWidth="true"
        android:maxLines="2"
        android:ellipsize="end"
        android:layout_marginTop="6dp"
        android:includeFontPadding="false"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintEnd_toEndOf="@id/main_album_cover_layout"
        app:layout_constraintTop_toBottomOf="@+id/main_album_cover_layout"
        app:layout_constraintStart_toStartOf="@+id/main_album_cover_layout"
        />

</androidx.constraintlayout.widget.ConstraintLayout>