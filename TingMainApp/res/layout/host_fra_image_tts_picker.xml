<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_color_white">

    <GridView
        android:id="@+id/host_gridview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/host_layout_btn"
        android:layout_below="@+id/host_rl_top_bar"
        android:layout_marginStart="1dp"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="1dp"
        android:layout_marginBottom="1dp"
        android:horizontalSpacing="1dp"
        android:numColumns="4"
        android:scrollbars="none"
        android:scrollingCache="false"
        android:verticalSpacing="1dp" />

    <View
        android:id="@+id/host_bg_dim"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/host_layout_btn"
        android:layout_below="@+id/host_rl_top_bar"
        android:alpha="0.5"
        android:background="@color/host_black"
        android:visibility="gone" />

    <RelativeLayout
        android:id="@+id/host_layout_btn"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:visibility="gone"
        android:layout_alignParentBottom="true"
        android:background="@color/host_color_ffffff_121212">

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_alignParentTop="true"
            android:background="@color/host_color_d9dfe5_2a2a2a" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_toLeftOf="@+id/host_divider"
            android:gravity="center">

            <TextView
                android:id="@+id/host_btn_select_bucket"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:includeFontPadding="false"
                android:text="@string/host_image_picker_all_pic"
                android:textColor="@drawable/host_text_selector_gray"
                android:textSize="17sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignBaseline="@+id/host_btn_select_bucket"
                android:layout_marginLeft="2dp"
                android:layout_toRightOf="@+id/host_btn_select_bucket"
                android:src="@drawable/host_img_picker_bucket"
                android:translationY="-5dp" />
        </RelativeLayout>

        <View
            android:id="@+id/host_divider"
            android:layout_width="1px"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="10dp"
            android:background="@color/host_color_d9dfe5_2a2a2a" />

        <TextView
            android:id="@+id/host_btn_preview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@+id/host_divider"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="预览"
            android:textColor="@drawable/host_text_selector_orange"
            android:textSize="17sp" />
    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/host_rl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />
</RelativeLayout>