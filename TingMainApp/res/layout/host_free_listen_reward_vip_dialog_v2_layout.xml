<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/host_color_b3000000"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:background="@drawable/host_bg_ffffff_282828_radius_10_10_0_0"
        android:id="@+id/main_free_listen_time_dialog_container"
        android:layout_width="match_parent"
        android:layout_height="280dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        app:corner_radius="12dp">

        <ImageView
            android:id="@+id/main_free_listen_time_dialog_bg"
            android:scaleType="fitXY"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


        <ImageView
            android:id="@+id/main_free_listen_time_close_iv"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:padding="2dp"
            android:background="@drawable/host_ic_x_close_n_line_regular_20_grey" />


        <ImageView
            android:id="@+id/tv_free_listen_get_vip_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:background="@drawable/main_free_listen_get_vip_title_img"
            android:layout_marginTop="20dp"
            android:gravity="center" />

        <TextView
            android:id="@+id/tv_free_listen_get_vip_sub_title"
            android:layout_below="@+id/tv_free_listen_get_vip_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:lines="1"
            app:autoSizeTextType="uniform"
            app:autoSizeMaxTextSize="13sp"
            app:autoSizeMinTextSize="9sp"
            app:autoSizeStepGranularity="1sp"
            android:text="完成所有任务享会员权益，畅听VIP专享内容"
            android:textColor="@color/host_color_acacaf_66666b"
            android:textFontWeight="400"
            android:textSize="13sp"/>

        <!--        任务栏-->
        <LinearLayout
            android:id="@+id/free_listen_get_vip_cards_layout"
            android:layout_marginLeft="24dp"
            android:layout_marginRight="24dp"
            android:layout_marginTop="16dp"
            android:layout_below="@+id/tv_free_listen_get_vip_sub_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:id="@+id/container_task_1"
                android:background="@drawable/host_dialog_bg_gradient_ffe9dc_ffd4ba"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="140dp">
                <ImageView
                    android:id="@+id/get_vip_iv_tag"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="14dp"
                    android:background="@drawable/host_ic_get_vip_remove_ad"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:id="@+id/tv_title_task_1"
                    android:textSize="13sp"
                    android:textFontWeight="400"
                    android:text="做任务免广告"
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:layout_marginTop="8dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/get_vip_iv_tag"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:id="@+id/tv_value_task_1"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:text="30分钟"
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:layout_marginTop="2dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/tv_title_task_1"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>

                <TextView
                    tools:visibility="gone"
                    android:id="@+id/btn_task_1"
                    android:gravity="center"
                    android:layout_marginRight="8dp"
                    android:layout_marginLeft="8dp"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:text="去完成"
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:background="@drawable/host_bg_color_ffffff_radius_22"
                    android:layout_marginTop="10dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/tv_value_task_1"
                    android:layout_centerHorizontal="true"
                    android:layout_height="26dp"
                    android:layout_width="match_parent"/>
                <ImageView
                    android:id="@+id/iv_task_gou_1"
                    tools:visibility="visible"
                    android:visibility="gone"
                    android:background="@drawable/host_ic_get_vip_gou"
                    android:layout_below="@+id/tv_value_task_1"
                    android:layout_marginTop="10dp"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/container_task_2"
                android:layout_marginLeft="8dp"
                android:background="@drawable/host_dialog_bg_gradient_ffe9dc_ffd4ba"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="140dp">
                <ImageView
                    android:id="@+id/iv_tag_get_vip_task_2"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="14dp"
                    android:background="@drawable/host_ic_get_vip_remove_ad"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:id="@+id/tv_title_task_2"
                    android:textSize="13sp"
                    android:textFontWeight="400"
                    android:text="做任务免广告"
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:layout_marginTop="8dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/iv_tag_get_vip_task_2"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:id="@+id/tv_value_task_2"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:text="60分钟"
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:layout_marginTop="2dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/tv_title_task_2"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:id="@+id/btn_task_2"
                    android:gravity="center"
                    android:layout_marginRight="8dp"
                    android:layout_marginLeft="8dp"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:text="未开启"
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:background="@drawable/host_bg_color_ffffff_radius_22"
                    android:layout_marginTop="10dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/tv_value_task_2"
                    android:layout_centerHorizontal="true"
                    android:layout_height="26dp"
                    android:layout_width="match_parent"/>
                <ImageView
                    android:id="@+id/iv_task_gou_2"
                    android:visibility="gone"
                    android:background="@drawable/host_ic_get_vip_gou"
                    android:layout_below="@+id/tv_value_task_2"
                    android:layout_marginTop="10dp"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <View
                    android:background="@color/host_color_b3ffffff_b3282828"
                    android:id="@+id/top_mask_task2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/container_task_3"
                android:layout_marginLeft="8dp"
                android:background="@drawable/host_dialog_bg_gradient_ffe1d1_fff0e7"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="140dp">
                <ImageView
                    android:background="@drawable/host_ic_get_vip_card_mask"
                    android:layout_height="match_parent"
                    android:layout_width="match_parent"/>
                <ImageView
                    android:id="@+id/iv_tag_get_vip_task_3"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="13dp"
                    android:background="@drawable/host_ic_get_vip_tag"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:id="@+id/tv_title_task_3"
                    android:textSize="13sp"
                    android:textFontWeight="400"
                    android:text="做任务领会员"
                    android:layout_marginTop="8dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/iv_tag_get_vip_task_3"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:id="@+id/tv_value_task_3"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:text="一天畅听"
                    android:layout_marginTop="2dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/tv_title_task_3"
                    android:layout_centerHorizontal="true"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>
                <TextView
                    android:lines="1"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMaxTextSize="13sp"
                    app:autoSizeMinTextSize="9sp"
                    app:autoSizeStepGranularity="1sp"
                    android:id="@+id/btn_task_3"
                    android:gravity="center"
                    android:layout_marginRight="8dp"
                    android:layout_marginLeft="8dp"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    android:text="未开启"
                    android:background="@drawable/host_bg_color_ffffff_radius_22"
                    android:layout_marginTop="10dp"
                    android:textColor="#732F06"
                    android:layout_below="@+id/tv_value_task_3"
                    android:layout_centerHorizontal="true"
                    android:layout_height="26dp"
                    android:layout_width="match_parent"/>
                <View
                    android:background="@color/host_color_b3ffffff_b3282828"
                    android:id="@+id/top_mask_task3"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>
            </RelativeLayout>
        </LinearLayout>

        <!--        进度条-->
        <RelativeLayout
            android:id="@+id/rl_progress"
            android:layout_marginLeft="73dp"
            android:layout_marginRight="73dp"
            android:layout_marginTop="15dp"
            android:layout_below="@+id/free_listen_get_vip_cards_layout"
            android:layout_width="match_parent"
            android:layout_height="24dp">

            <View
                android:layout_centerVertical="true"
                android:background="@color/host_color_fc8859"
                android:id="@+id/task_1_2_line"
                android:layout_height="2dp"
                android:layout_marginLeft="2dp"
                android:layout_marginRight="2dp"
                android:layout_width="wrap_content"/>

            <TextView
                android:layout_centerVertical="true"
                android:id="@+id/tv_progress_1"
                android:gravity="center"
                android:text="1"
                android:textColor="#ffffff"
                android:background="@drawable/host_bg_fc8859_radius_10"
                android:textSize="11sp"
                android:layout_width="16dp"
                android:layout_height="16dp"/>

            <TextView
                android:id="@+id/tv_progress_2"
                android:gravity="center"
                android:text="2"
                android:textColor="#ffffff"
                android:layout_centerInParent="true"
                android:background="@drawable/host_bg_fc8859_radius_10"
                android:textSize="11sp"
                android:layout_width="16dp"
                android:layout_height="16dp"/>

            <TextView
                android:id="@+id/tv_progress_3"
                android:gravity="center"
                android:text="3"
                android:textColor="#ffffff"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:background="@drawable/host_bg_fc8859_radius_10"
                android:textSize="11sp"
                android:layout_width="16dp"
                android:layout_height="16dp"/>

            <View
                android:layout_toRightOf="@+id/tv_progress_1"
                android:layout_toLeftOf="@+id/tv_progress_2"
                android:background="@color/host_color_b3ffffff_b3282828"
                android:id="@+id/top_mask_progress_1"
                android:layout_marginRight="-16.5dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
            <View
                android:layout_toRightOf="@+id/tv_progress_2"
                android:background="@color/host_color_b3ffffff_b3282828"
                android:id="@+id/top_mask_progress_2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </RelativeLayout>

        <RelativeLayout
            android:visibility="gone"
            android:id="@+id/rl_free_listen_get_vip_success"
            android:layout_below="@+id/tv_free_listen_get_vip_sub_title"
            android:layout_width="match_parent"
            android:layout_height="220dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            app:corner_radius="12dp">

            <View
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginTop="32dp"
                android:background="@drawable/host_bg_freelisten_dialog_album_top" />

            <ImageView
                android:id="@+id/host_ic_ad_get_vip_dialog_bg"
                android:layout_width="match_parent"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:layout_marginTop="16dp"
                android:layout_height="100dp"
                android:scaleType="fitXY"
                android:background="@drawable/host_ic_ad_get_vip_dialog_bg" />

            <ImageView
                android:visibility="invisible"
                android:id="@+id/host_iv_free_listen_vip_tag"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginLeft="25dp"
                android:background="@drawable/host_iv_free_listen_vip_tag"
                android:scaleType="fitXY" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@+id/tv_free_listen_get_vip_title_task_3"
                android:layout_marginRight="5dp"
                android:layout_marginTop="43dp"
                android:background="@drawable/host_iv_free_listen_vip_tree_left" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toRightOf="@+id/tv_free_listen_get_vip_title_task_3"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="43dp"
                android:background="@drawable/host_iv_free_listen_vip_tree_right" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignBottom="@+id/host_ic_ad_get_vip_dialog_bg"
                android:id="@+id/img_bottom_sign"
                android:scaleType="fitXY"
                android:src="@drawable/host_ic_commercial_dialog_mask"
                android:tint="@color/host_color_ffffff_282828" />

            <TextView
                android:id="@+id/tv_free_listen_get_vip_title_task_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="40dp"
                android:gravity="center"
                android:text="恭喜获得 1天 VIP畅听权益"
                android:textColor="@color/host_color_73270f"
                android:textFontWeight="600"
                android:textSize="17sp"/>

            <TextView
                android:layout_below="@+id/tv_free_listen_get_vip_title_task_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="畅听海量会员专享内容"
                android:textColor="@color/host_color_c2806b"
                android:textFontWeight="400"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/main_free_listen_get_vip_know_tn"
                android:layout_width="295dp"
                android:layout_height="44dp"
                android:layout_below="@id/img_bottom_sign"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="14dp"
                android:background="@drawable/host_bg_free_listen_time_dialog_btn"
                android:gravity="center"
                android:text="我知道了"
                android:textColor="@color/host_color_ffffff"
                android:textFontWeight="500"
                android:textSize="16dp"
                android:textStyle="bold" />
        </RelativeLayout>

    </RelativeLayout>


</RelativeLayout>