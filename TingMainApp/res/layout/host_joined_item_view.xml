<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.ximalaya.ting.android.host.view.CommunityAvatarView
        android:id="@+id/host_community_avatar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:height="@dimen/host_size_65_dp"
        app:showPost="true"
        app:width="@dimen/host_size_65_dp"/>

    <TextView
        android:id="@+id/host_tv_name"
        android:layout_width="match_parent"
        android:layout_height="24dp"
        android:background="@drawable/host_rect_white_radius_0_0_6_6"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="65dp"
        android:singleLine="true"
        android:textColor="@color/host_color_333333_dcdcdc"
        android:textSize="12sp"
        tools:text="哈哈" />

</LinearLayout>
