<?xml version="1.0" encoding="utf-8"?>
<com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="275dp"
    android:layout_height="wrap_content"
    app:corner="all"
    app:corner_radius="10dp">

    <LinearLayout
        android:layout_width="275dp"
        android:layout_height="wrap_content"
        android:background="@color/host_color_ffffff_282828"
        android:orientation="vertical"
        android:paddingStart="23dp"
        android:paddingTop="28dp"
        android:paddingEnd="23dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/host_protocol_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="领取时长规则"
            android:textStyle="bold"
            android:textColor="@color/host_color_333333_dcdcdc"
            android:textFontWeight="600"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/main_rule_image"
            android:layout_marginTop="20dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            tools:src="@drawable/host_free_listen_rule"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/host_protocol_button"
            android:layout_width="194dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="19dp"
            android:background="@drawable/host_ad_action_btn_on_selected"
            android:gravity="center"
            android:text="我知道了"
            android:textStyle="bold"
            android:textColor="#ffFFFFFF"
            android:textFontWeight="600"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/host_protocol_note"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="12dp"
            android:text="若有任何疑问，您可以在喜马拉雅APP【账号-联系客服】中咨询在线客服"
            android:textColor="@color/host_color_666666_8d8d91"
            android:textFontWeight="400"
            android:textSize="11sp" />

    </LinearLayout>
</com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>