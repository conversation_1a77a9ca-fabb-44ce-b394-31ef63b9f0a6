<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/host_ad_root_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.ximalaya.ting.android.host.view.ad.PlayVideoUsSurfaceView
        android:id="@+id/host_ad_video"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/host_ad_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/host_ad_str"
        android:scaleType="fitXY" />

    <ImageView
        android:id="@+id/host_ad_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="12dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="24dp"
        android:visibility="gone"
        tools:src="@drawable/host_ad_tag_style_2"
        tools:visibility="visible" />

    <!--wifi已下载-->
    <TextView
        android:id="@+id/host_wifi_loaded_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:layout_marginLeft="4dp"
        android:layout_toRightOf="@id/host_ad_tag"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="24dp"
        android:shadowColor="#80000000"
        android:shadowRadius="3.0"
        android:shadowDx="0"
        android:shadowDy="0"
        android:text="@string/host_ad_download_with_wifi"
        android:textColor="#ffffff"
        android:textSize="13sp"
        android:visibility="gone"
        tools:visibility="visible" />
</RelativeLayout>

