<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tools:background="@color/host_half_transparent_black">

    <ImageView
        android:id="@+id/host_free_listen_time_close_iv"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginStart="90dp"
        android:src="@drawable/main_free_listen_time_close_img2" />

    <com.ximalaya.ting.android.host.view.CornerRelativeLayout
        android:id="@+id/host_free_listen_time_dialog_container"
        android:layout_width="221dp"
        android:layout_height="332dp"
        android:layout_marginTop="16dp"
        app:corner_radius="8dp">

        <ImageView
            android:id="@+id/host_free_listen_time_reward_iv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:src="@drawable/host_free_listen_time_packet_img" />

    </com.ximalaya.ting.android.host.view.CornerRelativeLayout>

    <TextView
        android:id="@+id/host_free_listen_time_countdown_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="3s后自动收下"
        android:textColor="#DCDCDC"
        android:layout_marginTop="16dp"
        android:textSize="12sp"
        />


</LinearLayout>