<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#333333"
    android:background="@drawable/host_reward_video_halo_effect">

    <!-- 顶部标题图片 -->
    <ImageView
        android:id="@+id/host_iv_top_image"
        android:layout_width="311dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:adjustViewBounds="true"
        android:layout_marginBottom="24dp"
        android:layout_above="@id/host_rl_content"
        android:scaleType="centerCrop" />

    <ImageView
        android:id="@+id/host_light_effect"
        android:layout_width="645dp"
        android:layout_height="637dp"
        android:scaleType="centerCrop"
        android:layout_centerInParent="true"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/host_reward_video_light_effect" />

    <com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout
        android:id="@+id/host_rl_content"
        android:layout_width="311dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        app:corner="all"
        app:corner_radius="16dp">

        <View
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/host_ll_content"
            android:background="#ffF7F9FC" />

        <View
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/host_ll_content"
            android:background="@drawable/host_video_click_dialog_bg" />

        <ImageView
            android:id="@+id/host_close_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:padding="5dp"
            android:src="@drawable/host_ic_x_close_n_line_regular_16_ad" />

        <LinearLayout
            android:id="@+id/host_ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 弹窗标题 -->
            <TextView
                android:id="@+id/host_tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="36dp"
                android:layout_marginStart="24dp"
                android:layout_marginEnd="24dp"
                android:gravity="center_horizontal"
                android:textSize="18sp"
                android:textFontWeight="600"
                tools:text="直接体验这款游戏15秒，即可获得奖励！" />

            <!-- 广告容器 -->
            <RelativeLayout
                android:id="@+id/host_material_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/host_bg_ffffff_00ffffff_radius_8"
                android:layout_marginStart="39dp"
                android:layout_marginEnd="39dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:padding="12dp">

                <!-- 素材图标 -->
                <com.ximalaya.ting.android.framework.view.image.RoundImageView
                    android:id="@+id/host_iv_material_icon"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    app:corner_radius="6dp"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop" />

                <!-- 文字内容 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="8dp"
                    android:layout_toEndOf="@id/host_iv_material_icon"
                    android:orientation="vertical">

                    <!-- 素材主标题 -->
                    <TextView
                        android:id="@+id/host_tv_material_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#ff444444"
                        android:textFontWeight="500"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        tools:text="游戏名称" />

                    <!-- 素材副标题 -->
                    <TextView
                        android:id="@+id/host_tv_material_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="#66000000"
                        android:textFontWeight="400"
                        android:textSize="12sp"
                        tools:text="游戏描述信息，最多显示两行文字内容" />

                </LinearLayout>

            </RelativeLayout>

            <!-- 按钮 -->
            <TextView
                android:id="@+id/host_tv_button"
                android:layout_width="match_parent"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp"
                android:layout_height="wrap_content"
                android:paddingVertical="11dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/host_ad_action_btn_on_selected"
                android:gravity="center"
                android:textColor="#ffFFFFFF"
                android:textFontWeight="500"
                android:textSize="16sp"
                tools:text="点击体验小游戏，提前领奖励" />

            <!-- 自动关闭倒计时 -->
            <TextView
                android:id="@+id/host_tv_countdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="24dp"
                android:textColor="#ffACACAF"
                android:textFontWeight="400"
                android:textSize="12sp"
                tools:text="5s后自动放弃" />

        </LinearLayout>
    </com.ximalaya.ting.android.host.view.RatioCornerRelativeLayout>
</RelativeLayout>
