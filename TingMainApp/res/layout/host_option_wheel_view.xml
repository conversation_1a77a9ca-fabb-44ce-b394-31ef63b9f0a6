<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <include
        layout="@layout/host_include_pickerview_topbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_pickerview_topbar_height" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingBottom="14dp"
        android:paddingTop="14dp"
        >
        <com.ximalaya.ting.android.host.view.datepicker.WheelView
            android:id="@+id/host_wheel_option_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:bg_color="@android:color/white"
            app:lineColor="#E8E8E8"
            app:lineHeight="1dp"
            app:lineMargin="0dp"
            />
    </LinearLayout>

</LinearLayout>