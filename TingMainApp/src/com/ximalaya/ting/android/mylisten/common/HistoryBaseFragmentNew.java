package com.ximalaya.ting.android.mylisten.common;

import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;

public abstract class HistoryBaseFragmentNew extends BaseFragment2 {
    protected IHistoryDataContextNew mHistoryDataContext;

    protected int currentPositionInTabList;

    public void setHistoryDataContext(IHistoryDataContextNew historyDataContext) {
        if (this.mHistoryDataContext != historyDataContext) {
            this.mHistoryDataContext = historyDataContext;
        }
    }

    public void onGetPageItem(int position, HistoryBaseFragmentNew fragment) {
        currentPositionInTabList = position;
    }

    public void clearHistory() {

    }

    protected CharSequence getSearchNoContent(String hintKeyword) {
        String defaultString = getString(R.string.host_search_no_content_format, hintKeyword);
        SpannableString content = new SpannableString(defaultString);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#666666"));
        AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(BaseUtil.dp2px(getContext(), 15));
        content.setSpan(colorSpan, 0, hintKeyword.length() + 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        content.setSpan(sizeSpan, 0, hintKeyword.length() + 2, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
        return content;
    }

    public void setNoDataContent(View titleView, String keyWord) {
        if (titleView == null) return;
        if (titleView instanceof TextView) {
            TextView textView = (TextView) titleView;
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
            textView.setLineSpacing(0, 1.2f);
            int space = BaseUtil.dp2px(mContext, 40);
            textView.setPadding(space, 0, space, 0);
            textView.setGravity(Gravity.CENTER);
            textView.setVisibility(View.VISIBLE);
            String hintKeyword = keyWord;
            if (hintKeyword.length() > 16) {
                hintKeyword = hintKeyword.substring(0, 16) + "...";
            }
            textView.setText(getSearchNoContent(hintKeyword));
        }
    }

    public void setEditTextFocused() {

    }
}
