package com.ximalaya.ting.android.read.bean.search;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * @CreateDate: 2022/9/28 11:13 上午
 * @Author: ypp
 * @Description:
 */
public class ReadSearchResBean {

    public List<ReadSearchBookItem> readSearchResList = new ArrayList<>();
    public int totalPage;

    public ReadSearchResBean parse(String jsonStr) {
        if (TextUtils.isEmpty(jsonStr)) {
            return this;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            JSONObject response = jsonObject.optJSONObject("response");
            if (response != null) {
                totalPage = response.optInt("totalPage");
                JSONArray docs = response.optJSONArray("docs");
                if (docs != null && docs.length() > 0) {
                    for (int i = 0; i < docs.length(); i++) {
                        readSearchResList.add(new ReadSearchBookItem().parse(docs.optJSONObject(i)));
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }


    /**
     * "author": "骑马钓鱼",
     * "cover_path": "http://qijiupload-1251952132.picsh.myqcloud.com/storage/uploads/cover/2020/04/23/wHBpNLOwJD.jpg?imageView2/1/w/300/h/400",
     * "description": "一家白天看着平平无奇的当铺，却有一个奇怪的规矩，凡是典当物品，或者购买绝当物品的客户，都会免费送上他们一卦。\n而最奇怪的是，这家当铺，一旦到了午夜，就会迎来“光怪陆离”的客人们……\n亲们可以关注下我的抖音号：骑马钓鱼本尊\n亲们可以关注下我的微博号：骑马钓鱼本尊\n有惊喜哦",
     * "ebookCategoryId": 5,
     * "ebookCategoryName": "悬疑灵异",
     * "iting": "iting://open?msg_type=94&bundle=rn_book&name=BookDetail&origin=search&bookId=47840",
     * "id": 47840,
     * "viewCount": 3173540,
     * "isFinish": 0,
     * "bookName": "天字第一當",
     * "resourceType": "ebook",
     * "updatedAt": "2022-09-27 18:59:34",
     * "abInfo": "{}",
     * "highLightTitle": "<em>天字</em>第一當",
     * "highLightTitle2": "一家白天看着平平无奇的当铺，却有一个奇怪的规矩，凡是典当物品，或者购买绝当物品的客户，都会免费送上他们一卦。\n而最奇怪的是，这家当铺，一旦到了午夜，就会迎来“光怪陆离”的客人们……\n亲们可以关注下我的抖音号：骑马钓鱼本尊\n亲们可以关注下我的微博号：骑马钓鱼本尊\n有惊喜哦"
     */
    public static class ReadSearchBookItem {
        public String author;
        public String cover_path;
        public String description;
        public long ebookCategoryId;
        public String ebookCategoryName;
        public String iting;
        public long id;
        public long viewCount;
        public int isFinish;
        public String readingNum;
        public String bookName;
        public String resourceType;
        public String updatedAt;
        public String abInfo;
        public String highLightTitle;
        public String highLightTitle2;

        public ReadSearchBookItem parse(JSONObject jsonObject) {
            if (jsonObject == null) {
                return this;
            }
            author = jsonObject.optString("author");
            cover_path = jsonObject.optString("cover_path");
            description = jsonObject.optString("description");
            ebookCategoryId = jsonObject.optLong("ebookCategoryId");
            ebookCategoryName = jsonObject.optString("ebookCategoryName");
            iting = jsonObject.optString("iting");
            id = jsonObject.optLong("id");
            viewCount = jsonObject.optLong("viewCount");
            isFinish = jsonObject.optInt("isFinish");
            readingNum = jsonObject.optString("readingNum");
            bookName = jsonObject.optString("bookName");
            resourceType = jsonObject.optString("resourceType");
            updatedAt = jsonObject.optString("updatedAt");
            abInfo = jsonObject.optString("abInfo");
            highLightTitle = jsonObject.optString("highLightTitle");
            highLightTitle2 = jsonObject.optString("highLightTitle2");
            return this;
        }
    }
}
