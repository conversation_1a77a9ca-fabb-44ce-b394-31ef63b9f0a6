package com.ximalaya.ting.android.read.dialog

import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.read.utils.ImageLoader
import com.ximalaya.ting.android.read.utils.TimeUtil
import com.ximalaya.ting.android.read.widgets.customDialog.BaseCustomDialog
import com.ximalaya.ting.android.read.widgets.customDialog.ViewHolder
import com.ximalaya.ting.android.xmtrace.XMTraceApi


class ExitReadPageDialog(
    private val isAddBookShelf: Boolean,
    val bookId: String,
    val callback: IExitCallBack?
) :
    BaseCustomDialog() {

    companion object {
        private const val KEY_SHOW_DATE = "read_exit_page_show_date"

        private const val KEY_EXIT_DIALOG_IMG_URL =
            "https://imagev2.xmcdn.com/storages/9f6c-audiofreehighqps/CD/4D/GMCoOScIlPn7AAA71AI9l8ur.webp"

        @JvmStatic
        fun preLoadImg() {
            ImageLoader.preload(KEY_EXIT_DIALOG_IMG_URL)
        }

        @JvmStatic
        fun canShowDialog(bookId: String): Boolean {
            val context = BaseApplication.getMyApplicationContext()
            val showDate = MmkvCommonUtil.getInstance(context).getString(KEY_SHOW_DATE)
            val curDate = TimeUtil.getCurrentDay()
            if (showDate != curDate) {
                MmkvCommonUtil.getInstance(context).saveString(KEY_SHOW_DATE, curDate)
                return true
            }
            return false
        }

        @JvmStatic
        fun newInstance(
            isAddBookShelf: Boolean,
            bookId: String,
            callback: IExitCallBack?
        ): ExitReadPageDialog {
            return ExitReadPageDialog(isAddBookShelf, bookId, callback)
        }
    }

    override fun intLayoutId(): Int {
        return R.layout.read_dialog_bookshelf
    }

    override fun convertView(holder: ViewHolder?, dialog: BaseCustomDialog?) {
        if (holder == null || dialog == null) {
            return
        }
        val tvLeft = holder.getView<TextView?>(R.id.tv_left)
        val tvRight = holder.getView<TextView?>(R.id.tv_right)
        val ivLogo = holder.getView<ImageView?>(R.id.iv_logo)

        ivLogo?.run {
            ImageLoader.load(
                this, KEY_EXIT_DIALOG_IMG_URL, -1
            )
        }
        tvLeft?.text = "退出"
        if (isAddBookShelf) {
            tvRight?.text = "知道了"
        } else {
            tvRight?.text = "加入书架"
        }

        holder.getView<ImageView?>(R.id.iv_cancel)?.setOnClickListener {
            callback?.exitReadPage()
            // 主站小说阅读页-退出引导-btn  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(55998)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("Item", "关闭")
                .put("bookId", bookId)
                .put("currPage", "read_page")
                .createTrace()
        }
        tvRight?.setOnClickListener {
            if (isAddBookShelf) {
                callback?.exitReadPage()
                // 主站小说阅读页-退出引导-btn  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(55998)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("Item", "知道了")
                    .put("bookId", bookId)
                    .put("currPage", "read_page")
                    .createTrace()
            } else {
                callback?.addToBookShelf()
                // 主站小说阅读页-退出引导-btn  弹框控件点击
                XMTraceApi.Trace()
                    .setMetaId(55998)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("Item", "加入书架")
                    .put("bookId", bookId)
                    .put("currPage", "read_page")
                    .createTrace()
            }
        }
        tvLeft?.setOnClickListener {
            callback?.exitReadPage()
            // 主站小说阅读页-退出引导-btn  弹框控件点击
            XMTraceApi.Trace()
                .setMetaId(55998)
                .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                .put("Item", "退出")
                .put("bookId", bookId)
                .put("currPage", "read_page")
                .createTrace()
        }

        // 主站小说阅读页-退出引导  弹框展示
        XMTraceApi.Trace()
            .setMetaId(55997)
            .setServiceId("dialogView") // 弹窗展示时上报
            .put("bookId", bookId)
            .put("currPage", "read_page")
            .createTrace()
    }

    interface IExitCallBack {
        // 加入书架
        fun addToBookShelf()

        // 去书城
        fun goToBookHome()

        // 退出界面
        fun exitReadPage()
    }
}