package com.ximalaya.ting.android.read.utils

import android.app.Activity
import android.graphics.Color
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity
import com.ximalaya.ting.android.read.widgets.immersionbar.BarHide
import com.ximalaya.ting.android.read.widgets.immersionbar.ImmersionBar


object ReadSettingResetUtil {

    @JvmStatic
    fun reset(activity: Activity?) {
        if (activity == null || !activity.checkActivity()) {
            return
        }

        try {
            // 退出后  恢复导航栏颜色  恢复状态栏
            if (BaseFragmentActivity.sIsDarkMode) {
                ImmersionBar.with(activity).hideBar(BarHide.FLAG_SHOW_BAR)
                    .navigationBarColorInt(Color.parseColor("#121212")).init()
            } else {
                ImmersionBar.with(activity).hideBar(BarHide.FLAG_SHOW_BAR)
                    .navigationBarColorInt(Color.WHITE).init()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        BrightnessUtils.setDefaultBrightness(activity)
    }
}