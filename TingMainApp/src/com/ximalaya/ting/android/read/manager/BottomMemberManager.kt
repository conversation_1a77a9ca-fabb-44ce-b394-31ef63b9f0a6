package com.ximalaya.ting.android.read.manager

import android.graphics.Color
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.R
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.model.payment.UniversalPayment
import com.ximalaya.ting.android.host.model.play.PlayingSoundInfo
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.opensdk.model.track.Track
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.read.bean.TingReadMemberConfig
import com.ximalaya.ting.android.read.common.ReaderConstants
import com.ximalaya.ting.android.read.manager.BottomResManager.bottomViewCallBack
import com.ximalaya.ting.android.read.manager.BottomResManager.mBannerAdShowing
import com.ximalaya.ting.android.read.manager.BottomResManager.mMemberShowing
import com.ximalaya.ting.android.read.utils.LogUtils
import com.ximalaya.ting.android.read.utils.TimeUtil
import com.ximalaya.ting.android.read.widgets.pageview.PageStyle
import com.ximalaya.ting.android.read.widgets.pageview.TingReadPage
import com.ximalaya.ting.android.read.widgets.pageview.TingReadSettingManager


object BottomMemberManager {

    private const val TAG = "BottomMemberManager"

    private const val KEY_TING_READ_MEMBER_SHOW_DATE = "key_ting_read_member_show_date"

    private const val KEY_TING_READ_MEMBER_SHOW_COUNT = "key_ting_read_member_show_count"

    private var mPlayingSoundInfo: PlayingSoundInfo? = null

    private var memberConfig: TingReadMemberConfig? = null

    private var mHiddenRun: Runnable? = null

    private var mLastPlayTrackId = -1L

    // 1：全部vip专辑显示   2：非免费声音显示（部分试听、付费声音）
    private var mShowEnterFlag = 1

    private fun initConfig() {
        mShowEnterFlag = ConfigureCenter.getInstance()
            .getInt(ReaderConstants.CONFIG_CENTER_QIJI, "ting_reader_member_album_show_range", 1)

        val json = ConfigureCenter.getInstance()
            .getJsonString(ReaderConstants.CONFIG_CENTER_QIJI, "ting_reader_member_banner", "")
        LogUtils.d(TAG, "config:$json")
        if (!TextUtils.isEmpty(json)) {
            memberConfig = GsonUtils.parseJson(json, TingReadMemberConfig::class.java)
        }
    }


    @JvmStatic
    fun setPlayingSoundInfo(info: PlayingSoundInfo?, rootView: ViewGroup?, page: TingReadPage?) {
        mPlayingSoundInfo = info
        if (memberConfig == null) {
            initConfig()
        }
        // 听的声音加载成功数据 主动触发显示逻辑
        checkShowMember(rootView, page)
    }

    @JvmStatic
    fun setPageStyle(pageStyle: PageStyle?, rootView: ViewGroup?) {
        initPageStyle(pageStyle, rootView)
    }

    @JvmStatic
    fun onPageChange(rootView: ViewGroup?, page: TingReadPage?) {
        checkShowMember(rootView, page)
    }

    private fun checkShowMember(rootView: ViewGroup?, page: TingReadPage?) {
        if (rootView == null || page == null) {
            LogUtils.d(TAG, "传入参数空,不显示小黄条")
            return
        }

        // 外部父容器控制显示隐藏
        if (TingReadPage.VALUE_STRING_NORMAL_PAGE != page.pageType) {
            rootView.visibility = View.GONE
            return
        } else {
            rootView.visibility = View.VISIBLE
        }

        if (memberConfig == null || mPlayingSoundInfo == null || mPlayingSoundInfo?.albumInfo == null || mPlayingSoundInfo?.trackInfo == null) {
            LogUtils.d(TAG, "无配置信息")
            hiddenMemberView(rootView)
            return
        }

        val isVipAlbum =
            mPlayingSoundInfo!!.albumInfo!!.isVipFree || mPlayingSoundInfo!!.albumInfo!!.vipFreeType == 1
        val isChildVipAlbum = mPlayingSoundInfo!!.isChildVipAlbum

        // 1：全部vip专辑显示   2：非免费声音显示（部分试听、付费声音）
        if (mShowEnterFlag == 2) {
            if (bottomViewCallBack == null || !bottomViewCallBack!!.isNeedPaySound(mPlayingSoundInfo)) {
                LogUtils.d(TAG, "声音可以畅听,不显示小黄条")
                hiddenMemberView(rootView)
                return
            }
        } else {
            if (!isVipAlbum && !isChildVipAlbum) {
                if (bottomViewCallBack == null || !bottomViewCallBack!!.isNeedPaySound(
                        mPlayingSoundInfo
                    )
                ) {
                    LogUtils.d(TAG, "非VIP专辑,不需要购买的声音,不显示小黄条")
                    hiddenMemberView(rootView)
                    return
                }
            }
        }

        if (isVipAlbum) {
            if (UserInfoMannage.isVipUser()) {
                LogUtils.d(TAG, "vip用户不显示小黄条")
                hiddenMemberView(rootView)
                return
            }
        } else if (isChildVipAlbum) {
            if (UserInfoMannage.isChildVipUser()) {
                LogUtils.d(TAG, "儿童vip用户不显示小黄条")
                hiddenMemberView(rootView)
                return
            }
        }

        if (mLastPlayTrackId != -1L && mLastPlayTrackId == mPlayingSoundInfo!!.trackInfo!!.trackId) {
            LogUtils.d(TAG, "同一个声音只显示一次,当次不显示小黄条")
            return
        }

        val context = BaseApplication.mAppInstance

        // 显示后再次触发显示 次数+1
        if (mMemberShowing) {
            var showCount =
                MmkvCommonUtil.getInstance(context).getInt(KEY_TING_READ_MEMBER_SHOW_COUNT, 0)
            showCount++
            MmkvCommonUtil.getInstance(context).saveInt(KEY_TING_READ_MEMBER_SHOW_COUNT, showCount)
            return
        }

        val showDate =
            MmkvCommonUtil.getInstance(context).getString(KEY_TING_READ_MEMBER_SHOW_DATE, "")
        var showCount =
            MmkvCommonUtil.getInstance(context).getInt(KEY_TING_READ_MEMBER_SHOW_COUNT, 0)
        val curDate = TimeUtil.getCurrentDay()

        if (curDate != showDate) {
            MmkvCommonUtil.getInstance(context).saveString(KEY_TING_READ_MEMBER_SHOW_DATE, curDate)
            MmkvCommonUtil.getInstance(context).saveInt(KEY_TING_READ_MEMBER_SHOW_COUNT, 0)
            showCount = 0
        }

        if (showCount >= memberConfig!!.displayTimes) {
            LogUtils.d(
                TAG, "小黄条达到最大次数 当前:$showCount 最大:${memberConfig!!.displayTimes}"
            )
            return
        }

        val childView = LayoutInflater.from(context)
            .inflate(R.layout.host_ting_read_bottom_member_layout, rootView, false)

        val tvTitle = childView.findViewById<TextView?>(R.id.read_ting_tv_title_member)
        val tvPayment = childView.findViewById<TextView?>(R.id.read_ting_tv_payment_member)

        tvTitle?.text = "本节目为会员畅听内容"
        tvPayment?.text = "开通会员"

        childView?.setOnClickListener {
            bottomViewCallBack?.onMemberViewClick()

            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(context)
                return@setOnClickListener
            }

            try {
                val mainActionRouter =
                    Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)
                if (null != mainActionRouter) {
                    val track = mPlayingSoundInfo?.trackInfo2TrackM()
                    if (track !is Track) {
                        return@setOnClickListener
                    }
                    // 允许播放  不依赖声音信息
                    val isIndependent =
                        bottomViewCallBack == null || !bottomViewCallBack!!.isNeedPaySound(
                            mPlayingSoundInfo
                        )
                    val functionAction = mainActionRouter.functionAction
                    functionAction?.requestAndShowUniversalPaymentActionsDialog(
                        bottomViewCallBack?.getFragment(),
                        track,
                        UniversalPayment.SOURCE_AFTER_SAMPLE,
                        isIndependent, true
                    )
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        rootView.removeAllViews()
        try {
            rootView.addView(childView)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        showCount++
        MmkvCommonUtil.getInstance(context).saveInt(KEY_TING_READ_MEMBER_SHOW_COUNT, showCount)
        // 广告改为不显示  不然隐藏会员后 高度会出现空白一阵的情况
        mBannerAdShowing = false
        mMemberShowing = true
        mLastPlayTrackId = mPlayingSoundInfo!!.trackInfo!!.trackId

        initPageStyle(TingReadSettingManager.getInstance().pageStyle, rootView)

        bottomViewCallBack?.showBottomView()
        bottomViewCallBack?.onMemberViewShow()

        var delayTime = memberConfig!!.duration
        if (delayTime <= 10) {
            delayTime = 180
        }

        mHiddenRun = Runnable {
            mHiddenRun = null
            LogUtils.d(TAG, "${delayTime}秒延时到了,主动隐藏小黄条")
            hiddenMemberView(rootView)
        }
        HandlerManager.postOnUIThreadDelay(mHiddenRun, delayTime * 1000L)
        LogUtils.d(TAG, "延时${delayTime}秒隐藏小黄条")
    }

    private fun initPageStyle(pageStyle: PageStyle?, rootView: ViewGroup?) {
        if (pageStyle == null || rootView == null) {
            return
        }

        if (!mMemberShowing) {
            return
        }

        val leftContainer =
            rootView.findViewById<ViewGroup?>(R.id.read_ll_member_left_container) ?: return
        val tvTitle = rootView.findViewById<TextView?>(R.id.read_ting_tv_title_member)
        val tvSubTitle1 = rootView.findViewById<TextView?>(R.id.read_ting_tv_sub_title_1)
        val tvSubTitle2 = rootView.findViewById<TextView?>(R.id.read_ting_tv_sub_title_2)
        val tvSubTitle3 = rootView.findViewById<TextView?>(R.id.read_ting_tv_sub_title_3)
        val tvPoint1 = rootView.findViewById<View?>(R.id.read_ting_tv_sub_title_point_1)
        val tvPoint2 = rootView.findViewById<View?>(R.id.read_ting_tv_sub_title_point_2)
        val ivPayment = rootView.findViewById<ImageView?>(R.id.read_ting_iv_payment_member_open)
        val tvPayment = rootView.findViewById<TextView?>(R.id.read_ting_tv_payment_member)

        if (TingReadSettingManager.getInstance().isDarkModel) {
            leftContainer.setBackgroundResource(R.drawable.host_bg_ting_read_member_left_shape_night)
            tvPoint1.setBackgroundResource(R.drawable.host_bg_ting_read_member_point_shape_night)
            tvPoint2.setBackgroundResource(R.drawable.host_bg_ting_read_member_point_shape_night)
            ivPayment.setBackgroundResource(R.drawable.host_ic_ting_read_member_open_night)

            tvTitle?.setTextColor(Color.parseColor("#FED9C7"))
            tvSubTitle1?.setTextColor(Color.parseColor("#FED9C7"))
            tvSubTitle2?.setTextColor(Color.parseColor("#FED9C7"))
            tvSubTitle3?.setTextColor(Color.parseColor("#FED9C7"))
            tvPayment?.setTextColor(Color.parseColor("#FFB197"))
        } else {
            leftContainer.setBackgroundResource(R.drawable.host_bg_ting_read_member_left_shape)
            tvPoint1.setBackgroundResource(R.drawable.host_bg_ting_read_member_point_shape)
            tvPoint2.setBackgroundResource(R.drawable.host_bg_ting_read_member_point_shape)
            ivPayment.setBackgroundResource(R.drawable.host_ic_ting_read_member_open)

            tvTitle?.setTextColor(Color.parseColor("#461717"))
            tvSubTitle1?.setTextColor(Color.parseColor("#461717"))
            tvSubTitle2?.setTextColor(Color.parseColor("#461717"))
            tvSubTitle3?.setTextColor(Color.parseColor("#461717"))
            tvPayment?.setTextColor(Color.parseColor("#461717"))
        }
    }

    private fun hiddenMemberView(rootView: ViewGroup?) {
        val memberView = rootView?.findViewById<ViewGroup?>(R.id.read_ting_member_root_view)
        if (memberView != null) {
            mMemberShowing = false
            rootView.removeView(memberView)
            bottomViewCallBack?.closeBottomView()
        }
    }

    @JvmStatic
    fun onDestroy() {
        mPlayingSoundInfo = null
        mMemberShowing = false
        mLastPlayTrackId = -1
        HandlerManager.removeCallbacks(mHiddenRun)
        mHiddenRun = null
    }

}