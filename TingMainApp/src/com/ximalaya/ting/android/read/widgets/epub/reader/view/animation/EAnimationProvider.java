package com.ximalaya.ting.android.read.widgets.epub.reader.view.animation;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PointF;
import android.widget.Scroller;

/**
 * <AUTHOR>
 * @date 2018/12/4.
 * Description：
 */
public abstract class EAnimationProvider {

    public enum Direction {
        NONE(true), NEXT(true), PRE(true), UP(false), DOWN(false);

        public final boolean isHorizontal;

        Direction(boolean isHorizontal) {
            this.isHorizontal = isHorizontal;
        }
    }

    protected Bitmap mCurPageBitmap;
    protected Bitmap mNextPageBitmap;
    protected float myStartX;
    protected float myStartY;
    protected int myEndX;
    protected int myEndY;
    protected Direction myDirection;

    protected int mScreenWidth;
    protected int mScreenHeight;

    protected PointF mTouch = new PointF(); // 拖拽点
    private Direction direction = Direction.NONE;
    private boolean isCancel = false;

    public EAnimationProvider(int width, int height) {
        mCurPageBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        mNextPageBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);

        this.mScreenWidth = width;
        this.mScreenHeight = height;
    }

    //绘制滑动页面
    public abstract void drawMove(Canvas canvas);

    //绘制不滑动页面
    public abstract void drawStatic(Canvas canvas);

    //设置开始拖拽点
    public void setStartPoint(float startX, float startY) {
        myStartX = startX;
        myStartY = startY;
    }

    //设置拖拽点
    public void setTouchPoint(float startX, float startY) {
        mTouch.x = startX;
        mTouch.y = startY;
    }

    //设置方向
    public void setDirection(Direction direction) {
        this.direction = direction;
    }

    public Direction getDirection() {
        return direction;
    }

    public void setCancel(boolean isCancel) {
        this.isCancel = isCancel;
    }

    public abstract void startAnimation(Scroller scroller);

    /**
     * 转换页面，在显示下一章的时候，必须首先调用此方法
     */
    public void changePage() {
        Bitmap bitmap = mCurPageBitmap;
        mCurPageBitmap = mNextPageBitmap;
        mNextPageBitmap = bitmap;
    }

    public Bitmap getNextBitmap() {
        return mNextPageBitmap;
    }

    public Bitmap getBgBitmap() {
        return mNextPageBitmap;
    }

    public boolean getCancel() {
        return isCancel;
    }
}
