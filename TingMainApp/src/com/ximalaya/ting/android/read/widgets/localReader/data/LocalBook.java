package com.ximalaya.ting.android.read.widgets.localReader.data;

import com.ximalaya.ting.android.read.common.BookType;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/3/10 4:38 PM
 * @description
 */
public class LocalBook {

    public long bookId;
    public @BookType.BookReadType
    int type;
    //路径
    public String bookUrl;
    //文件名字
    public String originName;
    //书籍名称
    public String bookName;
    //封面
    public String coverUrl;
    //作者
    public String authorName;
    //字符集 （TXT）
    public String charset = null;
    //解析规则 （TXT）
    public String tocUrl = "";
    //简介
    public String intro;
    //阅读历史章节序号
    public int lastChapterId;


    public LocalBook() {
    }

    /**
     * 获取文件字符格式
     */
    public Charset fileCharset() {
        return charset != null ? Charset.forName(charset) : StandardCharsets.UTF_8;
    }

    /**
     * 是否切割长章节
     */
    public boolean getSplitLongChapter() {
        return true;
    }

}
