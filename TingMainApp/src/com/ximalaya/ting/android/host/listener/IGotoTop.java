package com.ximalaya.ting.android.host.listener;

import android.view.View;

/**
 * <AUTHOR> on 2017/7/12.
 */

public interface IGotoTop {
    void reset();
    void addOnClickListener(IGotoTopBtnClickListener listener);
    void removeOnClickListener(IGotoTopBtnClickListener listener);

    void addVisibleListener(IGotoTopBtnClickVisibleListener listener);

    void removeVisibleListener(IGotoTopBtnClickVisibleListener listener);
    void setState(boolean show);

    interface IGotoTopBtnClickListener {
        void onClick(View btn);
    }

    interface IGotoTopBtnClickVisibleListener {
        void isVisible(boolean visible);
    }
}
