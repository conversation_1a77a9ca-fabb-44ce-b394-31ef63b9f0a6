package com.ximalaya.ting.android.host.video;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.util.SparseBooleanArray;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArraySet;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IOnRequestAllowMobileNetworkListener;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.VideoInfoModel;
import com.ximalaya.ting.android.host.util.server.NetworkUtils;
import com.ximalaya.ting.android.host.util.server.PlayTools;
import com.ximalaya.ting.android.lifecycle.annotation.XmFragmentLifecycleCallback;
import com.ximalaya.ting.android.opensdk.httputil.util.freeflow.FreeFlowServiceUtil;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.player.video.listener.IXmVideoPlayStatusListener;
import com.ximalaya.ting.android.player.video.listener.IXmVideoView;
import com.ximalaya.ting.android.routeservice.service.freeflow.IFreeFlowService;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

//import androidx.collection.ArraySet;

/**
 * <AUTHOR>
 * @date 2019/3/14 19:57
 * 一个通用的视频播放管理类
 */
public class VideoPlayManager implements IMainFunctionAction.IVideoPlayManager {

    private static final String TAG = VideoPlayManager.class.getSimpleName();

    /**
     * 半小时，单位：毫秒
     */
    public static final long HALF_HOUR = 1800 * 1000;
    public static volatile VideoPlayManager manager;
    public static boolean hasVideoBundleInstallSuccess = false; ///默认为false,用前请确保checkVideoBundleInstall已调用；
    public static final int NO_PLAYING_VIDEO = -1; ///当前没有播放的视频；
    public static boolean openVoice = false;
    public IXmVideoView mVideoPlayer = null;
    public IVideoFunctionAction functionAction = null;
    public static boolean isAllowMobilePlayVideo = false;
    private Context mContext;
    private int videoLayoutWidth;
    private int videoLayoutHeigh;
    private int currentPlayPosition = NO_PLAYING_VIDEO;
    private int mLastVideoPlayPosition = NO_PLAYING_VIDEO;
    private long mLastRequestTime;

    private CopyOnWriteArrayList<IPlayPositionChangedListener> mPositionChangedListeners = new CopyOnWriteArrayList<>();

    private SparseBooleanArray mPosition2AutoPlayArray = new SparseBooleanArray();


    private ArraySet<IXmVideoPlayStatusListener> videoPlayStatusListeners = new ArraySet<>();
    private ArraySet<IOnRequestAllowMobileNetworkListener> requestAllowMobileNetworkListeners = new ArraySet<>();

    public VideoPlayManager(BaseFragment baseFragment) {
        if (baseFragment == null) {
            return;
        }
        baseFragment.setFragmentLifecycleCallback(new XmFragmentLifecycleCallback() {
            @Override
            public void onCreate() {

            }

            @Override
            public void onStart() {

            }

            @Override
            public void onResume() {

            }

            @Override
            public void onPause() {

            }

            @Override
            public void onStop() {

            }

            @Override
            public void onDestroy() {
                if (baseFragment != null) {
                    baseFragment.setFragmentLifecycleCallback(null);
                }
                stopAttachedVideoPlay();
            }

            @Override
            public void onMyResume() {

            }
        });
    }

    public VideoPlayManager(Activity myActivity) {
        if (myActivity != null) {
            myActivity.getApplication().registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
                @Override
                public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {

                }

                @Override
                public void onActivityStarted(@NonNull Activity activity) {

                }

                @Override
                public void onActivityResumed(@NonNull Activity activity) {

                }

                @Override
                public void onActivityPaused(@NonNull Activity activity) {

                }

                @Override
                public void onActivityStopped(@NonNull Activity activity) {

                }

                @Override
                public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {

                }

                @Override
                public void onActivityDestroyed(@NonNull Activity activity) {
                    if (activity == myActivity) {
                        myActivity.getApplication().unregisterActivityLifecycleCallbacks(this);
                    }
                    stopAttachedVideoPlay();
                }
            });}
    }

    public Context getContext() {
        return mContext == null ? MainApplication.getMyApplicationContext() : mContext;
    }

    public IXmVideoView getVideoPlayer() {
        if (mVideoPlayer == null) {
            try {
                functionAction = Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction();
                mVideoPlayer = functionAction.newXmVideoView(getContext());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (mVideoPlayer == null) {
            Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.videoBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        try {
                            functionAction = Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction();
                            mVideoPlayer = functionAction.newXmVideoView(getContext());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                }
            });

            return mVideoPlayer;
        }

        return mVideoPlayer;
    }


    public void setVideoVolume(float left, float right) {
        if (getVideoPlayer() != null) {
            getVideoPlayer().setVolume(left, right);
        }
    }

    public void setVideoPlayerPath(String url) {
        if (getVideoPlayer() == null) {
            return;
        }

        if (!openVoice) {
            setVideoVolume(0f, 0f);
        }

        getVideoPlayer().setVideoPath(url);
    }

    public boolean isVideoPlaying() {
        if (getVideoPlayer() != null) {
            return getVideoPlayer().isPlaying();
        }

        return false;
    }

    public void startPlay() {
        if (!openVoice) {
            setVideoVolume(0f, 0f);
        }
        startListenFocus();

        if (getVideoPlayer() != null) {
            getVideoPlayer().start();
        }
    }

    public void startPlayNoSettingVolume() {
        if (getVideoPlayer() != null) {
            setVideoVolume(1f, 1f);
            getVideoPlayer().start();
//            setAllowUseMobileNetwork(true);
        }
    }

    private void startListenFocus() {
        if (getVideoPlayer() == null) {
            return;
        }
        getVideoPlayer().setHandleAudioFocus(openVoice);
    }

    public void openVoice(boolean open) {
        openVoice = open;
        if (open) {
            PlayTools.pause(mContext, PauseReason.Business.VideoPlayManager_OpenVoice);
        }
        startListenFocus();
    }

    public void pausePlay() {
        if (getVideoPlayer() != null) {
            getVideoPlayer().pause();
        }
    }

    public void releaseVideoPlayer() {
        if (getVideoPlayer() != null) {
            getVideoPlayer().release(true);
        }
    }

    public void addVideoPlayStatusListener(IXmVideoPlayStatusListener listener) {
        if (listener != null && getVideoPlayer() != null) {
            getVideoPlayer().addXmVideoStatusListener(listener);
            videoPlayStatusListeners.add(listener);
        }
    }

    public void removeAllVideoPlayStatusListeners() {
        Iterator<IXmVideoPlayStatusListener> iterator = videoPlayStatusListeners.iterator();
        while (iterator.hasNext()) {
            IXmVideoPlayStatusListener videoPlayStatusListener = iterator.next();
            if (videoPlayStatusListener != null) {
                if (mVideoPlayer != null) {
                    mVideoPlayer.removeXmVideoStatusListener(videoPlayStatusListener);
                }
                iterator.remove();
            }
        }
    }

    public void addPlayPositionChangedListener(IPlayPositionChangedListener l) {
        if (l != null) {
            mPositionChangedListeners.add(l);
        }
    }

    public void removePlayPositionChangedListener(IPlayPositionChangedListener l) {
        if (l != null) {
            mPositionChangedListeners.remove(l);
        }
    }

    public void dispatchPlayPositionChangedEvent(int newPosition) {
        Logger.d(TAG, "dispatchPlayPositionChangedEvent listener size: " + mPositionChangedListeners.size());
        for (IPlayPositionChangedListener l : mPositionChangedListeners) {
            l.onPlayPositionChanged(newPosition);
        }
    }

    public int getVideoLayoutWidth() {
        videoLayoutWidth = BaseUtil.getScreenWidth(getContext()) - BaseUtil.dp2px(getContext(), 30);
        videoLayoutHeigh = (int) (9 * videoLayoutWidth / 16f);  //长宽比为 16 ：9
        return videoLayoutWidth;
    }

    public int getVideoLayoutHeight() {
        videoLayoutWidth = BaseUtil.getScreenWidth(getContext()) - BaseUtil.dp2px(getContext(), 30);
        videoLayoutHeigh = (int) (9 * videoLayoutWidth / 16f);  //长宽比为 16 ：9
        return videoLayoutHeigh;
    }


    public void removeVideoParent() {
        if (getVideoPlayer() == null) {
            return;
        }

        ViewGroup viewGroup = (ViewGroup) ((View) getVideoPlayer()).getParent();
        if (viewGroup != null) {
            getVideoPlayer().release(true);
            viewGroup.removeView((View) getVideoPlayer());
        }
    }

    public void setVideoPlayerFrameLayoutParams() {
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(getVideoLayoutWidth(), getVideoLayoutHeight());
        params.gravity = Gravity.CENTER;
        if (getVideoPlayer() != null && getVideoPlayer() instanceof View) {
            ((View) getVideoPlayer()).setLayoutParams(params);
        }
    }

    public LinearLayout.LayoutParams getLinearLayoutParams() {
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(getVideoLayoutWidth(), getVideoLayoutHeight());
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        return layoutParams;
    }

    public FrameLayout.LayoutParams getFrameLayoutParams() {
        FrameLayout.LayoutParams frameLayoutParams = new FrameLayout.LayoutParams(getVideoLayoutWidth(), getVideoLayoutHeight());
        frameLayoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        return frameLayoutParams;
    }

    public int getLastVideoPlayPosition() {
        return mLastVideoPlayPosition;
    }

    public void setLastVideoPlayPosition(int mLastVideoPlayPosition) {
        this.mLastVideoPlayPosition = mLastVideoPlayPosition;
    }


    public void resetAll() {
        this.currentPlayPosition = NO_PLAYING_VIDEO;
        this.mLastVideoPlayPosition = NO_PLAYING_VIDEO;
        this.mLastRequestTime = 0;
        removeAllVideoPlayStatusListeners();
        removeAllRequestAllowMobileNetworkListener();
        releaseVideoPlayer();
    }

    public void resetByVideoDetail() {
        removeAllVideoPlayStatusListeners();
        removeAllRequestAllowMobileNetworkListener();
        releaseVideoPlayer();
    }

    public void setCurrentPlayPosition(int currentPlayPosition) {
        this.currentPlayPosition = currentPlayPosition;
    }

    public int getCurrentPlayPosition() {
        return currentPlayPosition;
    }


    public long getLastRequestTime() {
        return mLastRequestTime;
    }

    public void setLastRequestTime() {
        this.mLastRequestTime = System.currentTimeMillis();
    }

    public void addRequestAllowMobileNetworkListener() {
        removeAllRequestAllowMobileNetworkListener();
        requestAllowMobileNetworkListeners.add(onRequestAllowMobileNetworkListener);
        if (functionAction != null) {
            functionAction.addOnRequestAllowMobileNetworkListener(onRequestAllowMobileNetworkListener);
        }
    }

    public void removeAllRequestAllowMobileNetworkListener() {
        Iterator<IOnRequestAllowMobileNetworkListener> iterator = requestAllowMobileNetworkListeners.iterator();
        while (iterator.hasNext()) {
            IOnRequestAllowMobileNetworkListener requestAllowMobileNetworkListener = iterator.next();
            if (requestAllowMobileNetworkListener != null) {
                if (functionAction != null) {
                    functionAction.removeOnRequestAllowMobileNetworkListener(requestAllowMobileNetworkListener);
                }
                iterator.remove();
            }
        }
    }

    public boolean isNetWorkConfirmDialogShow() {
        if (mShowDialog != null && mShowDialog.isShowing()) {
            return true;
        }

        return false;
    }

    private IOnRequestAllowMobileNetworkListener onRequestAllowMobileNetworkListener = new IOnRequestAllowMobileNetworkListener() {
        @Override
        public void onRequestAllowMobileNetwork() {
            HandlerManager.obtainMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    if (NetworkUtils.isNetworkTypeNeedConfirm()) {
                        if (isNetWorkConfirmDialogShow()) {
                            return;
                        }

                        if (getVideoPlayer() != null) {
                            getVideoPlayer().pause();
                        }
                    }
                }
            });
        }
    };


    public static void checkVideoBundleInstall() {

        if (!hasVideoBundleInstallSuccess) {

            Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
                @Override
                public void onInstallSuccess(BundleModel bundleModel) {
                    if (Configure.videoBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        hasVideoBundleInstallSuccess = true;
                    }
                }

                @Override
                public void onLocalInstallError(Throwable t, BundleModel bundleModel) {
                    if (Configure.videoBundleModel.bundleName.equals(bundleModel.bundleName)) {
                        hasVideoBundleInstallSuccess = false;
                    }
                }

                @Override
                public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {
                }
            });
        }
    }


    public static boolean forbidPlayInListVersion() {
        int version = android.os.Build.VERSION.SDK_INT;
        if (version <= 19) {
            return true;
        }

        return false;
    }

    private static final int CACHED_NUM = 10;

    /**
     * 缓存视频播放地址信息
     */
    private LinkedHashMap<Long, VideoInfoModel> mCachedVideoPlayInfoBeanMap = new LinkedHashMap<Long, VideoInfoModel>(CACHED_NUM, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<Long, VideoInfoModel> eldest) {
            return size() > CACHED_NUM;
        }
    };

    public VideoInfoModel getVideoPlayInfoBean(long trackId) {
        if (mCachedVideoPlayInfoBeanMap != null) {
            return mCachedVideoPlayInfoBeanMap.get(trackId);
        }

        return null;
    }

    public void putVideoPlayInfoBean(long trackId, VideoInfoModel videoInfoModel) {
        if (mCachedVideoPlayInfoBeanMap != null) {
            // 更新时间戳
            videoInfoModel.setTimestamp(System.currentTimeMillis());
            mCachedVideoPlayInfoBeanMap.put(trackId, videoInfoModel);
        }
    }

    private DialogBuilder mShowDialog;

    public void showNetworkCheckDialog(final DialogBuilder.DialogCallback okCallBack, final DialogBuilder.DialogCallback cancelCallBack) {

        if (mShowDialog != null && mShowDialog.isShowing()) {
            return;
        }

        if (isVideoPlaying()) {
            pausePlay();
        }

        mShowDialog = new DialogBuilder(BaseApplication.getTopActivity());
        IFreeFlowService freeFlowService = FreeFlowServiceUtil.getFreeFlowService();
        String message = "当前处于非Wi-Fi环境，是否使用流量观看视频？";
        if (freeFlowService != null && freeFlowService.freeFlowUseOver()) {
            message += "\r\n（您订购的喜马拉雅流量包已全部用完）";
        }
        mShowDialog.setMessage(message);

        mShowDialog.setOkBtn("继续播放", new DialogBuilder.DialogCallback() {
            @Override
            public void onExecute() {
                VideoPlayManager.isAllowMobilePlayVideo = true;
                startPlayNoSettingVolume();
                mShowDialog.cancle();
                if (okCallBack != null) {
                    okCallBack.onExecute();
                }
            }
        });

        mShowDialog.setCancelBtn("稍后观看", new DialogBuilder.DialogCallback() {
            @Override
            public void onExecute() {
                VideoPlayManager.isAllowMobilePlayVideo = false;
                mShowDialog.cancle();
                if (cancelCallBack != null) {
                    cancelCallBack.onExecute();
                }
            }
        });


        mShowDialog.setcancelApplyToButton(false);
        mShowDialog.setOutsideTouchCancel(false);
        mShowDialog.setCancelable(false);
        mShowDialog.showConfirm();
    }


    public void setAllowUseMobileNetwork(boolean isAllow) {
        if (functionAction != null) {
            functionAction.setAllowUseMobileNetwork(isAllow);
        }
    }

    /**
     * 用于检测视频是否播放
     */
    private HashSet<IVideoPlayHandleListener> mVideoPlayHandleListeners = new HashSet<>(2);

    public interface IVideoPlayHandleListener {
        boolean isVideoPlay();

        void stopVideo();

        void pauseVideo();

        default void checkToResumeVideo(){}

        default int getCurPos() {
            return -1;
        }
    }

    public void addVideoPlayHandleListener(IVideoPlayHandleListener listener) {
        mVideoPlayHandleListeners.add(listener);
    }


    public void removeVideoPlayHandleListener(IVideoPlayHandleListener listener) {
        mVideoPlayHandleListeners.remove(listener);
    }


    /**
     * 用于列表视频滑动播放的事件分发
     * HashMap根据key可以进行排序
     */
    private HashMap<IScrollScrollChangeListener, Integer> mScrollChangeListeners = new HashMap<>(2);


    public interface IScrollScrollChangeListener {

        /**
         * ScrollView Scroll callback
         *
         * @param hashCode Adapter hashcode
         * @param dx
         * @param dy
         */
        void onScrollViewScrolled(int hashCode, int dx, int dy);

        /**
         * Scroll state changed callback
         *
         * @param hashCode             Adapter hashcode
         * @param state                The view scroll state {@link android.widget.AbsListView.OnScrollListener}
         * @param firstVisiblePosition the index of the first visible cell
         * @param lastVisiblePosition  the index of the last visible cell
         * @return true indicate the VideoViewItem will play video, false indicate no VideoViewItem will play video
         */
        boolean onScrollStateChanged(int hashCode, int state, int firstVisiblePosition, int lastVisiblePosition);

        default boolean onScrollStateChanged(int state, int firstVisiblePosition, int lastVisiblePosition, boolean hasVideoPlayed) {
            return false;
        }

        default boolean isVideoPlaying() {
            return false;
        }

        default void stopPlay() {
        }

        default boolean percentOutStop() {
            return true;
        }
    }

    public void addScrollChangeListener(IScrollScrollChangeListener listener) {
        Logger.d(TAG, "addScrollChangeListener " + listener);
        mScrollChangeListeners.put(listener, mScrollChangeListeners.size());
    }

    public void addScrollChangeListener(IScrollScrollChangeListener listener, int position) {
        Logger.d(TAG, "addScrollChangeListener " + listener + ", position = " + position);
        mScrollChangeListeners.put(listener, position);
    }


    public void removeScrollChangeListener(IScrollScrollChangeListener listener) {
        Logger.d(TAG, "removeScrollChangeListener " + listener);
        mScrollChangeListeners.remove(listener);
    }

    @Override
    public void dispatchScrollChange(int hashCode, int dx, int dy) {
        Logger.d(TAG, "dispatchScrollChange listener size: " + mScrollChangeListeners.size());
        List<Map.Entry<IScrollScrollChangeListener, Integer>> scrollChangeListeners = sortMap(mScrollChangeListeners);
        Logger.d(TAG, "dispatchScrollChange -> Ergodic list for IScrollScrollChangeListener " + scrollChangeListeners.size());
        for (Map.Entry<IScrollScrollChangeListener, Integer> scrollChangeListener : scrollChangeListeners) {
            IScrollScrollChangeListener listener = scrollChangeListener.getKey();
            Logger.d(TAG, scrollChangeListener.getValue() + "");
            if (listener != null) {
                listener.onScrollViewScrolled(hashCode, dx, dy);
            }
        }
    }

    public void dispatchScrollStateChangeNew(int state, int firstVisiblePosition, int lastVisiblePosition) {
        for (Map.Entry<IScrollScrollChangeListener, Integer> listenner : mScrollChangeListeners.entrySet()) {
            if (listenner.getKey().isVideoPlaying()) {
                return;
            }
        }
        List<Map.Entry<IScrollScrollChangeListener, Integer>> scrollChangeListeners = sortMap(mScrollChangeListeners);
        boolean hasVideoPlayed = false;
        for (Map.Entry<IScrollScrollChangeListener, Integer> scrollChangeListener : scrollChangeListeners) {
            IScrollScrollChangeListener listener = scrollChangeListener.getKey();
            Logger.d(TAG, "dispatchScrollStateChangeNew: " + "videoPlaying: " + scrollChangeListener.getKey().isVideoPlaying()
                    + ", val: " + scrollChangeListener.getValue() + "");
            if (listener != null) {
                hasVideoPlayed = listener.onScrollStateChanged(state, firstVisiblePosition, lastVisiblePosition, hasVideoPlayed);
                // hasVideoPlayed true 有视频需要播放了
            }
        }
    }

    @Override
    public void dispatchScrollStateChange(int hashCode, int state, int firstVisiblePosition, int lastVisiblePosition) {
        Logger.d(TAG, "dispatchScrollStateChange listener size: " + mScrollChangeListeners.size());
        List<Map.Entry<IScrollScrollChangeListener, Integer>> scrollChangeListeners = sortMap(mScrollChangeListeners);
        Logger.d(TAG, "dispatchScrollStateChange -> Ergodic list for IScrollScrollChangeListener " + scrollChangeListeners.size());
        for (Map.Entry<IScrollScrollChangeListener, Integer> scrollChangeListener : scrollChangeListeners) {
            IScrollScrollChangeListener listener = scrollChangeListener.getKey();
            Logger.d(TAG, scrollChangeListener.getValue() + "");
            if (listener != null) {
                if (listener.onScrollStateChanged(hashCode, state, firstVisiblePosition, lastVisiblePosition)) {
                    // 有 VideoViewItem 将要播放视频时，停止分发
                    break;
                }
            }
        }
    }

    public boolean attachedViewsHasVideoPlay() {
        Iterator<IVideoPlayHandleListener> it = mVideoPlayHandleListeners.iterator();
        for (; it.hasNext(); ) {
            if (it.next().isVideoPlay()) {
                return true;
            }
        }
        return false;
    }

    public void stopAttachedVideoPlay(int pos) {
        Iterator<IVideoPlayHandleListener> it = mVideoPlayHandleListeners.iterator();
        for (; it.hasNext(); ) {
            IVideoPlayHandleListener listener = it.next();
            if (listener != null && listener.getCurPos() != pos) {
                listener.stopVideo();
            }
        }
    }

    public boolean stopAttachedVideoPlay() {
        if (mVideoPlayHandleListeners != null) {
            Iterator<IVideoPlayHandleListener> it = mVideoPlayHandleListeners.iterator();
            for (; it.hasNext(); ) {
                it.next().stopVideo();
            }
        }
        return false;
    }

    public boolean pauseAttachedVideoPlay() {
        Iterator<IVideoPlayHandleListener> it = mVideoPlayHandleListeners.iterator();
        while (it.hasNext()) {
            it.next().pauseVideo();
        }
        return false;
    }

    public void resumeAttachedVideoPlay() {
        Iterator<IVideoPlayHandleListener> it = mVideoPlayHandleListeners.iterator();
        while (it.hasNext()) {
            it.next().checkToResumeVideo();
        }
    }

    @Override
    public void restCurrentPlayPosition() {
        this.currentPlayPosition = NO_PLAYING_VIDEO;
    }

    @Override
    public void stopListViewPlay(ListView listView) {
        if (listView == null) {
            return;
        }

        if (getCurrentPlayPosition() == VideoPlayManager.NO_PLAYING_VIDEO) {
            return;
        }

        int currentPlaying = getCurrentPlayPosition();
        int firstVisible = listView.getFirstVisiblePosition();
        int pos = currentPlaying - firstVisible + listView.getHeaderViewsCount();

        if (pos < 0 || pos >= listView.getChildCount()) {
            return;
        }

        View child = listView.getChildAt(pos);
        if (child == null) {
            return;
        }

        View layout = child.findViewById(R.id.host_video_item_view_layout);
        if (layout != null) {
            if (layout instanceof VideoItemViewLayout) {
                VideoItemViewLayout videoItemViewLayout = (VideoItemViewLayout) layout;
                videoItemViewLayout.stopPlay();
            } else if (layout instanceof TopicPKVideoItemViewLayout) {
                TopicPKVideoItemViewLayout topicVideoItemViewLayout = (TopicPKVideoItemViewLayout) layout;
                topicVideoItemViewLayout.stopPlay();
            } else if (layout instanceof NoteVideoItemViewLayout) {
                NoteVideoItemViewLayout noteVideoItemViewLayout = (NoteVideoItemViewLayout) layout;
                noteVideoItemViewLayout.stopPlay();
            }
        }
    }


    /**
     * onRenderingStart 时调用
     *
     * @param position
     * @param isAutoPlay
     */
    public void putPosition2AutoPlay(int position, boolean isAutoPlay) {
        if (mPosition2AutoPlayArray == null) {
            mPosition2AutoPlayArray = new SparseBooleanArray();
        }

        mPosition2AutoPlayArray.put(position, isAutoPlay);
    }

    public boolean getPosition2AutoPlay(int position) {
        if (mPosition2AutoPlayArray == null) {
            return false;
        }

        return mPosition2AutoPlayArray.get(position);
    }

    /**
     * 如下场景触发清除操作
     * <p>
     * 1. 页面初始化/销毁时
     * 2. 切换TAB时
     * 3. 下拉刷新时
     */
    public void clearPosition2AutoPlayArray() {
        if (mPosition2AutoPlayArray != null) {
            mPosition2AutoPlayArray.clear();
        }
    }

    /**
     * VideoItemLayout has detached from window 时调用
     *
     * @param position
     */
    public void deletePosition2AutoPlay(int position) {
        if (mPosition2AutoPlayArray != null) {
            mPosition2AutoPlayArray.delete(position);
        }
    }

    public interface IPlayPositionChangedListener {
        void onPlayPositionChanged(int position);
    }

    // 根据map value进行升序排序
    private <T> List<Map.Entry<T, Integer>> sortMap(Map<T, Integer> map) {
        ArrayList<Map.Entry<T, Integer>> list = new ArrayList<>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<T, Integer>>() {
            @Override
            public int compare(Map.Entry<T, Integer> o1, Map.Entry<T, Integer> o2) {
                return o1.getValue().compareTo(o2.getValue());
            }
        });
        return list;
    }
}