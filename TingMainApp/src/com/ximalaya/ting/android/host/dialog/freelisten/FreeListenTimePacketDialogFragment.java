package com.ximalaya.ting.android.host.dialog.freelisten;

import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.firework.base.IFireworkPopPage;
import com.ximalaya.ting.android.firework.model.FireworkButton;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.FreeListenTimeFireworkInterceptor;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;

import java.util.UUID;

/**
 * 畅听时长模式首页弹窗广告
 */
public class FreeListenTimePacketDialogFragment extends BaseDialogFragment implements View.OnClickListener,
        IFireworkPopPage {
    private ViewGroup mContainer;
    private ImageView mCloseIv;
    private ImageView mPacketIv;
    private TextView mCountdownTv;

    private int  mDialogType;
    private String mImageUrl;
    private View.OnClickListener mClickListener;
    private View.OnClickListener mCloseListener;

    private String recordRequestId;

    public static FreeListenTimePacketDialogFragment getInstance(int dialogType, String image, View.OnClickListener clickListener, View.OnClickListener closeListener) {
        FreeListenTimePacketDialogFragment dialogFragment = new FreeListenTimePacketDialogFragment();
        dialogFragment.mDialogType = dialogType;
        dialogFragment.mImageUrl = image;
        dialogFragment.mClickListener = clickListener;
        dialogFragment.mCloseListener = closeListener;
        return dialogFragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            Window window = dialog.getWindow();
            if (window != null) {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                window.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN,
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN);
                window.setWindowAnimations(com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation);
            }
            setCancelable(true);
        }

        View contentView = inflater.inflate(R.layout.host_free_listen_time_style2_dialog_layout, container, false);
        mContainer = contentView.findViewById(R.id.host_free_listen_time_dialog_container);

        mCloseIv = contentView.findViewById(R.id.host_free_listen_time_close_iv);
        mPacketIv = contentView.findViewById(R.id.host_free_listen_time_reward_iv);
        mCountdownTv = contentView.findViewById(R.id.host_free_listen_time_countdown_tv);
        mCountdownTv.setText("3s后自动收下");
        mCloseIv.setOnClickListener(this);

        ImageManager.from(getContext()).displayImage(mPacketIv, mImageUrl, -1, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap == null) {
                    mPacketIv.setImageResource(R.drawable.host_free_listen_time_packet_img);
                }
                mPacketIv.setOnClickListener(FreeListenTimePacketDialogFragment.this);
                contentView.setOnClickListener(v -> dismiss());
            }
        });
        createCountdown();
        recordRequestId = XmRequestIdManager.getInstance(getContext()).getRequestId();
        String uuid = UUID.randomUUID().toString();
        if (mDialogType == FreeListenTimeFireworkInterceptor.TYPE_NEW_USER) {
            // 新首页-全站畅听首页引导V2  控件曝光
            new XMTraceApi.Trace()
                    .setMetaId(57457)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "newHomePage")
                    .put("dialogTitle", "恭喜你获得") // 根据实际文案传
                    .put("item", "7天免费收听权益") // 根据实际文案传
                    .put(XmRequestIdManager.XM_REQUEST_ID, recordRequestId)
                    .put(XmRequestIdManager.CONT_ID, uuid)
                    .createTrace();
        } else {
            new XMTraceApi.Trace()
                    .setMetaId(57457)
                    .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                    .put("currPage", "newHomePage")
                    .put("dialogTitle", "欢迎回来，送你一份") // 根据实际文案传
                    .put("item", "7天免费收听权益") // 根据实际文案传
                    .put(XmRequestIdManager.XM_REQUEST_ID, recordRequestId)
                    .put(XmRequestIdManager.CONT_ID, uuid)
                    .createTrace();
        }
        FreeListenDialogTrace.traceEvent("show");

        return contentView;
    }

    private void createCountdown() {
        CountDownTimer timer = new CountDownTimer(3000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long curTime = millisUntilFinished / 1000;
                if (mCountdownTv != null) {
                    mCountdownTv.setText(String.format("%ds后自动收下", curTime));
                }
            }

            @Override
            public void onFinish() {
                dismiss();
            }
        };
        HandlerManager.postOnUIThreadDelay(timer::start, 1000);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.host_free_listen_time_close_iv) {
            onClickClose();
            if (mCloseListener != null) {
                mCloseListener.onClick(v);
            }
            dismiss();
        } else if (id == R.id.host_free_listen_time_reward_iv) {
            // 新首页-全站畅听首页引导V2  点击事件
            if (mDialogType == FreeListenTimeFireworkInterceptor.TYPE_NEW_USER) {
                new XMTraceApi.Trace()
                        .click(57456) // 用户点击时上报
                        .put("currPage", "newHomePage")
                        .put("dialogTitle", "恭喜你获得") // 根据实际文案传
                        .put("item", "7天免费收听权益") // 根据实际文案传
                        .put(XmRequestIdManager.XM_REQUEST_ID, recordRequestId)
                        .createTrace();
            } else {
                new XMTraceApi.Trace()
                        .click(57456) // 用户点击时上报
                        .put("currPage", "newHomePage")
                        .put("dialogTitle", "欢迎回来，送你一份") // 根据实际文案传
                        .put("item", "7天免费收听权益") // 根据实际文案传
                        .put(XmRequestIdManager.XM_REQUEST_ID, recordRequestId)
                        .createTrace();
            }
            onClickContent("7天免费收听权益");
            FreeListenDialogTrace.traceEvent("click");

            if (mClickListener != null) {
                mClickListener.onClick(v);
            }
            dismiss();
        }
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window window = dialog.getWindow();
            if (window != null) {
                window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            }
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        mClickListener = null;
    }

    @Override
    public void onLoadSuccess() {

    }

    @Override
    public void onLoadFail() {

    }

    @Override
    public void onClose(Fragment fragment) {

    }

    @Override
    public void onJump(Fragment fragment, FireworkButton fireworkButton) {

    }

    @Override
    public boolean isFirework() {
        return false;
    }


    @Override
    public String getDialogSource() {
        return "free_listen_dialog";
    }

    @Override
    public String getBusinessId() {
        return "FreeListenTimePacketDialogFragment";
    }
}
