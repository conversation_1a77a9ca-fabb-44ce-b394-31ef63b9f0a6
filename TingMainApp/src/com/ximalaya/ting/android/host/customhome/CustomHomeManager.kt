package com.ximalaya.ting.android.host.customhome

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.encryptservice.DeviceTokenUtil
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.StringUtil
import com.ximalaya.ting.android.host.hybrid.utils.MD5Tool
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.Configure
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.configurecenter.ConfigureCenterUtil
import com.ximalaya.ting.android.host.manager.request.CommonRequestForSubscribeAlbum
import com.ximalaya.ting.android.host.model.homepage.CustomHomeData
import com.ximalaya.ting.android.host.model.track.TrackM
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.RecommendShowTagsUtilNew
import com.ximalaya.ting.android.host.util.onekey.DailyNewsUtil
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.model.PlayableModel
import com.ximalaya.ting.android.opensdk.model.track.CommonTrackList
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import org.json.JSONObject
import com.ximalaya.ting.android.host.model.recommend.ShowTag
import com.ximalaya.ting.android.host.util.HomeRnUtils
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import java.util.*
import kotlin.collections.ArrayList

/**
 * 定制首页相关逻辑
 */
object CustomHomeManager {

    const val TAG = "CustomHomeManager"

    const val MIN_ELEMENT_SIZE = 6
    const val TYPE_LOADING = "TYPE_LOADING"
    const val TYPE_LOADING_FINISHED = "TYPE_LOADING_FINISHED"

    const val KEY_ASMR_SELECTED = "custom_home_asmr_selected"
    const val KEY_HOT_SELECTED = "custom_home_hot_selected"
    const val KEY_SEARCH_PREFIX = "custom_home_search_selected_"

    //首页要看到的搜索词
    const val KEY_SEARCH_KEY_WORDS = "custom_home_search_key_words"
    const val KEY_HOT_LOCAL_DATA = "custom_home_hot_local_data"
    const val KEY_ASMR_LOCAL_DATA = "custom_home_asmr_local_data"

    //展示的顺序（刷新后会修改）
    const val KEY_ASMR_SHOW_INDEX = "custom_home_asmr_show_index"
    const val KEY_SEARCH_SHOW_INDEX = "custom_home_search_show_index"

    //搜索词对应的数据
    const val KEY_SEARCH_WORDS_DATA = "custom_home_search_words_data_"

    val searchGuideCardIcon: String =
        "https://imagev2.xmcdn.com/storages/ed1e-audiofreehighqps/84/70/GAqhfD0MJXuoAABTlAPJk6G0.png"
    var searchGuideCardClickUrl: String = "https://gpt4oimageprompt.com/"
    var searchGuideCardTitle: String = "添加「%s」板块到首页"
    var searchGuideCardSubTitle: String = "添加"

    var homeCardSubTitle: String = "你定制的内容"
    var insertIndex: Int = 0
    var disableShowModuleTypeList  = mutableListOf<String>("banner", "blockCard", "adBrandZoneCard", "activity", "warningCard", "spellCheckerCard")

    //不同类型数据当前的索引，每次刷新向后移动 6 位，超出后从头开始

    private val searchWordDataMap: MutableMap<String, CustomHomeData.SubElementWrapper> =
        mutableMapOf()

    fun init() {
        // TODO: 读取开关，如果开启，读取文案配置
    }

    fun enableGuide(): Boolean {
        if (HomeRnUtils.isShowRnFragment()) {
            //RN 首页还不支持
            return false;
        }
        val enable = ConfigureCenter.getInstance().getBool(CConstants.Group_toc.GROUP_NAME, "enable_custom_home", false)
        log("enableGuide, enable: $enable")

        if (!enable && !ConstantsOpenSdk.isDebug) {
            return false;
        }
        val abValue = ABTest.getInt("custom_home_style", 0)
        log("enableGuide, abValue: $abValue")
        if (abValue == 0 && !ConstantsOpenSdk.isDebug) {
            return false;
        }

        if (ToolUtil.getDebugSystemProperty("debug.shixin.custom_home_disable", "-1").equals("1")) {
            return false;
        }
        return true;
    }

    fun enableShowCustomHomeCard(): Boolean {
        return enableGuide()
    }

    /**
     * 保存这个搜索词的结果，当选择开启后，持久化
     */
    fun saveSearchResultData(searchWord: String, data: CustomHomeData.SubElementWrapper) {
        searchWordDataMap[searchWord] = data

        log("saveSearchResultData, $searchWord , size: ${data.subElements.size}")
    }

    fun getCustomHomeData(): String? {
        val enable = enableShowCustomHomeCard()
        log("getCustomHomeData s1, enable: $enable")
        if (!enable) {
            return null
        }

        val result = CustomHomeData()
        val itemList = mutableListOf<CustomHomeData.SubElementWrapper>()
        //1.如果勾选搜索词，获取本地的搜索词对应的数据列表
        val data = getCustomHomeSearchWordData()
        log("getCustomHomeData s2, getCustomHomeSearchWordData: $data")

        if (data != null) {
            itemList.addAll(data)
        }

        //  2.如果开启助眠，获取助眠声音列表
        val asmrData = getCustomHomeAsmrData()
        if (asmrData != null) {
            itemList.add(asmrData)
        }

        // 3.如果开启热点，获取热点声音列表
        val hotData = getCustomHomeHotData()
        log("getCustomHomeData s4, getCustomHomeHotData: $hotData")
        if (hotData != null) {
            itemList.add(hotData)
        }

        //后选的在前面
        itemList.sortWith { o1, o2 -> (o2.timestamp - o1.timestamp).toInt() }

        result.addItem(itemList)
        return GsonUtils.toJson(result)
    }

    /**
     * 获取热点数据（从搜索页面的热点接口获取）
     */
    private fun getCustomHomeHotData(): CustomHomeData.SubElementWrapper? {
        val isHotSelected = isHotSelected()
        if (!isHotSelected) {
            return null
        }

        return getCacheData(KEY_HOT_LOCAL_DATA)
    }

    /**
     * 获取助眠数据，目前为本地拼造
     */
    private fun getCustomHomeAsmrData(): CustomHomeData.SubElementWrapper? {
        val isAsmrSelected = isAsmrSelected()
        if (!isAsmrSelected) {
            return null
        }

        val data = getCacheData(KEY_ASMR_LOCAL_DATA)

        data ?: return null

        val currentIndex = MMKVUtil.getInstance().getInt(KEY_ASMR_SHOW_INDEX, 0)
        log("getCustomHomeAsmrData, index: $currentIndex")

        data.subElements = getShowDataByIndex(currentIndex, data.subElements)
        return data;
    }

    private fun getCacheData(key: String): CustomHomeData.SubElementWrapper? {
        try {
            val json = MMKVUtil.getInstance().getString(key)
            if (json.isNullOrEmpty()) {
                return null
            }

            val data =
                GsonUtils.parseJson(json, CustomHomeData.SubElementWrapper::class.java)

            if (data.subElements.isNotEmpty()) {
                return data
            }
        } catch (e: Exception) {
            log("getCacheData [$key] failed!!! ${e.message}")
            e.printStackTrace()
        }
        return null
    }

    /**
     * 获取搜索词数据，可能有多条
     */
    private fun getCustomHomeSearchWordData(): List<CustomHomeData.SubElementWrapper>? {
        val arrayList = MMKVUtil.getInstance().getArrayList(KEY_SEARCH_KEY_WORDS)
        if (arrayList == null || arrayList.isEmpty()) {
            log("getCustomHomeSearchWordData, s1 return")
            return null
        }

        val result = mutableListOf<CustomHomeData.SubElementWrapper>()
        for (searchKeyword in arrayList) {

            val data = getSearchWordData(searchKeyword)

            if (data?.subElements?.isNotEmpty() == true) {
                result.add(data)
            }
        }

        return result
    }

    private fun getSearchWordDataFull(searchKeyword: String?): CustomHomeData.SubElementWrapper? {
        searchKeyword ?: return null

        try {
            val key = buildSearchKey(searchKeyword)
            val json = MMKVUtil.getInstance().getString(key)

            if (json.isNullOrEmpty()) {
                return null
            }

            return GsonUtils.parseJson(json, CustomHomeData.SubElementWrapper::class.java);
        } catch (e: Exception) {
            log("getSearchWordData $searchKeyword failed!!! ${e.message}")
            e.printStackTrace()
        }
        return null
    }

    private fun getSearchWordData(searchKeyword: String?): CustomHomeData.SubElementWrapper? {
        searchKeyword ?: return null

        try {
            val key = buildSearchKey(searchKeyword)
            val json = MMKVUtil.getInstance().getString(key)

            if (json.isNullOrEmpty()) {
                return null
            }

            val data = GsonUtils.parseJson(json, CustomHomeData.SubElementWrapper::class.java)

            //从上次保存的索引开始展示
            val showIndexKey = buildSearchIndexKey(searchKeyword)
            val currentIndex = MMKVUtil.getInstance().getInt(showIndexKey, 0)
            log("getSearchWordData, showIndexKey: $showIndexKey, index: $currentIndex")

            data.subElements = getShowDataByIndex(currentIndex, data.subElements)

            return data;
        } catch (e: Exception) {
            log("getSearchWordData $searchKeyword failed!!! ${e.message}")
            e.printStackTrace()
        }
        return null
    }

    fun isAsmrSelected(): Boolean {
        return getBoolean(KEY_ASMR_SELECTED)
    }

    fun isHotSelected(): Boolean {
        return getBoolean(KEY_HOT_SELECTED)
    }

    fun isSearchWordSelected(searchKeyword: String): Boolean {
        //默认选中
        return MMKVUtil.getInstance().getBoolean(KEY_SEARCH_PREFIX + searchKeyword, true)
    }

    private fun getBoolean(key: String): Boolean {
        return MMKVUtil.getInstance().getBoolean(key)
    }

    /**
     * 保存要在首页显示的内容
     */
    fun saveSelection(
        cbOptionAsmr: Boolean,
        cbOptionHot: Boolean,
        searchKeyword: String,
        cbOptionSearchWord: Boolean
    ) {
        MMKVUtil.getInstance().saveBoolean(KEY_ASMR_SELECTED, cbOptionAsmr)
        MMKVUtil.getInstance().saveBoolean(KEY_HOT_SELECTED, cbOptionHot)
        MMKVUtil.getInstance().saveBoolean(KEY_SEARCH_PREFIX + searchKeyword, cbOptionSearchWord)

        var needRefresh = false

        var delayRefreshHome = 0L
        if (cbOptionAsmr) {
            saveOrClearAsmrData(true)
            delayRefreshHome += 500
            needRefresh = true
        }

        if (cbOptionHot) {
            saveOrClearHotData(true)

            delayRefreshHome += 1000
            needRefresh = true
        }

        if (cbOptionSearchWord) {
            val saveSucceed = saveOrClearSearchResultDataToDisk(searchKeyword, true)

            if (!saveSucceed && !needRefresh) {
                //保存失败，不刷新首页
                return
            }

            delayRefreshHome += 500
        }

        Handler(Looper.getMainLooper()).postDelayed(
            {
                //刷新首页
                Router.getActionRouter<MainActionRouter>(Configure.BUNDLE_MAIN)?.functionAction?.refreshHomepageRecommendFragmentForCustomHome()
            },
            delayRefreshHome
        )
    }

    /**
     * 删除某个数据
     * type: 助眠、热点或者某个搜索词
     */
    fun removeData(type: String) {
        if (type == KEY_ASMR_SELECTED) {
            saveOrClearAsmrData(false)
            return
        }
        if (type == KEY_HOT_SELECTED) {
            saveOrClearHotData(false)
            return
        }

        saveOrClearSearchResultDataToDisk(type, false)
    }

    /**
     * 获取新数据
     */
    fun refreshData(type: String, callBack: IDataCallBack<List<CustomHomeData.SubElementWrapper>>?) {
        callBack ?: return

        if (type == KEY_ASMR_SELECTED) {
            val data = getCustomHomeAsmrData()
            refreshData(type, KEY_ASMR_SHOW_INDEX, data, callBack)
            return
        }

        if (type == KEY_HOT_SELECTED) {
            //热点比较特殊，需要重新请求
            getNewHot(callBack)
            return
        }

        //搜索词
        val data = getSearchWordDataFull(type)
        val key = buildSearchIndexKey(type)
        refreshData(type, key, data, callBack)
    }

    /**
     * 数据刷新，修改的是从哪里开头
     */
    private fun refreshData(
        type: String,
        indexKey: String,
        data: CustomHomeData.SubElementWrapper?,
        callBack: IDataCallBack<List<CustomHomeData.SubElementWrapper>>
    ) {
        if (data == null) {
            callBack.onSuccess(data)
            return
        }

        val size = data.subElements.size
        var currentIndex = MMKVUtil.getInstance().getInt(indexKey, 0)
        log("refreshData: $type,indexKey:$indexKey, size: $size, index: $currentIndex")

        // 如果数据少于6个，直接返回原始数据
        if (size < 6) {
//            data.subElements.shuffle()
//            updateData(cacheKey, data)
            callBack.onSuccess(mutableListOf(data))
            return
        }

        // 计算新的起始索引
        if (currentIndex < size - 6) {
            currentIndex += 6
        } else {
            currentIndex = 0
        }

        data.subElements = getShowDataByIndex(currentIndex, data.subElements)

        log("refreshData, newIndex: $currentIndex, newSize: ${data.subElements.size}")

//        data.subElements.shuffle()
//        updateData(cacheKey, data)
        callBack.onSuccess(mutableListOf(data))

        MMKVUtil.getInstance().saveInt(indexKey, currentIndex)
    }

    /**
     * 获取展示的数据，根据上次刷新时的 index 开始
     */
    private fun getShowDataByIndex(
        index: Int,
        subElements: List<CustomHomeData.SubElement>
    ): MutableList<CustomHomeData.SubElement> {

        // 确保返回6个元素
        val resultElements = mutableListOf<CustomHomeData.SubElement>()
        var currentIndex = index
        val size = subElements.size

        var remainingElements = 6

        while (remainingElements > 0) {
            // 如果当前索引超出范围，从头开始
            if (currentIndex >= size) {
                currentIndex = 0
            }

            // 添加元素
            resultElements.add(subElements[currentIndex])
            currentIndex++
            remainingElements--
        }

        // 根据 refId 去重
        val seenRefIds = mutableSetOf<Long>()
        val uniqueElements = mutableListOf<CustomHomeData.SubElement>()
        
        for (element in resultElements) {
            val refId = element.refId ?: continue
            if (seenRefIds.add(refId)) {
                uniqueElements.add(element)
            }
        }

        return uniqueElements
    }

    private fun updateData(key: String, data: CustomHomeData.SubElementWrapper) {
        val json = GsonUtils.toJson(data)
        MMKVUtil.getInstance().saveString(key, json)
        log("更新数据：$key")
    }

    private fun saveOrClearSearchResultDataToDisk(searchKeyword: String, save: Boolean): Boolean {
        var arrayList = MMKVUtil.getInstance().getArrayList(KEY_SEARCH_KEY_WORDS)
        if (arrayList == null || arrayList.isEmpty()) {
            arrayList = ArrayList<String>()
        }
        val key = buildSearchKey(searchKeyword)
        if (!save) {
            //移除数据
            arrayList.remove(searchKeyword)
            MMKVUtil.getInstance().saveArrayList(KEY_SEARCH_KEY_WORDS, arrayList)

            MMKVUtil.getInstance().removeByKey(key)
            return true
        }

        //1.保存当前搜索词
        if (arrayList.contains(searchKeyword)) {
            arrayList.remove(searchKeyword);
        }
        arrayList.add(0, searchKeyword) //新的放前面

        MMKVUtil.getInstance().saveArrayList(KEY_SEARCH_KEY_WORDS, arrayList)

        //2.内存中的数据持久化
        val data = searchWordDataMap[searchKeyword]
        if (data == null || data.subElements.isEmpty()) {
            log("saveOrClearSearchResultDataToDisk failed, data is null or empty, $searchKeyword >> ${data?.subElements?.size}")
            return false
        }
        val json = GsonUtils.toJson(data)
        MMKVUtil.getInstance().saveString(key, json)
        log("saveOrClearSearchResultDataToDisk, $searchKeyword , key:$key")
        return true
    }

    public fun log(msg: String) {
        Log.d(TAG, msg)
    }

    /**
     * 搜索词本地缓存数据
     */
    private fun buildSearchKey(searchKeyword: String): String {
        return KEY_SEARCH_WORDS_DATA + MD5Tool.md5(searchKeyword)
    }

    /**
     * 搜索结果展示顺序的缓存 key
     */
    private fun buildSearchIndexKey(searchKeyword: String): String {
        return KEY_SEARCH_SHOW_INDEX + MD5Tool.md5(searchKeyword)
    }

    private fun saveOrClearHotData(save: Boolean) {
        if (!save) {
            MMKVUtil.getInstance().removeByKey(KEY_HOT_SELECTED)
            MMKVUtil.getInstance().removeByKey(KEY_HOT_LOCAL_DATA)
            return
        }

        getNewHot(null)
    }

    private fun getNewHot(callBack: IDataCallBack<List<CustomHomeData.SubElementWrapper>>?) {
        log("getNewHot request >>>")
        DailyNewsUtil.queryTracksByChannelId(
            80,
            object : IDataCallBack<(CommonTrackList<PlayableModel>)> {
                override fun onSuccess(data: CommonTrackList<PlayableModel>?) {
                    data ?: return
                    log("saveOrClearHotData onSuccess, size:${data.tracks?.size}")

                    val convertData = convertData(data.tracks)

                    if (convertData != null) {
                        callBack?.onSuccess(mutableListOf(convertData))
                        MMKVUtil.getInstance()
                            .saveString(KEY_HOT_LOCAL_DATA, GsonUtils.toJson(convertData))
                        log("saveOrClearHotData save to local")
                    } else {
                        callBack?.onSuccess(null)
                    }
                }

                override fun onError(code: Int, message: String?) {
                    log("saveOrClearHotData onError $code  $message >>>")

                    callBack?.onError(code, message)
                }

            })
    }

    /**
     * 声音列表转换为 CustomHomeData.SubElementWrapper 并保存到 mmkv
     */
    private fun convertData(tracks: List<PlayableModel>): CustomHomeData.SubElementWrapper? {

        log("convertData , size: ${tracks.size}")

        if (tracks.isEmpty()) {
            return null
        }

        val wrapper = CustomHomeData.SubElementWrapper()
        wrapper.title = KEY_HOT_SELECTED
        wrapper.timestamp = System.currentTimeMillis()

        for (content in tracks) {

            val result = CustomHomeData.SubElement()
            val track = content as? TrackM ?: continue
            result.contentType = "Track"
            result.title = track.trackTitle
            result.cover = track.validCover
            result.refId = track.dataId
            result.playCount = track.playCount.toLong()
            result.landingPage = "iting://open?msg_type=11&track_id=${track.dataId}"

            // 处理标签 声音是自己拼接显示的  所以暂时只同步播放量过去
            val extraInfo = CustomHomeData.ExtraInfo()
            if (track.duration > 0) {
                val duration = track.duration.toString()
                val showTag = ShowTag(
                    StringUtil.getFriendlyLeftTimeStr(track.duration, false),
                    RecommendShowTagsUtilNew.TYPE_DURATION, duration
                )
                extraInfo.showTags.add(showTag)
            }
            if (track.playCount > 0) {
                val count = track.playCount.toString()
                val showTag = ShowTag(
                    StringUtil.getFriendlyNumStr(track.playCount),
                    RecommendShowTagsUtilNew.TYPE_COUNT_PLAY, count
                )
                extraInfo.showTags.add(showTag)
            }

            val albumTitle = track.albumTitle
            if (!albumTitle.isNullOrEmpty()) {
                val showTag = ShowTag(
                    albumTitle,
                    RecommendShowTagsUtilNew.TYPE_OTHER,
                    albumTitle
                )
                extraInfo.showTags2.add(showTag)
            }

            result.extraInfo = extraInfo

            wrapper.addWithDuplicateCheck(result)
        }

        wrapper.sortByPlayCount()
        return wrapper
    }

    private fun saveOrClearAsmrData(save: Boolean) {
        if (!save) {
            MMKVUtil.getInstance().removeByKey(KEY_ASMR_SELECTED)
            MMKVUtil.getInstance().removeByKey(KEY_ASMR_LOCAL_DATA)
            return
        }

        //保存数据到本地
        val data = buildAmsrData()
        MMKVUtil.getInstance().saveString(KEY_ASMR_LOCAL_DATA, GsonUtils.toJson(data))
    }

    private fun buildAmsrData(): CustomHomeData.SubElementWrapper {

        val wrapper = CustomHomeData.SubElementWrapper()
        wrapper.title = KEY_ASMR_SELECTED
        wrapper.timestamp = System.currentTimeMillis()

        val jsonObject = JSONObject(CustomHomeData.ASMR_DATA)
        val array = jsonObject.getJSONArray("data")
        val length = array.length()

        for (i in 0 until length) {
            val s = array.getString(i)
            val json = JSONObject(s)

            val result = CustomHomeData.SubElement()
            result.contentType = "Track"
            result.title = "助眠音乐:" + json.getString("title")
            result.cover = json.getString("cover")
            result.refId = json.getLong("trackId")
            result.landingPage = "iting://open?msg_type=94&bundle=rn_asmr&showmode=present&reuse=true&themeId=1&categoryId=1&bgTrackId=${result.refId}"

            val durationNumber = json.getLong("duration")

            val playCountNumber = json.getLong("playCounts")
            result.playCount = playCountNumber

            // 处理标签
            val extraInfo = CustomHomeData.ExtraInfo()

            if (durationNumber > 0) {
                val duration = StringUtil.getFriendlyLeftTimeStr(durationNumber.toInt(), false)
                val showTag = ShowTag(StringUtil.getFriendlyLeftTimeStr(durationNumber.toInt(), false), RecommendShowTagsUtilNew.TYPE_DURATION, duration)
                extraInfo.showTags.add(showTag)
            }
            if (playCountNumber > 0) {
                val count = playCountNumber.toString()
                val showTag = ShowTag(StringUtil.getFriendlyNumStr(playCountNumber), RecommendShowTagsUtilNew.TYPE_COUNT_PLAY, count)
                extraInfo.showTags.add(showTag)
            }

            val sellPoint = json.getString("sellPoint")
            if (!sellPoint.isNullOrEmpty()) {
                val tag = ShowTag()
                tag.tag = sellPoint
                tag.value = sellPoint
                tag.type = RecommendShowTagsUtilNew.TYPE_OTHER
                extraInfo.showTags2.add(tag)
            }
            extraInfo.showTags2.addAll(extraInfo.showTags)
            result.extraInfo = extraInfo

            wrapper.addWithDuplicateCheck(result)
        }

        wrapper.sortByPlayCount()
        return wrapper
    }

    fun requestSearchResult(searchWord: String, callBack: IDataCallBack<Boolean>) {

        val param = mutableMapOf<String, Any>()
        //0: 搜索专辑 1：搜索声音,  不填默认0
        param.put("type", 0)
        param.put("kw", searchWord)
        param.put("size", 30)

        log("requestSearchResult >>> $searchWord")

        CommonRequestForSubscribeAlbum.searchForCustomHome(param, object : IDataCallBack<List<CustomHomeData.SubElement>> {
            override fun onSuccess(data: List<CustomHomeData.SubElement>?) {
                log("requestSearchResult result size: ${data?.size}")

                if (data == null || data.isEmpty()) {
                    callBack.onError(-1, "")
                    return
                }

                val wrapper = CustomHomeData.SubElementWrapper()

                wrapper.subElements = data
                wrapper.title = searchWord
                wrapper.timestamp = System.currentTimeMillis()

                wrapper.sortByPlayCount()

                saveSearchResultData(searchWord, wrapper)
                saveSelection(false, false, searchWord, true)
            }

            override fun onError(code: Int, message: String?) {
                log("requestSearchResult onError: ${code} $message")
                callBack.onError(code, message)
            }

        })
    }

    fun requestSearchTitle(searchWord: String, callBack: IDataCallBack<String>) {

        val param = mutableMapOf<String, Any>()
        //0: 搜索专辑 1：搜索声音,  不填默认0
        param.put("device_id", DeviceTokenUtil.getDeviceToken(BaseApplication.getMyApplicationContext()))
        param.put("kw", searchWord)
        param.put("uid", UserInfoMannage.getUid())

        log("requestSearchTitle >>> $searchWord")

        CommonRequestForSubscribeAlbum.getSearchTitle(param, object : IDataCallBack<String> {
            override fun onSuccess(data: String?) {
                log("requestSearchTitle result: ${data}")

                if (data == null || data.isEmpty()) {
                    callBack.onError(-1, "")
                    return
                }

                callBack.onSuccess(data)
            }

            override fun onError(code: Int, message: String?) {
                log("requestSearchTitle onError: ${code} $message")
                callBack.onError(code, message)
            }

        })
    }

    //如果第一个是特殊类型的卡片，不展示添加入口
    fun checkShowGuideCard(firstCardModuleType: String): Boolean {
        log("checkShowGuideCard >>> $firstCardModuleType")

        if (ToolUtil.getDebugSystemProperty("debug.shixin.disable_check_gu", "-1").equals("1")) {
            return true;
        }

        if (disableShowModuleTypeList == null || disableShowModuleTypeList.isEmpty()) {
            log("checkShowGuideCard >>> s1, is empty")
            return true;
        }

        val contains = disableShowModuleTypeList.contains(firstCardModuleType)
        log("checkShowGuideCard >>> contains: ${contains}, firstCardModuleType: $firstCardModuleType, list: $disableShowModuleTypeList")
        //包含就不展示
        return !contains;
    }

}