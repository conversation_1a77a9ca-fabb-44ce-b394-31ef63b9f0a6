package com.ximalaya.ting.android.host.manager.statistic;

import android.os.Looper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.manager.statistic.realtime.StatisticsTimerResult;
import com.ximalaya.ting.android.host.manager.statistic.realtime.TrackRealTimeStatistics;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.HttpParamsConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.TicketConstantsKt;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.NetworkType;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 2018/4/27.
 */

public class TrackPlayCountUploader extends BasePlayStatisticsUploaderInMain {
    TrackPlayCountUploader(@NonNull Looper looper, Track track) {
        super(looper);

        mXmPlayRecord.setId(track.getDataId());
        if (track.getAlbum() != null) {
            mXmPlayRecord.setAlbumId(track.getAlbum().getAlbumId());
        }
        if (track.getPlaySource() == ConstantsOpenSdk.PLAY_FROM_LISTEN_LIST
                && track.getTingListOpType() == AppConstants.TYPE_TINGLIST_TRACK
                && track.getTrackRecordAlbumId() > 0) {
            mXmPlayRecord.setTingListId(track.getTrackRecordAlbumId());
        }
        Logger.d("staticsUploader", "TrackPlayCountUploader: " + mXmPlayRecord + ", " + track.getDataId() + ", " + track.getTrackTitle());
    }

    @NonNull
    @Override
    public Map<String, String> getParams() {
        Map<String, String> params = new HashMap<>();
        int vipType = -1;
        if (UserInfoMannage.isVipUser() && UserInfoMannage.getInstance().getUser() != null) {
            if (UserInfoMannage.getInstance().getUser().getVipLevel() > 0) {
                vipType = 1;
            } else {
                vipType = 2;
            }
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(HttpParamsConstants.PARAM_USER_VIP_TYPE, String.valueOf(vipType));
            jsonObject.put(HttpParamsConstants.PARAM_USER_CHILD_VIP_TYPE, String.valueOf(UserInfoMannage.isChildVipUser() ? 1 : -1));
            jsonObject.put(HttpParamsConstants.PARAM_USER_XIMI_VIP_TYPE, String.valueOf(UserInfoMannage.isXimiVipUser() ? 1 : -1));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        params.put(HttpParamsConstants.PARAM_USER_VIP_TYPE, jsonObject.toString());
        params.put(HttpParamsConstants.PARAM_TRACK_ID, String.valueOf(mXmPlayRecord.getId()));
        params.put(HttpParamsConstantsInOpenSdk.PARAM_NONCE, mNonceQueue.poll());
        if(mXmPlayRecord.getAlbumId() > 0){
            params.put(HttpParamsConstants.PARAM_ALBUM_ID, String.valueOf(mXmPlayRecord.getAlbumId()));
        }
        params.put(HttpParamsConstants.PARAM_STARTED_AT, String.valueOf(mXmPlayRecord.getStartTime()));
        if (mXmPlayRecord.getTingListId() > 0) {
            params.put(HttpParamsConstants.PARAM_TING_LIST_ID, String.valueOf(mXmPlayRecord.getTingListId()));
        }
        if (mXmPlayRecord.getPlayId() != null) {
            params.put(HttpParamsConstants.PARAM_PLAY_ID, mXmPlayRecord.getPlayId());
        }

        return params;
    }

    @Override
    public Map<String, String> getHeader() {
        if (MMKVUtil.getInstance().getBoolean(PreferenceConstantsInOpenSdk.KEY_PLAY_COUNT_UPLOAD_TICKET, true)) {
            Map<String, String> header = new ArrayMap<>();
            String ticket = CommonRequestM.getTicket(TicketConstantsKt.TYPE_PLAY_COUNT);
            header.put("x-tk", String.valueOf(ticket));
            return header;
        }
        return super.getHeader();
    }

    @NonNull
    @Override
    protected String getPostUrl() {
        return UrlConstants.getInstanse().getTrackCountUrl();
    }

    @Nullable
    @Override
    protected String getPostUrlV2() {
        if(mXmPlayRecord.getAlbumId() > 0){
            return UrlConstants.getInstanse().getTrackCountUrlV3();
        }else {
            return UrlConstants.getInstanse().getTrackCountUrlV2();
        }
    }

    @Override
    public void onSuccess(@Nullable Object object) {
        super.onSuccess(object);
        if (object instanceof StatisticsTimerResult) {
            TrackRealTimeStatistics.updateTimers(((StatisticsTimerResult) object).getTimerWrapper(), mXmPlayRecord.getId(), mXmPlayRecord.getPlayId());
        } else {
            TrackRealTimeStatistics.updateTimers(null, 0L, null);
        }
    }

    @Override
    public void onError(int code, String message) {
        if (NetworkType.isConnectTONetWork(BaseApplication.getMyApplicationContext())
            && code != BaseCall.ERROR_CODE_DEFALUT) {
            XDCSCollectUtil.statErrorToXDCS("trackCountNonceExpire", " code:" + code + " message:" + message);
        }
    }
}
