package com.ximalaya.ting.android.host.manager.ad.videoad.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnlockUtil;
import com.ximalaya.ting.android.host.manager.ad.videoad.CanPauseCountDownTimer;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.ICustomViewToActivityClickRewardExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.countdown.RewardVideoCountDownStyleForVipFree;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.DateTimeUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.read.view.CircleProgressView;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * Created by zhao.peng.
 * describe
 * Date: 2024/8/20
 */
public class AdRewardCashTasksView extends RelativeLayout implements View.OnClickListener {

    private TextView rewardTitle;
    private TextView  tvCashNumber;
    private LinearLayout llCashAnimationLayout;
    private RelativeLayout rlCountDownLayout;
    private RelativeLayout rlCountDownEndLayout;
    private RelativeLayout rlClickLayout;
    private CircleProgressView adTimeCdProgress;
    private TextView tvCountDown;
    private TextView tvTaskTitle;
    private XmLottieAnimationView tvCountDownEndBtn;
    private TextView tvClickTitle,tvClickBtn;

    private View countDown;

    private CanPauseCountDownTimer countDownTimer;

    private AnimatorSet animatorSet;

    private boolean isDoHideAnimation;
    private int curTime = 0;
    private int totalTime;
    private int closeTime, remainTime;
    private int maxRewardTimes = 4;
    private int rewardTimes = 2;
    private double cashBalance;
    private boolean isClickRewardType;
    private View.OnClickListener viewListener;
    private RewardExtraParams rewardExtraParams;
    private ICustomViewToActivityClickRewardExt.ICountDownFinishCalLBack countDownFinishCalLBack;

    public AdRewardCashTasksView(Context context) {
        super(context);
        initView();
    }

    public AdRewardCashTasksView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public AdRewardCashTasksView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        View inflate = LayoutInflater.from(getContext()).inflate(R.layout.host_ad_reward_cash_task_view, this, true);
        rewardTitle = (TextView)inflate.findViewById(R.id.reward_tip_title);
        tvCashNumber = (TextView)inflate.findViewById(R.id.tv_cash_number);
        llCashAnimationLayout = (LinearLayout)inflate.findViewById(R.id.ll_ad_cash_animation_layout);
        llCashAnimationLayout.setAlpha(0f);

        rlCountDownLayout = (RelativeLayout)inflate.findViewById(R.id.rl_count_down_layout);
        adTimeCdProgress = (CircleProgressView)inflate.findViewById(R.id.ad_time_cd_progress);
        tvCountDown = (TextView)inflate.findViewById(R.id.tv_count_down);

        rlCountDownEndLayout = (RelativeLayout)inflate.findViewById(R.id.rl_count_down_end_layout);
        tvTaskTitle = (TextView)inflate.findViewById(R.id.tv_task_title);
        tvCountDownEndBtn = (XmLottieAnimationView) inflate.findViewById(R.id.tv_count_down_btn);
        rlCountDownEndLayout.setOnClickListener(this);

        rlClickLayout = (RelativeLayout)inflate.findViewById(R.id.rl_click_layout);
        tvClickTitle = (TextView)inflate.findViewById(R.id.tv_click_to_next_title);
        tvClickBtn = (TextView) inflate.findViewById(R.id.tv_click_btn);

    }

    public void setCustomViewToActivity(ViewGroup contentLayout, RewardExtraParams rewardExtraParams,
                            View.OnClickListener onClickListener, ICustomViewToActivityClickRewardExt.ICountDownFinishCalLBack countDownFinishCalLBack){
        if (contentLayout == null) {
            return ;
        }
        if (rewardExtraParams != null){
            Logger.i("AdRewardCashTasksView", "setCustomViewToActivity  1  " );
            if( rewardExtraParams.getCanCloseTime() > 0) {
                Logger.i("AdRewardCashTasksView", "setCustomViewToActivity  有canCloseTime " );
                closeTime = rewardExtraParams.getCanCloseTime();
                totalTime = rewardExtraParams.getVideoPlayOverTime();
                if (totalTime > 0) {
                    closeTime = Math.min(closeTime, totalTime);
                }
                remainTime = closeTime;
            }
            maxRewardTimes = rewardExtraParams.getMaxRewardTimes();
            rewardTimes = rewardExtraParams.getRewardTimes();
            cashBalance = rewardExtraParams.getCashBalance();
            Logger.i("AdRewardCashTasksView", "setCustomViewToActivity  maxtimes = " + maxRewardTimes+ " rewardtimes = "+rewardTimes+" cashbalance = "+cashBalance );
        }
        countDown = new View((Activity) contentLayout.getContext());
        countDown.setId(R.id.host_reward_count_down_welfare_cash);
        this.viewListener = onClickListener;
        this.rewardExtraParams = rewardExtraParams;
        this.countDownFinishCalLBack = countDownFinishCalLBack;
        setRewardSuccessCallBackForThirdSDK();
    }

    public void setRewardSuccessCallBackForThirdSDK(){
        if (rewardExtraParams != null) {
            rewardExtraParams.setRewardThirdSDKSuccessCallBack(new RewardExtraParams.IRewardThirdSDKSuccessCallBack() {
                @Override
                public void onSdkRewardSuccess() {
                    Log.d("AdRewardCashTasksView", " onSdkRewardSuccess: end");
                    updateRewardTip();
                }
            });
        }
    }

    boolean hasDuration = false;
    public void onAdPlayProgress(long curPosition, long duration) {
        if (!hasDuration) {
            totalTime = (int) (duration / 1000);
            if (totalTime > 0) {
                closeTime = Math.min(closeTime, totalTime);
            }
            Logger.i("AdRewardCashTasksView", "onAdPlayProgress : totalTime = " + totalTime);
            if (rewardExtraParams != null && rewardExtraParams.getAdvertis() != null && rewardExtraParams.getAdvertis() instanceof AdUnLockVipTrackAdvertis) {
                AdUnLockVipTrackAdvertis advertis = (AdUnLockVipTrackAdvertis) rewardExtraParams.getAdvertis();
                if (advertis.getVideoDuration() == 0) {
                    advertis.setVideoDuration(totalTime);
                }
            }
            if (!isClickRewardType) {
                startCountdown();
            }
            hasDuration = true;
        }
    }

    public void setData(Advertis advertis, boolean isClickRewardType) {
        if (AdManager.isThirdAd(advertis) && !AdManager.isBaiduAd(advertis)) {
            Logger.i("AdRewardCashTasksView", "是三方sdk激励广告  不展示任务ui");
            return;
        }
        Logger.i("AdRewardCashTasksView", "isClickRewardType  解锁模式是点击 = " + isClickRewardType);
        this.isClickRewardType = isClickRewardType;
        if (cashBalance > 0 && !isShowedToday()) {
            llCashAnimationLayout.setVisibility(VISIBLE);
            tvCashNumber.setText(String.format("%s", cashBalance));
            HandlerManager.postOnUIThreadDelay(() -> showCashLayout(true),2000);
        }
        rewardTitle.setVisibility(INVISIBLE);
        if (isClickRewardType) {
            rewardTitle.setVisibility(VISIBLE);
            //点击模式
            showClickModel(advertis);
        } else {
            // 时长模式
            if (rewardExtraParams != null && AdManager.isThirdAd(rewardExtraParams.getAdType())) {
                startCountdown();
            }
        }
        setVisibility(VISIBLE);
    }

    //今天是否已经弹出过
    public boolean isShowedToday() {
        long lastShowTime = MmkvCommonUtil.getInstance(ToolUtil.getCtx()).getLong(PreferenceConstantsInHost.KEY_FREE_LISTEN_WELFARE_CASH_SHOW_TIME, 0);
        return !DateTimeUtil.isAnotherDay(lastShowTime);
    }

    public void updateCountDownByExternal(int remainTimeSeconds, int totalTimeSeconds) {
        if (getVisibility() != VISIBLE) {
            return;
        }
        if (remainTimeSeconds == 0) {
            if (rewardExtraParams != null) {
                rewardExtraParams.setCanCloseTime(RewardVideoCountDownStyleForVipFree.LAST_COUNT_DOWN_FINISH);
            }
            rlCountDownLayout.setVisibility(GONE);
            updateRewardTip();
            //发奖励
            onRewardSuccess(rewardExtraParams);
        } else {
            if (rlCountDownLayout.getVisibility() != VISIBLE) {
                rlCountDownLayout.setVisibility(VISIBLE);
            }
            if (rewardTitle.getVisibility() != VISIBLE) {
                rewardTitle.setVisibility(VISIBLE);
            }
            tvCountDown.setText(String.valueOf(remainTimeSeconds));
            int progress= (totalTimeSeconds - remainTimeSeconds) * 100 / totalTimeSeconds;
            adTimeCdProgress.setProgress(progress,true);
        }
    }

    private void startCountdown() {
        if (totalTime == 0) {
            return;
        }
        rlCountDownLayout.setVisibility(VISIBLE);
        tvCountDown.setText(String.valueOf(totalTime));
        rewardTitle.setVisibility(VISIBLE);
        curTime = 0;
        Logger.i("AdRewardCashTasksView", "startCountdown : totalTime = " + totalTime);
        cancelCountdown();
        countDownTimer = new CanPauseCountDownTimer((totalTime > 0 ? totalTime : closeTime) * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                curTime++;
                remainTime = closeTime - curTime;
                Logger.i("AdRewardCashTasksView", "onTick : remainTime = " + remainTime+ " curTime = "+curTime+ " totalTime = "+totalTime);
                if (remainTime > 0) {
                    tvCountDown.setText(String.valueOf(remainTime));
                    int progress= curTime * 100 / closeTime;
                    adTimeCdProgress.setProgress(progress,true);
                }else if(remainTime == 0){
                    if (rewardExtraParams != null) {
                        rewardExtraParams.setCanCloseTime(RewardVideoCountDownStyleForVipFree.LAST_COUNT_DOWN_FINISH);
                    }
                    AdUnLockTimeManagerV3.getInstance().onCountDownFinish();
                    if (countDownFinishCalLBack != null) {
                        countDownFinishCalLBack.onCountDownFinish();
                    }
                    rlCountDownLayout.setVisibility(GONE);
                    updateRewardTip();
                    //发奖励
                    onRewardSuccess(rewardExtraParams);
                }
            }

            @Override
            public void onFinish() {

            }
        };
        countDownTimer.start();
    }

    public void updateRewardTip() {
        if (rlCountDownEndLayout == null || rewardTitle == null) {
            return;
        }
        //剩余任务数
        int remainTasks = maxRewardTimes - rewardTimes;
        if (remainTasks > 0) {
            rlCountDownEndLayout.setVisibility(VISIBLE);
            rewardTitle.setText(String.format("还剩%s个", remainTasks));
            rewardTitle.setVisibility(VISIBLE);
            if (tvCountDownEndBtn != null && !tvCountDownEndBtn.isAnimating()) {
                tvCountDownEndBtn.playAnimation();
            }
        } else {
            setVisibility(GONE);
        }
    }

    private void onRewardSuccess(RewardExtraParams rewardExtraParams) {
        if (rewardExtraParams != null) {
            rewardExtraParams.setRewardBeforeClose(true);
        }
        if (viewListener != null) {
            viewListener.onClick(countDown);
        }
    }

    private void showClickModel(Advertis advertis){
        rlCountDownEndLayout.setVisibility(GONE);
        rlCountDownLayout.setVisibility(GONE);
        rlClickLayout.setVisibility(VISIBLE);
        tvClickTitle.setText(String.format("点击后\n浏览%s秒", advertis.getTipStayTime() > 0 ? advertis.getTipStayTime() : 5));
    }

    private void showCashLayout(boolean show){
        // 一起执行动画
        animatorSet = new AnimatorSet();

        // 创建一个从0到150dp的动画
        int start = BaseUtil.dp2px(getContext(), show ? 60 : 150);
        int end = BaseUtil.dp2px(getContext(), show ? 150 : 60);
        ValueAnimator animator = ValueAnimator.ofInt(start, end);
        // 创建一个透明度为0到1的动画
        ValueAnimator animator2 = ValueAnimator.ofFloat(show ? 0f : 1f, show ? 1f : 0f);
        animator.addUpdateListener(animation -> {
            int animatedValue = (Integer) animation.getAnimatedValue();
            ViewGroup.LayoutParams layoutParams = llCashAnimationLayout.getLayoutParams();
            layoutParams.width= animatedValue;
            llCashAnimationLayout.setLayoutParams(layoutParams);
        });
        animator2.addUpdateListener(animation -> {
            float animatedValue = (float) animation.getAnimatedValue();
            llCashAnimationLayout.setAlpha(animatedValue);
        });
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                // 动画结束时，隐藏动画布局
                if (show && !isDoHideAnimation) {
                    isDoHideAnimation = true;
                    HandlerManager.postOnUIThreadDelay(() -> showCashLayout(false), 3000);
                } else {
                    llCashAnimationLayout.setVisibility(GONE);
                }
            }
        });
        animatorSet.playTogether(animator,animator2);
        // 动画持续时间
        animatorSet.setDuration(600); // 500毫秒
        animatorSet.start();
        MmkvCommonUtil.getInstance(ToolUtil.getCtx()).saveLong(
                PreferenceConstantsInHost.KEY_FREE_LISTEN_WELFARE_CASH_SHOW_TIME, System.currentTimeMillis());
    }

    public void onAdClick() {
        if (rewardExtraParams == null || !AdUnlockUtil.needCalculateJumpDuration(rewardExtraParams.getAdvertis())) {
            onAdCanReward();
        }
    }
    public void onAdCanReward(){
        if (rlClickLayout == null) {
            return;
        }
        //当前激励视频已经领取过奖励了，就不要在领取了
        if (rewardExtraParams != null && rewardExtraParams.isRewardBeforeClose()) {
            return;
        }
        if (rewardExtraParams != null) {
            rewardExtraParams.setRewardBeforeClose(true);
        }
        rlClickLayout.setVisibility(GONE);
        updateRewardTip();
        onRewardSuccess(rewardExtraParams);
    }

    public CanPauseCountDownTimer getCountDownTimer() {
        return countDownTimer;
    }

    public void cancelCountdown() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        if (animatorSet != null) {
            animatorSet.cancel();
        }
        if (tvCountDownEndBtn != null) {
            tvCountDownEndBtn.cancelAnimation();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.rl_count_down_end_layout) {
            //跳转到下一个激励视频页面
            if (rewardExtraParams != null && rewardExtraParams.getRewardNextVideoCallBack() != null) {
                rewardExtraParams.setRewardTimes(rewardTimes + 1);
                rewardExtraParams.setRewardGoNextTimes(rewardExtraParams.getRewardGoNextTimes() + 1);
                rewardExtraParams.setCanReward(false);
                rewardExtraParams.setRewardBeforeClose(false);
                rewardExtraParams.getRewardNextVideoCallBack().jumpToNextVideo((Activity) getContext(), rewardExtraParams);
            }
        }
    }

    public void showAnimation() {

    }

    public void cancelAnimation() {

    }
}
