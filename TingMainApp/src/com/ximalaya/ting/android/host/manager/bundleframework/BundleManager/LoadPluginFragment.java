package com.ximalaya.ting.android.host.manager.bundleframework.BundleManager;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.bundleframework.download.BaseBundleDownloadTask;
import com.ximalaya.ting.android.host.manager.bundleframework.download.BundleDownloadManager;
import com.ximalaya.ting.android.host.manager.bundleframework.download.IBundleDownloadCallBack;
import com.ximalaya.ting.android.host.view.XmLottieAnimationView;

/**
 * Created by luhang on 2020/9/8.
 *
 * <AUTHOR>
 * Email: <EMAIL>
 * Tel:15918812121
 */
public class LoadPluginFragment extends BaseDialogFragment implements IBundleDownloadCallBack {
    private String bundleName;
    private Handler handler;

    private TextView mTvPercent;
    private ProgressBar mPbDownloading;
    private long start = 0L;
    private OnClose onClose;


    public static LoadPluginFragment newInstance(String bundleName) {
        LoadPluginFragment fragment = new LoadPluginFragment();
        Bundle bundle = new Bundle();
        bundle.putString("bundleName", bundleName);
        fragment.setArguments(bundle);
        return fragment;
    }

    public void setOnClose(OnClose onClose) {
        this.onClose = onClose;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            this.bundleName = getArguments().getString("bundleName");
        }
        BundleDownloadManager.getInstance().addBundleDownloadListenerPriority(this);
        start = System.currentTimeMillis();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (getDialog() != null) {
            getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
            getDialog().setCanceledOnTouchOutside(false);
            getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            getDialog().getWindow().setDimAmount(0f);
        }
        return inflater.inflate(getContainerLayoutId(), container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initUi(view, savedInstanceState);
    }

    @Override
    public void onActivityCreated(Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        if (getDialog() != null) {
            getDialog().getWindow().getAttributes().windowAnimations = R.style.host_plugin_loading_page_anim;
        }
    }

    public LoadPluginFragment() {
        handler = new Handler(Looper.getMainLooper());
    }

    //    private View mLoadingView2; // 只是用于loadingview的一个缓存变量，不可直接使用这个变量进行操作
    private XmLottieAnimationView mLoadingLottieView;

    protected void initUi(View view, Bundle savedInstanceState) {
//        setTitle(R.string.host_load_plugin);
        mTvPercent = view.findViewById(R.id.host_tv_bundle_percent);
        mPbDownloading = view.findViewById(R.id.host_pb_bundle_downloading);

        try {
//            mLoadingView2 = View.inflate(getActivity(), R.layout.host_loading_view_progress, null);
            mLoadingLottieView = view.findViewById(R.id.host_loading_plugin_view_progress_xmlottieview);
            mLoadingLottieView.setImageAssetsFolder("lottie/host_loading/");
            mLoadingLottieView.setAnimation("lottie/host_loading/loading.json");
            mLoadingLottieView.loop(true);

//            mLoadingView2.setVisibility(View.VISIBLE);
            mLoadingLottieView.setVisibility(View.VISIBLE);
//            mLoadingView2.setBackgroundColor(getResourcesSafe().getColor(R.color.host_blue_b34990e2));

//            RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(
//                    ViewGroup.LayoutParams.WRAP_CONTENT,
//                    ViewGroup.LayoutParams.WRAP_CONTENT);
//            lp.addRule(RelativeLayout.CENTER_IN_PARENT);
//            lp.addRule(RelativeLayout.ABOVE, mTvPercent.getId());
//
//            ((RelativeLayout) rootView).addView(mLoadingView2, 0, lp);

            mLoadingLottieView.setProgress(0f);
            mLoadingLottieView.playAnimation();
        } catch (Exception e) {
            e.printStackTrace();
            //https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/4308297?pid=1
        }
    }

    public void close(String tips) {
        if (Looper.myLooper() != Looper.getMainLooper() && handler != null) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    finishLoading(tips);
                }
            });
        } else {
            finishLoading(tips);
        }
    }

    public int getContainerLayoutId() {
        return R.layout.host_fra_load_plugin;
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (onClose != null) {
            onClose.onClose();
        }
        handler = null;
        onClose = null;
        try {
            if (mLoadingLottieView != null) {
                mLoadingLottieView.cancelAnimation();
            }
        } catch (Exception e) {
        }
//        onPageLoadingCompleted(LoadCompleteType.OK);
        BundleDownloadManager.getInstance().removeBundleDownloadListener(this);
    }

    @Override
    public void onStartDownloadBundle(BaseBundleDownloadTask baseBundleDownloadTask) {
        if (baseBundleDownloadTask.bundleName.equals(bundleName)) {
            if (mPbDownloading == null) {
                return;
            }
            mPbDownloading.post(new Runnable() {
                @Override
                public void run() {
                    updateProgress(baseBundleDownloadTask.mProgress);
                }
            });
        }
    }

    @Override
    public void onCompleteDownloadBundle(BaseBundleDownloadTask baseBundleDownloadTask) {
        try {
            if (!baseBundleDownloadTask.bundleName.equals(this.bundleName)) {// 避免被其他下载的监听回调干扰
                return;
            }
            handler.post(() -> {
                updateProgress(100);
            });
            dismissAllowingStateLoss();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void onFailedDownloadBundle(BaseBundleDownloadTask baseBundleDownloadTask, Throwable e) {

        try {
            if (!baseBundleDownloadTask.bundleName.equals(this.bundleName)) {
                return;
            }
            close("插件下载失败,请检查您的网络！");
            if (e != null) {
                e.printStackTrace();
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

    @Override
    public void onUpdateDownloadBundle(BaseBundleDownloadTask baseBundleDownloadTask) {
        if (baseBundleDownloadTask.bundleName.equals(bundleName)) {
            if (mPbDownloading == null) {
                return;
            }
            mPbDownloading.post(new Runnable() {
                @Override
                public void run() {
                    updateProgress(baseBundleDownloadTask.mProgress);
                }
            });
        }
    }

    @Override
    public void onPauseDownloadBundle(BaseBundleDownloadTask baseBundleDownloadTask) {
    }

    public void finishLoading(String tips) {
        if (!isAdded()) {
            try {
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        dismissAllowingStateLoss();
                        showTips(tips);
                    }
                }, 1200);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            long t = System.currentTimeMillis() - start;
            if (t < 1200) {
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        dismissAllowingStateLoss();
                        showTips(tips);
                    }
                }, 1200 - t);
            } else {
                dismissAllowingStateLoss();
                showTips(tips);
            }

        }
    }

    private void showTips(String tips) {
        if (!TextUtils.isEmpty(tips)) {
            CustomToast.showFailToast(tips);
        }
    }

    private void updateProgress(int progress) {
        if (mPbDownloading == null) {
            return;
        }
        mPbDownloading.setProgress(progress);
        String proStr = "加载中 " + progress + "%";
        mTvPercent.setText(proStr);
    }

    interface OnClose {
        void onClose();
    }
}
