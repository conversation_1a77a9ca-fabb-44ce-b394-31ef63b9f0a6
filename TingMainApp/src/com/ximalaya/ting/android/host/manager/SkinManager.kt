package com.ximalaya.ting.android.host.manager

import android.graphics.Color
import android.text.TextUtils
import com.google.gson.Gson
import com.ximalaya.ting.android.configurecenter.ConfigureCenter
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.manager.GlobalGrayManager
import com.ximalaya.ting.android.framework.util.FileUtil
import com.ximalaya.ting.android.host.MainApplication
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.manager.childprotect.ChildProtectManager
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.manager.tabfragment.TabFragmentManager
import com.ximalaya.ting.android.host.model.skin.AtmosphereInfo
import com.ximalaya.ting.android.host.model.skin.AtmosphereType
import com.ximalaya.ting.android.host.model.skin.SkinBottomIcon
import com.ximalaya.ting.android.host.model.skin.SkinSettingInfo
import com.ximalaya.ting.android.host.util.ColorUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.main.util.MyListenAbUtil
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask
import com.ximalaya.ting.android.player.MD5
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager
import com.ximalaya.ting.android.xmutil.Logger
import java.io.File

/**
 * Created by WolfXu on 2020-08-18.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
object SkinManager {
    // object修饰的类，成员变量全都加上了static

    private val TAG = SkinManager::class.java.simpleName

    private const val REQUEST_SKIN_SETTING_INTERNAL = 5 * 60 * 1000 // 请求接口的时间间隔限制

    private var mLastUpdateTime: Long = 0
    private var mMainColor = ColorUtil.INVALID_COLOR
    private var mBottomColor = ColorUtil.INVALID_COLOR
    private var mBottomBgUrl = ""
    private val mListeners : MutableSet<ISkinSettingChangeListener> = mutableSetOf()
    private var mBottomIconMap : Map<Int, SkinBottomIcon?>? = null
    private var mBottomAtmosphereIconMap: Map<Int, SkinBottomIcon?>? = null
    private var mHasLoadedSuccessfullyFromNetwork = false
    private var mIsChildMode = ChildProtectManager.isChildMode(BaseApplication.getMyApplicationContext())
    private var mHasSkinData = false
    private var mAtmosphereInfo: AtmosphereInfo? = null
    private var tabConstants = listOf(
        TabFragmentManager.TAB_HOME_PAGE,
        TabFragmentManager.TAB_VIP,
        TabFragmentManager.TAB_FINDING,
        TabFragmentManager.TAB_MY_AND_LISTEN
    )
    val mainColor get() = mMainColor
    val bottomColor get() = mBottomColor
    val bottomBgUrl get() = mBottomBgUrl
    val atmosphereInfo get() = mAtmosphereInfo

    init {
        if (MyListenAbUtil.isKeepSubscribeInMine()) {
            tabConstants = listOf(
                TabFragmentManager.TAB_HOME_PAGE,
                TabFragmentManager.TAB_CATEGORY,
                TabFragmentManager.TAB_VIP,
                TabFragmentManager.TAB_MY_AND_LISTEN
            )
        } else {
            tabConstants = listOf(
                TabFragmentManager.TAB_HOME_PAGE,
                TabFragmentManager.TAB_VIP,
                TabFragmentManager.TAB_FINDING,
                TabFragmentManager.TAB_MY_AND_LISTEN
            )
        }
    }
    fun requestSkin() {
        val requestJsonCache = getRequestJsonCache()
        if (!TextUtils.isEmpty(requestJsonCache) && inAtmosphereExpTimeMs()) {
            LoadSkinSettingFromLocalTask(object : IDataCallBack<SkinSettingInfo> {
                override fun onSuccess(skinInfo: SkinSettingInfo?) {
                    skinInfo?.let {
                        it.atmosphereInfo?.xmRequestId = XmRequestIdManager.getInstance(ToolUtil.getCtx()).requestId
                    }
                    handleSkinSettingLoaded(skinInfo)
                }

                override fun onError(code: Int, message: String?) {
                }
            }).myexec()
        }
        // 做下时间间隔限制，避免过于频繁的请求
        val curTime = System.currentTimeMillis()
        if (curTime - mLastUpdateTime > REQUEST_SKIN_SETTING_INTERNAL) {
            mLastUpdateTime = curTime
            CommonRequestM.getSkinSettingInfo(object : IDataCallBack<SkinSettingInfo> {
                override fun onSuccess(skinInfo: SkinSettingInfo?) {
                    skinInfo?.let {
                        it.atmosphereInfo?.xmRequestId =
                            XmRequestIdManager.getInstance(ToolUtil.getCtx()).requestId
                    }
                    mHasLoadedSuccessfullyFromNetwork = true
                    val dataJson: String? = handleSkinSettingLoaded(skinInfo)
                    SaveSkinSettingToLocalTask().myexec(dataJson)
                }

                override fun onError(code: Int, message: String?) {
                    Logger.i(TAG, "failed to load skin info $message")
                    if (!mHasLoadedSuccessfullyFromNetwork) {
                        // 加载失败，且没有成功从服务端加载成功过，尝试加载缓存
                        LoadSkinSettingFromLocalTask(object : IDataCallBack<SkinSettingInfo> {
                            override fun onSuccess(skinInfo: SkinSettingInfo?) {
                                skinInfo?.let {
                                    it.atmosphereInfo?.xmRequestId =
                                        XmRequestIdManager.getInstance(ToolUtil.getCtx()).requestId
                                }
                                handleSkinSettingLoaded(skinInfo)
                            }

                            override fun onError(code: Int, message: String?) {
                            }
                        }).myexec()
                    }
                }
            })
        }
    }

    fun saveRequestJsonCache(json: String) {
        MMKVUtil.getInstance().saveString("key_home_page_data_cache", json)
    }

    private fun getRequestJsonCache(): String {
        return MMKVUtil.getInstance().getString("key_home_page_data_cache","")
    }

    private fun inAtmosphereExpTimeMs(): Boolean {
        val expTimeMs = ConfigureCenter.getInstance().getString(
            CConstants.Group_toc.GROUP_NAME,
            "app_123_bottom_atmosphere_exp_time_ms",
            "0"
        ).toLong()
        return System.currentTimeMillis() < expTimeMs
    }

    fun onRecommendFragmentVisibleChange(isVisible: Boolean) {
        if (isNeedGrayInRecommendFragment() == null || isNeedGrayInRecommendFragment() == false) {
            return
        }
        val activity = MainApplication.getMainActivity()
        if (isVisible) {
            GlobalGrayManager.initGlobalGray(true)
            GlobalGrayManager.setGlobalGrayNotCheck(activity?.window)
        } else {
            GlobalGrayManager.initGlobalGray(false)
            GlobalGrayManager.cancelGlobalGray(activity?.window)
        }
    }

    private fun isNeedGrayInRecommendFragment(): Boolean?{
        return mAtmosphereInfo?.let { it.isExist && (it.configType == AtmosphereType.normalSacrifice.num) }
    }

    fun isTabTitleColorDark(): Boolean {
        mAtmosphereInfo?.let {
            return it.topTabTitleColor == 1
        }
        return false
    }

    private fun handleSkinSettingLoaded(skinInfo: SkinSettingInfo?): String? {
        var mainColorNew = ColorUtil.INVALID_COLOR
        var bottomColorNew = ColorUtil.INVALID_COLOR
        var bottomBgUrlNew = ""
        var bottomIconMapNew: Map<Int, SkinBottomIcon?>? = null
        var bottomIconAtmosphereMapNew: Map<Int, SkinBottomIcon?>? = null
        var dataJson: String? = null
        var atmosphereInfo: AtmosphereInfo? = null
        mHasSkinData = false
        skinInfo?.let { it ->
            it.atmosphereInfo?.apply {
                if (it.atmosphereInfo?.isExist == true) {
                    atmosphereInfo = it.atmosphereInfo
                }
            }
            if (it.exist) {
                it.backgroundInfo?.apply {
                    headBackgroundInfo?.let { mainColorInfo->
                        if (mainColorInfo.hasColor) {
                            try {
                                mainColorNew = Color.parseColor(mainColorInfo.color)
                            } catch (e: Exception) {
                                Logger.e(e)
                            }
                        }
                    }
                    bottomBackgroundInfo?.let { colorInfo->
                        if (colorInfo.hasColor) {
                            try {
                                bottomColorNew = Color.parseColor(colorInfo.color)
                            } catch (e: Exception) {
                                Logger.e(e)
                            }
                        } else if (colorInfo.hasBackgroundPic) {
                            bottomBgUrlNew = colorInfo.backgroundPic ?: ""
                        }
                    }
                }
                bottomIconMapNew = it.bottomIconInfos?.filterNotNull()?.associate { iconInfo -> iconInfo.tabType to iconInfo.bottomIcon }
                mHasSkinData = true
            }
            it.bottomAtmospheres?.let { list ->
                if (list.size == 4) {
                    bottomIconAtmosphereMapNew = tabConstants.withIndex()
                        .associate { (index, tab) -> tab to it.bottomAtmospheres?.getOrNull(index)?.bottomIcon }
                        .toMutableMap()
                }
            }
            dataJson = it.json
        }
        if (mMainColor != mainColorNew) {
            mMainColor = mainColorNew
            mListeners.forEach { it.onMainColorChanged() }
        }
        if (!isBottomIconMapEqual(mBottomIconMap, bottomIconMapNew)) {
            mBottomIconMap = bottomIconMapNew
            mListeners.forEach { it.onBottomIconChanged() }
        }
        if (mBottomColor != bottomColorNew) {
            mBottomColor = bottomColorNew
            mListeners.forEach { it.onBottomColorChanged() }
        } else if (mBottomBgUrl != bottomBgUrlNew) {
            mBottomBgUrl = bottomBgUrlNew
            mListeners.forEach { it.onBottomColorChanged() }
        }

        if (!isBottomIconMapEqual(mBottomAtmosphereIconMap, bottomIconAtmosphereMapNew)) {
            mBottomAtmosphereIconMap = bottomIconAtmosphereMapNew
            mListeners.forEach { it.onBottomIconChanged() }
            mListeners.forEach { it.onBottomColorChanged() }
        }

        if ((atmosphereInfo?.equals(mAtmosphereInfo) != true)
                                    && (mAtmosphereInfo != atmosphereInfo)) {
            handleGlobalGrayChange(atmosphereInfo)
            mAtmosphereInfo = atmosphereInfo
            mListeners.forEach { it.onAtmosphereInfoChanged() }
        } else {
            if (atmosphereInfo == null) {
                GlobalGrayManager.setGlobalGray(false)
                if (GlobalGrayManager.isGlobalGray()) {
                    GlobalGrayManager.initGlobalGray(false)
                    var activity = MainApplication.getMainActivity()
                    GlobalGrayManager.cancelGlobalGray(activity?.window)
                }
            }
        }

        BottomTabManager.dealWithNoReadMessage(BottomTabManager.mNoReadModel)
        return dataJson
    }

    fun isNeedShowAtmosphereImage(): Boolean {
        return !getTopBgImage().isNullOrEmpty()
    }

    fun getTopBgImage(): String{
        mAtmosphereInfo?.let {
            if (it.configType == AtmosphereType.advertise.num
                    || it.configType == AtmosphereType.festival.num) {
                return it.topBackgroundUrl
            }
        }
        return ""
    }

    private fun handleGlobalGrayChange(atmosphereInfo: AtmosphereInfo?) {
        if (mAtmosphereInfo?.configType != atmosphereInfo?.configType) {
            val activity = MainApplication.getMainActivity()
            if (atmosphereInfo?.configType == AtmosphereType.greatSacrifice.num) {
                GlobalGrayManager.setGlobalGray(true)
                activity?.apply {
                    GlobalGrayManager.globalGray(activity.window)
                }
            } else {
                GlobalGrayManager.setGlobalGray(false)
                GlobalGrayManager.cancelGlobalGray(activity?.window)
            }
        }

    }
    fun hasValidMainColor() : Boolean {
        return mMainColor != ColorUtil.INVALID_COLOR && shouldShowSkin()
    }

    fun hasValidBottomColor() : Boolean {
        return mBottomColor != ColorUtil.INVALID_COLOR && shouldShowSkin()
    }

    fun hasValidBottomBg() : Boolean {
        return mBottomBgUrl.isNotEmpty() && shouldShowSkin()
    }

    fun canSetAtmosphereBottomTab() : Boolean {
        return shouldShowSkin() && mBottomAtmosphereIconMap?.size == 4
    }

    // 青少年和大字模式不展示
    private fun shouldShowSkin(): Boolean {
        return !mIsChildMode
                && !MMKVUtil.getInstance().getBoolean(PreferenceConstantsInHost.KEY_IS_ELDERLY_MODE, false)
    }

    fun getSkinBottomIcon(tabType : Int) : SkinBottomIcon? {
        if (mIsChildMode) {
            return null
        }
        if (tabType == TabFragmentManager.TAB_VIP && NovelTabAbManager.showVipTab()) {
            val tempVip = VipBottomTabIconManager.getAdaptSkinBottomIcon()
            val skin = mBottomIconMap?.get(tabType)
            if (skin != null && tempVip != null) {
                tempVip.charactersCheckedColor = skin.charactersCheckedColor
                tempVip.charactersUnCheckedColor = skin.charactersUnCheckedColor
            }
            return tempVip
        }
        return mBottomIconMap?.get(tabType)
    }

    fun getBottomAtmosphereIcon(tabType : Int) : SkinBottomIcon? {
        if (mIsChildMode) {
            return null
        }
        return mBottomAtmosphereIconMap?.get(tabType)
    }

    fun addSkinSettingChangeListener(listener : ISkinSettingChangeListener) {
        mListeners.add(listener)
    }

    fun removeSkinSettingChangeListener(listener: ISkinSettingChangeListener) {
        mListeners.remove(listener)
    }

    private fun isBottomIconMapEqual(map1 : Map<Int, SkinBottomIcon?>?, map2 : Map<Int, SkinBottomIcon?>?) : Boolean {
        if (map1 == null && map2 == null) {
            return true
        } else if (map1 == null) {
            return false
        } else if (map2 == null) {
            return false
        }
        if (map1.size != map2.size) {
            return false
        }

        map1.forEach {
            var isBottomIconEqual = false
            if (map2.containsKey(it.key)) {
                val skinBottomIcon = map2[it.key]
                if (skinBottomIcon != null) {
                    isBottomIconEqual = skinBottomIcon == it.value
                } else if (it.value == null) {
                    isBottomIconEqual = true
                }
            }
            if (!isBottomIconEqual) {
                return false
            }
        }
        return true
    }

    fun modeChange() {
        VipBottomTabIconManager.modeChange()
        mIsChildMode = ChildProtectManager.isChildMode(BaseApplication.getMyApplicationContext())
        if (!mHasSkinData) {
            return
        }
        mListeners.forEach {
            it.onMainColorChanged()
            it.onBottomIconChanged()
            it.onBottomColorChanged()
        }
    }

    private class SaveSkinSettingToLocalTask : MyAsyncTask<String?, Void?, Void?>() {
        override fun doInBackground(vararg params: String?): Void? {
            if (params.isNotEmpty()) {
                val context = MainApplication.getMyApplicationContext()
                if (context != null) {
                    val file = File(context.cacheDir,
                            MD5.md5(UrlConstants.getInstanse().skinSettingInfoUrl))
                    // 如果内容是空的，把缓存文件删掉
                    if (params[0].isNullOrEmpty()) {
                        if (file.exists() && file.isFile) {
                            file.delete()
                        }
                    } else {
                        FileUtil.writeStr2File(params[0], file.absolutePath)
                    }
                }
            }
            return null
        }
    }

    private class LoadSkinSettingFromLocalTask internal constructor(callBack : IDataCallBack<SkinSettingInfo>)
        : MyAsyncTask<Void?, Void?, SkinSettingInfo?>() {

        val mCallBack = callBack

        override fun doInBackground(vararg params: Void?): SkinSettingInfo? {
            val context = MainApplication.getMyApplicationContext()
            if (context != null) {
                val absolutePath = File(context.cacheDir
                        , MD5.md5(UrlConstants.getInstanse().skinSettingInfoUrl)).absolutePath
                val json = FileUtil.readStrFromFile(absolutePath)
                if (!TextUtils.isEmpty(json)) {
                    try {
                        return Gson().fromJson<SkinSettingInfo>(json, SkinSettingInfo::class.java)
                    } catch (e : Exception) {
                        FileUtil.deleteDir(absolutePath)
                        Logger.e(e)
                    }
                }
            }
            return null
        }

        override fun onPostExecute(result: SkinSettingInfo?) {
            mCallBack.onSuccess(result)
        }
    }
}

interface ISkinSettingChangeListener {
    fun onMainColorChanged()

    fun onBottomColorChanged()

    fun onBottomIconChanged()

    fun onAtmosphereInfoChanged()
}

open class SkinSettingChangeWrapListener : ISkinSettingChangeListener{
    override fun onMainColorChanged() {
    }

    override fun onBottomColorChanged() {
    }

    override fun onBottomIconChanged() {
    }

    override fun onAtmosphereInfoChanged() {
    }
}