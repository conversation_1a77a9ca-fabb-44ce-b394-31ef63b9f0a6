package com.ximalaya.ting.android.host.manager

import androidx.fragment.app.FragmentManager
import com.google.gson.annotations.SerializedName
import com.ximalaya.ting.android.firework.FireworkApi
import com.ximalaya.ting.android.firework.FireworkDebugger
import com.ximalaya.ting.android.firework.model.Firework
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.framework.util.BaseUtil
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost
import com.ximalaya.ting.android.host.fragment.other.ChildProtectDialogFragment
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager
import com.ximalaya.ting.android.host.manager.handler.HandlerManager
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.GsonUtils
import com.ximalaya.ting.android.host.util.common.DateTimeUtil
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil
import com.ximalaya.ting.android.xmabtest.ABTest
import com.ximalaya.ting.android.xmutil.Logger
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by WolfXu on 2021/10/15.
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
object ChildProtectDialogManager {

    enum class Position {
        HOMEPAGE,
        LIVE_TAB
    }

    enum class ShowTimeType {
        DELAY, // 延迟几秒出
        SLIDE, // 推荐页活动多少屏后出
        CONTROL // 直接出
    }

    private val TAG = ChildProtectDialogManager::class.java.simpleName

    // 未成年模式弹窗保证所有位置在内一天最多只出一次
    private const val MIN_SHOW_LIMIT_DAYS: Int = 1
    var showTimeType: ShowTimeType = ShowTimeType.CONTROL
        private set
    var showTimeExtra = 0.0
        private set
    private var mDialogWaitingShow = false

    //未成年弹窗专辑数据
    var childProtectDialogAlbum: ChildProtectDialogAlbum? = null

    private var mIsRecommendFragmentVisible = false
    private var mIsMainActivityVisible = true
    @Volatile
    private var mIsRequestingDialogInfo = false

    var showUnderAbControl = true
        private set

    fun checkShowDialog(position: Position): Boolean {
        val newUserActiveTime = MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getLong(
                PreferenceConstantsInHost.KEY_NEW_USER_ACTIVE_TIME_FOR_CHILD_PROTECT_DIALOG, 0)
        // 新用户首次激活后当天不出未成年弹窗
        if (DateTimeUtil.calNaturalDiffDay(newUserActiveTime) < 1) {
            return false
        }
        // 每次生命周期只请求一次接口与iOS保持同步
        if (childProtectDialogAlbum != null) {
            return false
        }

        // 首次安装不出未成年弹窗
        if (ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext())) {
            return false
        }

        return true
    }

    fun markDialogShow() {
        saveShowTime(System.currentTimeMillis())
    }

    private fun saveShowTime(time: Long) {
        MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveLong(
                PreferenceConstantsInHost.KEY_CHILD_PROTECT_DIALOG_LAST_SHOW_TIME, time)
    }

    fun previewChildProtectDialog(fragmentManager: FragmentManager, id: String) {
        val params = HashMap<String, String>()
        params["id"] = id
        CommonRequestM.getData(UrlConstants.getInstanse().protectDialogPreviewUrl, params,
            ChildProtectDialogAlbum::class.java,
            object : IDataCallBack<ChildProtectDialogAlbum> {
                override fun onSuccess(data: ChildProtectDialogAlbum?) {
                    Logger.i(TAG, "requestRecommendAlbumInfo onSuccess $data")
                    childProtectDialogAlbum = data
                    ChildProtectDialogFragment.getInstance("preview")
                        .show(fragmentManager, "child_protect")
                }

                override fun onError(code: Int, message: String?) {
                }
            })
    }

    fun requestDialogInfo(callBack: IDataCallBack<ChildProtectDialogInfo>) {
        FireworkDebugger.log("未成年弹窗, requestDialogInfo s1")

        if (mIsRequestingDialogInfo) {
            callBack.onError(-11, "正在请求，请稍等")
            return
        }
        mIsRequestingDialogInfo = true
        val url = ToolUtil.addTsToUrl(UrlConstants.getInstanse().childProtectDialogInfoUrl)
        val params = HashMap<String, String>()
        params["date"] = getDateInt().toString()

        FireworkDebugger.log("未成年弹窗, requestDialogInfo s2")

        CommonRequestM.getData(url, params, ChildProtectDialogInfo::class.java,
                object : IDataCallBack<ChildProtectDialogInfo> {
            override fun onSuccess(`object`: ChildProtectDialogInfo?) {
//                Logger.i(TAG, "requestDialogInfo onSuccess $`object`")
                FireworkDebugger.log("未成年弹窗, requestDialogInfo onSuccess")
                mIsRequestingDialogInfo = false
                callBack.onSuccess(`object`)
            }

            override fun onError(code: Int, message: String?) {
                mIsRequestingDialogInfo = false
                callBack.onError(code, message)
            }
        })
    }

    fun showDialog(toMergeFirework: Firework?, callBack: IShowResult?) {
        // 如果当前不再首页-推荐页，则直接显示未成年弹窗。否则根据配置中心决定显示时机。当离开首页-推荐页时，也立即显示弹窗。

        FireworkDebugger.log("未成年弹窗, showDialog showTimeType: ${showTimeType}, mIsRecommendFragmentVisible: ${mIsRecommendFragmentVisible}")

        loadShowTimeConfig()
        if (showTimeType == ShowTimeType.CONTROL || !mIsRecommendFragmentVisible) {
            requestShowDialogWithHomePage(callBack)
        } else {
            mDialogWaitingShow = true
            if (showTimeType == ShowTimeType.DELAY) {
                HandlerManager.postOnUIThreadDelay4Kt((showTimeExtra * 1000).toLong()) {
                    showDialogIfNeeded(callBack)
                }
            } else {
                callBack?.onShown(false)
            }
        }
    }

    //预览未成年弹窗，需要去掉不必要的检查
    fun showDialogForPreview(toMergeFirework: Firework?) {
        loadShowTimeConfig()
        realShowDialogWithFireworkCheck(null)
    }

    fun showDialogForPreview() {
        loadShowTimeConfig()
        realShowDialogWithFireworkCheck(null)
    }

    private fun mockMergeInfo(): Firework? {
        val json = "{\n" +
                "\t\t\t\"buttons\": [{\n" +
                "\t\t\t\t\"action\": \"\",\n" +
                "\t\t\t\t\"jumpType\": 0,\n" +
                "\t\t\t\t\"jumpUrl\": \"iting://open?msg_type=14&url=http%3A%2F%2Fwww.baidu.com\",\n" +
                "\t\t\t\t\"x1\": 0.7,\n" +
                "\t\t\t\t\"x2\": 0.98,\n" +
                "\t\t\t\t\"y1\": 0.66,\n" +
                "\t\t\t\t\"y2\": 0.93\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"contentType\": 12,\n" +
                "\t\t\t\"expireMilliseconds\": 1468802000,\n" +
                "\t\t\t\"id\": 4279,\n" +
                "\t\t\t\"jumpType\": 1,\n" +
                "\t\t\t\"name\": \"zsx半浮层合并半浮层\",\n" +
                "\t\t\t\"prevFireworkId\": 0,\n" +
                "\t\t\t\"resCallback\": \"\",\n" +
                "\t\t\t\"resource\": {\n" +
                "\t\t\t\t\"backImage\": \"https://audiotest.cos.tx.xmcdn.com/storages/32b0-audiotest/F6/05/GKwaO4wIDs9FAAMXEQAAofnj.png\",\n" +
                "\t\t\t\t\"backImageMd5\": \"05b7c20542f370130219989bf9e39049\",\n" +
                "\t\t\t\t\"buttonColor\": 1,\n" +
                "\t\t\t\t\"transparentImage\": \"https://audiotest.cos.tx.xmcdn.com/storages/1623-audiotest/8E/DC/GKwaO4wIDs9JAAAXIQAAofnq.png\",\n" +
                "\t\t\t\t\"transparentImageMd5\": \"b2499baf269e06fab8c4d2771a30f770\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"startAt\": 1681352893000\n" +
                "\t\t}"

        return GsonUtils.parseJson(json, Firework::class.java)
    }

    fun notifyRecommendFragmentVisibilityChanged(visible: Boolean) {
        if (visible != mIsRecommendFragmentVisible) {
            mIsRecommendFragmentVisible = visible
            if (!mIsRecommendFragmentVisible && mIsMainActivityVisible) {
                showUnderAbControl = false
                showDialogIfNeeded()
            }
        }
    }

    fun notifyMainActivityVisibilityChanged(visible: Boolean) {
        if (visible != mIsMainActivityVisible) {
            mIsMainActivityVisible = visible
        }

        if (ConstantsOpenSdk.isDebug) {
//            showDialog(null, null)
        }
    }

    fun notifyRecommendFragmentScrollHeightChanged(scrollHeight: Int) {
        if (mDialogWaitingShow && showTimeType == ShowTimeType.SLIDE &&
                scrollHeight >= (showTimeExtra * BaseUtil.getScreenHeight(BaseApplication.getMyApplicationContext()))) {
            showDialogIfNeeded()
        }
    }

    fun setNewUser() {
        val context = BaseApplication.getMyApplicationContext()
        if (ToolUtil.isFirstInstallApp(context)) {
            val activeTime = MmkvCommonUtil.getInstance(context).getLong(
                    PreferenceConstantsInHost.KEY_NEW_USER_ACTIVE_TIME_FOR_CHILD_PROTECT_DIALOG, 0)
            if (activeTime <= 0) {
                MmkvCommonUtil.getInstance(context).saveLong(
                        PreferenceConstantsInHost.KEY_NEW_USER_ACTIVE_TIME_FOR_CHILD_PROTECT_DIALOG,
                        System.currentTimeMillis())
            }
        }
    }

    fun notifyNewUserChangeToOldUser() {
        val context = BaseApplication.getMyApplicationContext()
        MmkvCommonUtil.getInstance(context).removeKey(
                PreferenceConstantsInHost.KEY_NEW_USER_ACTIVE_TIME_FOR_CHILD_PROTECT_DIALOG)
    }

    private fun readLastShowTimeFromTimeLimitManager(): Long {
        val timeLimit = TimeLimitManager.getInstance().getTimeLimit(TimeLimitManager.EVENT_CHILD_PROTECT_DIALOG) ?: return 0
        if (timeLimit.now >= 1) {
            return timeLimit.start
        }
        return 0
    }

    private fun showDialogIfNeeded(callBack: IShowResult? = null) {
        if (mDialogWaitingShow && mIsMainActivityVisible) {
            requestShowDialogWithHomePage(callBack)
        } else {
            callBack?.onShown(false)
        }
    }

    fun requestShowDialogWithHomePage(callBack: IShowResult?) {
        val activity = BaseApplication.getTopActivity() as? MainActivity

        val checkShowDialog = checkShowDialog(Position.HOMEPAGE)

        FireworkDebugger.log("未成年弹窗, 请求历史的今天数据之前, checkShowDialog: " + checkShowDialog)

        if (activity is MainActivity && checkShowDialog) {

            requestDialogInfo(object : IDataCallBack<ChildProtectDialogInfo> {
                override fun onSuccess(data: ChildProtectDialogInfo?) {
                    val activity = BaseApplication.getTopActivity() as? MainActivity
                    val isDebug = ToolUtil.getDebugSystemProperty("debug.shixin.child", "-1").equals("1")
                    if ((data?.ifPopup == true && activity?.canUpdateUi() == true) || isDebug) {
                        realShowDialogWithFireworkCheck(callBack)
                    } else {
                        callBack?.onShown(false)
                    }
                }

                override fun onError(code: Int, message: String?) {
                    callBack?.onShown(false)
                }

            })
        } else {
            callBack?.onShown(false)
        }
    }

    private fun realShowDialogWithFireworkCheck(callBack: IShowResult?) {
        //展示未成年弹窗
        HighValueFireworkManager.showAfterHighValueDirectly {
            val realShowDialoged = realShowDialog();
            callBack?.onShown(realShowDialoged)
        }
    }

    fun requestShowDialog(fragmentManager: FragmentManager, currPage: String) {

        FireworkDebugger.log("未成年弹窗, requestShowDialog >>> 不是弹屏控制的，不展示合并的弹窗")

        requestDialogInfo(object : IDataCallBack<ChildProtectDialogInfo>{
            override fun onSuccess(data: ChildProtectDialogInfo?) {
                val activity = BaseApplication.getTopActivity() as? MainActivity

                FireworkDebugger.log("未成年弹窗, requestShowDialog >>> onSuccess, ifPopup: ${data?.ifPopup}")

                if (data?.ifPopup == true && activity?.canUpdateUi() == true) {
                    markDialogShow()

                    //不是弹屏控制的，不展示合并的弹窗
                    HighValueFireworkManager.showAfterHighValueDirectly {
                        ChildProtectDialogFragment.getInstance(currPage).show(fragmentManager, "child_protect")
                    }
                }
            }

            override fun onError(code: Int, message: String?) {
            }

        })
    }

    private fun realShowDialog(): Boolean {
        var shown = false
        var shouldEnableFireworkApi = false
        if (mDialogWaitingShow) {
            shouldEnableFireworkApi = true
        }
        mDialogWaitingShow = false
        val activity = BaseApplication.getTopActivity() as? MainActivity
        if (activity?.canUpdateUi() == true) {
            shouldEnableFireworkApi = false
            val fragment = ChildProtectDialogFragment.getInstance("homepage")
            fragment.show(activity.supportFragmentManager, "child_protect")
            TempDataManager.getInstance().saveBoolean("start_dialog_shown", true)
            shown = true
        }
        // 如果在等待未成年弹窗弹出，而又没有弹出，则把统一弹屏的开关打开。因为在等待未成年弹窗弹出，那统一弹窗现在很可能是关闭的状态。
        if (shouldEnableFireworkApi) {
            FireworkApi.getInstance().setLocalEnableShow(true)
        }
        return shown
    }

    private fun getDateInt(): Int {
        val nowDate = Date(System.currentTimeMillis())
        try {
            return SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(nowDate).toInt()
        } catch (e: Exception) {
            Logger.e(e)
        }
        return -1
    }

    private fun loadShowTimeConfig() {
        showTimeType = ShowTimeType.CONTROL
        val config = ABTest.getString(CConstants.Group_toc.ITEM_TEENAGER_LAUNCH_AB, "")
        if (!config.isNullOrEmpty()) {
            try {
                val jsonObject = JSONObject(config)
                val type = jsonObject.optString("type")
                showTimeExtra = jsonObject.optDouble("value", 0.0)
                showTimeType = when (type) {
                    "delay" -> ShowTimeType.DELAY
                    "slide" -> ShowTimeType.SLIDE
                    else -> ShowTimeType.CONTROL
                }
            } catch (e: Exception) {
                Logger.e(e)
            }
        }
    }

    private fun getTimeLimit(position: Position): Int {
        return MIN_SHOW_LIMIT_DAYS
    }
}

data class ChildProtectDialogInfo(
        @SerializedName("ifPopup")
        val ifPopup: Boolean,
        @SerializedName("popupContent")
        val dialogContent: ChildProtectDialogContentModel? = null,
        @SerializedName("buttonText")
        val btnName: String? = null
)

data class ChildProtectDialogContentModel(
        @SerializedName("id")
        val id: Long,
        @SerializedName("eventName")
        val eventName: String? = null,
        @SerializedName("content")
        val content: String? = null,
        @SerializedName("picUrl")
        val picUrl: String? = null,
        @SerializedName("onlyPic")
        val onlyPic: Boolean,
        @SerializedName("linkUrl")
        val linkUrl: String? = null,
        @SerializedName("trackId")
        val trackId: Long = 0L,
        @SerializedName("sharePicUrl")
        val sharePicUrl: String? = "",
        @SerializedName("shareContent")
        val shareContent: String? = ""

)

data class ChildProtectDialogAlbum(
    @SerializedName("buttonTxt")
    val btnName: String? = "", //按钮文案
    @SerializedName("title")
    val title: String? = "",  //标题
    @SerializedName("subTitle")
    val subTitle: String? = "", //副标题
    @SerializedName("bgPic")
    val bgImageUrl: String? = "", //背景图
    @SerializedName("transparentPic")
    val elementImageUrl: String? = "", //前景图
    @SerializedName("screenLink")
    val jumpUrl: String? = "", //跳转链接
    @SerializedName("closeButtonColor")
    val closeButtonColor: Long, //关闭按钮颜色
    @SerializedName("id")
    val id: Long,
    @SerializedName("groupIds")
    val groupIds: String? = ""
)

interface IShowResult {
    fun onShown(isShown: Boolean)
}