package com.ximalaya.ting.android.host.manager.bundleframework.BundleManager;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.Util;
import com.ximalaya.ting.android.host.manager.bundleframework.constant.SpConstants;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.plugin.PluginAndPatchModel;
import com.ximalaya.ting.android.host.model.plugin.PluginInfoModel;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.host.util.constant.UrlConstants;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.httputil.BaseCall;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.opensdk.util.SharedPreferencesUtil;
import com.ximalaya.ting.android.xmutil.Logger;
import com.ximalaya.ting.android.xmutil.ProcessUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import static com.ximalaya.ting.android.host.manager.bundleframework.Util.CODE_1;
import static com.ximalaya.ting.android.host.manager.bundleframework.Util.CODE_2;

/**
 * Created by kevin.fang on 2018/12/11
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @tel 15671641463
 */
public class BundleRequestCache {

    private static final String TAG = "BundleRequestCache";
    public final static String APP_PACKAGE_NAME = "com.ximalaya.ting.android";
    public static String REMOTE_PLUGIN_DOMAIN = "";
    private static final String USE_TEST_PLUGIN = "use_test_plugin_sw";

    private static final String SIMULATE_RELEASE = "simulate_release";

    private static final String ON = "on", OFF = "off";

    private static final int MIN_REQUEST_BUNDLE_INFO_MILLS = 5 * 1000;


    private static final int MIN_FLUSH_INTERVAL = 10; //10 minutes

    private static final long FETCH_TIME_OUT = TimeUnit.SECONDS.toMillis(10); //10 sec

    private final TimeOutHandler mTimeOutHandler = new TimeOutHandler(this);

    private int mFlushInterval = MIN_FLUSH_INTERVAL;

    private boolean useTestPlugin = false;

    private volatile boolean mIsUpdating = false;

    private long lastRequestTimeMills;

    private Map<String, PluginInfoModel> mRemotePluginInfo = new ArrayMap<>();

    private final Map<String, List<IDataCallBack<PluginInfoModel>>> mPluginRequestMap = new ArrayMap<>();

    private Map<String, List<PluginInfoModel>> mRemotePatchInfo = new ArrayMap<>();

    private final Map<String, IDataCallBack<List<PluginInfoModel>>> mPatchRequestMap = new ArrayMap<>();


    /**
     * 改成：true 时，debug 环境关闭远程插件下载，避免影响本地测试。false 默认值，debug 下也会下载插件
     */
    private boolean simulateReleaseEv = false;

    private static class SingletonHolder {
        private static final BundleRequestCache INSTANCE = new BundleRequestCache();
    }


    public static BundleRequestCache getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private BundleRequestCache() {

        ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
            @Override
            public void onUpdateSuccess() {
                boolean boo = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_USEMERGEDREQUEST, true);
                SharedPreferences preferences = BaseApplication.getMyApplicationContext().getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE + DeviceUtil.getAppVersionCode(BaseApplication.getMyApplicationContext()), Context.MODE_MULTI_PROCESS);
                preferences.edit().putBoolean(SpConstants.KEY_USE_MERGED_REQUEST, boo).apply();

                int flushInterval = ConfigureCenter.getInstance().getInt("android", "flushCacheInterval", MIN_FLUSH_INTERVAL);

                preferences.edit().putInt(SpConstants.KEY_FLUSH_CACHE_INTERVAL, flushInterval).apply();
            }

            @Override
            public void onRequestError() {

            }
        });

        SharedPreferences preferences = BaseApplication.getMyApplicationContext().getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE + DeviceUtil.getAppVersionCode(BaseApplication.getMyApplicationContext()), Context.MODE_MULTI_PROCESS);

        useOldRequest = !preferences.getBoolean(SpConstants.KEY_USE_MERGED_REQUEST, true);

        mFlushInterval = preferences.getInt(SpConstants.KEY_FLUSH_CACHE_INTERVAL, MIN_FLUSH_INTERVAL);

        if (mFlushInterval < MIN_FLUSH_INTERVAL) {

            mFlushInterval = MIN_FLUSH_INTERVAL;
        }

        if (ConstantsOpenSdk.isDebug) {
            String label = SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).getString(USE_TEST_PLUGIN, null);
            if (OFF.equals(label)) {
                this.useTestPlugin = false;
            } else {
                this.useTestPlugin = true;
            }

            if (isUseTestPlugin()) {
                BaseCall.getInstanse().addInterceptor(new ChangeCookieInterceptor());
            }

            this.simulateReleaseEv = SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(SIMULATE_RELEASE, false);
        } else {
            useTestPlugin = false;
        }

//        if (!isUseTestPlugin()) {
//            MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).saveBoolean(
//                    PreferenceConstantsInHost.KEY_HAS_EVER_USE_ONLINE_ENVIROMENT_PLUGIN, true);
//        }
    }

    public void setUseTestPlugin(boolean useTestPlugin) {
        this.useTestPlugin = useTestPlugin;

        SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveString(USE_TEST_PLUGIN, useTestPlugin ? ON : OFF);

        if (isUseTestPlugin()) {
            BaseCall.getInstanse().addInterceptor(new ChangeCookieInterceptor());
        }
        BundleRequestCache.getInstance().startWork(BaseApplication.getMyApplicationContext());// 下载插件信息
    }

    public void setSimulateReleaseEv(boolean simulateReleaseEv) {
        this.simulateReleaseEv = simulateReleaseEv;

        SharedPreferencesUtil.getInstance(BaseApplication.getMyApplicationContext()).saveBoolean(SIMULATE_RELEASE, simulateReleaseEv);
    }

    public boolean isUseTestPlugin() {
        return useTestPlugin && ConstantsOpenSdk.isDebug;
    }

    public boolean isSimulateReleaseEv() {
        return simulateReleaseEv && ConstantsOpenSdk.isDebug;
    }

    private static boolean useOldRequest = false;

    private ScheduledFuture mTimer;

    private ScheduledExecutorService mExecutorService = Executors.newScheduledThreadPool(1, new ThreadFactory() {
        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "BundleRequestCache-flush-task");
        }
    });

    public static boolean isUseOldRequest() {
        return useOldRequest;
    }

    public void startWork(Context context) {
        if (!ProcessUtil.isMainProcess(context)) {
            return;
        }
        if (useOldRequest) {
            return;
        }

        if (mTimer != null) {
            mTimer.cancel(true);
        }

        mTimer = mExecutorService.scheduleAtFixedRate(new FlushCacheTask(), 0, mFlushInterval, TimeUnit.MINUTES);

    }

    public void shutdown() {
        if (useOldRequest) {
            return;
        }
        if (mTimer != null && !mTimer.isCancelled()) {
            mTimer.cancel(true);
        }
        mTimer = null;
    }

    public synchronized void getPluginInfo(boolean ifNotToRequest, String packageName, IDataCallBack<PluginInfoModel> callBack) {
        if (TextUtils.isEmpty(packageName)) {
            return;
        }

        if (useOldRequest) { // 不会用了，老方案
            Map<String, String> params = new HashMap<>();
            params.put("packageName", packageName);
            params.put("appVersion", DeviceUtil.getVersionFour(MainApplication.getMyApplicationContext()));
            CommonRequestM.getInstanse().getLastestPluginInfoList(params, callBack);
            return;
        }

        if (mIsUpdating) {
            addRequestPluginCallback(packageName, callBack);
            return;
        }
        if (ifNotToRequest) {
            Logger.i("cf_test", "getPluginInfo______ifNotToRequest___"+ packageName);
            performPluginInfoCallback(packageName, callBack);
        } else {
            getPluginInfoNow(packageName, callBack);
        }
    }

    private void getPluginInfoNow(String packageName, IDataCallBack<PluginInfoModel> callBack) {
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                PluginInfoModel model = mRemotePluginInfo.get(packageName);
                if (callBack != null) {
                    if (model != null) {
                        Logger.i("cf_test", "getPluginInfoNow___不为空————"+ model.getBundleName());
                        Util.uploadXdcsPluginLog("getPluginInfoNow___不为空————"+ model.getBundleName());
                    } else {

                    }
                    callBack.onSuccess(model);
                } else {
                   if (model != null) {
                        Logger.i("cf_test", "getPluginInfoNow___为空————"+ model.getBundleName());
                        Util.uploadXdcsPluginLog("getPluginInfoNow___为空————"+ model.getBundleName());
                    }
                }
            }
        });
    }


    private void addRequestPluginCallback(String packageName, IDataCallBack<PluginInfoModel> callBack) {
        List<IDataCallBack<PluginInfoModel>> list = mPluginRequestMap.get(packageName);
        if (list == null) {
            list = new CopyOnWriteArrayList<>();
        }
        list.add(callBack);
        mPluginRequestMap.put(packageName, list);
        Message msg = mTimeOutHandler.obtainMessage(MSG_GET_PLUGIN_TIME_OUT);
        PluginTimeOut timeOut = new PluginTimeOut();
        timeOut.callback = callBack;
        timeOut.packageName = packageName;
        msg.obj = timeOut;
        mTimeOutHandler.sendMessageDelayed(msg, FETCH_TIME_OUT);
    }

    private synchronized void performPluginInfoCallback(String packageName, IDataCallBack<PluginInfoModel> callBack) {
        PluginInfoModel model = mRemotePluginInfo.get(packageName);
        if (model == null) {
            boolean reRequest = ConfigureCenter.getInstance().getBool(CConstants.Group_android.GROUP_NAME, CConstants.Group_android.ITEM_RE_REQUEST_PLUGIN, true);
            if (reRequest) {
                if (callBack != null) {
                    addRequestPluginCallback(packageName, callBack);
                }
                long c = System.currentTimeMillis();
                if (c - lastRequestTimeMills >= MIN_REQUEST_BUNDLE_INFO_MILLS) {
                    loadAllPluginAndPatchInfo();
                } else { // 延迟5秒发出请求
                    mTimeOutHandler.sendMessageDelayed(mTimeOutHandler.obtainMessage(MSG_REQUEST_PLUGIN_INFO), MIN_REQUEST_BUNDLE_INFO_MILLS);
                }
                return;
            }
        }

        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                if (callBack != null) {
                    callBack.onSuccess(model);
                }
            }
        });
    }

    private final Map<String, Long> mPatchRequestTimestamp = new ArrayMap<>();
    private final long GAP_REQUEST_PATCH = TimeUnit.SECONDS.toMillis(10);//10s

    // 注意该方法不能回调多次
    public synchronized void getPatchInfo(String packageName, IDataCallBack<List<PluginInfoModel>> callBack) {
        if (TextUtils.isEmpty(packageName)) {
            return;
        }

        Long lastRequestTime = mPatchRequestTimestamp.get(packageName);
        if (lastRequestTime != null && (System.currentTimeMillis() - lastRequestTime) < GAP_REQUEST_PATCH) {
            return;
        }

        mPatchRequestTimestamp.put(packageName, System.currentTimeMillis());

        if (useOldRequest) {

            Map<String, String> params = new HashMap<>();

            params.put("packageName", packageName);
            params.put("appVersion", DeviceUtil.getVersionFour(MainApplication.getMyApplicationContext()));

            CommonRequestM.getInstanse().getLastPatchInfo(params, callBack);

            return;
        }

        if (mIsUpdating) {

            mPatchRequestMap.put(packageName, callBack);

            //make sure there is only one TimeOut message
            if (mTimeOutHandler.hasMessages(MSG_GET_PATCH_TIME_OUT)) {
                mTimeOutHandler.removeMessages(MSG_GET_PATCH_TIME_OUT);
            }

            Message msg = mTimeOutHandler.obtainMessage(MSG_GET_PATCH_TIME_OUT);

            PatchTimeOut timeOut = new PatchTimeOut();

            timeOut.callback = callBack;
            timeOut.packageName = packageName;

            msg.obj = timeOut;

            mTimeOutHandler.sendMessageDelayed(msg, FETCH_TIME_OUT);

            return;
        }

        performPatchInfoCallback(packageName, callBack);
    }

    private synchronized void performPatchInfoCallback(String packageName, IDataCallBack<List<PluginInfoModel>> callBack) {
        List<PluginInfoModel> pluginInfoModelList = mRemotePatchInfo.get(packageName);
        if (callBack != null) {
            callBack.onSuccess(pluginInfoModelList);
        }
    }

    /**
     * 关于abi 编码：
     * armeabi : 1
     * armeabi-v7a: 2
     * arm64-v8a:3
     * 其他用到再说 一般也用不到   请求上我会加一个参数 abi=2 or 3
     */
    private void loadAllPluginAndPatchInfo() {
        if (mIsUpdating) {
            //There must be some mistake!
            return;
        }
        mIsUpdating = true;

        Map<String, String> params = new HashMap<>();

        params.put("appVersion", DeviceUtil.getVersionFour(MainApplication.getMyApplicationContext()));
        params.put("packageName", APP_PACKAGE_NAME);
        boolean vmIs64Byte = DeviceUtil.is64Byte();
        int abi = 2;
        if (vmIs64Byte) {
            abi = 3;
        }
        params.put("abi", abi + "");

        String url = null;

        if (ConstantsOpenSdk.isDebug && useTestPlugin && BaseConstants.environmentId != AppConstants.ENVIRONMENT_TEST) { // 如果标记要使用测试环境的插件，则不在测试环境的请求都走测试环境
            url = UrlConstants.getInstanse().getPluginAndPatchInfoUrl();

            String host = null;
            String[] split = null;
            try {
                host = Uri.parse(url).getHost();
                split = host.split("\\.");
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (split != null) {
                if (split.length == 2) {
                    url = url.replaceFirst(host, "test." + host);
                } else if (split.length >= 3) {
                    String str = split[split.length - 2];
                    url = url.replaceFirst(host, host.replace(str, "test." + str));
                }
            }
        }
        Logger.i("cf_test", "开始获取远程插件列表————");
        Util.uploadXdcsPluginLog("开始获取远程插件列表————");
        getPluginAndPatchInfo(url, params, new IDataCallBack<PluginAndPatchModel>() {
            @Override
            public void onSuccess(@Nullable PluginAndPatchModel object) {
                lastRequestTimeMills = System.currentTimeMillis();
                if (object == null) {
                    uploadDownloadPluginErrorInfo(CODE_2, "onSuccess but response is null", null);
                    Logger.i(TAG, "Update onSuccess but response is null");
                    mIsUpdating = false;
                    doAfterUpdated(false);
                    return;
                }

//                synchronized (BundleRequestCache.this) {
                mergePluginAndPatch(object.getPlugin(), object.getPluginJar()); // 合并考虑数据线程安全
                Logger.i("cf_test", "获取到远程插件列表成功——————后续才能下载");
                Util.uploadXdcsPluginLog("获取到远程插件列表成功——————后续才能下载");
                mIsUpdating = false;
                doAfterUpdated(true);
//                }

                //trigger a host check
                BundleInfoManager.getInstance().checkBundleUpdate(Configure.dispatchBundleModel, true);
                getPluginDomain(object.getPlugin());
            }

            @Override
            public void onError(int code, String message) {
                lastRequestTimeMills = System.currentTimeMillis();
                Logger.i(TAG, "Update error , code : " + code + " msg : " + message);
                uploadDownloadPluginErrorInfo(CODE_1, "获取插件信息请求错误:" + message + " code" + code, null);
                mIsUpdating = false;
                doAfterUpdated(false);
            }
        });
    }

    private void getPluginDomain(List<PluginInfoModel> pluginList) {
        if (pluginList == null || pluginList.size() == 0) {
            return;
        }
        for (PluginInfoModel pluginInfoModel : pluginList) {
            if (pluginInfoModel == null || TextUtils.isEmpty(pluginInfoModel.getFileUrl())) {
                continue;
            }
            String pluginUrl = pluginInfoModel.getFileUrl();
            try {
                Uri uri = Uri.parse(pluginUrl);
                REMOTE_PLUGIN_DOMAIN = uri.getHost();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!TextUtils.isEmpty(REMOTE_PLUGIN_DOMAIN)) {
                break;
            }
        }
    }

    private void getPluginAndPatchInfo(String url, final Map<String, String> params,
                                      final IDataCallBack<PluginAndPatchModel> callBack) {
        Logger.i("cf_test", "获取到远程插件列表—————getPluginAndPatchInfo————onSuccess");
        CommonRequestM.getInstanse().getPluginAndPatchInfo(url, params, new IDataCallBack<PluginAndPatchModel>() {
            @Override
            public void onSuccess(@Nullable PluginAndPatchModel object) {
                Logger.i("cf_test", "获取到远程插件列表—————getPluginAndPatchInfo————onSuccess");
                MyAsyncTask.execute(new Runnable() {
                    @Override
                    public void run() {
                        Logger.i("cf_test", "获取到远程插件列表—————getPluginAndPatchInfo————callBack.onSuccess");
                        callBack.onSuccess(object);
                    }
                });
            }

            @Override
            public void onError(int code, String message) {
                MyAsyncTask.execute(new Runnable() {
                    @Override
                    public void run() {
                        callBack.onError(code, message);
                    }
                });
            }
        });

    }
    /**
     * merge plugin and patch
     *
     * @param pluginList
     * @param pluginJarList
     */
    // 1. 上报缺少插件信息
    // 2. mRemotePluginInfo mRemotePatchInfo 填充这两个字段 后面直接取这两个字段使用
    private void mergePluginAndPatch(List<PluginInfoModel> pluginList, List<PluginInfoModel> pluginJarList) {

        ArrayMap<Long, PluginInfoModel> bundleMap = new ArrayMap<>(20);
        List<String> lackBundleNames = new ArrayList<>(20);

        final Map<String, PluginInfoModel> tmpRemotePluginInfo = new ArrayMap<>(25);

        final Map<String, List<PluginInfoModel>> tmpRemotePatchInfo = new ArrayMap<>(10);
        Logger.i("cf_test","mergePluginAndPatch: ___");

        for (PluginInfoModel model : pluginList) {
//            mRemotePluginInfo
            tmpRemotePluginInfo.put(getBundlePackageName(model), model);
            Logger.i("cf_test","mergePluginAndPatch: ___" + getBundlePackageName(model));
            bundleMap.put(model.getId(), model);

            BundleModel bundleModel = Configure.getBundleByName(model.getBundleName());

            if (bundleModel != null) {
                bundleModel.remoteStatues = model.getStatus();
                if (model.getStatus() == PluginInfoModel.STATUS_PLUGIN_DISCARD) {
                    BundleInfoManager.getInstance().scheduleRevertPlugin(MainApplication.getMyApplicationContext(), bundleModel);
                }
            }

        }
        if (tmpRemotePluginInfo.size() == 0) {
            Logger.i(TAG, "Update onSuccess but response is null");
        } else {
            boolean hostExit = false;
            if (tmpRemotePluginInfo.get(APP_PACKAGE_NAME) != null) {
                hostExit = true;
            }
            for (BundleModel bundleModel : Configure.bundleList) {
                if (bundleModel.isBuildIn() || Configure.BUNDLE_DISPATCH.equals(bundleModel.bundleName)) {// 检查非buildin的
                    continue;
                }
                if (tmpRemotePluginInfo.get(APP_PACKAGE_NAME + "." + bundleModel.bundleName) == null) {
                    lackBundleNames.add(bundleModel.bundleName);
                }
            }
            if (!hostExit) {
                lackBundleNames.add("host");
            }
            if (lackBundleNames.size() > 0) {
                uploadDownloadPluginErrorInfo(CODE_2, "plugin信息中缺少以下bundle", lackBundleNames);
            }
        }

//        mRemotePatchInfo.clear();

        for (PluginInfoModel pluginJar : pluginJarList) {
            PluginInfoModel bundle = bundleMap.get(pluginJar.getRefPatchId());
            String packageName = getBundlePackageName(bundle);
            List<PluginInfoModel> list = tmpRemotePatchInfo.get(packageName);
            if (list == null) {
                list = new Vector<>();
            }
            list.add(pluginJar);
            tmpRemotePatchInfo.put(packageName, list);
        }
        synchronized (BundleRequestCache.this) {
            mRemotePluginInfo = tmpRemotePluginInfo;
            mRemotePatchInfo = tmpRemotePatchInfo;
        }
    }

    private String getBundlePackageName(PluginInfoModel bundle) {

        if (bundle == null) {
            return "";
        }

        //NOTICE!! 本地host bundle的名字叫dispatch_bundle,这里是兼容发布平台的命名.
        if ("host".equals(bundle.getBundleName())) {
            //host
            return APP_PACKAGE_NAME;
        }
        return APP_PACKAGE_NAME + "." + bundle.getBundleName();
    }


    /**
     * We should handle the request during the update
     */
    private void doAfterUpdated(boolean success) {

        //plugin request
        final Map<String, List<IDataCallBack<PluginInfoModel>>> tmpPluginRequestMap = new ArrayMap<>();

        tmpPluginRequestMap.putAll(mPluginRequestMap);
        synchronized (BundleRequestCache.this) {
            mPluginRequestMap.clear();
        }
        for (Map.Entry<String, List<IDataCallBack<PluginInfoModel>>> entry : tmpPluginRequestMap.entrySet()) {
            List<IDataCallBack<PluginInfoModel>> list = entry.getValue();

            if (list == null) {
                continue;
            }
            String packageName = entry.getKey();

            PluginInfoModel remote = mRemotePluginInfo.get(packageName);

            for (IDataCallBack<PluginInfoModel> callBack : list) {
                if (callBack != null) {
                    if (success) {
                        callBack.onSuccess(remote);
                    } else {
                        callBack.onError(0, "获取插件信息失败");
                    }
                }
                list.remove(callBack);
            }
        }


        //patch request
        final Map<String, IDataCallBack<List<PluginInfoModel>>> tmpPatchRequestMap = new ArrayMap<>();
        tmpPatchRequestMap.putAll(mPatchRequestMap);
        synchronized (BundleRequestCache.this) {
            mPatchRequestMap.clear();
        }
        for (Map.Entry<String, IDataCallBack<List<PluginInfoModel>>> entry : tmpPatchRequestMap.entrySet()) {
            IDataCallBack<List<PluginInfoModel>> callBack = entry.getValue();
            if (callBack == null) {
                continue;
            }
            String packageName = entry.getKey();
            List<PluginInfoModel> remoteList = mRemotePatchInfo.get(packageName);
            if (success) {
                callBack.onSuccess(remoteList);
            } else {
                callBack.onError(0, "获取patch 信息失败");
            }

        }

    }


    /**
     * 下载插件信息任务
     */
    private class FlushCacheTask implements Runnable {

        @Override
        public void run() {
            Logger.i(TAG, "FlushCacheTask run");

            //load all info
            loadAllPluginAndPatchInfo();
        }
    }

    private static final int MSG_GET_PLUGIN_TIME_OUT = 0;
    private static final int MSG_GET_PATCH_TIME_OUT = 1;
    private static final int MSG_REQUEST_PLUGIN_INFO = 2;


    private static class TimeOutHandler extends Handler {

        private BundleRequestCache outer;

        TimeOutHandler(BundleRequestCache outer) {
            super(HandlerManager.obtainBackgroundHandler().getLooper());
            this.outer = outer;
        }

        @Override
        public void dispatchMessage(Message msg) {

            switch (msg.what) {
                case MSG_GET_PLUGIN_TIME_OUT:
                    if (msg.obj instanceof PluginTimeOut) {
                        if (outer != null) {
                            outer.handlePluginTimeOut((PluginTimeOut) msg.obj);
                        }
                    }
                    break;

                case MSG_GET_PATCH_TIME_OUT:
                    if (msg.obj instanceof PatchTimeOut) {
                        if (outer != null) {
                            outer.handlePatchTimeOut((PatchTimeOut) msg.obj);
                        }
                    }
                    break;
                case MSG_REQUEST_PLUGIN_INFO:
                    if (outer != null) {
                        outer.loadAllPluginAndPatchInfo();
                    }
                    break;
            }

        }
    }

    private void handlePluginTimeOut(PluginTimeOut pluginTimeOut) {
        synchronized (mPluginRequestMap) {
            if (mPluginRequestMap.size() <= 0) {
                //fix https://bugly.qq.com/v2/crash-reporting/crashes/02e48e8a20/56509995?pid=1
                return;
            }
            List<IDataCallBack<PluginInfoModel>> callBacks = mPluginRequestMap.get(pluginTimeOut.packageName);
            if (callBacks != null && callBacks.remove(pluginTimeOut.callback)) {
                if (pluginTimeOut.callback != null) {
                    PluginInfoModel model = mRemotePluginInfo.get(pluginTimeOut.packageName);
                    if (model != null) {
                        pluginTimeOut.callback.onSuccess(model);
                    } else {
                        pluginTimeOut.callback.onError(0, "获取插件信息超时");
                    }
                }
            }
        }
    }

    private void handlePatchTimeOut(PatchTimeOut patchTimeOut) {
        synchronized (mPatchRequestMap) {
            IDataCallBack<List<PluginInfoModel>> callBack = mPatchRequestMap.get(patchTimeOut.packageName);
            if (callBack != null) {
                performPatchInfoCallback(patchTimeOut.packageName, patchTimeOut.callback);
            }
        }
    }

    private class PluginTimeOut {

        IDataCallBack<PluginInfoModel> callback;

        String packageName;
    }

    private class PatchTimeOut {

        IDataCallBack<List<PluginInfoModel>> callback;

        String packageName;
    }

    private void uploadDownloadPluginErrorInfo(int code, String info, List<String> bundleNames) {
//        XDCSCollectUtil.statErrorToXDCS("downloadPlugin", info + ":" + bundleNames);
        Util.sendPluginInfoError(code, info, bundleNames);
    }

    private class ChangeCookieInterceptor implements Interceptor {

        @Override
        public Response intercept(@NonNull Chain chain) throws IOException {

            //http://mobile.test.ximalaya.com/dog-portal/checkOld2/all/1606377953787?abi=3&appVersion=6.7.21.3&packageName=com.ximalaya.ting.android&signature=837dbb00bb72f432191495650d872568
            Request.Builder builder = chain.request().newBuilder();
            Request request = builder.build();

            if (isUseTestPlugin()) {
                String path = request.url().uri().getPath();
                if (path != null && path.contains("dog-portal/checkOld2/all")) {
                    String coolies = request.header("Cookie");
                    if (coolies != null && coolies.contains("&_device=android")) {
                        if (coolies.startsWith("1")) {
                            coolies = coolies.replaceAll("1&_device", "4&_device");
                            coolies = coolies.replaceAll("1&_token", "4&_token");
                        } else if (coolies.startsWith("6")) {
                            coolies = coolies.replaceAll("6&_device", "4&_device");
                            coolies = coolies.replaceAll("6&_token", "4&_token");
                        }

                        builder.header("Cookie", coolies);
                        request = builder.build();
                    }
                }
            }

            return chain.proceed(request);
        }
    }
}
