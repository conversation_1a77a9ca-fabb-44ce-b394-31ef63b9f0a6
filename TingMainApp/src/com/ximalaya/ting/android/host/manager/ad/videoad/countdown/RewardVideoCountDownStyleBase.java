package com.ximalaya.ting.android.host.manager.ad.videoad.countdown;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdUnLockVipTrackCloseRewardAgainDialog;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.view.AdUnlockCloseDialogV2;
import com.ximalaya.ting.android.host.manager.ad.videoad.CanPauseCountDownTimer;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoConfigManager;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmtrace.ubt.XmRequestIdManager;
import com.ximalaya.ting.android.xmutil.Logger;

public abstract class RewardVideoCountDownStyleBase implements ICustomViewToActivityClickRewardExt {
    public int closeTime = 0; // 单位s 总的关闭倒计时时长
    private int remainCloseTime = 0; // 单位s 剩余关闭倒计时时长
    private int totalTime = 0; // 单位s 视频总时长
    private int remainVideoTime = 0; // 单位s 剩余视频时长

    public static int rewardTime = 30; // 单位分钟
    private CanPauseCountDownTimer countDownTimer;

    protected View layout;
    protected View titleLayout;
    protected TextView titleView;
    protected TextView totalTimeTv;
    protected View dividerView;
    protected View countDown;
    protected View volume;
    protected View closeButton;

    private String defaultTitle = "看%s秒广告领取奖励";
    private String rewardTitle = "已获得奖励";
    private String defaultWatchVideoTitle  = "完整观看视频可领取奖励";
    private boolean isVideoComplete;
    private boolean isRewardSuccess;
    private View.OnClickListener viewListener;
    protected RewardExtraParams rewardExtraParams;
    private Activity rewardActivity;
    private int curTime = 0;
    private ICountDownFinishCalLBack countDownFinishCalLBack;
    private Drawable rewardIconDrawable;
    private String realConfigPositionName;

    public abstract int getLayoutId();

    public abstract void initUi(View inflate);

    @Override
    public View setCustomViewToActivity(ViewGroup contentLayout,
                                        @Nullable RewardExtraParams rewardExtraParams,
                                        View.OnClickListener onClickListener, ICountDownFinishCalLBack countDownFinishCalLBack) {
        this.countDownFinishCalLBack = countDownFinishCalLBack;
        return setCustomViewToActivity(contentLayout, rewardExtraParams, onClickListener);
    }

    @Override
    public View setCustomViewToActivity(ViewGroup contentLayout,
                                        @Nullable RewardExtraParams rewardExtraParams,
                                        View.OnClickListener onClickListener) {
        if (contentLayout == null) {
            return null;
        }
        View inflate;
        try {
            inflate =
                    LayoutInflater.from(contentLayout.getContext()).inflate(getLayoutId(), contentLayout, true);
        } catch (Exception e) {
            return null;
        }
        this.viewListener = onClickListener;
        this.rewardExtraParams = rewardExtraParams;
        this.rewardActivity = (Activity) contentLayout.getContext();
        if (rewardExtraParams != null) {
            rewardTime = rewardExtraParams.getRewardTime();
            if (rewardExtraParams.getCanCloseTime() > 0) {
                closeTime = rewardExtraParams.getCanCloseTime();
            }
            totalTime = rewardExtraParams.getVideoPlayOverTime();
            if (totalTime > 0 && closeTime > 0) {
                closeTime = Math.min(closeTime, totalTime);
            } else if (totalTime > 0) {
                closeTime = totalTime;
            }
            remainCloseTime = closeTime;
        }
        initConfig(contentLayout.getContext());
        initUi(inflate);
        initCloseAction(rewardExtraParams, (Activity) contentLayout.getContext(), onClickListener);
        initTopLayout((Activity) contentLayout.getContext(), rewardExtraParams, inflate, onClickListener);
        return layout;
    }

    private void initConfig(Context context) {
        realConfigPositionName = RewardVideoConfigManager.getInstance().getRealConfigPositionName(rewardExtraParams);
        defaultTitle = RewardVideoConfigManager.getInstance().getHeaderText(realConfigPositionName,
                RewardVideoConfigManager.STYLE_DURATION, rewardTime, 0);
        rewardTitle = RewardVideoConfigManager.getInstance().getHeaderRewardedText(realConfigPositionName, rewardTime);
        String iconImageUrl = RewardVideoConfigManager.getInstance().getRewardIconImageUrl(realConfigPositionName);
        if (!TextUtils.isEmpty(iconImageUrl)) {
            ImageManager.from(ToolUtil.getCtx()).downloadBitmap(iconImageUrl, null, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    if (bitmap != null) {
                        rewardIconDrawable = new BitmapDrawable(context.getResources(), bitmap);
                    }
                }
            });
        }
    }

    @Override
    public View getCustomVolumeView() {
        return volume;
    }

    @Override
    public void onAdPlayComplete() {
        isVideoComplete = true;
        updateRewardTip();
        totalTimeTv.setVisibility(View.GONE);
        dividerView.setVisibility(View.GONE);
    }

    boolean hasDuration = false;

    @Override
    public void onAdPlayProgress(long curPosition, long duration) {
        if (!hasDuration) {
            totalTime = (int) (duration / 1000);
            if (totalTime > 0) {
                closeTime = Math.min(closeTime, totalTime);
            }
            if (rewardExtraParams != null && rewardExtraParams.getAdvertis() != null && rewardExtraParams.getAdvertis() instanceof AdUnLockVipTrackAdvertis) {
                AdUnLockVipTrackAdvertis advertis = (AdUnLockVipTrackAdvertis) rewardExtraParams.getAdvertis();
                if (advertis.getVideoDuration() == 0) {
                    advertis.setVideoDuration(totalTime);
                }
            }
            startCountdown();
            hasDuration = true;
        }
    }

    protected void initTopLayout(Activity thirdSdkActivity, RewardExtraParams rewardExtraParams, View inflate, View.OnClickListener openVipClickListener) {
        layout.setPadding(0, 0, 0, 0);
        titleLayout.setPadding(BaseUtil.dp2px(thirdSdkActivity, 7), 0,
                BaseUtil.dp2px(thirdSdkActivity, 16), 0);
        volume.setVisibility(View.VISIBLE);
        if (totalTime != 0) {
            totalTimeTv.setText(String.format("%s秒", getCountDownTime(totalTime)));
        } else {
            totalTimeTv.setVisibility(View.GONE);
            dividerView.setVisibility(View.GONE);
        }
        if (closeTime > 0) {
            titleView.setText(String.format(defaultTitle, getCountDownTime(closeTime)));
        } else {
            titleView.setText("");
        }
        countDown = new View(thirdSdkActivity);
        countDown.setId(R.id.host_reward_count_down);
    }

    private void updateRewardTip() {
        if (isRewardSuccess) {
            return;
        }
        isRewardSuccess = true;
        if (titleView != null) {
            titleView.setText(rewardTitle);
            String textColor = RewardVideoConfigManager.getInstance().getHeaderRewardedTextColor(realConfigPositionName);
            titleView.setTextColor(Color.parseColor(textColor));
            int iconStyle = RewardVideoConfigManager.getInstance().getRewardIconImageStyle(realConfigPositionName);
            Drawable drawable;
            if (rewardIconDrawable != null) {
                drawable = rewardIconDrawable;
            } else {
                drawable = getRewardDrawable(iconStyle);
            }
            if (drawable != null) {
                drawable.setBounds(0, 0, BaseUtil.dp2px(ToolUtil.getCtx(), 16), BaseUtil.dp2px(ToolUtil.getCtx(), 16));
                titleView.setCompoundDrawables(drawable, null, null, null);
            }
        }
        if (rewardExtraParams != null && rewardExtraParams.getRewardPageStatusCallBack() != null) {
            rewardExtraParams.getRewardPageStatusCallBack().onRewardVerify();
        }
    }

    private Drawable getRewardDrawable(int style) {
        switch (style) {
            case 1:
                return ContextCompat.getDrawable(titleView.getContext(), R.drawable.host_reward_tip_vip_free_6);
            case 2:
                return ContextCompat.getDrawable(titleView.getContext(), R.drawable.host_reward_tip_point_coin);
            case 3:
                return ContextCompat.getDrawable(titleView.getContext(), R.drawable.host_reward_tip_vip);
            default:
                return null;
        }
    }

    private void startCountdown() {
        curTime = 0;
        if (closeTime <= 0) {
            closeTime = totalTime;
        } else {
            closeTime = Math.min(closeTime, totalTime);
        }
        remainVideoTime = totalTime;
        remainCloseTime = closeTime;
        if (totalTime == 0) {
            if (totalTimeTv != null) {
                totalTimeTv.setVisibility(View.GONE);
            }
            if (dividerView != null) {
                dividerView.setVisibility(View.GONE);
            }
        } else {
            totalTimeTv.setText(String.format("%s秒", getCountDownTime(totalTime)));
            totalTimeTv.setVisibility(View.VISIBLE);
            dividerView.setVisibility(View.VISIBLE);
            titleView.setText(String.format(defaultTitle, getCountDownTime(closeTime)));
        }
        cancelCountdown();
        countDownTimer = new CanPauseCountDownTimer(totalTime * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (isVideoComplete) {
                    return;
                }
                curTime ++;
                remainVideoTime = totalTime - curTime;
                remainCloseTime = closeTime - curTime;

                if (!isRewardSuccess) {
                    titleView.setText(String.format(defaultTitle, getCountDownTime(remainCloseTime)));
                    if (remainCloseTime > 0) {
                        rewardExtraParams.setCanCloseTime(remainCloseTime);
                    } else if (remainCloseTime == 0) {
                        updateRewardTip();
                        if (rewardExtraParams != null) {
                            rewardExtraParams.setCanCloseTime(RewardVideoCountDownStyleForVipFree.LAST_COUNT_DOWN_FINISH);
                        }
                        AdUnLockTimeManagerV3.getInstance().onCountDownFinish();
                        AdUnLockTimeManagerV2.getInstance().onCountDownFinish();
                        if (countDownFinishCalLBack != null) {
                            countDownFinishCalLBack.onCountDownFinish();
                        }
                    }
                }

                if (remainVideoTime <= 0) {
                    if (totalTimeTv != null) {
                        totalTimeTv.setVisibility(View.GONE);
                    }
                    if (dividerView != null) {
                        dividerView.setVisibility(View.GONE);
                    }
                } else if (totalTimeTv != null) {
                    totalTimeTv.setText(String.format("%s秒", getCountDownTime(remainVideoTime)));
                }
            }

            @Override
            public void onFinish() {
                if (totalTimeTv != null) {
                    totalTimeTv.setVisibility(View.GONE);
                }
                if (dividerView != null) {
                    dividerView.setVisibility(View.GONE);
                }
            }
        };
        countDownTimer.start();
        if (rewardExtraParams != null) {
            rewardExtraParams.setCountDownTimer(countDownTimer);
        }
    }

    private void cancelCountdown(){
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    // 点击关闭时达到解锁条件
    protected void onRewardSuccess(View.OnClickListener onClickListener) {
        if (onClickListener != null) {
            onClickListener.onClick(countDown);
        }
    }

    // 提前关闭，不可解锁
    protected void onRewardFail(View.OnClickListener onClickListener) {
        if (onClickListener != null) {
            onClickListener.onClick(closeButton);
        }
    }

    private void initCloseAction(RewardExtraParams rewardExtraParams, Activity thirdActivity, View.OnClickListener onClickListener) {
        closeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 倒计时已结束或者视频已完播，无需展示挽留弹窗
                if (isRewardSuccess) {
                    onRewardSuccess(onClickListener);
                    return;
                }
                if (RewardVideoConfigManager.getInstance().isNotShowCloseAlertDialog(realConfigPositionName)) {
                    // 不展示挽留弹窗
                    cancelCountdown();
                    onRewardFail(onClickListener); // 不可解锁
                } else {
                    showCloseAlertDialog(rewardExtraParams, thirdActivity, onClickListener);
                }
            }
        });
    }

    private void showCloseAlertDialog(RewardExtraParams rewardExtraParams, Activity thirdActivity, View.OnClickListener onClickListener) {
        String title = remainCloseTime > 0 ? RewardVideoConfigManager.getInstance().getCloseTipText(realConfigPositionName,
                RewardVideoConfigManager.STYLE_DURATION, rewardTime, 0) : defaultWatchVideoTitle;
        AdUnlockCloseDialogV2 closeDialog =
                new AdUnlockCloseDialogV2(remainCloseTime, true, title, realConfigPositionName, RewardVideoConfigManager.STYLE_DURATION,
                        thirdActivity, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!isRewardSuccess) {
                            // 倒计时未结束并且视频未完播
                            cancelCountdown();
                            onRewardFail(onClickListener); // 不可解锁
                        } else {
                            // 倒计时已结束或者已完播
                            onRewardSuccess(onClickListener); // 可以解锁
                        }
                    }
                }, new AdUnlockCloseDialogV2.DialogCancelListener() {
                    @Override
                    public void onDialogCancel() {
                    }
                });
        closeDialog.show();
        if (rewardExtraParams != null) {
            rewardExtraParams.setVipFreeCloseAlertDialog(closeDialog);
        }
    }

    private String getCountDownTime(int time) {
        if (time <= 0) {
            return "00";
        } else if (time < 10) {
            return "0" + time;
        } else {
            return time+"";
        }
    }

    public void videoCompleteAutoJump() {
    }

    @Override
    public void onAdClicked() {
    }

    @Override
    public void onAdCanReward() {
        updateRewardTip();
    }

    @Override
    public void onAdPlayStart() {

    }

    // 自动跳转落地页返回时直接关闭激励视频
    public void closeAdWhenAutoJumpBack() {
        if (viewListener != null && countDown != null) {
            viewListener.onClick(countDown);
        }
    }
}
