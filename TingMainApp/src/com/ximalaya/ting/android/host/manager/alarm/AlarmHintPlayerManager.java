package com.ximalaya.ting.android.host.manager.alarm;

import android.media.MediaPlayer;

import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.player.MiniPlayer;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;

import java.io.File;

public class AlarmHintPlayerManager implements MediaPlayer.OnCompletionListener {
    private static AlarmHintPlayerManager INSTANCE;
    private MiniPlayer mWeatherPlayer;
    private MediaPlayer.OnCompletionListener mOnCompletionListener;

    public static AlarmHintPlayerManager getInstance() {
        if (INSTANCE == null) {
            synchronized (AlarmHintPlayerManager.class) {
                if (INSTANCE == null) {
                    INSTANCE = new AlarmHintPlayerManager();
                }
            }
        }
        return INSTANCE;
    }

    public boolean initHintSound(String path) {
        if (mWeatherPlayer == null) {
            mWeatherPlayer = new MiniPlayer();
        }
        if (path == null) {
            return false;
        }

        if (!(path.startsWith("http://") || path.startsWith("https://"))) {
            if (!new File(path).exists()) {
                return false;
            }
        }

        try {
            mWeatherPlayer.init(path);
            mWeatherPlayer.setOnCompletionListener(this);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public void playHintSound() {
        if (mWeatherPlayer != null) {
            mWeatherPlayer.startPlay();
        }
    }

    public void setCompletionListener(MediaPlayer.OnCompletionListener completionListener) {
        this.mOnCompletionListener = completionListener;
    }

    public void removeCompletionListener() {
        this.mOnCompletionListener = null;
    }

    public void stopPlay() {
        if (mWeatherPlayer != null) {
            mWeatherPlayer.stopPlay();
        }
    }

    public boolean isPlaying() {
        if (mWeatherPlayer == null) {
            return false;
        }
        return mWeatherPlayer.isPlaying();
    }

    @Override
    public void onCompletion(MediaPlayer mp) {
        if (mWeatherPlayer != null) {
            XmPlayerManager.getInstance(BaseApplication.getMyApplicationContext()).play();
        }
        if (mOnCompletionListener != null) {
            mOnCompletionListener.onCompletion(mp);
        }
    }
}
