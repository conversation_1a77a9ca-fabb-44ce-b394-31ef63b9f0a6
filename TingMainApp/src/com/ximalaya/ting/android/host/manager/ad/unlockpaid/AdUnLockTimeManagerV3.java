package com.ximalaya.ting.android.host.manager.ad.unlockpaid;

import static com.ximalaya.ting.android.framework.view.snackbar.enums.SnackbarType.MULTI_LINE;
import static com.ximalaya.ting.android.host.util.constant.AppConstants.AD_POSITION_NAME_WELFARE_CASH_RECEIVE;

import android.app.Activity;
import android.app.Service;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.ad.manager.AdStateReportManager;
import com.ximalaya.ting.android.ad.manager.HostCommonRtbSortUtil;
import com.ximalaya.ting.android.ad.model.thirdad.AbstractThirdAd;
import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.adsdk.bridge.inner.model.SDKAdReportModel;
import com.ximalaya.ting.android.adsdk.util.EncryptPriceUtils;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.framework.view.snackbar.Snackbar;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.hybrid.providerSdk.ui.dialog.LoadingDialog;
import com.ximalaya.ting.android.host.manager.ElderlyModeManager;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.ad.AdManager;
import com.ximalaya.ting.android.host.manager.ad.AdPositionIdManager;
import com.ximalaya.ting.android.host.manager.ad.RewardAgainAdManager;
import com.ximalaya.ting.android.host.manager.ad.RewardCoinAgainAdManager;
import com.ximalaya.ting.android.host.manager.ad.ThirdAdLoadParams;
import com.ximalaya.ting.android.host.manager.ad.WebViewPreloadManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.HostRewardVideoRtbAdLoadManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.IRewardAdFragment;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBack;
import com.ximalaya.ting.android.host.manager.ad.videoad.IVideoAdStatueCallBackExt;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoAdManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoConfigManager;
import com.ximalaya.ting.android.host.manager.ad.videoad.RewardVideoReport;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.freeListen.FreeListenLogManager;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.manager.notification.SnackbarManager;
import com.ximalaya.ting.android.host.manager.play.FreeListenTimeManager;
import com.ximalaya.ting.android.host.manager.request.AdRequest;
import com.ximalaya.ting.android.host.model.ad.AdReportModel;
import com.ximalaya.ting.android.host.model.ad.AdStateData;
import com.ximalaya.ting.android.host.model.ad.AdUnLockVipTrackAdvertis;
import com.ximalaya.ting.android.host.model.ad.RewardExtraParams;
import com.ximalaya.ting.android.host.util.VibratorUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.model.PlayableModel;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;
import com.ximalaya.ting.android.opensdk.model.advertis.constants.IAdConstants;
import com.ximalaya.ting.android.opensdk.model.track.Track;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.player.check.PauseReason;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONObject;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 激励广告解锁单条付费声音
 */
public class AdUnLockTimeManagerV3 {
    private static final String TAG = "AdUnLockTimeManagerV3";

    private static volatile AdUnLockTimeManagerV3 mInstance;
    private static final int MAX_LOAD_TIME = 10000;
    private int maxAdLoadTime = MAX_LOAD_TIME; // 最长加载广告时间为10秒，超时报错
    private boolean isAdRequestOverTime = false;
    private String curSourceName = "";
    private boolean lastIsPlaying;

    private IAdUnLockStatusCallBack currentCallBack;
    private LoadingDialog loadingDialog;

    private int currentAdIndex;

    private List<AdUnLockVipTrackAdvertis> currentAdvertisList = new ArrayList<>();

    private AdUnLockVipTrackAdvertis currentAd;

    private long currentAlbumId;
    private long currentTrackId;

    private List<Long> commentShowTime = new ArrayList<>();
    private boolean isAdPlayComplete = false;
    private boolean isAdRewardVerify = false;
    private boolean hasNotifyClose = false;
    private boolean hasHandleUserClick = false;
    private long startLoadAdTime; // 开始加载广告的时间
    private long adBeginShowTime;

    private RewardExtraParams currentExtraParams;
    private RewardExtraParams lastExtraParams;
    private String cancelToast;
    private long startRequestTime;// 开始请求adx广告的时间

    private AdUnLockTimeManagerV3() {
    }

    public static AdUnLockTimeManagerV3 getInstance() {
        if (mInstance == null) {
            synchronized (AdUnLockTimeManagerV3.class) {
                if (mInstance == null) {
                    mInstance = new AdUnLockTimeManagerV3();
                }
            }
        }
        return mInstance;
    }

    public interface IAdUnLockStatusCallBack {
        void onRewardSuccess(@Nullable Advertis advertis, boolean realFinishTask);

        void onRewardFail(String message);

        void onPlayStart();
    }

    public void unlockTrack(int playMethod, String sourceName,
                            @NonNull IAdUnLockStatusCallBack callBack, RewardExtraParams extraParams) {
        long trackId = 0;
        long albumId = 0;
        PlayableModel playableModel = XmPlayerManager.getInstance(ToolUtil.getCtx()).getCurrSound();
        if (playableModel != null && playableModel instanceof Track) {
            trackId = playableModel.getDataId();
            Track track = ((Track) playableModel);
            if (track.getAlbum() != null) {
                albumId = track.getAlbum().getAlbumId();
            }
        }
        setRewardCallBack(playMethod, sourceName, callBack, extraParams);
        unlockTrack(playMethod, albumId, trackId, sourceName, callBack, extraParams, null);
    }

    public void setRewardCallBack(int playMethod ,String sourceName,  @NonNull IAdUnLockStatusCallBack callBack, RewardExtraParams rewardExtraParams){
        if(rewardExtraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE ||rewardExtraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE){
            rewardExtraParams.setRewardNextVideoCallBack(new RewardExtraParams.IRewardNextVideoCallBack() {
                @Override
                public void jumpToNextVideo(Activity activity, RewardExtraParams params) {
                    unlockTrack(playMethod, sourceName, callBack, params);
                }
            });
        }
    }

    /**
     * 吊起广告接口
     *
     * @param playMethod 播放方式，手动或者自动或者切换
     * @param albumId    专辑id
     * @param trackId    声音id
     * @param callBack   广告状态回调
     */
    public void unlockTrack(int playMethod, long albumId, long trackId, String sourceName,
                            @NonNull IAdUnLockStatusCallBack callBack, RewardExtraParams lastParam, Activity lastActivity) {
        Logger.i(TAG, "unlockTrack playMethod =" + playMethod + " albumId=" + albumId + " trackId=" + trackId);
        isAdPlayComplete = false;
        isAdRewardVerify = false;
        hasNotifyClose = false;
        hasHandleUserClick = false;
        commentShowTime.clear();
        currentAlbumId = albumId;
        currentTrackId = trackId;
        curSourceName = sourceName;
        currentCallBack = callBack;
        currentAd = null;
        Activity topActivity = lastActivity;
        if (topActivity == null) {
            topActivity = MainApplication.getMainActivity();
        }
        if (topActivity == null) {
            if (callBack != null) {
                callBack.onRewardFail("广告请求失败，请稍后重试");
            }
            return;
        }
        lastIsPlaying = XmPlayerManager.getInstance(ToolUtil.getCtx()).isPlaying();
        XmPlayerManager.getInstance(ToolUtil.getCtx()).stopSoundPatchPlay();
        if (lastIsPlaying) {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).pause(PauseReason.Business.AdUnlockPaidManagerV3);
        }
        loadingDialog = new LoadingDialog(topActivity);
        loadingDialog.setTitle("正在努力加载中");
        loadingDialog.showIcon(true);
        lastExtraParams = lastParam;
        currentExtraParams = lastExtraParams;
        showLoadingDialog();
        getUnlockAdvertis(playMethod, false, callBack);
    }

    // 请求广告物料
    private void getUnlockAdvertis(int playMethod, boolean duringPlay, IAdUnLockStatusCallBack callBack) {
        currentAdIndex = 0;
        currentAdvertisList.clear();

        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appid", "0");
        if (lastExtraParams != null && lastExtraParams.getPositionName() != null) {
            requestMap.put("name", lastExtraParams.getPositionName());
        } else {
            requestMap.put("name", AppConstants.AD_POSITION_NAME_INCENTIVE);
        }
        requestMap.put("duringPlay", duringPlay + "");
        requestMap.put("isDisplayedInScreen", true + "");
        if (!TextUtils.isEmpty(curSourceName)) {
            requestMap.put("sourceName", curSourceName);
        }
        if (ElderlyModeManager.getInstance().isElderlyMode()) {
            requestMap.put("pageMode", Advertis.PAGE_MODE_ELDERLY + "");
        } else {
            requestMap.put("pageMode", Advertis.PAGE_MODE_NORMAL + "");
        }
        requestMap.put("playMethod", playMethod + "");
        if (currentExtraParams != null && currentExtraParams.getExtInfo() != null) {
            try {
                requestMap.put("extInfo", URLEncoder.encode(currentExtraParams.getExtInfo(), "UTF-8"));
            } catch (Throwable e) {
                Logger.e(TAG, "getUnlockAdvertis extInfo error" + e.getMessage());
            }
        }
//        if (currentTrackId != 0) {
//            requestMap.put("trackId", currentTrackId + "");
//        }
//        if (currentAlbumId != 0) {
//            requestMap.put("album", currentAlbumId + "");
//        }
        requestMap.put("uid", UserInfoMannage.getUid() + "");
        maxAdLoadTime = ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_MAX_LOAD_TIME, MAX_LOAD_TIME);
        isAdRequestOverTime = false;
        startRequestTime = System.currentTimeMillis();
        CountDownTimer countDownTimer = new CountDownTimer(getAdMaxLoadTime(), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
            }

            @Override
            public void onFinish() {
                // 10秒之后广告仍未请求完成，认为此次广告加载失败
                isAdRequestOverTime = true;
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 loadVideo fail request ad timeout");
                notifyAdClose(null, false);
            }
        };
        countDownTimer.start();
        AdRequest.getVipFreeAd(requestMap, new IDataCallBack<List<AdUnLockVipTrackAdvertis>>() {
            @Override
            public void onSuccess(@Nullable List<AdUnLockVipTrackAdvertis> object) {
                if (isAdRequestOverTime) {
                    return;
                }
                countDownTimer.cancel();
                if (object == null || object.size() == 0) {
                    FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 getUnlockAdvertis fail return null");
                    dismissLoadingDialog();
                    notifyAdClose(null, false);
                    return;
                }
                AdUnlockUtil.updateAd(object);
                maxAdLoadTime = maxAdLoadTime - (int) (System.currentTimeMillis() - startRequestTime);
                currentAdvertisList.addAll(object);
                if(HostCommonRtbSortUtil.rtbRewardAdEnable(object)) {
                    loadVideoAdParallel(object, 7000, true);
                } else {
                    loadVideoAd();
                }
            }

            @Override
            public void onError(int code, String message) {
                if (isAdRequestOverTime) {
                    return;
                }
                countDownTimer.cancel();
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 getUnlockAdvertis fail onError");
                notifyAdClose(null, false);
            }
        });
    }

    private void loadVideoAdParallel(List<AdUnLockVipTrackAdvertis> advertisList, int parallelMaxTime, boolean rtbEnable) {
        Log.d("---RewardVideoAdManager", "loadVideoAdParallel parallelMaxTime =" + 0 + " maxLoadTime = " + maxAdLoadTime);
        startLoadAdTime = System.currentTimeMillis();
        if (currentCallBack == null) {
            return;
        }
        IVideoAdStatueCallBackExt videoAdStatueCallBack =  new IVideoAdStatueCallBackExt() {
            @Override
            public void onRewardVerify() {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onRewardVerify");
                Log.d(TAG, "onRewardVerify");
                //是否需要立即发送奖励
                if (currentExtraParams != null && currentExtraParams.getRewardThirdSDKSuccessCallBack() != null) {
                    Log.d("AdRewardCashTasksView", "onRewardVerify: ");
                    currentExtraParams.setRewardBeforeClose(true);
                    currentExtraParams.getRewardThirdSDKSuccessCallBack().onSdkRewardSuccess();
                }
                if (needSendRewardImmediately()) {
                    notifyAdOnReward(true);
                } else {
                    isAdRewardVerify = true;
                }
                AdStateReportManager.getInstance().onRewardVerify(currentAd, getCurAdPositionName());
                preloadInspireAd();
                reportRewardVerify();
            }

            boolean isAdClicked = false;
            @Override
            public void onAdLoad(AbstractThirdAd abstractThirdAd) {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdLoad");
                if (currentAd == null) {
                    return;
                }
                if (AdUnlockUtil.isWebAd(currentExtraParams)) {
                    // 加载开始时关闭加载中弹窗
                    dismissLoadingDialog();
                }
                String positionName =
                        currentExtraParams != null && !TextUtils.isEmpty(currentExtraParams.getPositionName())
                                ? currentExtraParams.getPositionName()
                                : AppConstants.AD_POSITION_NAME_INCENTIVE;
                AdReportModel.Builder builder = AdReportModel.newBuilder(
                                AppConstants.AD_LOG_TYPE_SITE_SHOW, positionName)
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .albumIdUseStr(currentAlbumId + "")
                        .adUserType(currentAd.getAdUserType())
                        .trackId(currentTrackId + "")
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(curSourceName + "")
                        .showStyle(currentAd.getShowstyle() + "");
                buildRewardReportData(builder);
                AdManager.adRecord(MainApplication.getMyApplicationContext(),
                        currentAd, builder.build());
                //曝光时机预加载
                WebViewPreloadManager.getInstance().preloadWhenAdExposure(currentAd);
            }

            @Override
            public void onAdLoadError(int code, String message) {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdLoadError code= " + code + " message =" + message);
                if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, true);
                } else {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, false);
                }
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 onAdLoadError");
                notifyAdClose(null, false);
            }

            @Override
            public void onAdPlayStart() {
                if (isAdPlayComplete) {
                    return;
                }
                RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, true, false);
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdPlayStart");
                dismissLoadingDialog();
                if (currentCallBack != null) {
                    currentCallBack.onPlayStart();
                }
                adBeginShowTime = System.currentTimeMillis();
                if (currentAd != null) {
                    AdReportModel.Builder builder = AdReportModel.newBuilder(
                                    AppConstants.AD_LOG_TYPE_SHOW_OB,
                                    getCurAdPositionName())
                            .sdkType(AdManager.getSDKType(currentAd) + "")
                            .dspPositionId(currentAd.getDspPositionId())
                            .uid(UserInfoMannage.getUid() + "")
                            .albumIdUseStr(currentAlbumId + "")
                            .trackId(currentTrackId + "")
                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                            .sourceName(curSourceName + "")
                            .showStyle(currentAd.getShowstyle() + "");
                    buildRewardReportData(builder);
                    AdManager.adRecord(MainApplication.getMyApplicationContext(),
                            currentAd, builder.build());
                }
            }

            @Override
            public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                if (isAdClicked && AdManager.isThirdAd(currentAd)) {
                    return;
                }
                if (currentAd != null) {
                    AdReportModel.Builder builder =
                            AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                            getCurAdPositionName())
                                    .sdkType(AdManager.getSDKType(currentAd) + "")
                                    .dspPositionId(currentAd.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .showStyle(currentAd.getShowstyle() + "")
                                    .albumIdUseStr(currentAlbumId + "")
                                    .trackId(currentTrackId + "")
                                    .autoPoll(isAutoClick ? 2 : 1)
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .sourceName(curSourceName + "");
                    buildRewardReportData(builder);
                    if (!AdManager.isThirdAd(currentAd) && !isAutoClick) {
                        builder.clickAreaType(clickAreaType);
                    }
                    if (AdManager.isThirdAd(currentAd) || currentAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT
                            || clickAreaType == 5) {
                        builder.ignoreTarget(true)
                                .onlyClickRecord(true);
                    }
                    // 点击样式需要做去重点击上报，防止触发三方反作弊机制
                    if (isAdClicked && !isAutoClick && currentExtraParams != null &&
                            (currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK || currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK)) {
                        builder.onlyGotoClickNoRecord(true);
                    }
                    AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), currentAd,
                            builder.build());
                }
                if (!isAutoClick) {
                    isAdClicked = true;
                }
            }

            @Override
            public void onAdClose(boolean isCustomCloseBtn) {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdClose isAdRewardVerify = " + isAdRewardVerify);
                Log.d(TAG, "onAdClose isAdRewardVerify = " + isAdRewardVerify);
                // 三方activity finish的时候会调用该方法
                if (AdManager.isThirdAd(currentAd) && !hasHandleUserClick) {
                    if (isAdRewardVerify) {
                        // 已达到奖励条件，可解锁
                        notifyAdClose(null, true);
                    } else {
                        // 未达到奖励条件，不可解锁
                        notifyAdCancel();
                    }
                }
            }

            @Override
            public void onAdPlayComplete() {
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdPlayComplete");
                int rewardType = AdUnlockUtil.getRewardType(currentAd);
                if (!AdManager.isThirdAd(currentAd) && rewardType != AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK && rewardType != AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
                    // 非点击样式，完播可以发放奖励
                    isAdRewardVerify = true;
                }
                if (isAdPlayComplete) {
                    return;
                }
                isAdPlayComplete = true;
                if (currentAd != null) {
                    AdReportModel.Builder builder = AdReportModel.newBuilder(
                                    AppConstants.AD_LOG_TYPE_SHOW_OB,
                                    getCurAdPositionName())
                            .sdkType(AdManager.getSDKType(currentAd) + "")
                            .dspPositionId(currentAd.getDspPositionId())
                            .uid(UserInfoMannage.getUid() + "")
                            .albumIdUseStr(currentAlbumId + "")
                            .trackId(currentTrackId + "")
                            .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                            .playFinish(isAdPlayComplete ? "1" : "0")
                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                            .sourceName(curSourceName + "")
                            .showStyle(currentAd.getShowstyle() + "");
                    buildRewardReportData(builder);
                    AdManager.adRecord(MainApplication.getMyApplicationContext(),
                            currentAd, builder.build());
                }
            }

            @Override
            public void onAdPlayError(int code, String message) {
                isAdRewardVerify = true;
                Log.d("---RewardVideoAdManager", "videoAdStatueCallBack onAdPlayError code =" + code + " msg = " + message);
                if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, true);
                } else {
                    RewardVideoReport.reportAllFinish(currentAd, System.currentTimeMillis() - startRequestTime, false, false);
                }
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 onAdPlayError");
                if (!AdManager.isThirdAd(currentAd)) {
                    notifyAdClose(null, false);
                }
            }

            @Override
            public View.OnClickListener getCloseClickListener(Activity rewardActivity) {
                return new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Logger.logToFile("rewardVideo", "CloseClickListener click" +
                                " rewardActivity =" + rewardActivity + " isValid =" + ToolUtil.activityIsValid(rewardActivity) + " view =" + v);
                        hasHandleUserClick = true;
                        if (!ToolUtil.activityIsValid(rewardActivity)) {
                            return;
                        }
                        if (v == null) {
                            return;
                        }
                        dismissLoadingDialog();
                        if (v.getId() == R.id.host_reward_count_down) {
                            // 10秒倒计时已结束，可获取解锁权限
                            notifyAdClose(rewardActivity, true);
                        } else if (v.getId() == R.id.host_reward_close_real) {
                            if (isAdRewardVerify) {
                                // 视频已播放完成，可解锁
                                notifyAdClose(rewardActivity, true);
                            } else {
                                // 视频未播放完，且10秒倒计时未结束，不可解锁
                                finishAdActivityOrFragment(rewardActivity);
                                notifyAdCancel();
                            }
                        } else if (v.getId() == R.id.host_reward_open_vip) {
                            // 点击开会员
                        }else if(v.getId() == R.id.host_reward_count_down_welfare_cash){
                            //福利页奖励
                            notifyAdOnReward(true);
                        } else if (v.getId() == R.id.host_reward_close_real_welfare_cash) {
                            //福利页关闭
                            notifyAdCancel();
                            finishAdActivityOrFragment(rewardActivity);
                        }else if((v.getId() == R.id.host_reward_count_down_gdt_immersive)){
                            //短剧激励已达成，通知rn发送奖励（关闭时发奖励） （立即发奖励 notifyAdReward(rewardActivity)）
                            isAdRewardVerify = true;
                            hasHandleUserClick= false;
                            AdStateReportManager.getInstance().onRewardVerify(currentAd, getCurAdPositionName());
                        }
                    }
                };
            }
        };
        long startRequestTime = System.currentTimeMillis();
        RewardVideoAdManager.IRewardAdLoadCallBack iRewardAdLoadCallBack = new RewardVideoAdManager.IRewardAdLoadCallBack() {
            @Override
            public void onAdLoadSuccess(AbstractRewardVideoAd rewardVideoAd) {
                Log.d("---RewardVideoAdManager", "onAdLoadSuccess rewardVideoAd" + rewardVideoAd);
                if (rewardVideoAd == null) {
                    dismissLoadingDialog();
                    FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 loadVideo fail loading timeout");
                    notifyAdClose(null, false);
                    return;
                }
                maxAdLoadTime = maxAdLoadTime - (int) (System.currentTimeMillis() - startRequestTime);
                realShowRewardAd(rewardVideoAd, videoAdStatueCallBack);
            }

            @Override
            public void onAdLoadError(int code, String message) {
                RewardVideoReport.reportAllFinish(null, System.currentTimeMillis() - startRequestTime, false, false);
                Log.d("---RewardVideoAdManager", "onAdLoadError message = " + message + " code = " + code);
                dismissLoadingDialog();
                FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 loadVideo fail loading timeout");
                notifyAdClose(null, false);
            }
        };
        if (rtbEnable) {
            ThirdAdLoadParams thirdAdLoadParams = new ThirdAdLoadParams(AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION, startRequestTime);
            thirdAdLoadParams.setDspRequestTimeMs(Math.min(parallelMaxTime, maxAdLoadTime));
            HostRewardVideoRtbAdLoadManager.loadAd(advertisList, thirdAdLoadParams, iRewardAdLoadCallBack, videoAdStatueCallBack);
        }
//        else {
//            RewardVideoAdManager.getInstance().loadRewardVideoAdParallel(advertisList,
//                    iRewardAdLoadCallBack, Math.min(parallelMaxTime, maxAdLoadTime), videoAdStatueCallBack);
//        }
    }

    private void realShowRewardAd(AbstractRewardVideoAd rewardVideoAd, IVideoAdStatueCallBackExt videoAdStatueCallBack) {
        Log.d("---RewardVideoAdManager", " realShowRewardAd maxLoadTime = " + maxAdLoadTime);
        currentAd = (AdUnLockVipTrackAdvertis) rewardVideoAd.getAdvertis();
        if (currentAd == null) {
            dismissLoadingDialog();
            FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 realShowRewardAd fail currentAd null");
            notifyAdClose(null, false);
            return;
        }
        int adType = currentAd.getAdtype();
        int showStyle = currentAd.getShowstyle();

        int dspAdType = AdUnlockUtil.getDspAdType(adType, showStyle);
        if (dspAdType == -1) {
            // 类型不匹配
            dismissLoadingDialog();
            notifyAdClose(null, false);
            return;
        }
        RewardExtraParams extraParams;
        if (lastExtraParams != null) {
            extraParams = lastExtraParams;
        } else {
            extraParams = new RewardExtraParams();
        }
        extraParams.setSourceName(curSourceName);
        extraParams.setRewardPageStatusCallBack(new RewardExtraParams.IRewardPageStatusCallBack() {
            @Override
            public void onPageResume(Activity activity, int source) {
            }

            @Override
            public void onRewardVerify() {
                preloadInspireAd();
                reportRewardVerify();
            }

            @Override
            public void onCommentShow(long commentTime) {
                commentShowTime.add(commentTime);
            }
        });
        extraParams.setCloseable(true);
        if (currentAd != null) {
            if (currentAd.getUnlockTime() > 0 && extraParams.getCanCloseTime() <= 0) {
                extraParams.setCanCloseTime(currentAd.getUnlockTime());
            }
            if (currentAd.getUnlockTimeV2() != 0) {
                extraParams.setCanCloseTime(currentAd.getUnlockTimeV2());
            }
            extraParams.setVideoPlayOverTime(currentAd.getVideoDuration());
        }
        extraParams.setXmVideoAdvertisModel(currentAd, extraParams.getPositionName());
        int rewardType = AdUnlockUtil.getRewardType(currentAd);
        extraParams.setUnlockType(rewardType);
        extraParams.setShakeEnable(AdUnlockUtil.isShakeEnable(currentAd, rewardType));
        if (rewardType == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK || rewardType == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT);
            }
            //如果是之前标记的倒计时模式，则重新标记
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD);
            }
        } else {
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD);
            }
        }
        cancelToast =  RewardVideoConfigManager.getInstance().getFailToast(RewardVideoConfigManager.getInstance().getRealConfigPositionName(extraParams));
        currentExtraParams = extraParams;
        RewardVideoAdManager.getInstance().showRewardVideoAdReal(rewardVideoAd, BaseApplication.getMainActivity(), extraParams, videoAdStatueCallBack);
    }

    private void loadVideoAd() {
        isAdRewardVerify = false;
        isAdPlayComplete = false;
        hasNotifyClose = false;
        hasHandleUserClick = false;
        if (currentCallBack == null) {
            return;
        }
        if (currentAdIndex >= currentAdvertisList.size()) {
            dismissLoadingDialog();
            FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 loadVideo fail index max");
            notifyAdClose(null, false);
            return;
        }
        currentAd = currentAdvertisList.get(currentAdIndex);
        if (currentAd == null) {
            dismissLoadingDialog();
            FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 loadVideo fail currentAd null");
            notifyAdClose(null, false);
            return;
        }
        String dspPotionId = currentAd.getDspPositionId();
        int adType = currentAd.getAdtype();
        int showStyle = currentAd.getShowstyle();

        int dspAdType = AdUnlockUtil.getDspAdType(adType, showStyle);
        if (dspAdType == -1) {
            // 类型不匹配
            dismissLoadingDialog();
            notifyAdClose(null, false);
            return;
        }
        RewardExtraParams extraParams;
        if (lastExtraParams != null) {
            extraParams = lastExtraParams;
        } else {
            extraParams = new RewardExtraParams();
        }
        extraParams.setSourceName(curSourceName);
        extraParams.setRewardPageStatusCallBack(new RewardExtraParams.IRewardPageStatusCallBack() {
            @Override
            public void onPageResume(Activity activity, int source) {
            }

            @Override
            public void onRewardVerify() {
                preloadInspireAd();
                reportRewardVerify();
            }

            @Override
            public void onCommentShow(long commentTime) {
                commentShowTime.add(commentTime);
            }
        });
        extraParams.setCloseable(true);
        if (currentAd != null) {
            if (currentAd.getUnlockTime() > 0 && extraParams.getCanCloseTime() <= 0) {
                extraParams.setCanCloseTime(currentAd.getUnlockTime());
            }
            if (currentAd.getUnlockTimeV2() != 0) {
                extraParams.setCanCloseTime(currentAd.getUnlockTimeV2());
            }
            extraParams.setVideoPlayOverTime(currentAd.getVideoDuration());
        }
        extraParams.setXmVideoAdvertisModel(currentAd, extraParams.getPositionName());
        int rewardType = AdUnlockUtil.getRewardType(currentAd);
        extraParams.setUnlockType(rewardType);
        extraParams.setShakeEnable(AdUnlockUtil.isShakeEnable(currentAd, rewardType));
        if (rewardType == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK || rewardType == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT);
            }
            //如果是之前标记的倒计时模式，则重新标记
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD);
            }
        } else {
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE || extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE);
            }
            if (extraParams.getRewardCountDownStyle() == RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD) {
                extraParams.setRewardCountDownStyle(RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD);
            }
        }
        cancelToast = RewardVideoConfigManager.getInstance().getFailToast(RewardVideoConfigManager.getInstance().getRealConfigPositionName(extraParams));
        currentExtraParams = extraParams;

        // SDK请求进行上报
        AdStateReportManager.getInstance().onUnlockRequestBegin(currentAd,
                getCurAdPositionName(), new AdStateReportManager.IAdStateBuilderInterceptor() {
                    @Override
                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                        builder.albumId(currentAlbumId + "");
                        builder.trackId(currentTrackId + "");
                        builder.uuid(UUID.randomUUID().toString());
                    }
                });
        startLoadAdTime = System.currentTimeMillis();
        RewardVideoAdManager.getInstance().loadRewardAd(BaseApplication.getMainActivity(), currentAd, dspPotionId, adType, dspAdType,
                extraParams,
                new IVideoAdStatueCallBackExt() {
                    @Override
                    public void onRewardVerify() {
                        Log.d(TAG, "onRewardVerify");
                        //是否需要立即发送奖励
                        if (needSendRewardImmediately()) {
                            notifyAdOnReward(true);
                        } else {
                            isAdRewardVerify = true;
                        }
                        AdStateReportManager.getInstance().onRewardVerify(currentAd, getCurAdPositionName());
                        preloadInspireAd();
                        reportRewardVerify();
                    }

                    boolean isAdClicked = false;
                    @Override
                    public void onAdLoad(AbstractThirdAd abstractThirdAd) {
                        // SDK请求返回
                        new XMTraceApi.Trace()
                                .pageView(32259, "videoAd")
                                .put("currPage", "videoAd")
                                .createTrace();
                        String positionName =
                                currentExtraParams != null && !TextUtils.isEmpty(currentExtraParams.getPositionName())
                                        ? currentExtraParams.getPositionName()
                                        : AppConstants.AD_POSITION_NAME_INCENTIVE;
                        AdStateReportManager.getInstance().onSDKBackSuccess(currentAd,
                                startLoadAdTime, positionName,
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(currentAlbumId + "");
                                        builder.trackId(currentTrackId + "");
                                    }
                                });
                        if (AdUnlockUtil.isWebAd(currentExtraParams)) {
                            // 加载开始时关闭加载中弹窗
                            dismissLoadingDialog();
                        }
                        if (currentAd != null) {
                            AdReportModel.Builder builder = AdReportModel.newBuilder(
                                            AppConstants.AD_LOG_TYPE_SITE_SHOW, positionName)
                                    .sdkType(AdManager.getSDKType(currentAd) + "")
                                    .dspPositionId(currentAd.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .albumIdUseStr(currentAlbumId + "")
                                    .adUserType(currentAd.getAdUserType())
                                    .trackId(currentTrackId + "")
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .sourceName(curSourceName + "")
                                    .showStyle(currentAd.getShowstyle() + "");
                            buildRewardReportData(builder);
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    currentAd, builder.build());
                            //曝光时机预加载
                            WebViewPreloadManager.getInstance().preloadWhenAdExposure(currentAd);
                        }
                    }

                    @Override
                    public void onAdLoadError(int code, String message) {
                        Log.d(TAG, "onAdLoadError message = " + message + " code = " + code);
                        int status = AdStateReportManager.STATUS_REQUEST_TIMEOUT_OR_ERROR;
                        if (IVideoAdStatueCallBack.ERROR_CODE_NO_AD == code) {
                            status = AdStateReportManager.STATUS_SDK_NO_BACK;
                        }
                        AdStateReportManager.getInstance().onShowFail(currentAd, status,
                                startLoadAdTime, getCurAdPositionName(),
                                new AdStateReportManager.IAdStateBuilderInterceptor() {
                                    @Override
                                    public void adStateBuilderInterceptor(@NonNull AdStateData.Builder builder) {
                                        builder.albumId(currentAlbumId + "");
                                        builder.trackId(currentTrackId + "");
                                    }
                                });
                        if (code == IVideoAdStatueCallBack.ERROR_CODE_AD_LOAD_OVER_TIME) {
                            // 加载超时时不顺延，直接返回错误
                            dismissLoadingDialog();
                            FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 loadVideo fail loading timeout");
                            if (currentExtraParams != null && currentExtraParams.isRewardingSecond()) {
                                currentExtraParams.setRewardingSecond(false);
                            } else {
                                notifyAdClose(null, false);
                            }
                        } else {
                            maxAdLoadTime = maxAdLoadTime - (int) (System.currentTimeMillis() - startLoadAdTime);
                            currentAdIndex++;
                            loadVideoAd();
                        }
                    }

                    @Override
                    public void onAdPlayStart() {
                        if (isAdPlayComplete){
                            return;
                        }
                        dismissLoadingDialog();
                        if (currentCallBack != null) {
                            currentCallBack.onPlayStart();
                        }
                        adBeginShowTime = System.currentTimeMillis();
                        if (currentAd != null) {
                            AdReportModel.Builder builder = AdReportModel.newBuilder(
                                            AppConstants.AD_LOG_TYPE_SHOW_OB,
                                            getCurAdPositionName())
                                    .sdkType(AdManager.getSDKType(currentAd) + "")
                                    .dspPositionId(currentAd.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .albumIdUseStr(currentAlbumId + "")
                                    .trackId(currentTrackId + "")
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .sourceName(curSourceName + "")
                                    .showStyle(currentAd.getShowstyle() + "");
                            buildRewardReportData(builder);
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    currentAd, builder.build());
                        }
                    }

                    @Override
                    public void onAdVideoClick(boolean isAutoClick, int clickAreaType) {
                        if (currentAd != null) {
                            AdReportModel.Builder builder =
                                    AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_SITE_CLICK,
                                                    getCurAdPositionName())
                                            .sdkType(AdManager.getSDKType(currentAd) + "")
                                            .dspPositionId(currentAd.getDspPositionId())
                                            .uid(UserInfoMannage.getUid() + "")
                                            .showStyle(currentAd.getShowstyle() + "")
                                            .albumIdUseStr(currentAlbumId + "")
                                            .trackId(currentTrackId + "")
                                            .autoPoll(isAutoClick ? 2 : 1)
                                            .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                            .sourceName(curSourceName + "");
                            buildRewardReportData(builder);
                            if (!AdManager.isThirdAd(currentAd) && !isAutoClick) {
                                builder.clickAreaType(clickAreaType);
                            }
                            if (AdManager.isThirdAd(currentAd) || currentAd.getClickType() == IAdConstants.IAdClickType.CLICK_TYPE_COLLECT
                                    || clickAreaType == 5) {
                                builder.ignoreTarget(true)
                                        .onlyClickRecord(true);
                            }
                            // 点击样式需要做去重点击上报，防止触发三方反作弊机制
                            if (isAdClicked && !isAutoClick && currentExtraParams != null &&
                                    (currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK || currentExtraParams.getUnlockType() == AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK)) {
                                builder.onlyGotoClickNoRecord(true);
                            }
                            AdManager.handlerAdClick(MainApplication.getMyApplicationContext(), currentAd,
                                    builder.build());
                        }
                        if (!isAutoClick) {
                            isAdClicked = true;
                        }
                    }

                    @Override
                    public void onAdClose(boolean isCustomCloseBtn) {
                        Log.d(TAG, "onAdClose isAdRewardVerify = " + isAdRewardVerify);
                        // 三方activity finish的时候会调用该方法
                        if (AdManager.isThirdAd(currentAd) && !hasHandleUserClick) {
                            if (isAdRewardVerify) {
                                // 已达到奖励条件，可解锁
                                notifyAdClose(null, true);
                            } else {
                                // 未达到奖励条件，不可解锁
                                notifyAdCancel();
                            }
                        }
                    }

                    @Override
                    public void onAdPlayComplete() {
                        int rewardType = AdUnlockUtil.getRewardType(currentAd);
                        if (!AdManager.isThirdAd(currentAd) && rewardType != AdUnlockUtil.REWARD_TYPE_VIDEO_CLICK && rewardType != AdUnlockUtil.REWARD_TYPE_IMAGE_CLICK) {
                            isAdRewardVerify = true;
                        }
                        if (isAdPlayComplete){
                            return;
                        }
                        isAdPlayComplete = true;
                        if (currentAd != null) {
                            AdReportModel.Builder builder = AdReportModel.newBuilder(
                                            AppConstants.AD_LOG_TYPE_SHOW_OB,
                                            getCurAdPositionName())
                                    .sdkType(AdManager.getSDKType(currentAd) + "")
                                    .dspPositionId(currentAd.getDspPositionId())
                                    .uid(UserInfoMannage.getUid() + "")
                                    .albumIdUseStr(currentAlbumId + "")
                                    .trackId(currentTrackId + "")
                                    .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                                    .playFinish(isAdPlayComplete ? "1" : "0")
                                    .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                                    .sourceName(curSourceName + "")
                                    .showStyle(currentAd.getShowstyle() + "");
                            buildRewardReportData(builder);
                            AdManager.adRecord(MainApplication.getMyApplicationContext(),
                                    currentAd, builder.build());
                        }
                    }

                    @Override
                    public void onAdPlayError(int code, String message) {
                        isAdRewardVerify = true;
                        Log.d(TAG, "onAdPlayError code =" + code + " msg = " + message);
                        dismissLoadingDialog();
                        FreeListenLogManager.writeLog("AdUnLockTimeManagerV3 onAdPlayError");
                        notifyAdClose(null, false);
                    }

                    @Override
                    public View.OnClickListener getCloseClickListener(Activity rewardActivity) {
                        return new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Logger.logToFile("rewardVideo", "CloseClickListener click" +
                                        " rewardActivity =" + rewardActivity + " isValid =" + ToolUtil.activityIsValid(rewardActivity) + " view =" + v);
                                hasHandleUserClick = true;
                                if (!ToolUtil.activityIsValid(rewardActivity)) {
                                    return;
                                }
                                if (v == null) {
                                    return;
                                }
                                dismissLoadingDialog();
                                if (v.getId() == R.id.host_reward_count_down) {
                                    // 10秒倒计时已结束，可获取解锁权限
                                    notifyAdClose(rewardActivity, true);
                                } else if (v.getId() == R.id.host_reward_close_real) {
                                    if (isAdRewardVerify) {
                                        // 视频已播放完成，可解锁
                                        notifyAdClose(rewardActivity, true);
                                    } else {
                                        // 视频未播放完，且10秒倒计时未结束，不可解锁
                                        finishAdActivityOrFragment(rewardActivity);
                                        notifyAdCancel();
                                    }
                                } else if (v.getId() == R.id.host_reward_open_vip) {
                                    // 点击开会员
                                }else if(v.getId() == R.id.host_reward_count_down_welfare_cash){
                                    //福利页奖励
                                    notifyAdOnReward(true);
                                } else if (v.getId() == R.id.host_reward_close_real_welfare_cash) {
                                    //福利页关闭
                                    notifyAdCancel();
                                    finishAdActivityOrFragment(rewardActivity);
                                }else if((v.getId() == R.id.host_reward_count_down_gdt_immersive)){
                                    //短剧激励已达成，通知rn发送奖励（关闭时发奖励） （立即发奖励 notifyAdReward(rewardActivity)）
                                    isAdRewardVerify = true;
                                    hasHandleUserClick = false;
                                    AdStateReportManager.getInstance().onRewardVerify(currentAd, getCurAdPositionName());
                                }
                            }
                        };
                    }
                });
    }

    private void buildRewardReportData(SDKAdReportModel.Builder builder) {
        if (builder != null && currentExtraParams != null) {
            String step = currentExtraParams.getPointCashStep();
            String rewardType = currentExtraParams.getPointCashType() + "";
            builder.step(step).rewardType(rewardType);
        }
    }

    private void notifyAdClose(Activity thirdSdkActivity, boolean isRealFinishTask) {
        replay();
        if (currentCallBack == null) {
            return;
        }
        finishAdActivityOrFragment(thirdSdkActivity);
        if (hasNotifyClose) {
            return;
        }
        hasNotifyClose = true;
        if (currentCallBack != null) {
            currentCallBack.onRewardSuccess(currentAd, isRealFinishTask);
        }
        reportOnAdClose(true, isRealFinishTask);
    }


    private void notifyAdReward(Activity thirdSdkActivity) {
//        if (currentCallBack == null) {
//            return;
//        }
//        if (hasNotifyClose) {
//            return;
//        }
//        hasNotifyClose = true;
//        if (currentCallBack != null) {
//            currentCallBack.onRewardForRN(
//                    currentAd != null ? currentAd.getAdid() : 0,
//                    currentAd != null ? currentAd.getResponseId() : 0);
//        }
    }

    private void notifyAdOnReward(boolean isRealFinishTask) {
        if (currentCallBack == null) {
            return;
        }
        if (hasNotifyClose) {
            return;
        }
        if (currentCallBack != null) {
            currentCallBack.onRewardSuccess(currentAd, isRealFinishTask);
        }
    }

    public void notifyAdCancel() {
        boolean isRewardBeforeClose = currentExtraParams != null && currentExtraParams.isRewardBeforeClose();
        // 延迟通知是等页面关闭后，再通知
        HandlerManager.postOnUIThreadDelay(() -> {
            if (currentCallBack != null && !isRewardBeforeClose) {
                currentCallBack.onRewardFail(cancelToast);
            }
            replay();
        }, 200);
        reportOnAdClose(isRewardBeforeClose, false);
    }

    // 结束广告相关的三方activity或者fragment
    private void finishAdActivityOrFragment(Activity adActivity) {
        if (currentExtraParams != null) {
            currentExtraParams.setVipFreeCloseAlertDialog(null);
            if (currentExtraParams.getCountDownTimer() != null) {
                currentExtraParams.getCountDownTimer().cancel();
                currentExtraParams.setCountDownTimer(null);
            }
            XmBaseDialog closeDialog = currentExtraParams.getVipFreeCloseAlertDialog();
            if (closeDialog != null && closeDialog.isShowing()) {
                closeDialog.dismiss();
            }
            currentExtraParams = null;
        }
        if (!ToolUtil.activityIsValid(adActivity)) {
            return;
        }
        if (adActivity instanceof MainActivity) {
            Fragment currentFragment = ((MainActivity) adActivity).getCurrentFragmentInManage();
            if (currentFragment instanceof IRewardAdFragment) {
                ((IRewardAdFragment) currentFragment).finish();
            }
        } else {
            adActivity.finish();
        }
    }

    private void reportRewardVerify() {
        if (currentAd == null) {
            return;
        }
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, AdReportModel.newBuilder(AppConstants.AD_LOG_TYPE_REWARD_VERIFY, getCurAdPositionName())
                        .sdkType(AdManager.getSDKType(currentAd) + "")
                        .dspPositionId(currentAd.getDspPositionId())
                        .uid(UserInfoMannage.getUid() + "")
                        .adUserType(currentAd.getAdUserType())
                        .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                        .sourceName(curSourceName)
                        .showStyle(currentAd.getShowstyle() + "")
                        .unlockType(currentAd.getUnlockType())
                        .rewardSuccess(true)
                        .build());
    }

    private void reportOnAdClose(boolean rewardSuccess, boolean isRealFinishTask) {
        if (currentAd == null) {
            return;
        }
        AdReportModel.Builder builder = AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_REWARD_CLOSE,
                        getCurAdPositionName())
                .sdkType(AdManager.getSDKType(currentAd) + "")
                .dspPositionId(currentAd.getDspPositionId())
                .uid(UserInfoMannage.getUid() + "")
                .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                .playFinish(isAdPlayComplete ? "1" : "0")
                .rewardSuccess(rewardSuccess)
                .rewardVideoDuration(!AdManager.isThirdAd(currentAd) ? currentAd.getVideoDuration() : 0)
                .adUserType(currentAd.getAdUserType())
                .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                .sourceName(curSourceName + "")
                .showStyle(currentAd.getShowstyle() + "")
                .unlockType(currentAd.getUnlockType())
                .fallBackReq(isRealFinishTask ? 0 : 1)
                .showCommentMss(getShowCommentMss())
                .listenTime(FreeListenTimeManager.getListenTime(ToolUtil.getCtx()));
        buildRewardReportData(builder);
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, builder.build());
    }

    private String getShowCommentMss() {
        List<Long> copy = new ArrayList<>();
        copy.addAll(commentShowTime);
        if (!ToolUtil.isEmptyCollects(copy)) {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < copy.size(); i++) {
                result.append(copy.get(i));
                if (i != copy.size() - 1) {
                    result.append(",");
                }
            }
            return result.toString();
        }
        return null;
    }

    private void showLoadingDialog() {
        if (loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    public void showRewardWelfareSuccessPush(Activity activity, String cashNumber) {
        try {
            if (AdManager.isThirdAd(currentAd)) {
                Activity recentAc = RewardVideoAdManager.getInstance().getRecentActivity();
                if (recentAc != null) {
                    activity = recentAc;
                }
            }
            View view = LayoutInflater.from(activity).inflate(R.layout.host_ad_reward_welfare_cash_succes, null);
            TextView textView = view.findViewById(R.id.host_reward_cash_number);
            textView.setText(cashNumber);
            Snackbar snackbar = Snackbar.with(activity)
                    .customView(view)
                    .swapVertical()
                    .type(MULTI_LINE)
                    .shadow(true)
                    .margin(BaseUtil.dp2px(activity, 16f),
                            BaseUtil.dp2px(activity, 12f),
                            BaseUtil.dp2px(activity, 16f), 0)
                    .duration(Snackbar.SnackbarDuration.LENGTH_NORMAL)
                    .position(Snackbar.SnackbarPosition.TOP);
            SnackbarManager.show(snackbar);
            Logger.i("listenEarnRewardWelfare", "  -- -- 弹出领取金钱成功的push");
        } catch (Exception e) {
            Logger.e("showRewardWelfareSuccessPush", e.getMessage());
        }
    }

    private void dismissLoadingDialog() {
        if (loadingDialog != null && loadingDialog.isShowing()) {
            try {
                loadingDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void replay() {
        if (lastIsPlaying) {
            XmPlayerManager.getInstance(ToolUtil.getCtx()).play();
            lastIsPlaying = false;
        }
    }

    public long getCurrentTrackId() {
        return currentTrackId;
    }

    public long getCurrentAlbumId() {
        return currentAlbumId;
    }

    /**
     * 广告内部接口：激励视频倒计时结束,满足奖励条件
     */
    public void onCountDownFinish() {
        if (currentAd == null) {
            return;
        }
        AdReportModel.Builder builder = AdReportModel.newBuilder(
                        AppConstants.AD_LOG_TYPE_SHOW_TIME,
                        getCurAdPositionName())
                .sdkType(AdManager.getSDKType(currentAd) + "")
                .dspPositionId(currentAd.getDspPositionId())
                .uid(UserInfoMannage.getUid() + "")
                .albumIdUseStr(currentAlbumId + "")
                .trackId(currentTrackId + "")
                .showTimeMs((int) (System.currentTimeMillis() - adBeginShowTime))
                .adUserType(currentAd.getAdUserType())
                .adReportScene(SDKAdReportModel.AD_REPORT_SCENE_REWARD)
                .sourceName(curSourceName + "")
                .showStyle(currentAd.getShowstyle() + "");
        buildRewardReportData(builder);
        AdManager.adRecord(MainApplication.getMyApplicationContext(),
                currentAd, builder.build());
    }

    public int getAdMaxLoadTime() {
        if (maxAdLoadTime < 0) {
            maxAdLoadTime = 0;
        }
        return maxAdLoadTime;
    }

    private String getCurAdPositionName() {
        if (currentAd != null) {
            if (!TextUtils.isEmpty(currentAd.getPositionName())) {
                return currentAd.getPositionName();
            } else if (currentExtraParams != null && !TextUtils.isEmpty(currentExtraParams.getPositionName())) {
                return currentExtraParams.getPositionName();
            } else if (currentAd.getPositionId() > 0) {
                return AdPositionIdManager.getPositionNameByPositionId(currentAd.getPositionId() + "");
            }
        }
        if (currentExtraParams != null && !TextUtils.isEmpty(currentExtraParams.getPositionName())) {
            return currentExtraParams.getPositionName();
        }
        return AppConstants.AD_POSITION_NAME_INCENTIVE;
    }

    private boolean needSendRewardImmediately() {
        if (currentExtraParams != null && !TextUtils.isEmpty(currentExtraParams.getPositionName())) {
            return currentExtraParams.getPositionName().equals(AD_POSITION_NAME_WELFARE_CASH_RECEIVE);
        }
        return false;
    }
    public String getCurAdPriceEncrypt() {
        if (currentAd != null && currentAd.getCommonReportMap()!=null) {
            String commonReportMap = currentAd.getCommonReportMap();
            try{
                JSONObject jsonObject = new JSONObject(commonReportMap);
                if (jsonObject.has("encryptType")) {
                    curEncryptType = jsonObject.optInt("encryptType");
                }
                if (currentAd.isMobileRtb() && currentAd.getCurrentDspRtbPrice() > 0) {
                    return EncryptPriceUtils.encodeAdsPrice(currentAd.getCurrentDspRtbPrice());
                }
                if (jsonObject.has("adxRtbSettlementPrice")) {
                    return jsonObject.optString("adxRtbSettlementPrice");
                } else {
                    return currentAd.getPriceEncrypt() != null ? currentAd.getPriceEncrypt() : "-1";
                }
            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "-1";
    }

    private int curEncryptType = 1;
    public String getCurAdPriceEncryptType() {
        return curEncryptType + "";
    }

    private void preloadInspireAd() {
        if (currentExtraParams != null && !currentExtraParams.canPreloadInspireAd()) {
            return;
        }
        if (currentAd == null || currentAd.getAgainPopupType() == 0) {
            return;
        }
        RewardCoinAgainAdManager.preloadInspireAd(currentAd.getAgainPopupType(), curSourceName);
    }
}
