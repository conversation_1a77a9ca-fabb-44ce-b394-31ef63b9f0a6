package com.ximalaya.ting.android.host.manager.bundleframework.route.action.music;

import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.host.listener.IFragmentFinish;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.base.IAction;
import com.ximalaya.ting.android.host.model.live.BgSound;

import java.util.List;

import javax.annotation.Nullable;

/**
 * Created by marvin.yang on 2019/10/17.
 *
 * <AUTHOR>
 */
public interface IMusicFragmentAction extends IAction {

    int LIVE_RECORD = 1; // 录音
    int LIVE_RADIO = 2; // 直播
    int LIVE_READ = 3; // 全民朗读
    int LIVE_PICTURE_DUBBING = 4; // 图片趣配音
    int LIVE_PPT_RECORD = 5; // PPT/图片录音
    int LIVE_TUNE = 6; // 工作台
    int LIVE_MYCLUB = 7; // MyClub
    int LIVE_ONE_KEY_MAKE = 8; // 一键成片

    String PAGE_NAME = "添加配乐";
    String SCENE_ALL = "";
    String SCENE_LIVE = "直播";
    String SCENE_RECORD = "主APP";

    /**
     * 录音创建添加配乐页面
     *
     * @param iFragmentFinish 选择配乐的回调
     * @param list            当前已选择的配乐列表
     * @param bizScene        业务场景
     */
    BaseFragment newAddMusicFragmentForRecord(
            IFragmentFinish iFragmentFinish, @Nullable List<BgSound> list, int bizScene
    );

    /**
     * 新录音创建添加配乐页面
     *
     * @param iFragmentFinish 选择配乐的回调
     * @param list            当前已选择的配乐列表
     * @param bizScene        业务场景
     */
    BaseFragment newAddMusicFragmentForRecordNew(
            IFragmentFinish iFragmentFinish, @Nullable List<BgSound> list, int bizScene
    );

    /**
     * 添加配乐页面
     *
     * @param fragmentFinish 选择配乐的回调
     * @param selectedBgs    当前已选择的配乐列表
     * @param bizScene       业务场景
     */
    BaseFragment newAddMusicFragment(
            IFragmentFinish fragmentFinish,
            @Nullable List<BgSound> selectedBgs,
            String scene,
            int bizScene
    );
}
