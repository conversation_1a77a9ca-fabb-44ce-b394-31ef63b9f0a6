package com.ximalaya.ting.android.host.live.net

import androidx.annotation.WorkerThread
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ximalaya.ting.android.host.live.net.model.LiveHistoryReqData
import com.ximalaya.ting.android.host.live.net.model.LiveHistoryResponseData
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import org.json.JSONObject

/**
 * 我的-最近、看过列表数据直播自接口请求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15026804470
 * @wiki Wiki网址放在这里
 * @server 服务端开发人员放在这里
 * @since 2024/12/31
 */
object LiveCommonRequest {
    /**
     * 同步根据请求直播数据
     */
    @WorkerThread
    fun queryLiveDatas(sourceList: List<LiveHistoryReqData>): List<LiveHistoryResponseData> {
        var resultList: MutableList<LiveHistoryResponseData> = ArrayList()
        CommonRequestM.basePostRequestJsonStrSync<Any?>(
            UrlConstants.getInstanse().liveHistoryListUrl,
            Gson().toJson(sourceList),
            object : IDataCallBack<Any?> {
                override fun onSuccess(data: Any?) {}
                override fun onError(code: Int, message: String) {}
            }
        ) { content: String? ->
            try {
                val jsonObject = JSONObject(content)
                if (jsonObject.has("data")) {
                    resultList = Gson().fromJson<List<LiveHistoryResponseData>>(
                        jsonObject.optString("data"),
                        object : TypeToken<List<LiveHistoryResponseData?>?>() {
                        }.type
                    ).toMutableList()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return resultList
    }
}
