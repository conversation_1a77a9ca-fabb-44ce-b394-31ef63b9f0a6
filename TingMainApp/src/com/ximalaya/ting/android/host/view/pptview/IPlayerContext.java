package com.ximalaya.ting.android.host.view.pptview;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.opensdk.model.track.Track;

/**
 * <AUTHOR> feiwen
 * date   : 2020-05-19
 * desc   :
 */
public interface IPlayerContext {
    @Nullable
    Track getCurTrack();

    int getTitleBarAlpha();

    void startFragment(Fragment fragment);

    void onNextClick();

    void onLastClick();

    void onReplayClick();

    void onDanmuSwitchStateChanged(boolean isOpen);

    void onControllerViewVisibilityChanged(boolean isHoverOn);
}
