package com.ximalaya.ting.android.host.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.LinearGradient;
import android.graphics.Shader;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatTextView;

public class GradientTextView extends AppCompatTextView {

    private int fromColor = 0xffffffff;
    private int toColor = 0xffffffff;
    private boolean isHorizontal = false;

    public GradientTextView(Context context) {
        super(context);
    }

    public GradientTextView(Context context,
                            AttributeSet attrs) {
        super(context, attrs);
        initAttrs(context, attrs);
    }

    public GradientTextView(Context context,
                            AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initAttrs(context, attrs);
    }

    private void initAttrs(Context context, AttributeSet attrs) {
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (isHorizontal) {
            getPaint().setShader(new LinearGradient(0, 0, getWidth(), 0, fromColor, toColor, Shader.TileMode.CLAMP));
        } else {
            getPaint().setShader(new LinearGradient(0, 0, 0, getHeight(), fromColor, toColor, Shader.TileMode.CLAMP));
        }
        super.onDraw(canvas);
    }

    public void setGradientColor(int fromColor, int toColor) {
        this.fromColor = fromColor;
        this.toColor = toColor;
        postInvalidate();
    }

    public void isHorizontal(boolean isHorizontal){
        this.isHorizontal = isHorizontal;
        postInvalidate();
    }
}
