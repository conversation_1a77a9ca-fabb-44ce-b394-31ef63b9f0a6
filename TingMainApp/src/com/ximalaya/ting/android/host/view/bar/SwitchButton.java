package com.ximalaya.ting.android.host.view.bar;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import android.widget.CheckBox;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.R;

/**
 * 自定义开关
 */
 /**
  * <AUTHOR>
  */
public class SwitchButton extends CheckBox {
    private final class PerformClick implements Runnable {
        public void run() {
            performClick();
        }
    }

    private final class SwitchAnimation implements Runnable {

        @Override
        public void run() {
            if (!mAnimating) {
                return;
            }
            doAnimation();
            FrameAnimationController.requestAnimationFrame(this);
        }
    }

    private int BORDER_WIDTH = 4;
    private Paint mPaint;

    private ViewParent mParent;

    private Bitmap mBottom;

    private Bitmap mCurBtnPic;

    private Bitmap mBtnOff;

    private Bitmap mBtnNormal;

    private Bitmap mMask;

    private RectF mSaveLayerRectF;

    private PorterDuffXfermode mXfermode;

    private float mFirstDownY; // 首次按下的Y

    private float mFirstDownX; // 首次按下的X

    private float mRealPos; // 图片的绘制位置

    private float mBtnPos; // 按钮的位置

    private float mBtnOnPos; // 开关打开的位置

    private float mBtnOffPos; // 开关关闭的位置

    private float mMaskWidth;

    private float mMaskHeight;

    private float mBtnWidth;

    private float mBtnInitPos;

    private int mClickTimeout;

    private int mTouchSlop;

    private final int MAX_ALPHA = 255;

    private int mAlpha = MAX_ALPHA;

    private boolean mChecked = false;

    private boolean mBroadcasting;

    private boolean mTurningOn;

    private PerformClick mPerformClick;

    private OnCheckedChangeListener mOnCheckedChangeListener;

    private OnCheckedChangeListener mOnCheckedChangeWidgetListener;

    private boolean mAnimating;

    private final float VELOCITY = 350;

    private float mVelocity;

    private final float EXTENDED_OFFSET_Y = 8;

    private float mExtendOffsetY; // Y轴方向扩大的区域,增大点击区域

    private float mAnimationPosition;

    private float mAnimatedVelocity;

    public SwitchButton(Context context) {
        this(context, null);
    }

    public SwitchButton(Context context, AttributeSet attrs) {
        this(context, attrs, android.R.attr.checkboxStyle);
    }

    public SwitchButton(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        initView(context, attrs);
    }

    /**
     * Tries to claim the user's drag motion, and requests disallowing any
     * ancestors from stealing events in the drag.
     */
    private void attemptClaimDrag() {
        mParent = getParent();
        if (mParent != null) {
            mParent.requestDisallowInterceptTouchEvent(true);
        }
    }

    private void doAnimation() {
        mAnimationPosition += mAnimatedVelocity
                * FrameAnimationController.ANIMATION_FRAME_DURATION / 1000;
        if (mAnimationPosition >= mBtnOnPos) {
            stopAnimation();
            mAnimationPosition = mBtnOnPos;
            setCheckedDelayed(true);
        } else if (mAnimationPosition <= mBtnOffPos) {
            stopAnimation();
            mAnimationPosition = mBtnOffPos;
            setCheckedDelayed(false);
        }
        moveView(mAnimationPosition);
    }

    /**
     * 将btnPos转换成RealPos
     *
     * @param btnPos
     * @return
     */
    private float getRealPos(float btnPos) {
        return btnPos - mBtnWidth / 2;
    }

    /**
     * 调用此方法不回触发OnCheckedChangeListener
     *
     * @param checked
     */
    public void initCheckedState(boolean checked) {

        if (mChecked != checked) {
            mChecked = checked;

            mBtnPos = checked ? mBtnOnPos : mBtnOffPos;
            mRealPos = getRealPos(mBtnPos);
            invalidate();

            // Avoid infinite recursions if setChecked() is called from a
            // listener
            if (mBroadcasting) {
                return;
            }

            mBroadcasting = true;

            mBroadcasting = false;
        }
    }

    private void initView(Context context, AttributeSet attrs) {
        mPaint = new Paint();
        mPaint.setColor(Color.WHITE);
        Resources resources = context.getResources();

        int btnOffRes = 0;
        int btnUnpressedRes = 0;
        int colorSrc = 0;
        if (attrs != null) {
            TypedArray a = resources.obtainAttributes(attrs,
                    R.styleable.SwitchButton);
            btnOffRes = a.getResourceId(
                    R.styleable.SwitchButton_button_off_background, 0);
            btnUnpressedRes = a.getResourceId(
                    R.styleable.SwitchButton_button_unpressed_background, 0);
            colorSrc = a.getResourceId(
                    R.styleable.SwitchButton_button_color_src, 0);
            a.recycle();
        }

        // get viewConfiguration
        mClickTimeout = ViewConfiguration.getPressedStateDuration()
                + ViewConfiguration.getTapTimeout();
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();

        // get Bitmap
        mBottom = BitmapFactory.decodeResource(resources, colorSrc != 0 ? colorSrc : R.drawable.host_bottom);
        mBtnNormal = BitmapFactory.decodeResource(resources,
                btnUnpressedRes != 0 ? btnUnpressedRes
                        : R.drawable.host_btn_unpressed);
        mBtnOff = BitmapFactory.decodeResource(resources,
                btnOffRes != 0 ? btnOffRes : R.drawable.host_btn_off);
        mMask = BitmapFactory.decodeResource(resources, R.drawable.host_mask);
        mCurBtnPic = mBtnNormal;

        mBtnWidth = mBtnNormal.getWidth();
        mMaskWidth = mMask.getWidth();
        mMaskHeight = mMask.getHeight();

        BORDER_WIDTH = BaseUtil.dp2px(context, 1);
        mBtnOnPos = mBtnWidth / 2;
        mBtnOffPos = mMaskWidth - mBtnWidth / 2 + BORDER_WIDTH;

        mBtnPos = mChecked ? mBtnOnPos : mBtnOffPos;
        mRealPos = getRealPos(mBtnPos);

        final float density = getResources().getDisplayMetrics().density;
        mVelocity = (int) (VELOCITY * density + 0.5f);
        mExtendOffsetY = (int) (EXTENDED_OFFSET_Y * density + 0.5f);

        mSaveLayerRectF = new RectF(0, mExtendOffsetY, mMask.getWidth() + 2 * BORDER_WIDTH, mMask.getHeight()
                + mExtendOffsetY);
        mXfermode = new PorterDuffXfermode(PorterDuff.Mode.SRC_IN);
    }

    public boolean isChecked() {
        return mChecked;
    }

    private void moveView(float position) {
        mBtnPos = position;
        mRealPos = getRealPos(mBtnPos);
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.saveLayerAlpha(mSaveLayerRectF, mAlpha ,Canvas.ALL_SAVE_FLAG);
        // 绘制蒙板
        canvas.drawBitmap(mMask, 0, mExtendOffsetY, mPaint);
        mPaint.setXfermode(mXfermode);

        // 绘制底部图片
        canvas.drawBitmap(mBottom, mRealPos, mExtendOffsetY, mPaint);
        mPaint.setXfermode(null);
        // 绘制边框

        // 绘制按钮
        canvas.drawBitmap(mCurBtnPic, mRealPos, mExtendOffsetY, mPaint);
        canvas.restore();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        setMeasuredDimension((int) mMaskWidth + BORDER_WIDTH * 2, (int) (mMaskHeight + 2 * mExtendOffsetY));
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        float x = event.getX();
        float y = event.getY();
        float deltaX = Math.abs(x - mFirstDownX);
        float deltaY = Math.abs(y - mFirstDownY);
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                attemptClaimDrag();
                mFirstDownX = x;
                mFirstDownY = y;
                // mCurBtnPic = mBtnPressed;
                mBtnInitPos = mChecked ? mBtnOnPos : mBtnOffPos;
                break;
            case MotionEvent.ACTION_MOVE:
                float time = event.getEventTime() - event.getDownTime();
                mBtnPos = mBtnInitPos + event.getX() - mFirstDownX;
                if (mBtnPos <= mBtnOffPos) {
                    mBtnPos = mBtnOffPos;
                }
                if (mBtnPos >= mBtnOnPos) {
                    mBtnPos = mBtnOnPos;
                }
                mTurningOn = mBtnPos > (mBtnOnPos - mBtnOffPos) / 2 + mBtnOffPos;

                mRealPos = getRealPos(mBtnPos);
                break;
            case MotionEvent.ACTION_UP:
                mCurBtnPic = mTurningOn ? mBtnNormal : mBtnOff;
                time = event.getEventTime() - event.getDownTime();
                if (deltaY < mTouchSlop && deltaX < mTouchSlop
                        && time < mClickTimeout) {
                    if (mPerformClick == null) {
                        mPerformClick = new PerformClick();
                    }
                    if (!post(mPerformClick)) {
                        performClick();
                    }
                } else {
                    startAnimation(!mTurningOn);
                }
                break;
        }

        invalidate();
        return isEnabled();
    }

    @Override
    public boolean performClick() {
        startAnimation(mChecked);
        return true;
    }

    /**
     * <p>
     * Changes the checked state of this button.
     * </p>
     *
     * @param checked true to check the button, false to uncheck it
     */
    public void setChecked(boolean checked) {

        if (mChecked != checked) {
            mChecked = checked;

            mBtnPos = checked ? mBtnOnPos : mBtnOffPos;
            mRealPos = getRealPos(mBtnPos);
            mCurBtnPic = checked ? mBtnNormal : mBtnOff;
            invalidate();

            // Avoid infinite recursions if setChecked() is called from a
            // listener
            if (mBroadcasting) {
                return;
            }

            mBroadcasting = true;
            if (mOnCheckedChangeListener != null) {
                mOnCheckedChangeListener.onCheckedChanged(SwitchButton.this,
                        mChecked);
            }
            if (mOnCheckedChangeWidgetListener != null) {
                mOnCheckedChangeWidgetListener.onCheckedChanged(
                        SwitchButton.this, mChecked);
            }

            mBroadcasting = false;
        }
    }

    public void setCheckedNoFireEvent(boolean checked) {

        if (mChecked != checked) {
            mChecked = checked;

            mBtnPos = checked ? mBtnOnPos : mBtnOffPos;
            mRealPos = getRealPos(mBtnPos);
            mCurBtnPic = checked ? mBtnNormal : mBtnOff;
            invalidate();
        }
    }

    /**
     * 内部调用此方法设置checked状态，此方法会延迟执行各种回调函数，保证动画的流畅度
     *
     * @param checked
     */
    private void setCheckedDelayed(final boolean checked) {
        this.postDelayed(new Runnable() {

            @Override
            public void run() {
                setChecked(checked);
            }
        }, 10);
    }

    @Override
    public void setEnabled(boolean enabled) {
        mAlpha = enabled ? MAX_ALPHA : MAX_ALPHA / 2;
        super.setEnabled(enabled);
    }

    /**
     * Register a callback to be invoked when the checked state of this button
     * changes.
     *
     * @param listener the callback to call on checked state change
     */
    public void setOnCheckedChangeListener(OnCheckedChangeListener listener) {
        mOnCheckedChangeListener = listener;
    }

    /**
     * Register a callback to be invoked when the checked state of this button
     * changes. This callback is used for internal purpose only.
     *
     * @param listener the callback to call on checked state change
     * @hide
     */
    void setOnCheckedChangeWidgetListener(OnCheckedChangeListener listener) {
        mOnCheckedChangeWidgetListener = listener;
    }

     public SwitchButton setBtnOff(Bitmap btnOff) {
         mBtnOff = btnOff;
         return this;
     }

     public SwitchButton setBtnNormal(Bitmap btnNormal) {
         mBtnNormal = btnNormal;
         return this;
     }

     public SwitchButton setBottom(Bitmap bottom) {
         mBottom = bottom;
         return this;
     }

     public SwitchButton setMask(Bitmap mask) {
         mMask = mask;
         return this;
     }

     public SwitchButton updateValue() {
         mBtnWidth = mBtnNormal.getWidth();
         mMaskWidth = mMask.getWidth();
         mMaskHeight = mMask.getHeight();
         mBtnOnPos = mBtnWidth / 2;
         mBtnOffPos = mMaskWidth - mBtnWidth / 2;
         mBtnPos = mChecked ? mBtnOnPos : mBtnOffPos;
         mRealPos = getRealPos(mBtnPos);
         mCurBtnPic = mChecked ? mBtnNormal : mBtnOff;

         final float density = getResources().getDisplayMetrics().density;
         mVelocity = (int) (VELOCITY * density + 0.5f);
         mExtendOffsetY = (int) (EXTENDED_OFFSET_Y * density + 0.5f);

         mSaveLayerRectF = new RectF(0, mExtendOffsetY, mMask.getWidth() + 2 * BORDER_WIDTH, mMask.getHeight()
                 + mExtendOffsetY);
         return this;
     }

    private void startAnimation(boolean turnOn) {
        mAnimating = true;
        mAnimatedVelocity = turnOn ? -mVelocity : mVelocity;
        mAnimationPosition = mBtnPos;

        new SwitchAnimation().run();
    }

    private void stopAnimation() {
        mAnimating = false;
    }

    public void toggle() {
        setChecked(!mChecked);
    }

    public static class FrameAnimationController {
        private static final int MSG_ANIMATE = 1000;

        public static final int ANIMATION_FRAME_DURATION = 1000 / 60;

        private static final Handler mHandler = new AnimationHandler();

        private FrameAnimationController() {
            throw new UnsupportedOperationException();
        }

        public static void requestAnimationFrame(Runnable runnable) {
            Message message = new Message();
            message.what = MSG_ANIMATE;
            message.obj = runnable;
            mHandler.sendMessageDelayed(message, ANIMATION_FRAME_DURATION);
        }

        public static void requestFrameDelay(Runnable runnable, long delay) {
            Message message = new Message();
            message.what = MSG_ANIMATE;
            message.obj = runnable;
            mHandler.sendMessageDelayed(message, delay);
        }

        private static class AnimationHandler extends Handler {
            public void handleMessage(Message m) {
                switch (m.what) {
                    case MSG_ANIMATE:
                        if (m.obj != null) {
                            ((Runnable) m.obj).run();
                        }
                        break;
                }
            }
        }
    }
}
