package com.ximalaya.ting.android.host.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.socialModule.util.CommunityLogicUtil;
import com.ximalaya.ting.android.host.model.feed.community.CommunityInfo;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;

/**
 * Created by xmly on 2019-11-23
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13761896887
 */
public class CommunityAvatarView extends RelativeLayout {
    //是否展示99+新帖这种字样
    private boolean showPost;
    //头像宽
    private int width;
    //头像高
    private int height;
    //是否全部都是圆弧
    private boolean isRound;

    private MyRoundImageView ivCommunityCover;
    private ImageView ivZoneLabel;
    private ImageView ivAdminLabel;
    private TextView tvNoticeCount;
    private ViewGroup tvNoticeCountWrapper;

    public CommunityAvatarView(Context context) {
        this(context, null);
    }

    public CommunityAvatarView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CommunityAvatarView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        if (attrs != null) {

            TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.CommunityAvatar);

            width = typedArray.getDimensionPixelSize(R.styleable.CommunityAvatar_width, getResources().getDimensionPixelSize(R.dimen.host_size_65_dp));

            height = typedArray.getDimensionPixelSize(R.styleable.CommunityAvatar_height, getResources().getDimensionPixelSize(R.dimen.host_size_65_dp));

            showPost = typedArray.getBoolean(R.styleable.CommunityAvatar_showPost, false);

            isRound = typedArray.getBoolean(R.styleable.CommunityAvatar_isRound, false);

            typedArray.recycle();
        }

        View.inflate(getContext(), R.layout.host_community_avatar_view, this);

        ViewGroup avatarLayout = findViewById(R.id.zone_community_avatar_layout);
        ivCommunityCover = findViewById(R.id.host_iv_community_cover);
        ivZoneLabel = findViewById(R.id.host_iv_icon);
        ivAdminLabel = findViewById(R.id.host_iv_label);
        tvNoticeCountWrapper = findViewById(R.id.host_tv_noticeCount_wrapper);
        tvNoticeCount = findViewById(R.id.host_tv_noticeCount);

        ViewGroup.LayoutParams params = avatarLayout.getLayoutParams();

        params.width = width;

        params.height = height;

        avatarLayout.setLayoutParams(params);

        ivCommunityCover.setScaleType(ImageView.ScaleType.CENTER_CROP);
    }

    public void setRadii(int leftTopRadius_x, int leftTopRadius_y, int rightTopRadius_x, int rightTopRadius_y,
                         int rightBottomRadius_x, int rightBottomRadius_y, int leftBottomRadius_x, int leftBottomRadius_y) {
        if (ivCommunityCover != null) {
            ivCommunityCover.setRadii(leftTopRadius_x, leftTopRadius_y, rightTopRadius_x, rightTopRadius_y,
                    rightBottomRadius_x, rightBottomRadius_y, leftBottomRadius_x, leftBottomRadius_y);
        }
    }

    public void setRadii(int leftTopRadius_x, int leftTopRadius_y, int rightTopRadius_x, int rightTopRadius_y) {
        setRadii(leftTopRadius_x, leftTopRadius_y, rightTopRadius_x, rightTopRadius_y,
                0, 0, 0, 0);
    }

    public void setRadii(int radius) {
        if (ivCommunityCover != null) {
            ivCommunityCover.setRadii(radius);
        }
    }

    private void setCover(String avatar) {
        ImageManager.from(getContext()).displayImage(ivCommunityCover, avatar, R.drawable.host_solid_rect_f3f4f5_2a2a2a);
    }

    private void setIdentifyLabel(int memberType) {
        if (memberType == CommunityLogicUtil.TYPE_ANCHOR_INFO_OWNER) {

            ivAdminLabel.setVisibility(VISIBLE);

            if (isRound) {
                ivAdminLabel.setImageResource(R.drawable.host_label_club_zhu);
            } else {
                ivAdminLabel.setImageResource(R.drawable.host_label_zhu_direct);
            }
        } else if (memberType == CommunityLogicUtil.TYPE_ANCHOR_INFO_ADMINISTER) {

            ivAdminLabel.setVisibility(VISIBLE);

            if (isRound) {
                ivAdminLabel.setImageResource(R.drawable.host_label_club_guan);
            } else {
                ivAdminLabel.setImageResource(R.drawable.host_label_guan_direct);
            }
        } else {

            ivAdminLabel.setVisibility(INVISIBLE);
        }
    }

    private void setCommunityIcon(String communityIcon) {
        if (TextUtils.isEmpty(communityIcon)) {
            ivZoneLabel.setVisibility(View.GONE);
        } else {
            ImageManager.from(getContext()).displayImage(ivZoneLabel, communityIcon, 0);
            ivZoneLabel.setVisibility(View.VISIBLE);
        }

        ViewGroup.LayoutParams labelParams = ivZoneLabel.getLayoutParams();

        labelParams.width = (int) (width * 1.0f / 70 * 30);

        labelParams.height = (int) (height * 1.0f / 70 * 13);

        ivZoneLabel.setLayoutParams(labelParams);
    }

    private void setNoticeCount(int newArticleNotifySwitch, int noticeCount) {
        tvNoticeCountWrapper.setVisibility(showPost && newArticleNotifySwitch != 1 && noticeCount > 0 ? VISIBLE : INVISIBLE);
        if (noticeCount > 99) {
            tvNoticeCount.setText("99+新帖");
        } else {
            tvNoticeCount.setText(noticeCount + "新帖");
        }
    }

    public void setData(CommunityInfo communityInfo) {
        setData(communityInfo.logo, communityInfo.memberType, communityInfo.icon, communityInfo.newArticleNotifySwitch, communityInfo.noticeCount);

        AutoTraceHelper.bindData(ivCommunityCover, AutoTraceHelper.MODULE_DEFAULT, communityInfo);
    }

    public void setData(String avatar, String communityIcon) {
        setCover(avatar);
        setCommunityIcon(communityIcon);
    }

    /**
     * @Param avatar 圈子头像
     * @Param memberType 身份信息
     * @Param communityIcon 圈子类型图标
     * @Param newArticleNotifySwitch  1 消息免打扰，不显示新帖提醒
     * @Param noticeCount 圈子类型图标 消息数量
     */
    public void setData(String avatar, int memberType, String communityIcon, int newArticleNotifySwitch, int noticeCount) {
        setCover(avatar);

        setIdentifyLabel(memberType);

        setCommunityIcon(communityIcon);

        setNoticeCount(newArticleNotifySwitch, noticeCount);
    }

    public void setData(String avatar, int memberType, String communityIcon) {
        setCover(avatar);

        setIdentifyLabel(memberType);

        setCommunityIcon(communityIcon);
    }

    public void setAvatarClickListener(final CommunityAvatarViewListener communityAvatarViewListener) {

        ivCommunityCover.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {

                if (!OneClickHelper.getInstance().onMiddleTimeGapClick(v)) {

                    return;
                }
                if (communityAvatarViewListener != null) {
                    communityAvatarViewListener.onAvatarClick();
                }
            }
        });
    }

    interface CommunityAvatarViewListener {
        void onAvatarClick();
    }
}
