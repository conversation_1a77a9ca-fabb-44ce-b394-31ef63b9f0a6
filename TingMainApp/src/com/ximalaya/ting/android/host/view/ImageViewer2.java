package com.ximalaya.ting.android.host.view;

import android.Manifest;
import android.animation.AnimatorInflater;
import android.animation.AnimatorSet;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.BitmapUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.FileProviderUtil;
import com.ximalaya.ting.android.framework.util.FileUtil;
import com.ximalaya.ting.android.framework.util.PadAdaptUtil;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.framework.view.pageindicator.CirclePageIndicator;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.manager.ImageExt;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.request.CommonRequestM;
import com.ximalaya.ting.android.host.model.dialog.BaseDialogModel;
import com.ximalaya.ting.android.host.socialModule.imageviewer.longimage.ImageSource;
import com.ximalaya.ting.android.host.socialModule.imageviewer.longimage.SubsamplingScaleImageView;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.util.constant.HttpParamsConstants;
import com.ximalaya.ting.android.host.view.photoview.PhotoView;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmutil.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.microedition.khronos.egl.EGL10;
import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.egl.EGLContext;
import javax.microedition.khronos.egl.EGLDisplay;

/**
 * 最新版查看大图
 * 将PopupWindow 替换为 DialogFragment
 * <AUTHOR>
 * @email <EMAIL>
 * @createTime 2024/1/26
 * @wiki
 */
public class ImageViewer2 implements View.OnClickListener, View.OnLongClickListener,
        ViewPager.OnPageChangeListener {

    private static final String TAG = ImageViewer2.class.getSimpleName();
    private static int maximumTextureSize = -1;
    private final boolean mIsHasDismissAnimation = false;
    private Context mContext;
    private ViewPager mViewPager;

    private com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog xmDialog;
    private RelativeLayout mMainLayout;
    private View mContent;
    private CirclePageIndicator mIndicator;
    private ProgressBar mProgressBar;
    private boolean enableShowNumber;
    private ImageAdapter mImageAdapter;
    private boolean isCenterInside = false;
    private ImageView mSaveBtn;
    private TextView mTvBrowseOrigin;
    private TextView mIndicatorTv;
    private List<ImageUrl> mImageUrls;
    private int mDefaultResId;//默认图url
    private boolean isFullScreen = true;//是否全屏显示
    private boolean isShowSaveBtn = true;
    private final boolean hasLongImageView;//是否有长图
    private boolean[] mFirstLoadComplete;
    private boolean[] mDisplayCallBacked;
    private View[] mIvs;
    private boolean mIsLoadingOrigin;
    private boolean shouldHideBrowserOrigin;
    private IImageViewDismissListener dismissListener;

    public ImageViewer2(Context context) {
        this(context, false);
    }

    public ImageViewer2(Context context, boolean userLongImageView) {
        mContext = context;
        this.hasLongImageView = userLongImageView;
        init();
    }

    private static void savePathToDbAndSendBroadcast(Context context, String path, Bitmap bitmap) {
        if (context == null || TextUtils.isEmpty(path)) {
            return;
        }

        Logger.d("xm_viewer", "writeImageToContextProvider path = " + path);

        ContentValues values = new ContentValues(4);
        values.put(MediaStore.Images.Media.DATA, path);

        values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
        values.put(MediaStore.Images.Media.WIDTH, bitmap != null ? bitmap.getWidth() : 0);
        values.put(MediaStore.Images.Media.HEIGHT, bitmap != null ? bitmap.getHeight() : 0);

        ContentResolver content = context.getContentResolver();

        String state = Environment.getExternalStorageState();
        Uri uri;
        if (state.equals(Environment.MEDIA_MOUNTED)) {
            Logger.d(TAG, "writeImageToContextProvider EXTERNAL_CONTENT_URI path = " + path);
            uri = content.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
            if (uri != null) {
                Logger.d(TAG, "writeImageToContextProvider EXTERNAL_CONTENT_URI uri = " + uri.toString());
            }
        } else {
            Logger.d(TAG, "writeImageToContextProvider INTERNAL_CONTENT_URI path = " + path);
            uri = content.insert(MediaStore.Images.Media.INTERNAL_CONTENT_URI, values);
            if (uri != null) {
                Logger.d(TAG, "writeImageToContextProvider INTERNAL_CONTENT_URI uri = " + uri.toString());
            }
        }
        context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                FileProviderUtil.fromFile(new File(path))));
    }

    /**
     * 初始化
     */
    private void init() {
        if (xmDialog == null) {
            Activity activity = BaseApplication.getTopActivity();
            if (activity != null) {
                mContext = activity;
            }

            mContent = View.inflate(mContext, R.layout.host_layout_view_pager, null);
            initDialogView();

            mMainLayout = (RelativeLayout) mContent;
            mMainLayout.setOnClickListener(this);
            AutoTraceHelper.bindData(mMainLayout, "");
            mIndicator = mContent
                    .findViewById(R.id.host_indicator_dot);
            mProgressBar = mContent.findViewById(R.id.host_pb_loading);
            mViewPager = mContent.findViewById(R.id.host_pager_image);
            mIndicatorTv = mContent.findViewById(R.id.host_image_shower_indicator_tv);
            mSaveBtn = mContent.findViewById(R.id.host_save_btn);
            mTvBrowseOrigin = mContent.findViewById(R.id.host_tv_browse_origin);
            mSaveBtn.setOnClickListener(this);
            if (!isShowSaveBtn) {
                mSaveBtn.setVisibility(View.INVISIBLE);
            }
            mTvBrowseOrigin.setOnClickListener(this);
            AutoTraceHelper.bindData(mSaveBtn, "");
            AutoTraceHelper.bindData(mTvBrowseOrigin, "");

            mImageAdapter = new ImageAdapter();
            mImageAdapter.useLongImageView = hasLongImageView;
            mViewPager.setAdapter(mImageAdapter);
            mViewPager.addOnPageChangeListener(this);
            mIndicator.setViewPager(mViewPager);
        }
    }

    /**
     * 初始化dialogView
     */
    private void initDialogView() {
        if (xmDialog == null) {
            xmDialog = new XmBaseDialog(mContext,R.style.host_avatar_dialog);
            xmDialog.setContentView(mContent);
            xmDialog.setCancelable(true);
            xmDialog.setCanceledOnTouchOutside(true);
            xmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialogInterface) {
                    if (dismissListener != null) {
                        dismissListener.imageViewDismiss();
                    }

                }
            });
        }

    }

    public void enableSaveBtn(boolean isShow) {
        isShowSaveBtn = isShow;
        if (mSaveBtn != null) {
            mSaveBtn.setVisibility(isShow ? View.VISIBLE : View.INVISIBLE);
        }
    }


    /**
     * 填充图片的链接
     */
    public void setData(List<String> urls) {
        if (urls != null && urls.size() != 0) {
            List<ImageUrl> imageUrls = getImageUrls(urls);
            setImageUrls(imageUrls);
        }
    }

    /**
     * 填充图片的链接
     */
    public void setData2(List<CharSequence> urls) {
        if (urls != null && urls.size() != 0) {
            List<ImageUrl> imageUrls = getImageUrls2(urls);
            setImageUrls(imageUrls);
        }
    }

    public @Nullable
    List<ImageUrl> getImageUrls(List<String> urls) {
        if (urls != null && urls.size() != 0) {
            List<ImageUrl> imageUrls = new ArrayList<>();
            for (String url : urls) {
                ImageUrl imageUrl = new ImageUrl();
                imageUrl.largeUrl = url;
                imageUrls.add(imageUrl);
            }

            return imageUrls;
        }
        return null;
    }

    public @Nullable
    List<ImageUrl> getImageUrls2(List<CharSequence> urls) {
        if (urls != null && urls.size() != 0) {
            List<ImageUrl> imageUrls = new ArrayList<>();
            for (CharSequence url : urls) {
                ImageUrl imageUrl = new ImageUrl();
                imageUrl.largeUrl = (String) url;
                imageUrls.add(imageUrl);
            }

            return imageUrls;
        }
        return null;
    }

    public void setImageUrls(List<ImageUrl> imageUrls) {
        setImageUrls(imageUrls, imageUrls != null && imageUrls.size() > 1);
    }

    public void setImageUrls(List<ImageUrl> imageUrls, boolean showIndicator) {
        mImageUrls = imageUrls;
        setIndicatorVisible(showIndicator);
        mImageAdapter.notifyDataSetChanged();

        if (imageUrls != null) {
            mFirstLoadComplete = new boolean[imageUrls.size()];
            mDisplayCallBacked = new boolean[imageUrls.size()];
            mIvs = new View[imageUrls.size()];
        }
    }

    public void setData(List<String> urls, boolean showIndicator) {
        if (urls != null && urls.size() != 0) {
            List<ImageUrl> imageUrls = getImageUrls(urls);
            setImageUrls(imageUrls, showIndicator);
        }
    }

    public void setData(long trackId) {
        mProgressBar.setVisibility(View.VISIBLE);
        Map<String, String> httpParams = new HashMap<>();
        httpParams.put(HttpParamsConstants.PARAM_TRACK_ID, trackId + "");

        CommonRequestM.getTrackImages(httpParams,
                new IDataCallBack<List<String>>() {

                    @Override
                    public void onSuccess(List<String> result) {
                        mProgressBar.setVisibility(View.GONE);
                        if (xmDialog != null && xmDialog.isShowing()) {
                            if (result != null && result.size() > 0) {
                                setData(result);
                            } else {
                                CustomToast.showFailToast(mContext.getString(R.string.host_get_photo_fail));
                            }
                        }
                    }

                    @Override
                    public void onError(int code, String message) {
                        mProgressBar.setVisibility(View.GONE);
                    }
                });
    }

    public void setPictureCenterInside() {
        isCenterInside = true;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.layout) {
            dismiss();

        } else if (i == R.id.host_pager_image) {
            dismiss();

        } else if (i == R.id.host_save_btn) {
            String url = getImageUrl(mViewPager.getCurrentItem());
            savePhoto(url);
        } else if (i == R.id.host_tv_browse_origin) {
            if (mIsLoadingOrigin) {
                return;
            }
            mIsLoadingOrigin = true;
            final int position = mViewPager.getCurrentItem();
            mImageUrls.get(position).browseOriginChosen = true;
            mProgressBar.setVisibility(View.VISIBLE);
            shouldHideBrowserOrigin = true;
            ImageManager.from(mContext).downloadBitmap(getImageUrl(position), null, new ImageManager.DisplayCallback() {
                @Override
                public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                    View iv = mIvs[position];
                    if (bitmap != null) {
                        if (iv instanceof ImageView) {
                            ImageManager.setBitmapToView(bitmap, (ImageView) iv);
                        } else if (iv instanceof SubsamplingScaleImageView) {
                            ((SubsamplingScaleImageView) iv).setImage(ImageSource.bitmap(bitmap));
                        }

                        if (shouldHideBrowserOrigin) {
                            mTvBrowseOrigin.setText("加载完成");
                            Animation hiddenAction = new AlphaAnimation(1, 0);
                            hiddenAction.setDuration(1000);
                            mTvBrowseOrigin.startAnimation(hiddenAction);
                            mTvBrowseOrigin.setVisibility(View.INVISIBLE);
                        }
                    } else {
                        CustomToast.showFailToast("下载原图失败");
                    }
                    mProgressBar.setVisibility(View.INVISIBLE);
                    mIsLoadingOrigin = false;
                }
            }, false);

        }
    }

    /**
     * 显示指定位置的图片
     */
    public void show(int position) {
        mMainLayout.getBackground().setAlpha(255);
        if (!xmDialog.isShowing()) {
            try {
                xmDialog.show();

                if (isFullScreen) {
                    mSaveBtn.setBackgroundResource(0);
                    mSaveBtn.setImageResource(R.drawable.host_live_ting_circle_download);
                    Window window = xmDialog.getWindow();
                    if (window != null){
                        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                    }
                }
            } catch (Exception e) {

            }
        }
        if (mImageUrls != null && position < mImageUrls.size()) {
            mViewPager.setCurrentItem(position);
        }
        updateIndicator();
        showOrHideBrowseOrigin(position);

        if (mFirstLoadComplete != null
                && position < mFirstLoadComplete.length
                && position >= 0
                && !mFirstLoadComplete[position]) {
            mProgressBar.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 设置是否全屏显示
     */
    public void setIsFullScreen(boolean isFullScreen) {
        this.isFullScreen = isFullScreen;
    }

    /**
     * 展示 1/99  这种纯文字的指示器
     */
    public void enableShowNumber(boolean enable) {
        enableShowNumber = enable;
    }

    /**
     * 展示 圆点指示器
     */
    public void setIndicatorVisible(boolean visible) {
        if (visible) {
            mIndicator.setVisibility(View.VISIBLE);
        } else {
            mIndicator.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * 关闭图片查看器界面
     */
    @SuppressLint("NewApi")
    @SuppressWarnings("ResourceType")
    public void dismiss() {
//        mViewPager.removeAllViews();
        if (mIsHasDismissAnimation) {
            AnimatorSet set = (AnimatorSet) AnimatorInflater.loadAnimator(
                    mContext, R.anim.host_fade_out);
            set.setTarget(xmDialog);
            set.start();
        }

        if (xmDialog.isShowing()) {
            xmDialog.dismiss();
        }
    }

    public boolean isShowing() {
        return xmDialog.isShowing();
    }

    @Override
    public boolean onLongClick(View v) {
        //长按弹窗
        if (mContext == null || mContext instanceof Application) {
            return false;
        } else {
            List<BaseDialogModel> models = new ArrayList<>();
            models.add(new BaseDialogModel(R.drawable.host_theme_ic_title_bar_download_pressed, "保存图片", 0));
            new BaseBottomDialog(mContext, models) {
                @Override
                public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                    dismiss();
                    String url = getImageUrl(mViewPager.getCurrentItem());
                    savePhoto(url);
                }
            }.show();
            return false;
        }
    }

    //保存图片
    private void savePhoto(final String url) {
        if (!checkPermission()) {
            return;
        }

        if (mProgressBar.getVisibility() == View.VISIBLE) {  //正在执行其他任务，暂不处理保存图片任务，否则可能崩溃
            return;
        }

        mProgressBar.setVisibility(View.VISIBLE);
        boolean isLocalPath = false;
        String urlNew = url;
        try {
            File file = new File(urlNew);
            if (file.exists()) {
                //本地 path 转换成 file 协议 path
                urlNew = Uri.fromFile(file).toString();
                isLocalPath = true;
            }
        } catch (Exception e) {
            Logger.e(e);
        }

        boolean finalIsLocalPath = isLocalPath;

        ImageManager.from(mContext).downloadBitmap(urlNew, null, new ImageManager.DisplayCallback() {
            @Override
            public void onCompleteDisplay(String lastUrl, Bitmap bitmap) {
                if (bitmap != null && url != null && lastUrl != null) {
                    SaveImageTask saveImageTask = new SaveImageTask(ImageViewer2.this, lastUrl, bitmap, ImageManager.isGifUrl(url));
                    if (finalIsLocalPath) {
                        saveImageTask.setIsLocalPath(true);
                        saveImageTask.setOriginPath(url);
                    }
                    saveImageTask.myexec();
                }
            }
        }, false);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        showOrHideBrowseOrigin(position);
        shouldHideBrowserOrigin = false;
        if (!mFirstLoadComplete[position]) {
            mProgressBar.setVisibility(View.VISIBLE);
        } else {
            mProgressBar.setVisibility(View.INVISIBLE);
        }
        updateIndicator();
    }

    private void updateIndicator() {
        if (!enableShowNumber) {
            mIndicatorTv.setVisibility(View.INVISIBLE);
            return;
        }
        if (mImageUrls.size() == 0) {
            mIndicatorTv.setText("");
            mIndicatorTv.setVisibility(View.INVISIBLE);
            return;
        }
        mIndicatorTv.setVisibility(View.VISIBLE);
        int currItem = mViewPager.getCurrentItem();
        int count = mImageUrls.size();
        mIndicatorTv.setText((currItem + 1) + "/" + count);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    private void showOrHideBrowseOrigin(int position) {
        if (mTvBrowseOrigin == null) {
            return;
        }
        if (mImageUrls == null || position > mImageUrls.size() || position < 0) {
            mTvBrowseOrigin.setVisibility(View.INVISIBLE);
            return;
        }
        if (!isLoadOrigin(position) &&
                position < mImageUrls.size() &&
                mImageUrls.get(position) != null &&
                !TextUtils.isEmpty(mImageUrls.get(position).originUrl)) {
            mTvBrowseOrigin.setText("查看原图");
            mTvBrowseOrigin.setVisibility(View.VISIBLE);
        } else {
            mTvBrowseOrigin.setVisibility(View.INVISIBLE);

        }
    }

    /**
     * 是否加载原图
     *
     * @param position 第几张图
     * @return 是否加载原图
     */
    private boolean isLoadOrigin(int position) {
        if (mImageUrls != null && position < mImageUrls.size() &&
                !TextUtils.isEmpty(mImageUrls.get(position).largeUrl) &&
                !TextUtils.isEmpty(mImageUrls.get(position).originUrl)) {
            return (mImageUrls.get(position).browseOriginChosen ||
                    ImageManager.from(mContext).isImageCachedInDisk(mImageUrls.get(position).originUrl));

        } else {
            return false;
        }
    }

    private String getImageUrl(int position) {
        String url = null;
        try {
            if (isLoadOrigin(position)) {
                url = mImageUrls.get(position).originUrl;
            }
            if (TextUtils.isEmpty(url)) {
                url = mImageUrls.get(position).largeUrl;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }

    /**
     * 返回小图地址，应该是外面已经展示的地址， 用于第一次进来先展示一个小图，而不是一个默认图活着黑色背景 所以构造ImageUrl的时候，thumb是你在外面展示的那个地址，large优先是那个大图
     *
     * @param position 位置
     **/
    private String getThumbImageUrl(int position) {
        if (position < 0 || mImageUrls == null || position >= mImageUrls.size()) {
            return "";
        }
        String thumbPref = null;
        try {
            thumbPref = mImageUrls.get(position).thumbUrl;
            if (TextUtils.isEmpty(thumbPref)) {
                thumbPref = mImageUrls.get(position).largeUrl;
            }
            if (TextUtils.isEmpty(thumbPref)) {
                thumbPref = mImageUrls.get(position).originUrl;
            }
        } catch (IndexOutOfBoundsException e) {
            e.printStackTrace();
        }

        return thumbPref;
    }

    public void setDefaultImageUrl(int resId) {
        this.mDefaultResId = resId;
    }

    /**
     * 从picasso缓存目录，decode一个小图，当作第一次展示的默认图
     *
     * @param url 最好是小图地址
     **/
    private Bitmap decodeThumbBitmap(String url) {
        if (TextUtils.isEmpty(url)) {
            return null;
        }
        String localPath = ImageManager.from(mContext).getPicassoCachePath(url);
        if (TextUtils.isEmpty(localPath)) {
            return null;
        }
        File file = new File(localPath);
        if (!file.exists()) {
            return null;
        }
        int maxWidthAndHeight = BaseUtil.dp2px(mContext, 100);
        Bitmap bitmap;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(localPath, options);
        options.inJustDecodeBounds = false; // 设为 false
        int h = options.outHeight;
        int w = options.outWidth;
        int beWidth = w / maxWidthAndHeight;
        int beHeight = h / maxWidthAndHeight;
        int be;
        if (beWidth < beHeight) {
            be = beWidth;
        } else {
            be = beHeight;
        }
        if (be <= 0) {
            be = 1;
        }
        options.inSampleSize = be;
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        bitmap = BitmapFactory.decodeFile(localPath, options);
        return bitmap;
    }

    private void checkImageSize(Bitmap bitmap, View view) {
        if (bitmap == null || view == null) {
            return;
        }

        if (maximumTextureSize < 0) {
            try {
                maximumTextureSize = getMaximumTextureSize();
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        // 如果大小超出了最大的范围则不启动硬件加速
        if (bitmap.getWidth() > maximumTextureSize || bitmap.getHeight() > maximumTextureSize) {
            view.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        }

    }

    private int getMaximumTextureSize() {
        EGL10 egl = (EGL10) EGLContext.getEGL();
        EGLDisplay display = egl.eglGetDisplay(EGL10.EGL_DEFAULT_DISPLAY);

        // Initialise
        int[] version = new int[2];
        egl.eglInitialize(display, version);

        // Query total number of configurations
        int[] totalConfigurations = new int[1];
        egl.eglGetConfigs(display, null, 0, totalConfigurations);

        // Query actual list configurations
        EGLConfig[] configurationsList = new EGLConfig[totalConfigurations[0]];
        egl.eglGetConfigs(display, configurationsList, totalConfigurations[0], totalConfigurations);

        int[] textureSize = new int[1];
        int maximumTextureSize = 0;

        // Iterate through all the configurations to located the maximum texture size
        for (int i = 0; i < totalConfigurations[0]; i++) {
            // Only need to check for width since opengl textures are always squared
            egl.eglGetConfigAttrib(display, configurationsList[i], EGL10.EGL_MAX_PBUFFER_WIDTH, textureSize);

            // Keep track of the maximum texture size
            if (maximumTextureSize < textureSize[0]) {
                maximumTextureSize = textureSize[0];
            }

        }

        // Release
        egl.eglTerminate(display);

        return maximumTextureSize;
    }

    public void setDismissListener(IImageViewDismissListener listener) {
        this.dismissListener = listener;
    }

    public interface IImageViewDismissListener {

        void imageViewDismiss();
    }

    private static class SaveImageTask extends MyAsyncTask<String, Object, Boolean> {

        private final WeakReference<ImageViewer2> mRef;
        private final String mUrl;
        private final Bitmap mBitmap;
        private final boolean isGif;
        private File mFile;
        private boolean isLocalPath;
        private String mOriginPath;

        SaveImageTask(ImageViewer2 imageViewer,
                      String url, Bitmap bitmap,
                      boolean isGif) {
            mRef = new WeakReference<>(imageViewer);
            mUrl = url;
            mBitmap = bitmap;
            this.isGif = isGif;
        }

        public void setOriginPath(String originPath) {
            this.mOriginPath = originPath;
        }

        public void setIsLocalPath(boolean isLocalPath) {
            this.isLocalPath = isLocalPath;
        }

        @Override
        protected Boolean doInBackground(String... strings) {
            boolean isUrlFileGif = false;
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            // 本地图片
            if (isLocalPath) {
                BitmapFactory.decodeFile(mOriginPath, options);
            }
            // 从网络下载的图片
            else {
                if (!TextUtils.isEmpty(mUrl) && new File(mUrl).exists()) {
                    BitmapFactory.decodeFile(mUrl, options);
                } else {
                    BitmapFactory.decodeFile(ImageManager.getPicassoCachePath(MainApplication.getMyApplicationContext(), mUrl), options);
                }
            }
            if (!TextUtils.isEmpty(options.outMimeType)) {
                isUrlFileGif = options.outMimeType.contains("gif") || options.outMimeType.contains("webp");
            }

            String fileName = FileUtil.checkAndChangePicName(mUrl);
            if (isGif || isUrlFileGif) {
                if (fileName == null) {
                    fileName = UUID.randomUUID().toString();
                }
                int index = fileName.lastIndexOf('.');
                if (index == -1) {
                    fileName = fileName + ".gif";
                } else {
                    fileName = fileName.substring(0, index) + ".gif";
                }

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    return ImageExt.saveGifAndroidQ(MainApplication.getMyApplicationContext().getContentResolver(), mUrl, fileName);
                } else {
                    FileInputStream fis = null;
                    FileOutputStream fos = null;

                    try {
                        String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                                + "/喜马拉雅" + "/" + fileName;
                        File gif;
                        // 本地图片
                        if (isLocalPath) {
                            gif = new File(mOriginPath);
                        }
                        // 从网络下载的图片
                        else {
                            if (!TextUtils.isEmpty(mUrl) && new File(mUrl).exists()) {
                                gif = new File(mUrl);
                            } else {
                                gif = new File(ImageManager.getPicassoCachePath(MainApplication.getMyApplicationContext(), mUrl));
                            }
                        }

                        if (gif.exists()) {
                            mFile = FileUtil.fileIsExistCreate(path);
                            fis = new FileInputStream(gif);
                            fos = new FileOutputStream(mFile);

                            int len;
                            byte[] buffer = new byte[512];
                            while ((len = fis.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                            }
                            savePathToDbAndSendBroadcast(MainApplication.getMyApplicationContext(), path, null);
                            return true;
                        }
                        savePathToDbAndSendBroadcast(MainApplication.getMyApplicationContext(), path, null);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        if (fis != null) {
                            try {
                                fis.close();
                            } catch (Exception ignored) {
                            }
                        }

                        if (fos != null) {
                            try {
                                fos.close();
                            } catch (Exception ignored) {
                            }
                        }
                    }
                }
            } else {
                try {
                    // 图片存储位置由私有目录改为公共目录，解决更新不到系统相册问题
                    String path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                            + "/喜马拉雅" + "/" + fileName;
                    boolean result = false;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        Uri uri = ImageExt.saveToAlbum(mBitmap, ToolUtil.getCtx(), fileName, path, 100);
                        if (uri != null) {
                            result = true;
                        }
                    } else {
                        mFile = FileUtil.fileIsExistCreate(path);
                        int lastQuality = BitmapUtils.mCompressQuality;
                        BitmapUtils.mCompressQuality = 100;
                        result = BitmapUtils.writeBitmapToFile(mBitmap, path, fileName);
                        BitmapUtils.mCompressQuality = lastQuality;
                        savePathToDbAndSendBroadcast(MainApplication.getMyApplicationContext(), path, mBitmap);
                    }

                    return result;
                } catch (Exception e) {
                    return false;
                }
            }

            return false;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            ImageViewer2 imageViewer = mRef.get();
            if (imageViewer == null) {
                return;
            }

            if (result) {
                CustomToast.showSuccessToast("图片已保存");
            } else {
                CustomToast.showFailToast("图片保存失败");
            }
            imageViewer.mProgressBar.setVisibility(View.INVISIBLE);
        }
    }

    private static boolean checkPermission() {
        final Activity activity = MainApplication.getTopActivity();
        if (activity instanceof IMainFunctionAction.ISetRequestPermissionCallBack) {
            try {
                return Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().hasPermissionAndRequest(activity,
                        (IMainFunctionAction.ISetRequestPermissionCallBack) activity, new HashMap<String, Integer>() {
                            {
                                put(Manifest.permission.WRITE_EXTERNAL_STORAGE, R.string.host_deny_perm_sdcard);
                            }
                        });
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public static class ImageUrl {

        @SerializedName(value = "data-large")
        public String largeUrl;    //大图的url
        @SerializedName(value = "data-origin")
        public String originUrl;   //原图的url
        public String thumbUrl;    //外面展示的小图，大图设置到largeUrl
        boolean browseOriginChosen;
    }

    /**
     * 图片适配器
     *
     * <AUTHOR>
     */
    private class ImageAdapter extends PagerAdapter {

        boolean useLongImageView;

        @Override
        public int getCount() {
            if (mImageUrls != null) {
                return mImageUrls.size();
            }
            return 0;
        }

        @Override
        public boolean isViewFromObject(View arg0, Object arg1) {
            return arg0 == arg1;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
            mFirstLoadComplete[position] = false;
        }

        private boolean isGifUrl(String url) {
            if (TextUtils.isEmpty(url)) {
                return false;
            }
            return url.indexOf(".gif") > 0;
        }

        @Override
        public Object instantiateItem(ViewGroup container, final int position) {
            String url = getImageUrl(position);
            View view;
            if (isGifUrl(url)) {
                PhotoView iv = new PhotoView(mContext);
                if (isCenterInside) {
                    mViewPager.setLayoutParams(new RelativeLayout.LayoutParams(
                            RelativeLayout.LayoutParams.WRAP_CONTENT,
                            RelativeLayout.LayoutParams.WRAP_CONTENT));
                }
                iv.setScaleType(ImageView.ScaleType.FIT_CENTER);
                ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                iv.setLayoutParams(lp);
                //走不到下面的回调，第一次加载gif，mProgressBar永远不消失
                mProgressBar.setVisibility(View.GONE);
                ImageManager.from(mContext).displayImage(iv, url, R.drawable.host_image_default_f3f4f5,
                        (lastUrl, bitmap) -> {
                            checkImageSize(bitmap, iv);
                            mFirstLoadComplete[position] = true;
                            mDisplayCallBacked[position] = true;
                            if (mViewPager.getCurrentItem() == position) {
                                mProgressBar.setVisibility(View.GONE);
                            }
                        });
                view = iv;
            } else {
                view = useLongImageView ? getLongImageView(url, position) : getPhotoView(url, position);
            }

            view.setOnClickListener(v -> dismiss());
            AutoTraceHelper.bindData(view, AutoTraceHelper.MODULE_DEFAULT, "");

            if (position >= 0 && mIvs != null && position < mIvs.length) {
                mIvs[position] = view;
            } else {
                CustomToast.showDebugFailToast("ImageViewer instantiateItem failed! Please check index!");
            }
            //长按弹窗图片保存
            view.setOnLongClickListener(ImageViewer2.this);
            container.addView(view);
            return view;
        }

        private View getLongImageView(String url, int position) {
            SubsamplingScaleImageView iv = new SubsamplingScaleImageView(mContext);
            iv.setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_START);
            if (TextUtils.isEmpty(url)) {
                mFirstLoadComplete[position] = true;
                if (mViewPager.getCurrentItem() == position) {
                    mProgressBar.setVisibility(View.GONE);
                }
                iv.setImage(ImageSource.resource(
                        mDefaultResId > 0 ? mDefaultResId : R.drawable.host_anchor_default_big));
            } else {
                String thumbUrl = getThumbImageUrl(position);
                boolean displayCallBacked = mDisplayCallBacked != null
                        && position >= 0
                        && position < mDisplayCallBacked.length - 1
                        && mDisplayCallBacked[position];
                if (thumbUrl != null && !thumbUrl.equals(url) && !displayCallBacked) {
                    Bitmap local = decodeThumbBitmap(thumbUrl);
                    if (local != null) {
                        iv.setImage(ImageSource.bitmap(local));
                    }
                }

                ImageManager.from(mContext).downloadBitmap(url,
                        (lastUrl, bitmap) -> {
                            Logger.i(TAG, "onCompleteDisplay invoked");
                            checkImageSize(bitmap, iv);

                            if (position < mFirstLoadComplete.length) {
                                mFirstLoadComplete[position] = true;
                            }
                            if (position < mDisplayCallBacked.length) {
                                mDisplayCallBacked[position] = true;
                            }
                            if (mViewPager.getCurrentItem() == position) {
                                mProgressBar.setVisibility(View.GONE);
                            }

                            if (bitmap != null) {
                                float screenWidth = mContent.getResources().getDisplayMetrics().widthPixels;
                                float screenHeight = mContent.getResources().getDisplayMetrics().heightPixels;
                                float bitmapWidth = bitmap.getWidth();
                                float bitmapHeight = bitmap.getHeight();
                                if (PadAdaptUtil.isPad(mContext)) {
                                    Activity activity = BaseApplication.getTopActivity();
                                    screenWidth = Math.min(PadAdaptUtil.getWidth(activity), PadAdaptUtil.getHeight(activity));
                                    screenHeight = Math.max(PadAdaptUtil.getWidth(activity), PadAdaptUtil.getHeight(activity));
                                }

                                if (bitmapHeight / bitmapWidth > screenHeight / screenWidth) {  //图片的长宽比大于屏幕的长宽比
                                    float scale = screenHeight / bitmapHeight;
                                    float currentBitmapWidth = bitmapWidth * scale;
                                    final float midScale = screenWidth / currentBitmapWidth; //midScale 必定大于1
                                    float maxScale = midScale * 1.5f;
                                    iv.setMaxScale(maxScale);
                                    iv.setDoubleTapZoomScale(midScale);
                                }
                                iv.setImage(ImageSource.bitmap(bitmap));
                            }
                        });
            }
            return iv;
        }

        public View getPhotoView(String url, final int position) {
            final PhotoView iv = new PhotoView(mContext);
            if (isCenterInside) {
                iv.setScaleType(ImageView.ScaleType.FIT_XY);
                mViewPager.setLayoutParams(new RelativeLayout.LayoutParams(
                        RelativeLayout.LayoutParams.WRAP_CONTENT,
                        RelativeLayout.LayoutParams.WRAP_CONTENT));
            }

            if (TextUtils.isEmpty(url)) {
                mFirstLoadComplete[position] = true;
                if (mViewPager.getCurrentItem() == position) {
                    mProgressBar.setVisibility(View.GONE);
                }
                iv.setImageResource(mDefaultResId > 0 ? mDefaultResId : R.drawable.host_anchor_default_big);
            } else {
                String thumbUrl = getThumbImageUrl(position);
                boolean displayCallBacked = mDisplayCallBacked != null
                        && position >= 0
                        && position < mDisplayCallBacked.length - 1
                        && mDisplayCallBacked[position];
                int defaultResId = mDefaultResId > 0 ? mDefaultResId : R.drawable.host_anchor_default_big;
                if (thumbUrl != null && !thumbUrl.equals(url) && !displayCallBacked) {
                    Bitmap local = decodeThumbBitmap(thumbUrl);
                    if (local != null) {
                        ImageManager.setBitmapToView(local, iv);
                        defaultResId = -1;
                    }
                }
                ImageManager.from(mContext).displayImage(iv, url, defaultResId, -1, -1,
                        (lastUrl, bitmap) -> {
                            Logger.i(TAG, "onCompleteDisplay invoked");
                            checkImageSize(bitmap, iv);

                            if (position < mFirstLoadComplete.length) {
                                mFirstLoadComplete[position] = true;
                            }
                            if (position < mDisplayCallBacked.length) {
                                mDisplayCallBacked[position] = true;
                            }
                            if (mViewPager.getCurrentItem() == position) {
                                mProgressBar.setVisibility(View.GONE);
                            }

                            if (bitmap != null) {
                                float screenWidth = mContent.getResources().getDisplayMetrics().widthPixels;
                                float screenHeight = mContent.getResources().getDisplayMetrics().heightPixels;
                                float bitmapWidth = bitmap.getWidth();
                                float bitmapHeight = bitmap.getHeight();
                                if (PadAdaptUtil.isPad(mContext)) {
                                    Activity activity = BaseApplication.getTopActivity();
                                    screenWidth = Math.min(PadAdaptUtil.getWidth(activity), PadAdaptUtil.getHeight(activity));
                                    screenHeight = Math.max(PadAdaptUtil.getWidth(activity), PadAdaptUtil.getHeight(activity));
                                }

                                if (bitmapHeight / bitmapWidth > screenHeight / screenWidth) {  //图片的长宽比大于屏幕的长宽比
                                    float scale = screenHeight / bitmapHeight;
                                    float currentBitmapWidth = bitmapWidth * scale;
                                    final float midScale = screenWidth / currentBitmapWidth; //midScale 必定大于1
                                    float maxScale = midScale * 1.5f;
                                    float currentMidScale = iv.getMediumScale();
                                    if (maxScale > currentMidScale) {
                                        iv.setMaximumScale(maxScale);
                                        iv.setMediumScale(midScale);
                                    } else {
                                        iv.setMediumScale(midScale);
                                        iv.setMaximumScale(maxScale);
                                    }
                                    iv.setNeedToFitScreen(true);
                                }
                            }
                        });
            }
            return iv;
        }

        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }
    }
}
