package com.ximalaya.ting.android.host.push;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.firework.dialog.monitor.IDialogInfoProvider;
import com.ximalaya.ting.android.firework.dialog.monitor.IDialogMonitor;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.ViewUtil;
import com.ximalaya.ting.android.framework.view.dialog.DialogBuilder;
import com.ximalaya.ting.android.host.R;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.constant.PreferenceConstantsInHost;
import com.ximalaya.ting.android.host.dialog.NoPermissionFollowGuideDialog;
import com.ximalaya.ting.android.host.dialog.freelisten.FreeListenDialogTrace;
import com.ximalaya.ting.android.host.fragment.other.BaseDialogFragment;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.MainActionRouter;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.manager.firework.HighValueFireworkManager;
import com.ximalaya.ting.android.host.util.NotificationUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.opensdk.player.statistic.PlayErrorStatisticManager;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.xmabtest.ABTest;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * <AUTHOR> feiwen
 * date   : 2022/8/10
 * desc   :
 */
public class NotificationPermissionOpenManager {

    private static final String KEY_FIRST_FOLLOW_DATE = "key_first_follow_date";
    private static final String KEY_FIRST_LIKE_DATE = "key_first_like_date";
    private static final String KEY_FIRST_COMMENT_DATE = "key_first_comment_date";
    private static final String KEY_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE = "key_notification_permission_dialog_show_date";
    private static final String KEY_HOME_PAGE_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE = "key_home_page_crash_notification_permission_dialog_show_date";
    private static final String KEY_HOME_PAGE_NORMAL_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE = "key_home_page_normal_notification_permission_dialog_show_date";
    private static final String KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE = "key_play_page_app_crash_notification_permission_dialog_show_date";
    private static final String KEY_PLAY_PAGE_NORMAL_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE = "key_play_page_normal_notification_permission_dialog_show_date";
    private static final String KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_COUNT = "key_play_page_app_crash_notification_permission_dialog_show_count";
    public static final long ONE_DAY_MS = 24 * 60 * 60 * 1000;
    private static final String DEFAULT_TITLE = "请允许我们发送推送通知";
    private static final String DEFAULT_SUB_TITLE = "精准推荐，给你想听的好内容";
    private static final String KEY_HOME_NOTIFICATION_POP_WINDOW_STYLE = "push_reopen_pop_up_v3";
    private static final String KEY_PLAY_PAGE_NOTIFICATION_POP_WINDOW_STYLE = "push_play_page_pop_up_v2";
    private static final String KEY_NOTIFICATION_PERMISSION_UPLOAD_MESSAGE = "key_notification_permission_upload_message";

    public static boolean sNotificationPermissionOpenDialogShowed = false; // 标识首页/播放页已经弹过弹窗了，互斥，不再弹

    public enum NotificationEntranceType {
        // 首页app误杀｜当天首次关注｜当天首次点赞｜当天首次评论 | 首页默认 | 播放页因app crash展示 | 播放页普通展示
        FOLLOW, LIKE, COMMENT, APP_CRASH, APP_NORMAL_HOME, PLAY_PAGE_APP_CRASH, PLAY_PAGE_NORMAL, NEW_PRODUCT_RESERVE
    }

    private static class NotificationMessage {
        public String title;
        public String subTitle;
    }

    private static class NotificationHomeNormalMessage {
        public int daysInterval;
        public String subtitle;
        public String title;
    }

    private static class NotificationPlayPageMessage {
        public String title;
        public String subtitle;
        public int appCrashDaysInterval;
        public int appCrashShowMaxTimes;
        public int normalDaysInterval;
    }

    private static class NotificationPermissionUploadMessage {
        public String dialogType;
        public String dialogTitle;
        public String currPage;
    }

    public static void checkToShowHomePageNotificationPermissionDialog
            (MainActivity activity) {
        HighValueFireworkManager.showAfterHighValueDirectly(new Runnable() {
            @Override
            public void run() {
                checkToShowHomePageNotificationPermissionDialogInner(activity);
            }
        });
    }

    public static boolean isPushOpenOptimize() {
        return ABTest.getBoolean(CConstants.ABTest.AB_ITEM_IS_PUSH_OPEN_DIALOG_OPTION, false);
    }

    public static void checkToShowHomePageNotificationPermissionDialogInner
            (MainActivity activity) {
        Logger.d("feiwen", "____checkToShowHomePageNotificationPermissionDialog____ " +
                " PlayErrorStatisticManager.getSingleInstance().isPlayErrorByApplicationCrash() = " + PlayErrorStatisticManager.getSingleInstance().isLastPlayErrorByApplicationCrash());
        if (activity == null) {
            return;
        }
        if (sNotificationPermissionOpenDialogShowed) {
           return;
        }
        if (ViewUtil.haveDialogIsShowing(activity)) {
            return;
        }

        // 首次安装不出
        if (ToolUtil.isFirstInstallApp(activity)) {
            return;
        }

        if (NotificationUtil.isSystemNotificationEnable(activity)) {
            checkToShowHomePageNormalNotificationPermissionDialog(activity);
            return;
        }
        if (!canShowHomePageCrashNotificationPermissionDialog()) {
            checkToShowHomePageNormalNotificationPermissionDialog(activity);
            return;
        }
        if (PlayErrorStatisticManager.getSingleInstance().isLastPlayErrorByApplicationCrash()) {
            NotificationHomeNormalMessage notificationMessage;
            if (isPushOpenOptimize()) {
                notificationMessage = getNotificationHomeNormalMessage();
                if (notificationMessage == null || TextUtils.isEmpty(notificationMessage.subtitle)) {
                    return;
                }
            } else {
                String str = ConfigureCenter.getInstance().getString("toc", "android_play_push_open", "");
                notificationMessage = new NotificationHomeNormalMessage();
                try {
                    JSONObject jsonObject = new JSONObject(str);
                    notificationMessage.subtitle = jsonObject.optString("subTitle", "");
                    notificationMessage.title = jsonObject.optString("title", "");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
            IDialogInfoProvider dialogInfoProvider = new IDialogInfoProvider() {
                @Override
                public String getDialogSource() {
                    return "push_open";
                }

                @Override
                public String getBusinessId() {
                    return notificationMessage.title;
                }
            };
            if (isPushOpenOptimize()) {
                showNewPlayErrorPushOpenDialog(activity, notificationMessage);
            } else {
                showOldPlayErrorPushOpenDialog(activity, notificationMessage);
            }
            IDialogMonitor.DEFAULT.onCommonDialogShow(dialogInfoProvider);
            FreeListenDialogTrace.traceEvent("NotificationPermissionDialog_show");
            sNotificationPermissionOpenDialogShowed = true;
        } else {
            checkToShowHomePageNormalNotificationPermissionDialog(activity);
        }
    }

    private static void showOldPlayErrorPushOpenDialog(MainActivity activity, NotificationHomeNormalMessage notificationMessage) {
        if (activity == null) {
            return;
        }
        IDialogInfoProvider dialogInfoProvider = new IDialogInfoProvider() {
            @Override
            public String getDialogSource() {
                return "push_open";
            }

            @Override
            public String getBusinessId() {
                return notificationMessage.title;
            }
        };

        new DialogBuilder(activity)
                .setTitle(notificationMessage.title)
                .setMessage(notificationMessage.subtitle)
                .setOkBtn("去开启", () -> {
                    DeviceUtil.openSystemNotificationPage(activity);
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("dialogTitle", notificationMessage.title)
                            .put("currPage", "首页")
                            .put("dialogType", getDialogType(NotificationEntranceType.APP_CRASH))
                            .put("item", "去开启")
                            .put("style", "0")
                            .createTrace();

                    saveNotificationPermissionUploadMessage("首页", getDialogType(NotificationEntranceType.APP_CRASH), notificationMessage.title);

                    IDialogMonitor.DEFAULT.onDialogClicked(dialogInfoProvider, false, "去开启");
                })
                .setCancelBtn("取消", () -> {
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("dialogTitle", notificationMessage.title)
                            .put("currPage", "首页")
                            .put("dialogType", getDialogType(NotificationEntranceType.APP_CRASH))
                            .put("item", "取消")
                            .put("style", "0")
                            .createTrace();
                    IDialogMonitor.DEFAULT.onDialogClicked(dialogInfoProvider, true, "取消");
                })
                .showConfirm();
        // 打开push通知弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(46907)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", "首页")
                .put("dialogType", getDialogType(NotificationEntranceType.APP_CRASH))
                .put("dialogTitle", notificationMessage.title)
                .createTrace();
        saveShowHomePageCrashNotificationPermissionDialog();
    }

    private static void showNewPlayErrorPushOpenDialog(MainActivity activity, NotificationHomeNormalMessage notificationMessage) {
        if (ViewUtil.haveDialogIsShowing(activity)) {
            return;
        }
        NotificationHomeNormalMessage notificationHomeNormalMessage = getNotificationHomeNormalMessage();
        if (notificationHomeNormalMessage == null || TextUtils.isEmpty(notificationHomeNormalMessage.subtitle)) {
            return;
        }
        if (TextUtils.isEmpty(notificationMessage.title)) {
            notificationMessage.title = "打开推送通知";
        }
        DialogBuilder dialogBuilder = new DialogBuilder(activity)
                .setTitle(notificationMessage.title)
                .setTitleTextSize(21)
                .setMessage(notificationMessage.subtitle)
                .setMsgTextSize(15)
                .setTitleTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_titleColor))
                .setMsgTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_mediumTextColor))
                .setContentLayoutBg(R.drawable.framework_round18_bg_white)
                .setOkBtnBg(R.drawable.framework_round_bottom_btn_right_corner18_bg_selector)
                .setCancelBtnBg(R.drawable.framework_round_bottom_btn_left_corner18_bg_selector)
                .setOkBtn("去开启", () -> {
                    try {
                        Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().
                                setPushSettingOpenIncludeLogoutState(PreferenceConstantsInHost.TINGMAIN_KEY_IS_PUSH_ALL, true);
                        if (NotificationUtil.isSystemNotificationEnable(activity)) {
                            CustomToast.showToast("打开推送通知成功");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (!NotificationUtil.isSystemNotificationEnable(activity)) {
                        DeviceUtil.openSystemNotificationPage(activity);
                    }
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("dialogTitle", notificationMessage.subtitle)
                            .put("currPage", "首页")
                            .put("dialogType", getDialogType(NotificationEntranceType.APP_CRASH))
                            .put("item", "去开启")
                            .put("style", "1")
                            .createTrace();

                    saveNotificationPermissionUploadMessage("首页", getDialogType(NotificationEntranceType.APP_CRASH), notificationMessage.title);
                })
                .setCancelBtn("暂不开启", () -> {
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("dialogTitle", notificationMessage.subtitle)
                            .put("currPage", "首页")
                            .put("dialogType", getDialogType(NotificationEntranceType.APP_CRASH))
                            .put("item", "取消")
                            .put("style", "1")
                            .createTrace();
                });
        View contentLayout = dialogBuilder.getContentLayout();
        if (contentLayout != null) {
            ViewGroup.LayoutParams layoutParams = contentLayout.getLayoutParams();
            layoutParams.width = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 275);
        }

        FrameLayout topBgLayout = dialogBuilder.getTopBgLayout();
        if (topBgLayout != null) {
            topBgLayout.setVisibility(View.VISIBLE);
            topBgLayout.setBackgroundResource(R.drawable.host_14corner_f5f5f5_414141);
            topBgLayout.removeAllViews();

            topBgLayout.setPadding(BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6), 0);

            ImageView imageView = new ImageView(topBgLayout.getContext());
            imageView.setImageResource(R.drawable.main_bg_play_page_open_notification_top);
            topBgLayout.addView(imageView);
        }
        dialogBuilder.showConfirm();
        // 打开push通知弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(46907)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", "首页")
                .put("dialogType", getDialogType(NotificationEntranceType.APP_CRASH))
                .put("dialogTitle", notificationMessage.subtitle)
                .createTrace();
        sNotificationPermissionOpenDialogShowed = true;
        saveShowHomePageCrashNotificationPermissionDialog();
    }

    public static void checkToShowHomePageNormalNotificationPermissionDialog
            (MainActivity activity) {
        if (activity == null) {
            return;
        }
        // 判断是否在黑名单里就不出弹窗
        if (isNotificationBlackListContained()) {
            return;
        }
        try {
            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().isPushSettingOpenIncludeLogoutState(
                    PreferenceConstantsInHost.TINGMAIN_KEY_IS_PUSH_ALL, new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean object) {
                            boolean isSystemNotificationEnable = NotificationUtil.isSystemNotificationEnable(activity);
                            // 默认没开启？
                            boolean isPushSettingOpen = false;
                            if (object != null) {
                                isPushSettingOpen = object;
                            }
                            if (!isPushSettingOpen || !isSystemNotificationEnable) {
                                doRealShowHomePageNormalNotificationPermissionDialog(activity, isPushSettingOpen, false);
                            }
                        }

                        @Override
                        public void onError(int code, String message) {
                            boolean isSystemNotificationEnable = NotificationUtil.isSystemNotificationEnable(activity);
                            if (!isSystemNotificationEnable) {
                                doRealShowHomePageNormalNotificationPermissionDialog(activity, true, false);
                            }
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断是不是在黑名单里面
     */
    private static boolean isNotificationBlackListContained() {
        String str = ConfigureCenter.getInstance().getString("toc", "push_reopen_blacklist_v2", "");
        if (TextUtils.isEmpty(str)) {
            return false;
        }
        String[] arr = str.split(",");
        if (arr.length == 0) {
            return false;
        }
        String userIdStr = String.valueOf(DeviceUtil.getDeviceToken(BaseApplication.getMyApplicationContext()));
        for (String s : arr) {
            if (TextUtils.equals(s, userIdStr)) {
                return true;
            }
        }
        return false;
    }

    private static void doRealShowHomePageNormalNotificationPermissionDialog(MainActivity activity, boolean isPushSettingOpen, boolean isForKill) {
        if (ViewUtil.haveDialogIsShowing(activity)) {
            return;
        }
        NotificationHomeNormalMessage notificationHomeNormalMessage = getNotificationHomeNormalMessage();
        if (notificationHomeNormalMessage == null || TextUtils.isEmpty(notificationHomeNormalMessage.subtitle)) {
            return;
        }
        if (!canShowHomePageNormalNotificationPermissionDialog(notificationHomeNormalMessage.daysInterval)) {
            return;
        }
        showHomePageNormalNotificationPermissionDialog(activity, isPushSettingOpen, notificationHomeNormalMessage.subtitle, notificationHomeNormalMessage.title, isForKill);
    }
    // 首页普通通知栏权限打开引导弹窗
    public static void showHomePageNormalNotificationPermissionDialog
            (Activity activity, boolean isPushSettingOpen, String subTitle, String title, boolean isForKill) {
        if (TextUtils.isEmpty(title)) {
            title = "打开推送通知";
        }
        DialogBuilder dialogBuilder = new DialogBuilder(activity)
                .setTitle(title)
                .setTitleTextSize(21)
                .setMessage(subTitle)
                .setMsgTextSize(15)
                .setTitleTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_titleColor))
                .setMsgTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_mediumTextColor))
                .setContentLayoutBg(R.drawable.framework_round18_bg_white)
                .setOkBtnBg(R.drawable.framework_round_bottom_btn_right_corner18_bg_selector)
                .setCancelBtnBg(R.drawable.framework_round_bottom_btn_left_corner18_bg_selector)
                .setOkBtn("去开启", () -> {
                    if (!isPushSettingOpen) {
                        try {
                            Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().
                                    setPushSettingOpenIncludeLogoutState(PreferenceConstantsInHost.TINGMAIN_KEY_IS_PUSH_ALL, true);
                            if (NotificationUtil.isSystemNotificationEnable(activity)) {
                                CustomToast.showToast("打开推送通知成功");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    if (!NotificationUtil.isSystemNotificationEnable(activity)) {
                        DeviceUtil.openSystemNotificationPage(activity);
                    }
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("dialogTitle", subTitle)
                            .put("currPage", "首页")
                            .put("dialogType", getDialogType(isForKill? NotificationEntranceType.APP_CRASH : NotificationEntranceType.APP_NORMAL_HOME))
                            .put("item", "去开启")
                            .put("style", isForKill? "1" : "0")
                            .createTrace();

                    saveNotificationPermissionUploadMessage("首页", getDialogType(isForKill? NotificationEntranceType.APP_CRASH : NotificationEntranceType.APP_NORMAL_HOME), subTitle);
                })
                .setCancelBtn("暂不开启", () -> {
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("dialogTitle", subTitle)
                            .put("currPage", "首页")
                            .put("dialogType", getDialogType(isForKill? NotificationEntranceType.APP_CRASH : NotificationEntranceType.APP_NORMAL_HOME))
                            .put("item", "取消")
                            .put("style", isForKill? "1" : "0")
                            .createTrace();
                });
        View contentLayout = dialogBuilder.getContentLayout();
        if (contentLayout != null) {
            ViewGroup.LayoutParams layoutParams = contentLayout.getLayoutParams();
            layoutParams.width = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 275);
        }

        FrameLayout topBgLayout = dialogBuilder.getTopBgLayout();
        if (topBgLayout != null) {
            topBgLayout.setVisibility(View.VISIBLE);
            topBgLayout.setBackgroundResource(R.drawable.host_14corner_f5f5f5_414141);
            topBgLayout.removeAllViews();

            topBgLayout.setPadding(BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6), 0);

            ImageView imageView = new ImageView(topBgLayout.getContext());
            imageView.setImageResource(R.drawable.main_bg_play_page_open_notification_top);
            topBgLayout.addView(imageView);
        }
        dialogBuilder.showConfirm();
        // 打开push通知弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(46907)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", "首页")
                .put("dialogType", getDialogType(isForKill? NotificationEntranceType.APP_CRASH : NotificationEntranceType.APP_NORMAL_HOME))
                .put("dialogTitle", subTitle)
                .put("style", isForKill? "1" : "0")
                .createTrace();

        sNotificationPermissionOpenDialogShowed = true;
        if (!isForKill) {
            saveShowHomePageNormalNotificationPermissionDialog();
        } else {
            saveShowHomePageCrashNotificationPermissionDialog();
        }
    }

    public static boolean checkToShowNotificationPermissionDialog(Activity activity, NotificationEntranceType notificationEntranceType,
                                                                  String currPage) {
        if (activity == null) {
            return false;
        }
        if (NotificationUtil.isSystemNotificationEnable(activity)) {
            return false;
        }
        if (!canShowNotificationPermissionDialog()) {
            return false;
        }
        // 66版本移除ab代码
//        if (!isABTestEnable()) {
//            return false;
//        }
        NotificationMessage notificationMessage = new NotificationMessage();
        String str = "";
        switch (notificationEntranceType) {
            case FOLLOW:
                str = ConfigureCenter.getInstance().getString("toc", "follow_push_reopen_title", "");
                break;
            case LIKE:
                str = ConfigureCenter.getInstance().getString("toc", "like_push_reopen_title", "");
                break;
            case COMMENT:
                str = ConfigureCenter.getInstance().getString("toc", "comment_push_reopen_title", "");
                break;
            default:
                break;
        }
        try {
            JSONObject jsonObject = new JSONObject(str);
            notificationMessage.subTitle = jsonObject.optString("subTitle", "");
            notificationMessage.title = jsonObject.optString("title", "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(notificationMessage.title)) {
            notificationMessage = new NotificationMessage();
            switch (notificationEntranceType) {
                case FOLLOW:
                    notificationMessage.title = "别错过TA的消息";
                    notificationMessage.subTitle = "打开推送，第一时间获得TA的动态和作品信息";
                    break;
                case LIKE:
                    notificationMessage.title = "打开推送，获取你喜欢的内容";
                    notificationMessage.subTitle = "每天都能收到你感兴趣的声音哦～";
                    break;
                case COMMENT:
                    notificationMessage.title = "打开推送接收你的社交动态";
                    notificationMessage.subTitle = "第一时间获取与你有关的评论通知";
                    break;
                default:
                    notificationMessage.title = DEFAULT_TITLE;
                    notificationMessage.subTitle = DEFAULT_SUB_TITLE;
                    break;
            }
        }
        NotificationMessage finalNotificationMessage = notificationMessage;
        DialogBuilder dialogBuilder = new DialogBuilder(activity)
                .setTitle(notificationMessage.title)
                .setMessage(notificationMessage.subTitle)
                .setTitleTextSize(21)
                .setMsgTextSize(15)
                .setTitleTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_titleColor))
                .setMsgTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_mediumTextColor))
                .setContentLayoutBg(R.drawable.framework_round18_bg_white)
                .setOkBtnBg(R.drawable.framework_round_bottom_btn_right_corner18_bg_selector)
                .setCancelBtnBg(R.drawable.framework_round_bottom_btn_left_corner18_bg_selector)
                .setOkBtn("去开启", () -> {
                    DeviceUtil.openSystemNotificationPage(activity);
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("currPage", currPage)
                            .put("dialogType", getDialogType(notificationEntranceType)) // app误杀｜当天首次关注｜当天首次点赞｜当天首次评论
                            .put("dialogTitle", finalNotificationMessage.title)
                            .put("item", "去开启")
                            .createTrace();

                    saveNotificationPermissionUploadMessage(currPage, getDialogType(notificationEntranceType), finalNotificationMessage.title);
                })
                .setCancelBtn("暂不开启", () -> {
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("currPage", currPage)
                            .put("dialogType", getDialogType(notificationEntranceType)) // app误杀｜当天首次关注｜当天首次点赞｜当天首次评论
                            .put("dialogTitle", finalNotificationMessage.title)
                            .put("item", "取消")
                            .createTrace();
                });
        View contentLayout = dialogBuilder.getContentLayout();
        if (contentLayout != null) {
            ViewGroup.LayoutParams layoutParams = contentLayout.getLayoutParams();
            layoutParams.width = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 275);
        }

        FrameLayout topBgLayout = dialogBuilder.getTopBgLayout();
        if (topBgLayout != null) {
            topBgLayout.setVisibility(View.VISIBLE);
            topBgLayout.setBackgroundResource(R.drawable.host_14corner_f5f5f5_414141);
            topBgLayout.removeAllViews();

            topBgLayout.setPadding(BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6), 0);

            ImageView imageView = new ImageView(topBgLayout.getContext());
            imageView.setImageResource(R.drawable.main_bg_open_notification_top);
            topBgLayout.addView(imageView);
        }
        dialogBuilder.showConfirm();
        saveShowNotificationPermissionDialog();
        // 打开push通知弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(46907)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", currPage)
                .put("dialogType", getDialogType(notificationEntranceType)) // 当天首次关注｜当天首次点赞｜当天首次评论
                .put("dialogTitle", finalNotificationMessage.title)
                .createTrace();
        return true;
    }

    public static boolean checkToShowPlayFragmentNotificationPermissionDialog(Activity activity, NotificationEntranceType notificationEntranceType,
                                                                              String currPage) {
        if (activity == null) {
            return false;
        }
        if (sNotificationPermissionOpenDialogShowed) {
            return false;
        }
        if (NotificationUtil.isSystemNotificationEnable(activity)) {
            return false;
        }

        // 首次安装不出
        if (ToolUtil.isFirstInstallApp(BaseApplication.getMyApplicationContext())) {
            return false;
        }

        if (!canShowNotificationPermissionDialog()) {
            return false;
        }
        NotificationPlayPageMessage notificationPlayPageMessage = getNotificationPlayPageMessage();
        if (notificationPlayPageMessage == null) {
            notificationPlayPageMessage = new NotificationPlayPageMessage();
            notificationPlayPageMessage.appCrashDaysInterval = 7;
            notificationPlayPageMessage.appCrashShowMaxTimes = 3;
            notificationPlayPageMessage.normalDaysInterval = 30;
            notificationPlayPageMessage.subtitle = "建议打开通知权限，避免因系统杀死播放进程中断播放，影响收听体验~";
        }
        if (!canShowPlayPageNotificationPermissionDialog(notificationPlayPageMessage, notificationEntranceType)) {
            return false;
        }
        String titleTemp = "打开通知权限";
        if (!TextUtils.isEmpty(notificationPlayPageMessage.title)) {
            titleTemp = notificationPlayPageMessage.title;
        }
        String title = titleTemp;
        DialogBuilder dialogBuilder = new DialogBuilder(activity)
                .setTitle(title)
                .setMessage(notificationPlayPageMessage.subtitle)
                .setTitleTextSize(21)
                .setMsgTextSize(15)
                .setTitleTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_titleColor))
                .setMsgTextColor(ContextCompat.getColor(BaseApplication.getMyApplicationContext(), R.color.host_color_mediumTextColor))
                .setContentLayoutBg(R.drawable.framework_round18_bg_white)
                .setOkBtnBg(R.drawable.framework_round_bottom_btn_right_corner18_bg_selector)
                .setCancelBtnBg(R.drawable.framework_round_bottom_btn_left_corner18_bg_selector)
                .setOkBtn("去开启", () -> {
                    DeviceUtil.openSystemNotificationPage(activity);
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("currPage", currPage)
                            .put("dialogType", getDialogType(notificationEntranceType)) // app误杀｜当天首次关注｜当天首次点赞｜当天首次评论
                            .put("dialogTitle", title)
                            .put("item", "去开启")
                            .createTrace();

                    saveNotificationPermissionUploadMessage(currPage, getDialogType(notificationEntranceType), title);
                })
                .setCancelBtn("暂不开启", () -> {
                    // 打开push通知弹窗-操作  弹框控件点击
                    new XMTraceApi.Trace()
                            .setMetaId(46908)
                            .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                            .put("currPage", currPage)
                            .put("dialogType", getDialogType(notificationEntranceType)) // app误杀｜当天首次关注｜当天首次点赞｜当天首次评论
                            .put("dialogTitle", title)
                            .put("item", "取消")
                            .createTrace();
                });
        View contentLayout = dialogBuilder.getContentLayout();
        if (contentLayout != null) {
            ViewGroup.LayoutParams layoutParams = contentLayout.getLayoutParams();
            layoutParams.width = BaseUtil.dp2px(BaseApplication.getMyApplicationContext(), 275);
        }

        FrameLayout topBgLayout = dialogBuilder.getTopBgLayout();
        if (topBgLayout != null) {
            topBgLayout.setVisibility(View.VISIBLE);
            topBgLayout.setBackgroundResource(R.drawable.host_14corner_f5f5f5_414141);
            topBgLayout.removeAllViews();

            topBgLayout.setPadding(BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6),
                    BaseUtil.dp2px(ToolUtil.getCtx(), 6), 0);

            ImageView imageView = new ImageView(topBgLayout.getContext());
            imageView.setImageResource(R.drawable.main_bg_play_page_open_notification_top);
            topBgLayout.addView(imageView);
        }
        dialogBuilder.showConfirm();

        sNotificationPermissionOpenDialogShowed = true;
        if (notificationEntranceType == NotificationEntranceType.PLAY_PAGE_APP_CRASH) {
            saveShowPlayPageAppCrashNotificationPermissionDialogCount();
            saveShowPlayPageAppCrashNotificationPermissionDialog();
        } else {
            saveShowPlayPageNormalNotificationPermissionDialog();
        }
        saveShowNotificationPermissionDialog();
        // 打开push通知弹窗  弹框展示
        new XMTraceApi.Trace()
                .setMetaId(46907)
                .setServiceId("dialogView") // 弹窗展示时上报
                .put("currPage", currPage)
                .put("dialogType", getDialogType(notificationEntranceType)) // 当天首次关注｜当天首次点赞｜当天首次评论
                .put("dialogTitle", title)
                .createTrace();
        return true;
    }

    public static String getDialogType(NotificationEntranceType notificationEntranceType) {
        String dialogType = "";
        switch (notificationEntranceType) {
            case FOLLOW:
                dialogType = "当天首次关注";
                break;
            case LIKE:
                dialogType = "当天首次点赞";
                break;
            case COMMENT:
                dialogType = "当天首次评论";
                break;
            case APP_CRASH:
                dialogType = "app误杀";
                break;
            case APP_NORMAL_HOME:
                dialogType = "首页默认";
                break;
            case PLAY_PAGE_APP_CRASH:
                dialogType = "播放页误杀";
                break;
            case PLAY_PAGE_NORMAL:
                dialogType = "播放页普通";
                break;
            case NEW_PRODUCT_RESERVE:
                dialogType = "新品预约";
                break;
            default:
                break;
        }
        return dialogType;
    }

    public static void saveFirstFollow() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_FIRST_FOLLOW_DATE, time);
    }

    public static boolean isTodayFirstFollowed() {
        String dialogShowDate = MMKVUtil.getInstance().getString(KEY_FIRST_FOLLOW_DATE);
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        return TextUtils.equals(dialogShowDate, time);
    }

    public static void saveFirstLike() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_FIRST_LIKE_DATE, time);
    }

    public static boolean isTodayFirstLiked() {
        String dialogShowDate = MMKVUtil.getInstance().getString(KEY_FIRST_LIKE_DATE);
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        return TextUtils.equals(dialogShowDate, time);
    }

    public static void saveFirstComment() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_FIRST_COMMENT_DATE, time);
    }

    public static boolean isTodayFirstCommented() {
        String dialogShowDate = MMKVUtil.getInstance().getString(KEY_FIRST_COMMENT_DATE);
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        return TextUtils.equals(dialogShowDate, time);
    }

    private static void saveShowHomePageCrashNotificationPermissionDialog() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_HOME_PAGE_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE, time);
    }

    private static boolean canShowHomePageCrashNotificationPermissionDialog() {
        String dialogShowDate = MMKVUtil.getInstance().getString(KEY_HOME_PAGE_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE);
        if (TextUtils.isEmpty(dialogShowDate)) {
            return true;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        long lastTimeMs = 0;
        long curTimeMs = System.currentTimeMillis();
        try {
            lastTimeMs = sdf.parse(dialogShowDate).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (curTimeMs - lastTimeMs) >= ONE_DAY_MS * 3;
    }

    private static void saveShowHomePageNormalNotificationPermissionDialog() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_HOME_PAGE_NORMAL_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE, time);
    }

    private static boolean canShowHomePageNormalNotificationPermissionDialog(int daysInterval) {
        // 两天内，三类弹窗至多出现一次
        String dialogShowDate = MMKVUtil.getInstance().getString(KEY_HOME_PAGE_NORMAL_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE);
        if (daysInterval <= 0) {
            daysInterval = 7;
        }
        if (TextUtils.isEmpty(dialogShowDate)) {
            return true;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        long lastTimeMs = 0;
        long curTimeMs = System.currentTimeMillis();
        try {
            lastTimeMs = sdf.parse(dialogShowDate).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (curTimeMs - lastTimeMs) >= ONE_DAY_MS * daysInterval;
    }

    private static void saveShowNotificationPermissionDialog() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE, time);
    }

    private static boolean canShowNotificationPermissionDialog() {
        // 两天内，三类弹窗至多出现一次
        String dialogShowDate = MMKVUtil.getInstance().getString(KEY_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE);
        if (TextUtils.isEmpty(dialogShowDate)) {
            return true;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        long lastTimeMs = 0;
        long curTimeMs = System.currentTimeMillis();
        try {
            lastTimeMs = sdf.parse(dialogShowDate).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (curTimeMs - lastTimeMs) >= ONE_DAY_MS * 2;
    }

    private static NotificationHomeNormalMessage getNotificationHomeNormalMessage() {
        String notificationMessageStr = ConfigureCenter.getInstance().getString("toc", KEY_HOME_NOTIFICATION_POP_WINDOW_STYLE, "");
        if (TextUtils.isEmpty(notificationMessageStr)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(notificationMessageStr);
            NotificationHomeNormalMessage notificationHomeNormalMessage = new NotificationHomeNormalMessage();
            notificationHomeNormalMessage.subtitle = jsonObject.optString("subtitle", "");
            notificationHomeNormalMessage.daysInterval = jsonObject.optInt("daysInterval", 7);
            notificationHomeNormalMessage.title = jsonObject.optString("title", "");
            return notificationHomeNormalMessage;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void saveShowPlayPageAppCrashNotificationPermissionDialogCount() {
        int count = MMKVUtil.getInstance().getInt(KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_COUNT, 0);
        MMKVUtil.getInstance().saveInt(KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_COUNT, count + 1);
    }

    private static void saveShowPlayPageAppCrashNotificationPermissionDialog() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE, time);
    }

    private static void saveShowPlayPageNormalNotificationPermissionDialog() {
        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString(KEY_PLAY_PAGE_NORMAL_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE, time);
    }

    private static boolean canShowPlayPageNotificationPermissionDialog(NotificationPlayPageMessage notificationPlayPageMessage,
                                                                       NotificationEntranceType notificationEntranceType) {
        String dialogShowDate = MMKVUtil.getInstance().getString((notificationEntranceType == NotificationEntranceType.PLAY_PAGE_APP_CRASH) ?
                KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE : KEY_PLAY_PAGE_NORMAL_NOTIFICATION_PERMISSION_DIALOG_SHOW_DATE);
        int daysInterval = (notificationEntranceType == NotificationEntranceType.PLAY_PAGE_APP_CRASH) ? notificationPlayPageMessage.appCrashDaysInterval :
                notificationPlayPageMessage.normalDaysInterval;
        if (daysInterval <= 0) {
            daysInterval = 7;
        }
        int count = MMKVUtil.getInstance().getInt(KEY_PLAY_PAGE_APP_CRASH_NOTIFICATION_PERMISSION_DIALOG_SHOW_COUNT, 0);
        if ((notificationEntranceType == NotificationEntranceType.PLAY_PAGE_APP_CRASH) && count >= notificationPlayPageMessage.appCrashShowMaxTimes) {
            return false;
        }
        if (TextUtils.isEmpty(dialogShowDate)) {
            return true;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        long lastTimeMs = 0;
        long curTimeMs = System.currentTimeMillis();
        try {
            lastTimeMs = sdf.parse(dialogShowDate).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (curTimeMs - lastTimeMs) >= ONE_DAY_MS * daysInterval;
    }

    private static NotificationPlayPageMessage getNotificationPlayPageMessage() {
        String notificationMessageStr = ConfigureCenter.getInstance().getString("toc", KEY_PLAY_PAGE_NOTIFICATION_POP_WINDOW_STYLE, "");
        if (TextUtils.isEmpty(notificationMessageStr)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(notificationMessageStr);
            NotificationPlayPageMessage notificationHomeNormalMessage = new NotificationPlayPageMessage();
            notificationHomeNormalMessage.title = jsonObject.optString("title", "");
            notificationHomeNormalMessage.subtitle = jsonObject.optString("subtitle", "");
            notificationHomeNormalMessage.appCrashDaysInterval = jsonObject.optInt("appCrashDaysInterval", 7);
            notificationHomeNormalMessage.appCrashShowMaxTimes = jsonObject.optInt("appCrashShowMaxTimes", 3);
            notificationHomeNormalMessage.normalDaysInterval = jsonObject.optInt("normalDaysInterval", 30);
            return notificationHomeNormalMessage;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void saveNotificationPermissionUploadMessage(String currPage, String dialogType, String dialogTitle) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("currPage", currPage);
            jsonObject.put("dialogType", dialogType);
            jsonObject.put("dialogTitle", dialogTitle);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        MMKVUtil.getInstance().saveString(KEY_NOTIFICATION_PERMISSION_UPLOAD_MESSAGE, jsonObject.toString());
    }

    /**
     * 判断通知权限真的开启并上报
     */
    public static void checkNotificationPermissionStatusAndUpload() {
        NotificationPermissionUploadMessage notificationPermissionUploadMessage = getNotificationPermissionUploadMessage();
        if (notificationPermissionUploadMessage == null || TextUtils.isEmpty(notificationPermissionUploadMessage.dialogType)) {
            return;
        }
        // 清空
        MMKVUtil.getInstance().saveString(KEY_NOTIFICATION_PERMISSION_UPLOAD_MESSAGE, "");
        if (NotificationUtil.isSystemNotificationEnable(ToolUtil.getCtx())) {
            // 打开push通知弹窗-操作  弹框控件点击
            new XMTraceApi.Trace()
                    .setMetaId(46908)
                    .setServiceId("dialogClick") // 用户在弹窗内点击就上报
                    .put("currPage", notificationPermissionUploadMessage.currPage)
                    .put("dialogType", notificationPermissionUploadMessage.dialogType) // app误杀｜当天首次关注｜当天首次点赞｜当天首次评论
                    .put("dialogTitle", notificationPermissionUploadMessage.dialogTitle)
                    .put("item", "真的开启")
                    .createTrace();
        }
    }

    private static NotificationPermissionUploadMessage getNotificationPermissionUploadMessage() {
        String notificationMessageStr = MMKVUtil.getInstance().getString(KEY_NOTIFICATION_PERMISSION_UPLOAD_MESSAGE);
        if (TextUtils.isEmpty(notificationMessageStr)) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(notificationMessageStr);
            NotificationPermissionUploadMessage notificationPermissionUploadMessage = new NotificationPermissionUploadMessage();
            notificationPermissionUploadMessage.dialogType = jsonObject.optString("dialogType", "");
            notificationPermissionUploadMessage.dialogTitle = jsonObject.optString("dialogTitle", "");
            notificationPermissionUploadMessage.currPage = jsonObject.optString("currPage", "");
            return notificationPermissionUploadMessage;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }



    /**-------------以下是关注主播、预约新品的弹窗逻辑-----------------**/


    public static boolean checkToShowFollowNotificationPermissionDialog(NotificationEntranceType notificationEntranceType,
                                                                        String iconUrl,
                                                                        String currPage) {
        Context context = BaseApplication.getMyApplicationContext();
        if (NotificationUtil.isSystemNotificationEnable(context) &&
                MmkvCommonUtil.getInstance(context).getBooleanCompat(PreferenceConstantsInHost.TINGMAIN_KEY_IS_PUSH_ALL, false) &&
                MmkvCommonUtil.getInstance(context).getBooleanCompat(PreferenceConstantsInHost.TINGMAIN_KEY_PUSH_RESERVATION, false)) {
            return false;
        }
        if (!MmkvCommonUtil.getInstance(context).containsKeyCompat(PreferenceConstantsInHost.TINGMAIN_KEY_IS_PUSH_ALL) ||
                !MmkvCommonUtil.getInstance(context).containsKeyCompat(PreferenceConstantsInHost.TINGMAIN_KEY_PUSH_RESERVATION)) {
            try {
                Router.<MainActionRouter>getActionRouter(Configure.BUNDLE_MAIN).getFunctionAction().isPushSettingOpenIncludeLogoutState(
                        PreferenceConstantsInHost.TINGMAIN_KEY_IS_PUSH_ALL, null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (!NotificationUtil.isSystemNotificationEnable(context)) {
                return false;
            }
        }

        if (!UserInfoMannage.hasLogined()) {
            return false;
        }
        if (!(BaseApplication.getMainActivity() instanceof MainActivity)) {
            return false;
        }
        if (!canShowFrequencyControl(notificationEntranceType)) {
            return false;
        }

        String time = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(System.currentTimeMillis());
        MMKVUtil.getInstance().saveString("new_key_notification_permission_date_" + getDialogType(notificationEntranceType), time);
        BaseDialogFragment dialog = NoPermissionFollowGuideDialog.Companion.newInstance(notificationEntranceType, iconUrl, currPage);
        dialog.show(((MainActivity) BaseApplication.getMainActivity()).getSupportFragmentManager(), "NoPermissionFollowGuideDialog");
        return true;
    }

    private static boolean canShowFrequencyControl(NotificationEntranceType notificationEntranceType) {
        String dialogShowDate = MMKVUtil.getInstance().getString("new_key_notification_permission_date_" + getDialogType(notificationEntranceType));
        if (TextUtils.isEmpty(dialogShowDate)) {
            return true;
        }
        if ("1".equals(ToolUtil.getSystemProperty("debug.sjc.permissiontime", ""))) {
            return true;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        long lastTimeMs = 0;
        long curTimeMs = System.currentTimeMillis();
        try {
            lastTimeMs = sdf.parse(dialogShowDate).getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        int configDay = 1;
        String configDayStr = ConfigureCenter.getInstance().getString("toc", "reserve_notification_interval", "");
        if (!TextUtils.isEmpty(configDayStr)) {
            try {
                JSONObject jsonObject = new JSONObject(configDayStr);
                if (notificationEntranceType == NotificationEntranceType.FOLLOW) {
                    configDay = jsonObject.optInt("follow", 1);
                } else {
                    configDay = jsonObject.optInt("reserve", 1);
                }
            } catch (JSONException e) {
            }
        }
        return (curTimeMs - lastTimeMs) >= ONE_DAY_MS * configDay;
    }
}
