package com.ximalaya.ting.android.host.hybrid.providerSdk.storage;


import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;
import com.ximalaya.ting.android.hybridview.utils.ThreadPool;

import org.json.JSONObject;

/**
 * Created by vive on 2017/4/11.
 * <AUTHOR>
 */

public class ClearItemAction extends BaseStorageAction {
    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args,
                         final AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        final String domain = getCompId(comp, scheme);
        ThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                HybridCacheManager.getInstance().clearItem(domain);
                callback.callback(NativeResponse.success());
            }
        });
    }
}
