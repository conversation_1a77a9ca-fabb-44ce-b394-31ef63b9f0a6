package com.ximalaya.ting.android.host.hybrid.provider.account;

import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew;
import com.ximalaya.ting.android.hybridview.IHybridContainer;
import com.ximalaya.ting.android.hybridview.NativeResponse;
import com.ximalaya.ting.android.hybridview.component.Component;

import org.json.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/18.
 * <AUTHOR>
 */

public class GetUserInfoAction extends BaseAccountAction {
    @Override
    public boolean needStatRunloop() {
        return false;
    }

    @Override
    public void doAction(IHybridContainer hybridContainer, JSONObject args,
                         AsyncCallback callback, Component comp, String scheme) {
        super.doAction(hybridContainer, args, callback, comp, scheme);
        LoginInfoModelNew loginInfoModel = UserInfoMannage.getInstance().getUser();
        callback.callback(NativeResponse.success(getAccountCallBackParams(loginInfoModel)));
    }
}
