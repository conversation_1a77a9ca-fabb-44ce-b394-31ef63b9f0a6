package com.ximalaya.ting.android.host.hybrid.utils;

/**
 * Created by vive on 2017/11/2.
 * <AUTHOR>
 * 检查是否是从外部scheme打开到组件页面，为了保证jsb的接口安全，不允许未授权的外部应用直接打开组件容器
 */

public class ComponentSecuritySignUtils {
    private static final ComponentSecuritySignUtils ourInstance = new ComponentSecuritySignUtils();

    public static ComponentSecuritySignUtils getInstance() {
        return ourInstance;
    }

    private int mSignCount = 0;


    private ComponentSecuritySignUtils() {
        mSignCount += Math.random() * 10;
    }

    public void randCount() {
        mSignCount += Math.random() * 10;
    }

    public int getCount() {
        return mSignCount;
    }
}
