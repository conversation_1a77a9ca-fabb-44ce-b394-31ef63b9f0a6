package com.ximalaya.ting.android.host.adapter;

import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;

import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;

/**
 * <AUTHOR> on 2017/1/10.
 */

public class MyFragmentStatePagerAdapter extends FragmentStatePagerAdapter {

    public MyFragmentStatePagerAdapter(FragmentManager fm) {
        super(fm);
    }

    public MyFragmentStatePagerAdapter(FragmentManager fm, int behavior) {
        super(fm, behavior);
    }

    /**
     * Return the Fragment associated with a specified position.
     *
     * @param position
     */
    @Override
    public Fragment getItem(int position) {
        return null;
    }

    /**
     * Return the number of views available.
     */
    @Override
    public int getCount() {
        return 0;
    }

    @Override
    public void finishUpdate(ViewGroup container) {
        try {
            super.finishUpdate(container);
        } catch (Exception e) {
            e.printStackTrace();
            if (ConstantsOpenSdk.isDebug) {
                throw e;
            } else {
                CrashReport.postCatchedException(e);
            }
        }
    }
}
