package com.ximalaya.ting.android.host.model.ad;

import android.app.Activity;

import com.ximalaya.ting.android.ad.model.thirdad.RewardVideo.AbstractRewardVideoAd;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.framework.view.dialog.XmBaseDialog;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV2;
import com.ximalaya.ting.android.host.manager.ad.unlockpaid.AdUnLockTimeManagerV3;
import com.ximalaya.ting.android.host.manager.ad.videoad.CanPauseCountDownTimer;
import com.ximalaya.ting.android.host.manager.configurecenter.CConstants;
import com.ximalaya.ting.android.host.util.constant.AppConstants;
import com.ximalaya.ting.android.opensdk.model.advertis.Advertis;

/**
 * Created by le.xin on 2020/5/15.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class RewardExtraParams {
    public static final int DEFAULT_CLOSE_TIME = 10;
    public static final int DEFAULT_WATCH_VIDEO_TIME = 30;  // 默认的查看视频的时间

    public static final int REWARD_COUNT_DOWN_STYLE = 1;            // 默认的倒计时样式
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_UNLOCK = 2; // 付费解锁
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE = 3; // 全站畅听
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3 = 4; // 畅听第3阶段
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3_AD_FIRST = 5; // 畅听第3阶段 广告优先
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_4 = 6; // 畅听4
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_5 = 7; // 畅听5
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_FORWARD_VIDEO = 8 ; // 前插视频
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_V2 = 9; // 时长模式
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK = 10; // 单集解锁
    public static final int REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2 = 11; // 时长模式点击奖励
    public static final int REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK = 12; // 单集解锁点击奖励
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_SKITS_UNLOCK = 13; // 短剧解锁
    public static final int REWARD_COUNT_DOWN_STYLE_FOR_POINT = 14; // 积分奖励
    public static final int REWARD_CLICK_STYLE_FOR_POINT = 15; // 积分点击奖励

    public static final int REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_VIP = 16; // vip时长模式
    public static final int REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP = 17; // vip点击模式

    public static final int REWARD_COUNT_DOWN_STYLE_FOR_WELFARE = 18; // 福利页奖励现金时长模式
    public static final int REWARD_CLICK_STYLE_FOR_WELFARE = 19; // 福利页奖励现金点击模式

    public static final int REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD = 20; // 下载激励视频时长奖励
    public static final int REWARD_CLICK_STYLE_FOR_DOWNLOAD = 21; // 下载激励视频点击奖励

    public static final int REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW = 22; // 免广告时长时长模式
    public static final int REWARD_CLICK_STYLE_FOR_FREE_AD_NEW = 23; // 免广告时长点击模式

    // 需要观看视频的个数
    private int needLookCount;
    private int indexLook;

    private boolean closeable = true;   // 是否可以关闭广告
    private int canCloseTime = DEFAULT_CLOSE_TIME;   // 在什么时间可以关闭(单位s)
    private int videoPlayOverTime = DEFAULT_WATCH_VIDEO_TIME; // 视频总共需要播放多长时间,不根据穿山甲那边的回调进行处理
    private int rewardTime ;// 单次奖励的时长,单位分钟
    private int rewardCountDownStyle = REWARD_COUNT_DOWN_STYLE;   // 激励视频倒计时的样式
    private int unlockType;// 解锁形式 0-时长 1-视频点击 2-图片点击 3-时长点击叠加
    private String clickRewardString;

    private IRewardStateCallBack rewardStateCallBack;

    private CanPauseCountDownTimer mCountDownTimer;

    private XmBaseDialog mCloseDialog;

    private int adType;

    private Advertis mAdvertis;
    private String positionName;
    private String configPositionName;// 虚拟positionName, 用于获取激励视频相关配置，避免多个业务场景共用一个广告位，但是配置不同的情况.
    private String sourceName;

    // 全站畅听解锁的专辑
    private String unLockTrackTitle;

    private IRewardTwiceCallBack rewardTwiceCallBack;
    private IRewardPageStatusCallBack rewardPageStatusCallBack;
    private IRewardNextVideoCallBack rewardNextVideoCallBack;
    private IRewardThirdSDKSuccessCallBack onThirdSDKSuccessCallBack;

    private boolean isRewardSecond; // 是否是第二次解锁状态
    private boolean isFirstRewardSuccess; // 第一次解锁是是否成功
    private boolean isSecondRewardSuccess; // 第二次解锁是否成功
    private boolean isSupportRewardAgain; // 是否支持二次解锁，剩余解锁次数等于0时，不再支持二次解锁

    // 时长+点击模式
    private int extraClickRewardTime ;// 时长+点击模式,额外点击获得的奖励时长，单位分钟
    private boolean isExtraClickRewardSuccess;// 时长+点击模式，是否点击了广告可奖励额外的时长

    private int rewardTypeParam = 1; // 畅听奖励接口参数，1:观看视频奖励, 2:每日时长赠送 3:浮层领取,4:赠送弹窗继续领取 5:全天免费时长 6:奖励一天会员 7: 再看一个弹窗 8. 播放页免广奖励
    private String rewardFailToast; // 奖励失败或者取消toast
    private int rewardInfoType; // 按rewardInfos中的type返回，没有就传0
    private boolean isShakeEnable; // 是否支持摇一摇
    private boolean canReward; // 是否达到奖励条件
    private String pointCashStep;
    private int pointCashType;

    private int rewardTimes;//当前第几步
    private double cashBalance;//奖励金额
    private int maxRewardTimes;//总步数
    private int mGoNextTimes;//执行跳转下一个激励视频次数
    private boolean isRewardBeforeClose; //是否已经奖励过在关闭广告之前
    private int rewardFreeAdTime;
    public String extInfo; //激励视频广告的扩展信息 Rn透传
    public AbstractRewardVideoAd abstractThirdAd; // dsp广告物料
    private boolean isNeedRequest = true; // 是否需要请求服务端接口发放奖励
    private boolean canPreloadInspireAd = true; // 是否可以在激励视频完成后预请求唤端广告,rn发放奖励的流程，不需要客户端预请求唤端

    public void setXmVideoAdvertisModel(Advertis advertis, String positionName) {
        mAdvertis = advertis;
        this.positionName = positionName;
    }

    public int getNeedLookCount() {
        return needLookCount;
    }

    public void setNeedLookCount(int needLookCount) {
        this.needLookCount = needLookCount;
    }

    public int getIndexLook() {
        return indexLook;
    }

    public void setIndexLook(int indexLook) {
        this.indexLook = indexLook;
    }

    public boolean isCloseable() {
        return closeable;
    }

    public void setCloseable(boolean closeable) {
        this.closeable = closeable;
    }

    public int getCanCloseTime() {
        return canCloseTime;
    }

    public void setCanCloseTime(int canCloseTime) {
        this.canCloseTime = canCloseTime;
    }

    public RewardExtraParams() {

    }

    public IRewardStateCallBack getRewardStateCallBack() {
        return rewardStateCallBack;
    }

    public CanPauseCountDownTimer getCountDownTimer() {
        return mCountDownTimer;
    }

    public void setCountDownTimer(CanPauseCountDownTimer countDownTimer) {
        mCountDownTimer = countDownTimer;
    }

    public void setVipFreeCloseAlertDialog(XmBaseDialog closeAlertDialog) {
        mCloseDialog = closeAlertDialog;
    }

    public XmBaseDialog getVipFreeCloseAlertDialog() {
        return mCloseDialog;
    }

    public int getAdType() {
        return adType;
    }

    public void setAdType(int adType) {
        this.adType = adType;
    }

    public Advertis getAdvertis() {
        return mAdvertis;
    }

    public AbstractRewardVideoAd getAbstractThirdAd() {
        return abstractThirdAd;
    }

    public void setAbstractThirdAd(AbstractRewardVideoAd abstractThirdAd) {
        this.abstractThirdAd = abstractThirdAd;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getConfigPositionName() {
        return configPositionName;
    }

    public void setConfigPositionName(String configPositionName) {
        this.configPositionName = configPositionName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    // 是否是畅听类型
    public static boolean isFreeListen(int style) {
        return style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_3_AD_FIRST
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_4
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_VIP_FREE_5
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_V2
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SKITS_UNLOCK
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_POINT
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_POINT
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_VIP
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_LISTEN_VIP
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_WELFARE
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_WELFARE
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_DOWNLOAD
                || style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_AD_NEW
                || style == RewardExtraParams.REWARD_CLICK_STYLE_FOR_FREE_AD_NEW;
    }

    public static int getMaxLoadTime(int style) {
        if (style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_FREE_LISTEN_V2
                || style == REWARD_CLICK_STYLE_FOR_FREE_LISTEN_V2 ) {
            return AdUnLockTimeManagerV2.getInstance().getAdMaxLoadTime();
        } else if (isV3Style(style)) {
            return AdUnLockTimeManagerV3.getInstance().getAdMaxLoadTime();
        } else {
            return ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_MAX_LOAD_TIME, 10000);
        }
    }

    // 返回单位：ms
    public static int getMaxLoadTime(RewardExtraParams extraParams) {
        if (extraParams == null) {
            return ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_MAX_LOAD_TIME, 10000);
        }
        int maxLoadTime;
        if (AppConstants.AD_POSITION_NAME_INCENTIVE_DURATION.equals(extraParams.getPositionName())) {
            maxLoadTime = AdUnLockTimeManagerV2.getInstance().getAdMaxLoadTime();
        } else {
            maxLoadTime = AdUnLockTimeManagerV3.getInstance().getAdMaxLoadTime();
        }
        if (maxLoadTime == 0) {
            return ConfigureCenter.getInstance().getInt(CConstants.Group_ad.GROUP_NAME, CConstants.Group_ad.ITEM_REWARD_VIDEO_MAX_LOAD_TIME, 10000);
        } else {
            return maxLoadTime;
        }
    }

    private static boolean isV3Style(int style){
        return style == RewardExtraParams.REWARD_COUNT_DOWN_STYLE_FOR_SINGLE_TRACK_UNLOCK
                || style == REWARD_CLICK_STYLE_FOR_SINGLE_TRACK_UNLOCK
                || style == REWARD_COUNT_DOWN_STYLE_FOR_SKITS_UNLOCK
                || style == REWARD_COUNT_DOWN_STYLE_FOR_POINT
                || style == REWARD_CLICK_STYLE_FOR_POINT
                || style == REWARD_COUNT_DOWN_STYLE_FOR_DOWNLOAD
                || style == REWARD_CLICK_STYLE_FOR_DOWNLOAD;
    }

    public void setPointCashStep(String pointCashStep) {
        this.pointCashStep = pointCashStep;
    }

    public String getPointCashStep() {
        return pointCashStep;
    }

    public void setPointCashType(int pointCashType) {
        this.pointCashType = pointCashType;
    }

    public int getPointCashType() {
        return pointCashType;
    }

    public void setCashBalance(double cashBalance) {
        this.cashBalance = cashBalance;
    }

    public double getCashBalance() {
        return cashBalance;
    }

    public void setMaxRewardTimes(int maxRewardTimes) {
        this.maxRewardTimes = maxRewardTimes;
    }

    public int getMaxRewardTimes() {
        return maxRewardTimes;
    }

    public void setRewardTimes(int rewardTimes) {
        this.rewardTimes = rewardTimes;
    }

    public int getRewardTimes() {
        return rewardTimes;
    }

    public void setRewardGoNextTimes(int rewardTimes) {
        this.mGoNextTimes = rewardTimes;
    }
    public int getRewardGoNextTimes() {
        return mGoNextTimes;
    }

    public void setRewardBeforeClose(boolean rewardBeforeClose) {
        isRewardBeforeClose = rewardBeforeClose;
    }

    public boolean isRewardBeforeClose() {
        return isRewardBeforeClose;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setRewardFreeAdTime(int rewardFreeAdTime) {
        this.rewardFreeAdTime = rewardFreeAdTime;
    }

    public int getRewardFreeAdTime() {
        return rewardFreeAdTime;
    }

    public interface IRewardStateCallBack {
        void onVideoCountDownOver();

        void onVideoPlayNextClick();

        void onActivityPause();

        void onActivityResume();
    }

    public interface IRewardTwiceCallBack {
          void callReward(); // 视频达到奖励条件，调用解锁接口
          void onFirstVideoCloseWithOutReward(Activity rewardActivity); // 第一次解锁时，未达到解锁条件，直接退出
          void onFirstVideoCloseWithReward(Activity rewardActivity); // 只支持一次解锁时达到奖励条件
          void onLoadSecondVideo(RewardExtraParams rewardExtraParams, Activity thirdActivity); // 第一次解锁成功，开启二次解锁
          void onSecondVideoCloseWithOutReward(Activity rewardActivity); // 第二次解锁时，未达到解锁条件退出
          void onSecondVideoCloseWithReward(Activity rewardActivity); // 第二次解锁时，达到解锁条件退出
    }

    public interface IRewardNextVideoCallBack{
        void jumpToNextVideo(Activity activity, RewardExtraParams params);
    }

    public interface IRewardThirdSDKSuccessCallBack{
        void onSdkRewardSuccess();
    }

    public interface IRewardPageStatusCallBack {
        int SOURCE_FROM_AD_PAGE = 0;
        int SOURCE_FROM_OPEN_VIP_LISTENER_PAGE = 1;

        void onPageResume(Activity activity, int source);

        void onRewardVerify();

        void onCommentShow(long commentTime);
    }

    public RewardExtraParams(int needLookCount, int indexLook, IRewardStateCallBack stateChange) {
        this.needLookCount = needLookCount;
        this.indexLook = indexLook;
        this.rewardCountDownStyle = REWARD_COUNT_DOWN_STYLE_FOR_UNLOCK;
        this.rewardStateCallBack = stateChange;
    }

    public int getRewardCountDownStyle() {
        return rewardCountDownStyle;
    }

    public void setRewardCountDownStyle(int rewardCountDownStyle) {
        this.rewardCountDownStyle = rewardCountDownStyle;
    }

    public int getVideoPlayOverTime() {
        return videoPlayOverTime;
    }

    public void setVideoPlayOverTime(int videoPlayOverTime) {
        this.videoPlayOverTime = videoPlayOverTime;
    }

    public void setUnLockTrackTitle(String trackTitle) {
        this.unLockTrackTitle = trackTitle;
    }

    public String getUnLockTrackTitle() {
        return unLockTrackTitle;
    }

    public static class RecordModel {
        long beginShowTime;

        public long getBeginShowTime() {
            return beginShowTime;
        }

        public void setBeginShowTime(long beginShowTime) {
            this.beginShowTime = beginShowTime;
        }
    }
    public IRewardTwiceCallBack getRewardTwiceCallBack() {
        return rewardTwiceCallBack;
    }

    public void setRewardTwiceCallBack(IRewardTwiceCallBack rewardTwiceCallBack) {
        this.rewardTwiceCallBack = rewardTwiceCallBack;
    }

    public void setRewardNextVideoCallBack(IRewardNextVideoCallBack rewardNextVideoCallBack) {
        this.rewardNextVideoCallBack = rewardNextVideoCallBack;
    }

    public IRewardNextVideoCallBack getRewardNextVideoCallBack() {
        return rewardNextVideoCallBack;
    }

    public void setRewardThirdSDKSuccessCallBack(IRewardThirdSDKSuccessCallBack onThirdSDKSuccessCallBack) {
        this.onThirdSDKSuccessCallBack = onThirdSDKSuccessCallBack;
    }

    public IRewardThirdSDKSuccessCallBack getRewardThirdSDKSuccessCallBack() {
        return onThirdSDKSuccessCallBack;
    }

    public IRewardPageStatusCallBack getRewardPageStatusCallBack() {
        return rewardPageStatusCallBack;
    }

    public void setRewardPageStatusCallBack(IRewardPageStatusCallBack rewardPageStatusCallBack) {
        this.rewardPageStatusCallBack = rewardPageStatusCallBack;
    }

    public boolean isRewardingSecond() {
        return isRewardSecond;
    }

    public void setRewardingSecond(boolean rewardSecond) {
        isRewardSecond = rewardSecond;
    }

    public boolean isSupportRewardAgain() {
        return isSupportRewardAgain;
    }

    public void setSupportRewardAgain(boolean supportRewardAgain) {
        isSupportRewardAgain = supportRewardAgain;
    }

    public boolean isFirstRewardSuccess() {
        return isFirstRewardSuccess;
    }

    public void setFirstRewardSuccess(boolean firstRewardSuccess) {
        isFirstRewardSuccess = firstRewardSuccess;
    }

    public boolean isSecondRewardSuccess() {
        return isSecondRewardSuccess;
    }

    public void setSecondRewardSuccess(boolean secondRewardSuccess) {
        isSecondRewardSuccess = secondRewardSuccess;
    }

    public int getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(int rewardTime) {
        this.rewardTime = rewardTime;
    }

    public int getUnlockType() {
        return unlockType;
    }

    public void setUnlockType(int unlockType) {
        this.unlockType = unlockType;
    }

    public int getExtraClickRewardTime() {
        return extraClickRewardTime;
    }

    public void setExtraClickRewardTime(int extraClickRewardTime) {
        this.extraClickRewardTime = extraClickRewardTime;
    }

    public boolean isExtraClickRewardSuccess() {
        return isExtraClickRewardSuccess;
    }

    public void setExtraClickRewardSuccess(boolean clickRewardSuccess) {
        isExtraClickRewardSuccess = clickRewardSuccess;
    }

    public int getRewardTypeParam() {
        return rewardTypeParam;
    }

    public void setRewardTypeParam(int rewardTypeParam) {
        this.rewardTypeParam = rewardTypeParam;
    }

    public String getRewardFailToast() {
        return rewardFailToast;
    }

    public void setRewardFailToast(String rewardFailToast) {
        this.rewardFailToast = rewardFailToast;
    }

    public int getRewardInfoType() {
        return rewardInfoType;
    }

    public void setRewardInfoType(int rewardDurationType) {
        this.rewardInfoType = rewardDurationType;
    }

    public boolean canReward() {
        return canReward;
    }

    public void setCanReward(boolean canReward) {
        this.canReward = canReward;
    }

    public boolean isShakeEnable() {
        return isShakeEnable;
    }

    public void setShakeEnable(boolean shakeEnable) {
        isShakeEnable = shakeEnable;
    }

    public boolean isNeedRequest() {
        return isNeedRequest;
    }

    public void setNeedRequest(boolean needRequest) {
        isNeedRequest = needRequest;
    }

    public boolean canPreloadInspireAd() {
        return canPreloadInspireAd;
    }

    public void setCanPreloadInspireAd(boolean canPreloadInspireAd) {
        this.canPreloadInspireAd = canPreloadInspireAd;
    }
}
