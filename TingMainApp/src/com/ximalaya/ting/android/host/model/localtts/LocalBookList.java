package com.ximalaya.ting.android.host.model.localtts;

import android.os.AsyncTask;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.xmlymmkv.util.MMKVUtil;

import org.jetbrains.annotations.Nullable;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class LocalBookList {

    public static final String MMKV_KEY_LOCAL_LIST = "xima_debug_data_local_book_list";
    public static final String MMKV_KEY_LOCAL_PATH_URL_MAP = "xima_debug_data_local_path_url_map";

    private static final Gson sGson = new Gson();
    private static CopyOnWriteArrayList<LocalBookInfo> sLocalBookList;

    private static void initLocalList() {
        if (sLocalBookList == null) {
            String json = MMKVUtil.getInstance().getString(MMKV_KEY_LOCAL_LIST);
            try {
                sLocalBookList = sGson.fromJson(json,
                        new TypeToken<List<LocalBookInfo>>() {
                        }.getType());
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (sLocalBookList == null) {
                sLocalBookList = new CopyOnWriteArrayList<>();
            }
        }
    }

    public static List<LocalBookInfo> getLocalData() {
        initLocalList();

        if (sLocalBookList != null && !sLocalBookList.isEmpty()) {
            Collections.sort(sLocalBookList, (o1, o2) -> {
                if (o1 == null || o2 == null) {
                    return 0;
                }

                if (o1.lastVisit > o2.lastVisit) {
                    return -1;
                } else if (o1.lastVisit == o2.lastVisit) {
                    return 0;
                }
                return 1;
            });
        }

        Log.d("local_book", "getTestData, size: " + sLocalBookList);

        return sLocalBookList;
    }

    public static void saveLocalData(List<LocalBookInfo> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        initLocalList();

        AsyncTask.execute(() -> {
            synchronized (LocalBookList.class) {
                CopyOnWriteArrayList<LocalBookInfo> mergeList = new CopyOnWriteArrayList<>();
                if (sLocalBookList != null && !sLocalBookList.isEmpty()) {
                    // 合并不同的
                    for (LocalBookInfo bookInfo : sLocalBookList) {
                        // 返回的列表不包含 并且当前合并的数据不包含  则添加
                        if (!list.contains(bookInfo) && !mergeList.contains(bookInfo)) {
                            mergeList.add(bookInfo);
                        }
                    }

                    mergeList.addAll(list);
                } else {
                    mergeList.addAll(list);
                }

                sLocalBookList = mergeList;
                String json = sGson.toJson(sLocalBookList);
                MMKVUtil.getInstance().saveString(MMKV_KEY_LOCAL_LIST, json);
            }
        });
    }

    public static void updateLocalDataAndSave(LocalBookInfo bookInfo) {
        initLocalList();

        AsyncTask.execute(() -> {
            synchronized (LocalBookList.class) {
                if (sLocalBookList == null) {
                    return;
                }

                // 重写了equal 用md5做的判断  先移除在添加  替换整体的书籍对象数据
                if (sLocalBookList.contains(bookInfo)) {
                    sLocalBookList.remove(bookInfo);
                    sLocalBookList.add(bookInfo);
                } else {
                    sLocalBookList.add(bookInfo);
                }

                String json = null;
                if (sLocalBookList != null) {
                    json = sGson.toJson(sLocalBookList);
                }
                MMKVUtil.getInstance().saveString(MMKV_KEY_LOCAL_LIST, json);
            }
        });
    }

    @Nullable
    public static LocalBookInfo getLocalBookByBookId(long bookId) {
        initLocalList();

        if (sLocalBookList != null) {

            if (ConstantsOpenSdk.isDebug) {
                Log.d("local_book", "getLocalPathByBookId: " + bookId + ", size: " + sLocalBookList.size());
            }

            for (LocalBookInfo bookInfo : sLocalBookList) {
                if (bookInfo.bookId == bookId) {
                    return bookInfo;
                }
            }
        }
        return null;
    }

    public static void clearLocalData() {
        if (sLocalBookList != null) {
            sLocalBookList.clear();
        }
        MMKVUtil.getInstance().saveString(MMKV_KEY_LOCAL_LIST, "");
    }

    public static void delLocalDataAndSave(LocalBookInfo bookInfo) {
        initLocalList();

        // 重写了equal 用md5做的判断
        if (sLocalBookList == null || !sLocalBookList.contains(bookInfo)) {
            return;
        }

        AsyncTask.execute(() -> {
            synchronized (LocalBookList.class) {
                if (sLocalBookList == null) {
                    return;
                }
                sLocalBookList.remove(bookInfo);

                String json = null;
                if (sLocalBookList != null) {
                    json = sGson.toJson(sLocalBookList);
                }
                MMKVUtil.getInstance().saveString(MMKV_KEY_LOCAL_LIST, json);
            }
        });
    }
}
