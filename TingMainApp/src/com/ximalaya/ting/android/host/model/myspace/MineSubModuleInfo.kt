package com.ximalaya.ting.android.host.model.myspace

/**
 * Created by dekai.liu on 6/24/21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18392566603
 */
data class MineSubModuleInfo(
        val id: Long = 0,
        val name: String = "",
        val entranceIds: List<Long> = mutableListOf()
)

data class MineEntranceItemStyleInfo(
        val labelIcon: String = "", // 标签图
        val bottomBackground: String = "" // 底部背景图
)