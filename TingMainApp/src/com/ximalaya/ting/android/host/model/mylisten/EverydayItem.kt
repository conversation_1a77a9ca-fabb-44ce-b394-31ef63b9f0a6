package com.ximalaya.ting.android.host.model.mylisten

import com.ximalaya.ting.android.host.adapter.base.entity.BaseExpandNode
import com.ximalaya.ting.android.host.adapter.base.entity.BaseNode
import com.ximalaya.ting.android.host.model.album.AlbumM
import com.ximalaya.ting.android.host.model.track.TrackM

/**
 * author : mingming.li
 * email : <EMAIL>
 * date : 2022/10/19
 */
data class EverydayItem(
    val type: Int = TYPE_TRACK,
    var trackM: TrackM? = null,
    var albumM: AlbumM? = null,
    override val childNode: MutableList<BaseNode>? = null
) : BaseExpandNode() {

    init {
        isExpanded = false
    }

    companion object {
        const val TYPE_TRACK = 777
        const val TYPE_ALBUM = 888
    }
}