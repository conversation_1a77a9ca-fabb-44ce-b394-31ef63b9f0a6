package com.ximalaya.ting.android.host.model.album;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by ervin.li on 2019-10-21.
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
public class AppointedDay implements Parcelable {
    public static final int FINISHED_TODAY = 0;
    public static final int FINISHED_NOT_TODAY = 1;
    public static final int UNFINISHED = 3;
    public static final int NO_CONTENT = 4;

    public static final int NOT_INIT_DAY = -2;
    public static final int NOT_TRAINING_DAY = -1;
    public static final int TRAINING_FREE_LISTEN_DAY = 0;
    public static final int TRAINING_OPEN_DAY = 1;
    public static final int TRAINING_END_DAY = 2;
    public static final int TRAINING_DAY = 3;

    public static final int STATUS_CHECK_TODAY = 0;
    public static final int STATUS_CHECK_LATER = 1;
    public static final int STATUS_COURSE_UNFINISH = 3;
    public static final int STATUS_NO_CONTENT = 4;

    public long day;
    public String dayStr;
    //试听日通过用0判断
    public int order = -1;
    //0 打卡，1 补卡， 3 学习未完成， 4 没有内容
    public int status = -1;
    // 本地定义 -1 不是训练营日，0试听日，1训练营开营日，2训练营结束日
    public int trainingDayType = NOT_INIT_DAY;

    protected AppointedDay(Parcel in) {
        day = in.readLong();
        dayStr = in.readString();
        order = in.readInt();
        status = in.readInt();
    }

    public static final Creator<AppointedDay> CREATOR = new Creator<AppointedDay>() {
        @Override
        public AppointedDay createFromParcel(Parcel in) {
            return new AppointedDay(in);
        }

        @Override
        public AppointedDay[] newArray(int size) {
            return new AppointedDay[size];
        }
    };

    public static AppointedDay parse(String json) {
        AppointedDay appointedDay = null;
        if (!TextUtils.isEmpty(json)) {
            try {
                JSONObject jsonObject = new JSONObject(json);
                appointedDay = new AppointedDay();
                appointedDay.order = jsonObject.optInt("order", -1);
                appointedDay.status = jsonObject.optInt("status", -1);
                appointedDay.day = jsonObject.optLong("day");
                appointedDay.dayStr = jsonObject.optString("dayStr");
            } catch (JSONException e) {
                e.printStackTrace();
            }

        }
        return appointedDay;
    }


    public AppointedDay() {
    }


    @Override
    public boolean equals(Object obj) {
        if (obj instanceof AppointedDay) {
            AppointedDay that = (AppointedDay) obj;
            return TextUtils.equals(that.dayStr, this.dayStr) && that.day == this.day && this.order == that.order && this.status == that.status;
        }
        return super.equals(obj);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(day);
        dest.writeString(dayStr);
        dest.writeInt(order);
        dest.writeInt(status);
    }

    public boolean isFinished() {
        return status == FINISHED_TODAY || status == FINISHED_NOT_TODAY;
    }

    public boolean isFreeListenDay() {
        return trainingDayType == TRAINING_FREE_LISTEN_DAY;
    }

    public boolean isTrainingOpenDay() {
        return trainingDayType == TRAINING_OPEN_DAY;
    }

    public boolean isTrainingEndDay() {
        return trainingDayType == TRAINING_END_DAY;
    }


    public boolean isTrainingDay() {
        return trainingDayType <= TRAINING_DAY && trainingDayType >= TRAINING_FREE_LISTEN_DAY;
    }

}
