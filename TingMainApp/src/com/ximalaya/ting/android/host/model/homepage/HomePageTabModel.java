package com.ximalaya.ting.android.host.model.homepage;

import android.text.TextUtils;

import com.ximalaya.ting.android.host.model.category.CategoryMetadata;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by WolfXu on 2018/8/17.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13670268092
 */
@SuppressWarnings("unused")
public class HomePageTabModel {

    public static final String RECOMMEND_TYPE_FIXED = "fixed"; // 固定
    public static final String RECOMMEND_TYPE_NORMAL = "normal"; // 普通

    public static final String CORNER_MARK_HOT = "hot"; // 热门标签
    public static final String CORNER_MARK_NEW = "new"; // 新品标签

    public static final String ITEM_TYPE_RECOMMEND = "recommend";  // 推荐
    public static final String ITEM_TYPE_FOLLOW = "concern";  // 关注
    public static final String ITEM_TYPE_CATEGORY = "category"; // 分类
    public static final String ITEM_TYPE_ANCHOR = "anchor"; // 主播
    public static final String ITEM_TYPE_AUDIO_STREAM = "audio_stream";  // 声音流
    public static final String ITEM_TYPE_VIP = "vip";  // VIP
    public static final String ITEM_TYPE_SINGLE_CATEGORY = "single_category"; // 具体分类
    public static final String ITEM_TYPE_TEMPLATE_CATEGORY = "template_category"; // 频道页模板化
    public static final String ITEM_TYPE_LAMIA = "lamia"; // 直播
    public static final String ITEM_TYPE_LIVE = "live";  // 广播
    public static final String ITEM_TYPE_H5 = "h5";
    public static final String ITEM_TYPE_ACTIVITY = "activity";  // 活动
    public static final String ITEM_TYPE_MICRO_LESSON = "micro_lesson"; // 微课
    public static final String ITEM_TYPE_REACT_NATIVE = "react_native"; // rn
    public static final String ITEM_TYPE_WOTING_NEW = "my_listen"; // 新版我听我听,abtest增加我听类型，6.5.66之前返回的文案是"我听"，之后返回的是"订阅"
    public static final String ITEM_TYPE_LOCAL_LISTEN = "local_listen"; // 本地听
    public static final String ITEM_TYPE_VIDEO_TAB = "video"; // 视频

    // 所有的类型都要加到这里来。
    // 本来应该改成enum，这样不用加两个地方。但是已经有很多地方直接调用了上面定义的常量，改起来麻烦。
    private static final List<String> ITEM_TYPES = new ArrayList<String>(){{
        add(ITEM_TYPE_RECOMMEND);
        add(ITEM_TYPE_CATEGORY);
        add(ITEM_TYPE_FOLLOW);
        add(ITEM_TYPE_ANCHOR);
        add(ITEM_TYPE_AUDIO_STREAM);
        add(ITEM_TYPE_VIP);
        add(ITEM_TYPE_SINGLE_CATEGORY);
        add(ITEM_TYPE_LAMIA);
        add(ITEM_TYPE_LIVE);
        add(ITEM_TYPE_H5);
        add(ITEM_TYPE_VIDEO_TAB);
        add(ITEM_TYPE_ACTIVITY);
        add(ITEM_TYPE_MICRO_LESSON);
        add(ITEM_TYPE_REACT_NATIVE);
        add(ITEM_TYPE_WOTING_NEW);
        add(ITEM_TYPE_LOCAL_LISTEN);
        add(ITEM_TYPE_TEMPLATE_CATEGORY);
    }};

    // 通用
    private String itemType;
    private String title;
    private String id; // tab唯一标识

    // 主要在首页tab上使用
    private String unactiveCoverPath;
    private String activeCoverPath;
    private HomePageTabTheme tabTheme; // 包含背景色、前景主题色
    private SearchBoxRightContent searchBoxRightContent; // 包含搜索框右边的icon类型、文案、跳转用的iting链接
    private CustomTheme customTheme; // tab自定义颜色

    // 这些字段主要在自定义tab页显示tab时用
    private String coverPath;     // 在自定义tab页显示的图标
    private String recommendType; // fixed:固定 recommend:推荐 normal:普 通
    private String cornerMark; // 角标

    // 声音流用
    private long audioStreamId;

    // 主要是是h5用
    private String url;

    //是否默认选中
    private boolean defaultSelected;

    // 单个分类用，可能还有其他的页面用，不能确定。
    private int categoryId;
    private CategoryMetadata metadata;  // 搜索框右侧显示的元数据

    // 本地听用
    private String cityCode;

    private String xmRequestId;
    private boolean isLocalCache;

    // 本地加的字段。
    private transient HomePageTabGroup mBelongToTabGroup; // 所属的分组。不是服务端返回的，客户端设置的字段。
    private transient boolean mHasAddedToMyTabs; // 是否已添加到用户首页的tab中
    private transient boolean mHideInModify; // 编辑模式下是否隐藏

    public boolean isDefaultSelected() {
        return defaultSelected;
    }

    public void setDefaultSelected(boolean defaultSelected) {
        this.defaultSelected = defaultSelected;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUnactiveCoverPath() {
        return unactiveCoverPath;
    }

    public void setUnactiveCoverPath(String unactiveCoverPath) {
        this.unactiveCoverPath = unactiveCoverPath;
    }

    public String getActiveCoverPath() {
        return activeCoverPath;
    }

    public void setActiveCoverPath(String activeCoverPath) {
        this.activeCoverPath = activeCoverPath;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    public String getRecommendType() {
        return recommendType;
    }

    public void setRecommendType(String recommendType) {
        this.recommendType = recommendType;
    }

    public String getCornerMark() {
        return cornerMark;
    }

    public void setCornerMark(String cornerMark) {
        this.cornerMark = cornerMark;
    }

    public long getAudioStreamId() {
        return audioStreamId;
    }

    public void setAudioStreamId(long audioStreamId) {
        this.audioStreamId = audioStreamId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public HomePageTabGroup getBelongToTabGroup() {
        return mBelongToTabGroup;
    }

    public void setBelongToTabGroup(HomePageTabGroup belongToTabGroup) {
        mBelongToTabGroup = belongToTabGroup;
    }

    public boolean hasAddedToMyTabs() {
        return mHasAddedToMyTabs;
    }

    public void setHasAddedToMyTabs(boolean hasAddedToMyTabs) {
        mHasAddedToMyTabs = hasAddedToMyTabs;
    }

    public HomePageTabTheme getTabTheme() {
        return tabTheme;
    }

    public void setTabTheme(HomePageTabTheme tabTheme) {
        this.tabTheme = tabTheme;
    }

    public SearchBoxRightContent getSearchBoxRightContent() {
        return searchBoxRightContent;
    }

    public void setSearchBoxRightContent(SearchBoxRightContent searchBoxRightContent) {
        this.searchBoxRightContent = searchBoxRightContent;
    }

    public boolean isHideInModify() {
        return mHideInModify;
    }

    public void setHideInModify(boolean hideInModify) {
        mHideInModify = hideInModify;
    }

    public CategoryMetadata getMetadata() {
        return metadata;
    }

    public void setMetadata(CategoryMetadata metadata) {
        this.metadata = metadata;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public CustomTheme getCustomTheme() {
        return customTheme;
    }

    public void setCustomTheme(CustomTheme customTheme) {
        this.customTheme = customTheme;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof HomePageTabModel) {
            //noinspection SimplifiableIfStatement
            if (this == obj) {
                return true;
            }
            return !TextUtils.isEmpty(this.id) && this.id.equals(((HomePageTabModel) obj).id);
        }
        return false;
    }

    public boolean isRecognizableItem() {
        return !TextUtils.isEmpty(getItemType()) && ITEM_TYPES.contains(getItemType());
    }

    public String getXmRequestId() {
        return xmRequestId;
    }

    public void setXmRequestId(String xmRequestId) {
        this.xmRequestId = xmRequestId;
    }

    public boolean isLocalCache() {
        return isLocalCache;
    }

    public void setLocalCache(boolean localCache) {
        isLocalCache = localCache;
    }

    public String getCurrPageForTrace() {
        String type = "other";
        switch (itemType) {
            case ITEM_TYPE_RECOMMEND:
                type = "newHomePage";
                break;
            case ITEM_TYPE_VIP:
                type = "vip";
                break;
            case ITEM_TYPE_SINGLE_CATEGORY:
                type = "categoryRecommend";
                break;
        }
        return type;
    }
}
