package com.ximalaya.ting.android.host.util.constant;

import android.os.Build;

import com.ximalaya.ting.android.loginservice.base.ILogin;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;

import java.util.HashSet;


/**
 * 应用普通全局常量
 */

/**
 * SharedPreference 有关常量移动到PreferenceConstantsInMain
 *
 * <AUTHOR>
 */
public class AppConstants extends BaseConstants {
    public static final String LIVE_PROGRAM_DEFAULT_TEXT = "暂无节目单";
    public static final String GOOLGE_CHANNEL = "and-GP";


    // 后台正在播放
    public static final String PLAYINGNOW = "PLAYINGNOW";
    public static final boolean isPageCanSlide = true;
    public static final String API_VERSION = "api1";
    public static final String DEVICE_TOKEN = "DEVICE_TOKEN";
    public static final String SUGGEST = "suggest";
    public static final String FRONT_VERSION = "front/v1";
    public static final String HOT_SEARCH_WORD = "m/hot_search_keys";
    public static final String RADIO_TOP_LIST = "getTopRadiosList";
    public static final String RADIO_LIST_BYTYPE = "getRadiosListByType";
    public static final String RADIO_PROVINCE_LIST = "getProvinceList";
    public static final String RADIO_HOME_PAGE_LIST = "getHomePageRadiosList";
    public static final int DOWNLOAD_ALBUM_SOUNDLIST_REVERSE_ORDER = -1;
    public static final int DOWNLOAD_ALBUM_SOUNDLIST_NORMAL_ORDER = 1;
    //车载锁屏
    public static final String LOCK_SCREAN_KEY = "lock_screan_key";
    public static final int LOCK_SCREAN = 1;
    public static final int UNLOCK_SCREAN = 2;
    // app更新
    public static final int UPGRADE_ASK = 0;
    public static final int UPGRADE_FORCE = 1;
    public static final int UPGRADE_NORMAL = 0x0a3;
    public static final int UPGRADE_PERCENT = 2;
    public static final int DOWN_OK = 3;
    public static final int DOWN_ERROR = 4;
    public static final int DOWN_PAUSE = 7;
    public static final int REQUEST_TIME_OUT = 5;
    public static final int UPGRADE_NO_ASK = 6;
    /******* 以下广告统计的"position_name"对应广告请求URL中的参数"name" ***/
    public static final String AD_POSITION_NAME_FIND_BANNER = "find_banner";//推荐页
    public static final String AD_POSITION_NAME_FIND_NATIVE = "find_native";//推荐页native
    public static final String AD_POSITION_NAME_CATA_BANNER = "cata_banner";//分类推荐页底部广告
    public static final String AD_POSITION_NAME_CATA_LIST = "cate_list";//分类详情页-插入的广告
    public static final String AD_POSITION_NAME_CATA_INDEX_BANNER = "cata_index_banner";//分类页
    public static final String AD_POSITION_NAME_FEED_BANNER = "banner";//订阅听-订阅页
    public static final String AD_POSITION_NAME_WALLET_BANNER = "wallet_banner";//充值页面banner
    public static final String AD_POSITION_NAME_FEED_COLLECT = "feed_collect";//订阅听-推荐页
    public static final String AD_POSITION_NAME_PLAY_COMMENT_TOP = "comm_top";//播放页_banner
    public static final String AD_POSITION_NAME_PLAY_NATIVE = "native_play";//播放页——native
    // public static final String AD_POSITION_NAME_FEED = "feed";
    public static final String AD_POSITION_NAME_FEED_FOLLOW = "feed_follow";//定制听-订阅页-插入的广告
    public static final String AD_POSITION_NAME_FEED_COLLECT_LIST = "feed_collect_list";
    //定制听-推荐-插入的广告
    public static final String AD_POSITION_NAME_CITY_COLUMN = "local_banner";//本地听页面广告(貌似没用)
    public static final String AD_POSITION_NAME_BROADCAST_NATIVE = "broadcast_native";// 广播native广告
    //local_list_native
    public static final String AD_POSITION_NAME_CITY_NATIVE = "local_list_native";//本地听页面广告
    public static final String AD_POSITION_NAME_ALBUM_NOTICE = "album_notice";//专辑页通知原生广告
    public static final String AD_POSITION_NAME_PURCHASE_MIDDLE_BOTTOM = "purchase_middle_bottom";//付费专辑页横幅和挂件广告
    public static final String AD_POSITION_NAME_POPUP = "popup"; //首页弹窗广告
    public static final String AD_POSITION_NAME_FIREWORK_POPUP = "popup_new"; //首页弹窗广告
    public static final String AD_POSITION_NAME_HOME_PAGE_SNACK_BAR = "newHomePage" ;// 全场景到期提示snackbar入口
    public static final String AD_POSITION_NAME_QUIT = "quit"; // 退出广告
    public static final String AD_POSITION_NAME_SEARCH = "search_banner"; // 搜索页广告
    public static final String AD_POSITION_NAME_SEARCH_TOP_BRAND = "search_brand_zone"; // 搜索页品牌广告
    public static final String AD_POSITION_NAME_PAYPAGE_POP = "paypage_popup"; // 付费精品
    public static final String AD_POSITION_NAME_HOME_TITLEBAR = "home_titlebar"; //   titlebar广告
    public static final String AD_POSITION_NAME_HOME_MIDDLE = "home_middle"; //     中部广告
    public static final String AD_POSITION_NAME_HOME_BOTTOM = "home_bottom";        //   底部广告
    public static final String AD_POSITION_NAME_HOME_DROP_DOWN = "home_drop_down";        // 首页下拉广告
    public static final String AD_POSITION_NAME_PLAY_SKIN = "play_skin";    // 播放页皮肤广告
    public static final String AD_POSITION_NAME_PLAY_READ = "play_read";    // 播放页录音广告
    public static final String AD_POSITION_NAME_CHAT_ROOM = "live";    // 直播广告
    public static final String AD_POSITION_NAME_FOCUS = "focus";    // 焦点图
    public static final String AD_POSITION_NAME_PLAY_CENTER = "play_large";    // 播放页中部广告
    public static final String AD_POSITION_NAME_BROADCASTER_BANNER = "broadcaster_banner";    // 主播页banner广告
    public static final String AD_POSITION_NAME_SHARE_FLOAT = "share_float";    // 分享广告
    public static final String AD_POSITION_NAME_MORE_OPERATE = "operate_float";    // 播放页更多 广告
    public static final String AD_POSITION_NAME_PURCHASE_MIDDLE = "purchase_middle";    // 付费专辑 中部广告
    public static final String AD_POSITION_NAME_PURCHASE_BOTTOM = "purchase_bottom";    // 付费专辑 底部广告
    public static final String AD_POSITION_NAME_XIAOYA_FLOAT = "xiaoya_float";    // 小雅浮层
    public static final String AD_POSITION_NAME_FIND_FLOAT = "find_float";    // 发现页浮层
    public static final String AD_POSITION_NAME_PLAY_YELLOW_BAR = "play_yellow_bar";    // 小黄条
    public static final String AD_POSITION_NAME_BRAND_FEATURE = "brand_feature";// 汽车广告
    public static final String AD_POSITION_NAME_MY_COOPERATION = "my_cooperation";// 关于页广告
    public static final String AD_POSITION_NAME_BROCASTER_COOPERATION = "brocaster_cooperation";
    // 主播页投放联系入口
    public static final String AD_POSITION_NAME_WAISTBAND = "waistband";// 会员腰封广告
    public static final String AD_POSITION_NAME_LIVE_BANNER = "live_banner";// 直播腰封广告
    public static final String AD_POSITION_NAME_GIANT_SCREEN = "giant_screen";// 巨幕广告
    public static final String AD_POSITION_NAME_PLAY_ICON = "icon";// 播放页icon广告
    public static final String AD_POSITION_NAME_SEARCH_EGGS = "search_eggs";// 搜索彩蛋
    public static final String AD_POSITION_NAME_COLUMN_SPONSORSHIP = "column_sponsorship";// 频道冠名广告
    public static final String AD_POSITION_NAME_PLAY_COMMENT = "comment";   // 播放页评论广告
    public static final String AD_POSITION_NAME_TITLEBAR_MIDDLE_BOTTOM = "titlebar_middle_bottom";  // 首页悬浮touch 广告
    public static final String AD_POSITION_NAME_FORWARD_VIDEO = "forward_video";  // 前插视频广告
    public static final String AD_POSITION_NAME_FLOW_SEARCH_AD = "search_entry_page_information_flow";  // 搜索信息流bannar广告
    public static final String AD_POSITION_NAME_INCENTIVE_BUY_POP = "incentive_buy_pop";  // 解锁视频弹窗广告
    public static final String AD_POSITION_NAME_VIP_FREE_REWARD_VIDEO = "incentive"; // 全站畅听激励视频广告
    public static final String AD_POSITION_NAME_ANCHOR_CALL = "anchor_call";  // 主播打call
    public static final String AD_POSITION_NAME_INCENTIVE_PLAY_BOMB = "incentive_play_bomb";  // 提示购买
    public static final String AD_POSITION_NAME_INCENTIVE_PLAY_BOMB_BAR = "incentive_play_bomb_bar";  // 提示购买
    public static final String AD_POSITION_NAME_PLAY_BUTTON = "incentive_play_button";  // 播放页封面区的按钮
    public static final String AD_POSITION_NAME_INCENTIVE_SALELOCK = "incentive_salelock";  // 整张专辑点锁请求sdk
    public static final String AD_POSITION_NAME_INCENTIVE = "incentive";    // 付费解锁
    public static final String AD_POSITION_NAME_INCENTIVE_DURATION = "incentive_duration" ;// 畅听-时长解锁
    public static final String AD_POSITION_NAME_INCENTIVE_SINGLE = "incentive_single" ;// 单集解锁
    public static final String AD_POSITION_NAME_INCENTIVE_SKITS = "incentive_playlet" ;// 短剧解锁

    public static final String AD_POSITION_NAME_INCENTIVE_MIX = "incentive_mix" ;// 激励视频混投
    public static final String AD_POSITION_NAME_PLAY_IMMERSIVE_SKIN = "play_immersive_skin";    // 播放页皮肤广告

    public static final String AD_POSITION_NAME_MAIN_SEARCH_BANNER = "main_search_banner";    // 搜索页，推荐 banner 广告

    public static final String AD_POSITION_NAME_WELFARE_LAYER = "welfare_layer";    // 用户营销福利， 砸金蛋页面 浮层
    public static final String AD_POSITION_NAME_WELFARE_DIALOG = "welfare_dialog";    // 用户营销福利， 砸金蛋结果页 弹窗
    public static final String AD_POSITION_NAME_WELFARE_INCENTIVE_VIDEO = "welfare_incentive_video";    // 用户营销福利， 砸金蛋 观看激励视频
    public static final String AD_POSITION_NAME_REMOVE_AD_POSITION_NAME = "remove_ad";    // 删除广告

    public static final String AD_POSITION_NAME_PAOPAO_ACTIVITY_ENTRY = "card_pop_up_layer"; //泡泡条位置的，卡片弹层广告活动入口
    public static final String AD_POSITION_NAME_CHASE_RECOMMEND = "chase_recommend"; // 声音流+更多广告位

    public static final String AD_POSITION_NAME_GAME_CENTER_VIDEO = "game_center_video"; // 游戏中心H5加载激励视频
    public static final String AD_POSITION_NAME_LIVE_LITTLE_BANNER = "studio_pendant"; // 直播轮播小挂件里的广告
    public static final String AD_POSITION_NAME_NATIVE_CONTENT_AD = "native_content"; // 内容推广落地页
    public static final String AD_POSITION_NAME_RECOMMEND_PROMOTION = "recommend_promotion"; // 首页大促广告

    public static final String AD_POSITION_NAME_STAGGERED_HOME_AD = "staggered_home_ad"; // 双排信息流广告(本地定义的)
    public static final String AD_POSITION_NAME_WINDOW_SHOP = "window_shop"; // 橱窗追投广告

    public static final String AD_POSITION_NAME_SOUND_PATCH_PLAY_BANNER = "play_banner"; //  //下挂广告

    public static final String AD_POSITION_NAME_SOUND_PATCH_BANNER = "sound_patch_banner"; // 贴片-下挂
    public static final String AD_POSITION_NAME_MINE_AD = "my_page_feed"; // 我的页广告
    public static final String AD_POSITION_NAME_SEARCH_KEY_WORDS_FIRST = "search_keywords_first"; // 搜索结果页广告，出现在精选最佳匹配位置
    public static final String AD_POSITION_NAME_POINTS_CENTER_TASK_BANNER = "points_center_task_banner"; // 积分中心拉活广告
    public static final String AD_POSITION_NAME_SEARCH_KEYWORDS_RESULT = "search_keywords_result"; // 积分中心拉活广告
    public static final String AD_POSITION_NAME_ALBUM_COMPONENT_1 = "album_component_1"; // 小说tab推荐1屏广告
    public static final String AD_POSITION_NAME_ALBUM_COMPONENT_2 = "album_component_2"; // 小说tab推荐2屏广告
    public static final String AD_POSITION_NAME_AIGC_NATIVE = "aigc_native"; // aigc中插声音广告

    public static final String AD_SEARCH_ASSOCIATE_WORD = "search_associate_word"; // 搜索联想词广告
    public static final String AD_POSITION_NAME_RECOMMEND_LISTEN_LIST = "home_page_listening_list"; // 新首页社会化听单广告，单条
    public static final String AD_POSITION_NAME_RECOMMEND_WHOLE_LISTEN_LIST = "home_page_whole_listen_list"; // 新首页社会化听单广告，整个
    public static final String AD_POSITION_NAME_RECOMMEND_RANK_LIST = "ranking_list"; // 新首页榜单广告

    public static final String AD_POSITION_NAME_SEARCH_BOTTOM_WORDS = "search_bottom_words"; // 搜索框底词
    public static final String AD_POSITION_NAME_HOT_SEARCH_LIST = "hot_search_list"; //搜索页热搜榜广告词
    public static final String AD_POSITION_NAME_POINT_CASH = "inspire_video_listen_book"; // 新版听书任务积分现金奖励
    public static final String AD_POSITION_NAME_INCENTIVE_SPECIES = "incentive_species"; // 积分中心看视频任务
    public static final String AD_POSITION_NAME_IINTEGRAL_CENTER_INSPIRE_VIDEO = "integral_center_inspire_video"; // 积分中心看视频任务
    public static final String AD_POSITION_NAME_WELFARE_CASH_RECEIVE = "welfare_cash_receive"; // 福利页领现金
    public static final String AD_POSITION_NAME_WELFARE_CASH_WITHDRAWAL = "welfare_cash_withdrawal"; // 福利页提取现金
    public static final String AD_POSITION_NAME_INCENTIVE_WELFARE = "incentive_welfare"; // 福利页领金币
    public static final String AD_POSITION_NAME_DOWNLOAD_REWARD_VIDEO = "track_download_incentive"; // 下载激励视频
    public static final String AD_POSITION_NAME_PLAY_ISOLATE_LARGE = "play_isolate_large"; // 播放页独立大图
    public static final String AD_POSITION_NAME_WELFARE_PLAY_LET_TASK = "welfare_playlet_task"; // 福利页短剧任务
    public static final String AD_POSITION_NAME_WELFARE_MALL_TASK = "welfare_mall_task"; // 福利页穿山甲商城任务

    /***
     * !!!!! 这里面的LogType如果有上报到第三方的需求需要在 AdManager#isTingShowRecordType(AdReportModel) 配置下
     */
    public static final String AD_LOG_TYPE_SITE_SHOW = "tingShow";
    public static final String AD_LOG_TYPE_SITE_CLICK = "tingClick";
    public static final String AD_LOG_TYPE_RTBENTRY_CLICK = "rtbEntryClick";    // 关于页广告合作点击
    public static final String AD_LOG_TYPE_RTBENTRY_SHOW = "rtbEntryShow";      // 关于页广告合作展示
    public static final String AD_LOG_TYPE_SOUND_SHOW = "soundShow";            // 声音展示
    public static final String AD_LOG_TYPE_SOUND_COMPLETE = "soundComplete";    // 声音播放完成
    public static final String AD_LOG_TYPE_SOUND_CLICK = "soundClick";          // 声音点击
    public static final String AD_LOG_TYPE_SOUND_INTERACTI_CLICK = "interact-click";    // 手势打开
    public static final String AD_LOG_TYPE_SOUND_TINGCLOSE = "tingClose";
    public static final String AD_LOG_TYPE_LINK_CLOSE = "linkClose";
    public static final String AD_LOG_TYPE_WELFAREACTION = "welfareAction";
    public static final String AD_LOG_TYPE_VIDEO_COMPLETE = "videoComplete";
    public static final String AD_LOG_TYPE_BOOTUP_SHOW = "bootUpShow";  // 开屏长图广告
    public static final String AD_LOG_TYPE_VIP_COPYWRITING_CLOSE = "vipCopywritingClose";  // 会员文案关闭按钮点击上报
    public static final String AD_LOG_TYPE_LOADING_UNITED_GIANT = "loading_united_giant";  // 联合霸屏
    public static final String AD_LOG_TYPE_SHOW_TIME = "showTime";  // 贴片广告展示时长
    public static final String AD_LOG_TYPE_REWARD_CLOSE = "rewardClose"; // 激励视频关闭上报
    public static final String AD_LOG_TYPE_REWARD_VERIFY = "rewardVerify"; // 激励视频任务完成上报
    public static final String AD_LOG_TYPE_REQUEST_REWARD = "requestReward"; // 激励视频发放奖励上报
    public static final String AD_LOG_TYPE_SOUND_START = "soundStart";  // 客户端展示的第一时间
    public static final String AD_LOG_TYPE_ANSWER_SHOW = "answerShow";  // 反馈图展示
    public static final String AD_LOG_TYPE_ANSWER_CLICK = "answerClick";  // 反馈图点击
    public static final String AD_LOG_TYPE_SHOW_OB = "showOb";  // 前插视频展示上报
    public static final String AD_LOG_TYPE_CLICK_OB = "clickOb";  // 前插视频点击上报
    public static final String AD_LOG_TYPE_LANDING_PAGE_LOAD = "landingPageLoad";  // 落地页加载完成后上报
    public static final String AD_LOG_TYPE_LANDING_PAGE_FIRST_PAINT_TIME = "landingPageFirstPaintTime";  // 接收到首屏渲染结束事件时上报
    public static final String AD_LOG_TYPE_LANDING_PAGE_ACTION = "landingPageAction";  // 落地页点击/滑动次数
    public static final String AD_LOG_TYPE_DP_CALL = "dpCall";  // dplink的吊起状态
    public static final String AD_LOG_TYPE_LANDING_PAGE_RES_LOAD = "landingPageResLoad";  // 预加载离线包资源上报
    public static final String AD_LOG_TYPE_BANNER_BANNER_CLIKCK = "bannerClick"; // 贴片-下挂 banner点击
    public static final String AD_LOG_TYPE_BANNER_BANNER_CLOSE = "bannerClose"; // 贴片-下挂banner 关闭
    public static final String AD_LOG_TYPE_BANNER_GIF_CLICK = "gifClick"; // 下挂gif图 点击
    public static final String AD_LOG_TYPE_BANNER_GIF_CLOSE = "gifClose"; // 下挂gif图关闭

    public static final String AD_LOG_TYPE_AUDIO_MORE_AD_BAR = "audioMoreAdBar";  // 引导条曝光上报
    public static final String AD_LOG_TYPE_LIKE = "likeAd"; // 点赞广告
    public static final String AD_LOG_TYPE_PLAY_CONTROL = "playControl" ;// 手动暂停、播放广告
    public static final String AD_LOG_TYPE_CLICK_DIALOG_ACTION = "clickDialogAction" ;// 激励视频中间点击弹窗

    /***
     * !!!!! 这里面的LogType如果有上报到第三方的需求需要在 AdManager#isTingShowRecordType(AdReportModel) 配置下
     */

    // 声音广告
    public static final String AD_POSITION_NAME_LOADING = "loading";
    public static final String AD_POSITION_NAME_FIND_FOCUS = "focus_map";
    public static final String AD_POSITION_NAME_CATEGORY_FOCUS = "focus_map";
    public static final String AD_POSITION_NAME_SOUND_PATCH = "sound_patch";
    public static final String AD_POSITION_NAME_SOUND_PATCH_BROADCAST = "sound_patch_broadcast";
    public static final String AD_POSITION_NAME_CATEGORY_LIST = "android_cata_list";
    public static final String AD_POSITION_NAME_INSERT_SOUND_PATCH = "insert_sound_patch";

    // 儿童哄睡模式
    public static final String CHILD_SLEEP_MODE = "child_sleep_mode";
    // 展示样式
    public static final String AD_SPONSOR_STYLE_SPONSOR = "sponsor";
    public static final String AD_SPONSOR_STYLE_BANNER = "banner";
    public static final String AD_SPONSOR_STYLE_TOUCH = "touch";

    public static final String NOTIFICATION = "NOTIFICATION";
    public static final String NOTIFICATION_XIAOMI_PAY_ACTIVE = "payload";
    //自定义 Action Names
    public final static String ACTION_START_ALARM = "com.ximalaya.ting.android.action.START_ALARM";
    public final static String ACTION_START_ALARM_DOWNLOAD_FORECAST = "com.ximalaya.ting.android.action.START_ALARM_DOWNLOAD_FORECAST";
    public final static String ACTION_ALARM_LATER = "com.ximalaya.ting.android.action.ALARM_LATER";
    public final static String ACTION_RN_COMMON_ALARM = "com.ximalaya.ting.android.action.RN_COMMON_ALARM";
    public final static String ACTION_RN_REPEAT_ALARM = "com.ximalaya.ting.android.action.RN_REPEAT_ALARM";

    //登录相关
    public final static int LOGIN_FLAG_XIMALAYA = ILogin.LOGIN_FLAG_XIMALAYA;
    public final static int LOGIN_FLAG_WEIBO = ILogin.LOGIN_FLAG_WEIBO;
    public final static int LOGIN_FLAG_QQ = ILogin.LOGIN_FLAG_QQ;
    public final static int LOGIN_FLAG_RENN = ILogin.LOGIN_FLAG_RENN;
    public final static int LOGIN_FLAG_WEIXIN = ILogin.LOGIN_FLAG_WEIXIN;
    public final static int LOGIN_FLAG_XIAOMI = ILogin.LOGIN_FLAG_XIAOMI;
    public final static int LOGIN_FLAG_PHONE = ILogin.LOGIN_FLAG_PHONE;
    public final static int LOGIN_FLAG_MEIZU = ILogin.LOGIN_FLAG_MEIZU;
    //绑定相关
    public final static int BIND_FLAG_WEIBO = 12;
    public final static int BIND_FLAG_QQ = 13;
    public final static int BIND_FLAG_QZONE = 15;
    // share setting
    public final static int SETTING_FLAG_WEIBO = 1;
    public final static String CLOUD_HISTORY_SYNCPOINT = "cloud_history_syncpoint";//云历史同步点
    /*******拍照或选择图片相关*********/
    //最大的选择图片数量
    public static final int MAX_IMAGE_SIZE_NEW_POST = 9;
    //相册中图片对象集合
    public static final String EXTRA_IMAGE_LIST = "image_list";
    //当前选择的照片位置
    public static final String EXTRA_CURRENT_IMG_POSITION = "current_img_position";
    //已选择数量
    public static final String EXTRA_SELECTED_SIZE = "selected_size";
    //还能添加的数量
    public static final String EXTRA_CAN_ADD_SIZE = "can_add_size";
    public static final String EXTRA_MAX_SIZE = "max_size";
    public static final String EXTRA_CONFIRM_ACTION_LABEL = "confirm_action_label";
    public static final String EXTRA_NEED_LOCAL_BUCKET_NAME = "need_local_bucket_name";
    public static final String EXTRA_SHOULD_FINISH_IN_ZOOM_FRAGMENT =
            "should_finish_in_zoom_fragment";

    // oppo 推送 url key
    public static final String EXTRA_OPPO_ITING_URL = "iting_url";

    public static final String REQUEST_CODE_KEY_ALBUM_FRAGMENT = "request_code_key_album_fragment";
    public static final int REQUEST_CODE_ALBUM_FRAGMENT_SUBSCRIBE = 0X1001;
    public static final int REQUEST_CODE_ALBUM_FRAGMENT_COMMENT = 0X1002;
    public static final int REQUEST_CODE_ALBUM_FRAGMENT_PAY_ALBUM_STATE = 0X1003;

    public static final String HOMEPAGE_CUSTOM_TABS_SAVE_NAME = "homePageCustomTabs"; // 首页自定义tab
    public static final String HOMEPAGE_CUSTOM_TABS_DEFAULT = "homePageDefaultTabs"; // 默认tab
    // 保存文件名，实际文件名还要取md5

    public static final int XIMI_VIP_NOT_FREE = 0; // 铁粉团不免费
    public static final int XIMI_VIP_FREE = 1; // 铁粉团免费

    // 铁粉团优先，0： 非主播会员抢先听 1： 主播会员抢先听 2： 主播会员抢先听过期
    public static final int XIMI_VIP_NOT_PRIORITY = 0;
    public static final int XIMI_VIP_PRIORITY = 1;
    public static final int XIMI_VIP_PRIORITY_OUT_DATE = 2;

    //云历史相关
    public static final int DEVICE_TYPE_XIAOYA = 6; //智能硬件
    public static final int MAX_INTERVAL = 10 * 1000;

    //record记录
    public static final String REPORT_PLAY_RECORD = "reportPlayRecord";

    public static final String EXTRA_IMAGE_SELECT_SHOW_CAMERA = "image_select_show_camera";
    /**
     * 打开应用
     */
    public final static int PAGE_APP = 9;
    /**
     * 打开私信页（即消息中心）(打开私信 iting://open?msg_type=10) 和 打开给用户发私信页(给用户发私信
     * iting://open?msg_type=10&uid=48646617)
     */
    public final static int PAGE_LETTER = 10;
    /**
     * 打开声音详情页
     */
    public final static int PAGE_SOUND = 11;
    /**
     * 打开他人首页
     */
    public final static int PAGE_HOMEPAGE = 12;
    /**
     * 打开专辑详情页
     */
    public final static int PAGE_ALBUM = 13;
    /**
     * 带url的，打开wap页
     */
    public final static int PAGE_URL = 14;
    /**
     * 打开收到的评论页
     */
    public final static int PAGE_IN_COMMENT = 15;
    /**
     * 打开我的粉丝页
     */
    public final static int PAGE_FANS = 16;
    /**
     * 打开新鲜事页面
     */
    public final static int PAGE_EVENT = 17;
    /**
     * 打开找好友页面
     */
    public final static int PAGE_FRIEND = 18;
    /**
     * 打开活动页面
     */
    public final static int PAGE_ACTIVITY = 19;
    /**
     * 打开录音上传页面
     */
    public final static int PAGE_UPLOAD = 20;
    /**
     * 打开视频合作配音
     */
    public final static int PAGE_VIDEO_DUB = 113;
    /**
     * 打开登录页面
     */
    public final static int PAGE_LOGIN = 21;
    /**
     * 打开绑定手机页面
     */
    public final static int PAGE_BIND_PHONE = 22;
    /**
     * 发现页
     */
    public final static int PAGE_FIND = 23;
    /**
     * 打开客服页面（即拉雅的对话页），已废弃
     */
    public final static int PAGE_CUSTOMER_SERVICE = 26;
    /**
     * 打开圈子页面
     */
    public final static int PAGE_ZONE = 24;
    /**
     * 打开帖子页面，并定位到该条评论
     */
    public final static int PAGE_ZONE_COMMENT = 25;
    /**
     * daka打开应用并播放
     */
    public static final int PAGE_APP_PLAY = 27;
    /**
     * 应用最小化
     */
    public static final int PAGE_MIN = 28;
    /**
     * 打开专辑并播放
     */
    public static final int PAGE_ALBUM_PLAY = 29;
    /**
     * 打开我的页面
     */
    public static final int PAGE_MINE_HOME = 30;

    /********推送对应的操作**********/
    /**
     * 打开主播管理中心
     */
    public static final int PAGE_MINE_CENTER = 31;
    /**
     * 打开搜索结果页面
     */
    public static final int PAGE_SEARCH_RESULT = 32;
    /**
     * 打开反馈页面
     */
    public static final int PAGE_FEEDBACK = 33;
    /**
     * 打开分类页
     */
    public static final int PAGE_CATEGORY = 34;
    /**
     * 打开流量包订购页面
     */
    public static final int PAGE_OPEN_FLOW = 35;
    /**
     * 打开听单
     */
    public static final int PAGE_TING_LIST = 36;
    /**
     * 录音审核通过后分享声音
     */
    public static final int PAGE_SHARE_MY_TRACK = 37;
    /**
     * 打开榜单列表页
     */
    public static final int PAGE_RANK_LIST = 38;
    /**
     * 跳转广播首页
     */
    public static final int PAGE_TO_RADIO = 39;
    /**
     * 跳转到现场直播播放页
     */
    public static final int PAGE_TO_LIVE = 40;
    /**
     * 跳转到我的已购
     */
    public static final int PAGE_TO_BOUGHT = 41;
    /**
     * 跳转到主播会员页面
     */
    public static final int PAGE_TO_MEMBER = 42;
    /**
     * 个人资料编辑页
     */
    public static final int PAGE_TO_EDIT = 43;
    /**
     * 跳到钱包
     */
    public static final int PAGE_TO_WALLET = 44;
    /**
     * 打开充值页并选中充值项
     */
    public static final int PAGE_TO_RECHARGE_ITEM = 45;
    /**
     * 打开用户反馈页交谈页面
     */
    public static final int PAGE_TO_FEEDBACK_CHAT = 46;
    /**
     * 打开用户反馈页工单列表
     */
    public static final int PAGE_TO_FEEDBACK_LIST = 47;
    /**
     * 打开录音界面
     */
    public static final int PAGE_TO_RECORD_FRA = 48;
    /**
     * 打开付费专辑评论列表页面
     */
    public static final int PAGE_TO_PAYALBUM_COMMENT = 49;

    /**
     * 打开电台播放页
     */
    public static final int PAGE_TO_RADIO_PLAY = 50;

    /**
     * 根据直播间 id 跳转到个播直播间
     */
    public static final int PAGE_TO_LIVE_BY_ROOM_ID = 52;
    /**
     * 根据 open_type 跳转到首页的直播Tab页（open_type = 0）还是独立的直播首页（open_type = 1）
     */
    public static final int PAGE_TO_LIVE_HOME_PAGE = 53;

    /**
     * 去大咖读书会
     */
    public static final int PAGE_TO_DAKA = 51;
    /**
     * 跳转猜你喜欢
     */
    public static final int PAGE_TO_GUESS_YOU_LIKE = 54;
    /**
     * 跳转社群的群组列表
     */
    public static final int PAGE_TO_GROUP_LIST = 55;

    /**
     * 跳转到兑换码兑换页面
     */
    public static final int PAGE_TO_REDEEM_CODE = 56;

    /**
     * 跳转付费精品页
     */
    public static final int PAGE_TO_BOUTIQUE = 57;

    /**
     * 群话题
     */
    public static final int PAGE_TO_GROUP_TOPIC = 58;

    /**
     * 群通知
     */
    public static final int PAGE_TO_GROUP_NOTICE = 59;

    /**
     * 听友圈
     */
    public static final int PAGE_TO_TINGYOU = 60;

    /**
     * 群详情页
     */
    public static final int PAGE_TO_GROUP_DETAIL = 61;

    /**
     * 打开我的作品页
     */
    public static final int PAGE_TO_MY_WORKS = 62;

    /**
     * 使用iting再解析一次数据，该类型用于方便服务端向客户端发送推送消息
     * 以免推送后台和前端对接太多字段
     */
    public static final int PAGE_USE_ITING = 63;

    /**
     * 打开本地听首页
     */
    public static final int PAGE_TO_LOCAL = 64;

    /**
     * 打开直播分类页
     */
    public static final int PAGE_TO_LIVE_CATEGORY = 65;

    /**
     * 每日必听
     */
    public static final int PAGE_TO_DAILY = 66;

    /**
     * 喜马头条
     */
    public static final int PAGE_TO_XIMATOUTIAO = 67;

    /*
     * 打开广播列表
     */
    public static final int PAGE_TO_RADIO_LIST = 70;

    /*
     * 打开省市广播列表（带tag栏）
     */
    public static final int PAGE_TO_RADIO_PROVINCE_LIST = 71;

    /**
     * 打开听头条
     */
    public static final int PAGE_TO_LISTEN_HEAD_LINE = 72;

    /**
     * 打开工单反馈详情
     */
    public static final int PAGE_TO_FEEDBACK_DETAIL = 73;

    /**
     * 打开一键听
     */
    public static final int PAGE_TO_ONE_KEY_LISTEN = 74;

    /**
     * 一键听中间页
     */
    public static final int PAGE_TO_ONE_KEY_LISTEN_DETAIL = 75;


    public static final int PAGE_CATEGORY_TAB = 76;

    public static final int PAGE_CATEGORY_DETAIL = 77;

    public static final int PAGE_BOUTIQUE_VIRTUAL = 78;//精品页虚拟分类


    /**
     * 打开帖子详情页
     */
    public static final int PAGE_COMMUNITY_POST = 79;

    /**
     * 视频播放页
     */
    public static final int PAGE_VIDEO_PLAY = 80;

    /**
     * 打开知识直播 微课的详情页面
     */
    public static final int PAGE_WEIKE_COURSE_DETAIL = 81;

    /**
     * 打开我的圈子页面
     */
    public static final int PAGE_MY_COMMUNITIES = 82;

    /**
     * 配音秀推荐流
     */
    public static final int PAGE_DUBBING_FLOW = 83;

    /**
     * 账号绑定
     */
    public static final int PAGE_BINDING_ACCOUNT = 84;

    /**
     * 打开微课首页, 该iting已经废弃
     */
    @Deprecated
    public static final int PAGE_WEIKE_HOMEPAGE = 85;

    /**
     * 打开新圈子页面
     */
    public static final int PAGE_COMMUNITY_HOMEPAGE = 86;

    /**
     * 打开配音秀配音资源页
     */
    public static final int PAGE_RECORD_DUB_SHOW = 87;

    /**
     * 打开配音秀播放页
     */
    public static final int PAGE_DUBBING_PLAY = 88;

    /**
     * 打开主播分类列表页
     */
    public static final int PAGE_ANCHOR_LIST = 89;


    /**
     * 打开圈子话题日历
     */
    public static final int PAGE_COMMUNITY_TOPIC_CALENDAR = 90;

    /**
     * 打开提升点击率页
     */
    public static final int PAGE_IMPROVE_CLICK_RATE = 91;

    /**
     * 打开知识微课已购页面
     */
    public static final int PAGE_WEIKE_PAID_LIST = 92;

    /**
     * 打开专辑编辑页
     */
    public static final int PAGE_EDIT_ALBUM = 95;

    /**
     * 打开直播间管理员列表页面
     */
    public static final int PAGE_TO_LIVE_ADMIN_LIST = 96;

    /**
     * 打开RN页面
     */
    public static final int PAGE_TO_RN = 94;

    /**
     * vip频道页
     */
    public static final int PAGE_TO_HOME_VIP = 99;

    /**
     * 知识直播微课分类页面
     * 该iting已经废弃
     */
    @Deprecated
    public static final int PAGE_TO_WEIKE_CLASSIFY = 100;

    /**
     * 打开创建直播页
     */
    public static final int PAGE_TO_LIVE_CREATE = 102;


    /**
     * 打开微课可用优惠券课程列表
     */
    public static final int PAGE_TO_WEIKE_COUPON_COURSELIST = 104;


    /**
     * 打开微课 课程动态页面
     */
    public static final int PAGE_TO_WEIKE_NEWSCENTER = 105;


    /**
     * 打开榜单列表页
     */
    public static final int PAGE_TO_AGGREGATE_RANK_LIST = 107;


    /**
     * 打开微课 个人所有课程页面
     * 该iting已经废弃
     */
    @Deprecated
    public static final int PAGE_TO_WEIKE_HOST_ALLCOURSES = 108;

    /**
     * 打开搜索  搜索专辑内声音
     */
    public static final int PAGE_TO_OPEN_SEARCH_TRACK = 109;

    /**
     * 打开微课 直播间页面
     */
    public static final int PAGE_TO_OPEN_WEIKE_LIVEROOM = 110;

    /**
     * 定位到发现页趣配音tab
     */
    public static final int PAGE_TO_OPEN_FIND_DUBSHOW_TAB = 111;

    /**
     * 打开有特定内容的趣配音素材列表
     */
    public static final int PAGE_TO_DUB_MATERIAL = 112;

    //动态详情页
    public static final int PAGE_TO_DYNAMIC_DETAIL = 114;

    /**
     * 付费侧热词列表页
     */
    public static final int PAGE_TO_HOTWORD_PAID = 98;


    /**
     * 动态的话题详情页
     */
    public static final int PAGE_TO_TOPIC_DETAIL = 116;

    /**
     * 新榜挑战话题详情页
     */
    public static final int PAGE_TO_NEW_TOPIC_DETAIL = 117;

    /**
     * 打开音乐个性化电台
     */
    public static final int PAGE_TO_PERSONAL_MUSIC_RADIO = 121;

    /**
     * 个性化推荐直播
     */
    public static final int PAGE_TO_RECOMMEND_LIVE = 122;

    /**
     * 海选作品弹框
     */
    public final static int PAGE_TO_AUDITION = 118;

    /**
     * 动态评论详情页
     */
    public final static int PAGE_TO_DYNAMIC_COMMENT_REPLY_DETAIL = 119;

    /**
     * 城市切换页面
     */
    public final static int PAGE_TO_CITY_LIST = 120;

    /**
     * 打包发布动态页面
     */
    public final static int PAGE_TO_CREATE_DYNAMIC_FRAGMENT = 123;

    /**
     * vip频道页
     * 截屏反馈页面，该值为安卓自定义，分服务端定义
     */
    public static final int PAGE_TO_FEEDBACK = 501;

    /**
     * TTS详情页，该值为安卓自定义，分服务端定义
     */
    public static final int PAGE_TO_TTS_MAIN = 502;

    /**
     * 支付后跳转到群详情页，该值为安卓自定义
     */
    public static final int PAGE_TO_GROUP_DETAIL_PAID = 503;

    /**
     * 上传拍摄工具统计值，该值为安卓自定义
     */
    public static final int PAGE_TO_SHOOT_UPLOAD_RECORD = 510;

    /**
     * 节目全部评论页
     */
    public static final int PAGE_TO_COMMENT_LIST = 106;

    /**
     * 支付宝支付returnUrl回调打开空的iting回调页
     */
    public static final int PAGE_TO_PAY_BACK_NONE = -11;

    /**
     * 跳转到趣配音个人页
     */
    public final static int PAGE_TO_MY_DUB_USER_INFO = 124;

    /**
     * 跳转到我听
     */
    public final static int PAGE_TO_MY_LISTERN = 125;


    /**
     * 群聊页
     */
    public final static int PAGE_TO_GROUP_CHAT = 126;

    /**
     * 跳转到儿童保护禁止播放页面
     * <p>
     * 参数:
     * from=1 来自声音，
     * from=2 来自专辑
     * from=3 来自直播
     */
    public final static int PAGE_TO_CHILD_PROTECT_FORBID_PLAYPAGE = 127;

    /**
     * 跳转到新人听页面
     */
    public final static int PAGE_TO_NEW_USER_LISTEN = 128;

    /**
     * 跳转到提问详情页面
     */
    public final static int PAGE_TO_QUESTION_DETAIL = 129;

    // 跳转到技能设置页面
    public final static int PAGE_TO_ANCHOR_SKILL_SETTING = 130;

    public final static int PAGE_TO_VOICE_FRIEND = 131;

    // 跳转到应用管理设置页面
    public final static int PAGE_TO_APPLICATION_SETTING = 132;

    /**
     * 跳转到账户安全页面
     */
//    public final static int PAGE_TO_ACCOUNT_SECURITY = 134;

    /**
     * 跳转到发票页面
     */
//    public final static int PAGE_TO_INVOICE= 135;

    /**
     * 跳转娱乐厅直播间
     */
    public final static int PAGE_TO_ENT_HALL_ROOM_FRAGMENT = 137;

    // 跳转已下载页面
    public final static int PAGE_TO_DOWNLOADED = 138;

    // 打开历史页面
    public final static int PAGE_TO_HISTORY = 139;

    // 打开听单-我喜欢的声音页面
    public final static int PAGE_TO_LIKE_PAGE = 140;

    // 跳转到小雅频道页
    public final static int PAGE_TO_XIAO_YA = 141;

    /**
     * 精品榜
     */
    public final static int PAGE_TO_BOUTIQUE_RANK_FRA = 143;

    /**
     * 评论详情
     */
    public final static int PAGE_TO_COMMENT_DETAIL = 144;

    /**
     * UGC 我的素材页面
     */
    public final static int PAGE_TO_UGC_MY_MATERIAL = 145;

    /**
     * 自定义听单
     */
    public final static int PAGE_TO_TINGLIST = 146;

    /**
     * 日签预览
     */
    public final static int PAGE_TO_DAILYSIGN = 147;
    /**
     * 专辑申请退款页
     */
    public final static int PAGE_TO_ALBUM_REFUND = 148;
    /**
     * 专辑退款详情页
     */
    public final static int PAGE_TO_ALBUM_REFUND_INFO = 149;

    /**
     * 打开有声漫画素材广场页
     */
    public final static int PAGE_TO_AUDIO_COMIC_MATERIAL = 150;

    /**
     * 打开直播首页选中分类
     */
    public final static int PAGE_TO_LIVE_HOME_PAGE_SELECTED_CATEGORY_TAB = 151;
    public final static int PAGE_TO_DYNAMIC_SHORT_VIDEO = 153;
    public final static int PAGE_TO_SHOOT_CAPTURE = 166;

    /**
     * 免费专辑评价详情
     */
    public final static int PAGE_TO_FREE_ALBUM_RATE_DETAIL = 154;

    /**
     * 我的听单
     */
    public final static int PAGE_TO_MY_TINGLIST = 155;

    /**
     * 残疾人认证
     */
    public final static int PAGE_TO_DISABLED_VERIFY = 157;

    /**
     * 喜马热评墙
     */
    public final static int PAGE_TO_XIMALAYA_HOT_COMMENT = 158;

    // 打开发现页推荐tab
    public final static int PAGE_TO_FEED_RECOMMEND = 160;
    // 打开发现页发布按钮
    public final static int PAGE_TO_FEED_RECOMMEND_DYNAMIC_POP = 161;
    // 打开发现页视频选择页
    public final static int PAGE_TO_FEED_CHOOSE_VIDEO = 162;

    /**
     * 新用户推荐页面-专辑流
     */
    public static final int PAGE_CATEGORY_TAB_ALBUM = 167;

    /**
     * 新用户推荐页面-声音流
     */
    public static final int PAGE_CATEGORY_TAB_TRACK = 168;

    /**
     * 直播间内打开Pk战报弹窗
     */
    public final static int PAGE_TO_OPEN_PK_RESULT_DIALOG = 171;

    /**
     * 专辑评价
     */
    public static final int PAGE_TO_ALBUM_RATE = 172;

    /**
     * 明星圈相关专辑
     */
    public final static int PAGE_COMMUNITY_RELATED_ALBUM = 169;

    /**
     * 明星圈相关声音
     */
    public final static int PAGE_COMMUNITY_RELATED_TRACK = 170;

    /**
     * 有声漫播放页
     */
    public final static int PAGE_CARTOON_PLAY_VIDEO = 174;

    /**
     * 直播间打开收听奖励弹窗
     */
    public final static int PAGE_TO_OPEN_LISTEN_AWARD_DIALOG = 173;

    /**
     * 打开版权书库录音
     */
    public final static int PAGE_TO_COPYRIGHT_BOOK_RECORD = 175;

    /**
     * 开屏广告直播间导流
     */
    public static final int PAGE_TO_OPEN_LIVE_FROM_ADVERTISEMENT = 176;

    /**
     * 打开我的直播页
     */
    public static final int PAGE_TO_LIVE_MY_LIVES_FRAGMENT = 181;

    /**
     * 发现页视频合拍页
     */
    public final static int PAGE_TO_FEED_ANCHOR_VIDEO_FRAGMENT = 178;

    /**
     * 创建主播圈
     */
    public final static int PAGE_CREATE_COMMUNITY = 179;

    /**
     * 圈子广场
     */
    public final static int PAGE_COMMUNITY_SQUARE = 180;


    /**
     * 评论设置
     */
    public final static int PAGE_COMMENT_SETTING = 182;

    /**
     * 听更新
     */
    public static final int PAGE_TO_EVERYDAY_UPDATE = 177;
    /**
     * 周播剧落地页
     */
    public static final int PAGE_TO_WEEKLY_DRAMA = 183;

    /**
     * 直播间打开 H5 自定义弹窗
     */
    public static final int PAGE_TO_LIVE_PROVIDE_FOR_H5_CUSTOMER_DIALOG = 184;

    /**
     * 付费专辑评价详情
     */
    public final static int PAGE_TO_PAID_ALBUM_RATE_DETAIL = 185;
    /**
     * 消息中心评论页
     */
    public final static int PAGE_TO_CHAT_COMMENT = 186;
    /**
     * 打开短内容咔嚓页面
     */
    public static final int PAGE_TO_SHORT_CONTENT = 187;
    /**
     * 打开通知设置页面
     */
    public static final int PAGE_TO_OPEN_NOTIFY_SETTING = 188;

    /**
     * 打开录音tab页面
     */
    public static final int PAGE_TO_OPEN_RECORD_HOME_PAGE = 189;
    /**
     * 发现-添加好友页
     */
    public static final int PAGE_TO_OPEN_FIND_FRIEND = 190;

    /**
     * @deprecated 电台房模式已下线，业务代码已删除
     *
     * 娱乐派对我加入的守护团
     */
    public static final int PAGE_TO_OPEN_MY_JOINED_GUARDIAN_PAGE = 193;

    /**
     * 唤起直播间礼物面板
     */
    public static final int PAGE_TO_OPEN_GIFT_PANEL = 194;

    /**
     * 打开背包并定位到指定物品
     */
    public static final int PAGE_TO_OPEN_GIFT_PACKAGE_ITEM = 195;

    /**
     * 打开听单评论详情页
     */
    public static final int PAGE_TO_OPEN_CREATE_TINGLIST = 191;

    /**
     * 打开听单评论详情页
     */
    public static final int PAGE_TO_OPEN_TING_COMMENT_DETAIL = 192;

    /**
     * 主播工作台-我的所有专辑
     */
    public static final int PAGE_TO_ANCHOR_MY_ALL_ALBUMS = 196;

    /**
     * 打开用户资料卡片
     */
    public static final int PAGE_TO_OPEN_LIVE_USER_CARD = 197;

    /**
     * 打开首页，并切换到指定tab
     */
    public static final int PAGE_TO_OPEN_HOME_PAGE_WITH_TAB = 198;

    /**
     * 打开付费专辑排行榜页面
     */
    public static final int PAGE_TO_PAY_ALBUM_RANK_PAGE = 199;
    /**
     * 打开咔嚓笔记页面
     */
    public static final int PAGE_TO_KACHA_NOTE_TIMELINE = 200;

    public static final int PAGE_TO_CHILD_PLATFORM_BIND = 202;


    /**
     * 打开视频直播间页面
     */
    public static final int PAGE_TO_VIDEO_LIVE_ROOM = 201;

    /**
     * 频道页音&视频直播列表页
     */
    public static final int PAGE_TO_VIDEO_LIVE_LIST = 203;


    /**
     * 邀请游客加入到录音语聊房
     */
    public static final int PAGE_TO_RECORD_JOIN_CHAT_ROOM = 204;

    // 打开电子书阅读器
    public static final int PAGE_TO_BOOK_READER = 206;

    /**
     * 邀请用户加入群组的页面
     */
    public static final int PAGE_TO_HANDLE_GROUP_INVITE = 207;

    /**
     * 打开小程序列表页
     */
    public static final int PAGE_TO_RECORD_UPLOAD_FRAGMENT = 212;


    /**
     * 付费专辑新品限时免费列表页
     */
    public static final int PAGE_TO_NEW_ALBUM_TIME_LIMIT_FREE_LIST = 214;

    /**
     * 待评价列表页面
     */
    public static final int PAGE_TO_UNCOMMENTED_ALBUM = 210;

    /**
     * 处理iting ab，配置这个iting，则实际iting根据次iting携带的url请求的结果而定
     */
    public static final int PAGE_TO_HANDLE_ITING_AB = 213;

    /**
     * 打开新用户推荐专辑卡片播放页
     */
    public static final int PAGE_TO_RECOMMEND_ALBUM = 218;

    /**
     * 打开主播下的青少年拦截页，用于RN
     */
    public static final int PAGE_TO_ANCHOR_CHILD_PROTECT_PAGE = 215;

    /**
     * 跳转话题录音页
     */
    public static final int PAGE_TO_RECORD_TOPIC_PAGE = 220;

    /**
     * 跳转驾驶模式v2
     */
    public static final int PAGE_TO_DRIVE_MODE_V2_PAGE = 221;

    /**
     * 跳转到有声漫录制页
     */
    public static final int PAGE_TO_RECORD_AUDIO_COMIC = 222;

    /**
     * 消息中心通知页
     */
    public static final int PAGE_TO_NEWSCENTER_NOTIFY_PAGE = 216;

    /**
     * 消息中心评论/喜欢页
     */
    public static final int PAGE_TO_NEWSCENTER_COMMENT_LIKE_PAGE = 217;

    /**
     * 打开定时开播配置页
     */
    public static final int PAGE_TO_ALARM_SETTING = 250;

    /**
     * 客服新增iting
     * <p>
     * 252 修改密码页面
     * 253 未成年人保护模式--忘记密码页面
     * 254 下载缓存设置页面
     */
    public static final int PAGE_TO_MODIFY_PWD = 252;
    public static final int PAGE_TO_CHILD_PROTECT_FORGET_PED = 253;
    public static final int PAGE_TO_DOWNLOAD_CACHE = 254;
    /**
     * H5唤起专辑、声音底部推荐半浮层
     */
    public static final int PAGE_TO_RECOMMEND_BOTTOM_DIALOG = 333;

    // 跳转到可以解锁的付费声音专辑    iting://open?msg_type=255&albumId=xxx&trackId=xxx  trackId没有传,就到此专辑的首条付费声音
    public static final int PAGE_TO_UNLOCK_PAID_ALBUM = 255;
    /**
     * 打开定制化榜单单榜
     */
    public static final int PAGE_TO_CUSTOMIZE_RANK_FRAGMENT = 257;
    /**
     * 打开分类元数据聚合榜
     */
    public static final int PAGE_TO_GROUP_RANK_SINGLE_FRAGMENT = 258;

    // H5页面反向给出对fragment的操作
    public static final int PAGE_TO_H5_DIALOG_OPERATION = 256;

    /**
     * 儿童模式
     */
    public static final int PAGE_TO_KIDS_HOME = 260;
    public static final int PAGE_TO_KIDS_KEY_WORD = 261;
    public static final int PAGE_TO_KIDS_IP_SERIAL = 262;
    public static final int PAGE_TO_KIDS_SERIAL_DETAIL = 263;
    public static final int PAGE_TO_KIDS_TRACK_PLAYING = 264;
    public static final int PAGE_TO_KIDS_PB_PLAYING = 265;
    //情感活动 帖子发布页
    public static final int PAGE_TO_CREATE_POST = 266;

    /**
     * 喜马优品
     */
    public static final int PAGE_TO_PREFFERED_CONTENT = 267;
    /**
     * 往期优选
     */
    public static final int PAGE_TO_HISTORY_PREFFERED = 268;
    /**
     * 最新上架
     */
    public static final int PAGE_TO_NEW_PUBLISH = 269;

    /**
     * 关闭视频直播浮窗
     */
    public static final int PAGE_TO_CLOSE_VIDEO_FLOAT_WINDOW = 270;

    /**
     * epub阅读器
     */
    public static final int PAGE_TO_EPUB_READER = 276;

    /**
     * 创建专辑页
     */
    public static final int PAGE_TO_CREATE_ALBUM = 277;

    /**
     * 儿童模式
     */
    public static final int PAGE_TO_KIDS_SINGLE_KEY_WORD = 271;
    public static final int PAGE_TO_KIDS_SINGLE_RANK = 272;
    public static final int PAGE_TO_KIDS_SUBSCRIBE = 273;
    public static final int PAGE_TO_KIDS_HISTORY = 274;

    //申请打开蓝牙的RequestCode PS：ATT最大长度为512，故取该值
    public static final int REQUEST_CODE_ENABLE_BLUETOOTH = 512;
    //小雅AI图书馆扫码绑定
    public static final int PAGE_TO_XIAO_YA_SCAN_BIND = 275;
    //打开智能设备页面
    public static final int PAGE_TO_SMART_DEVICE = 278;
    //打开咔嚓投稿组件
    public static final int PAGE_TO_KACHA_POST = 281;

    /**
     * 我听书架tab
     */
    public static final int PAGE_TO_MY_LISTEN_EBOOK_TAB = 282;

    public static final int PAGE_TO_My_POST = 283;

    // 儿童模式读书计划列表页、详情页
    public static final int PAGE_TO_KIDS_READING_LIST = 284;
    public static final int PAGE_TO_KIDS_READING = 285;

    public static final int PAGE_TO_ELDERLY_MODE = 288;

    /**
     * 直播-小额礼物
     */
    public static final int PAGE_TO_LIVE_RECHARGE_MODEL = 292;

    /**
     * 微信公众号音频上传(uploadId标识的音频)
     */
    public static final int PAGE_TO_UPLOAD_AUDIO_BY_UPLOADID = 294;

    //提问页面
    public static final int PAGE_TO_QUESTION_POST = 295;


    //老年模式分类聚合排行榜
    public static final int PAGE_TO_ELDERLY_RANK_PAGE = 297;


    /***************智能硬件设备跳转web url************/
    /**
     * 设备管理列表web ur
     * 账号页面智能硬件设备入口URL，用于判断拦截是否账号页面智能硬件设备入口URL，进行预加载bundle
     */
    public static final String SMART_DEVISES_LIST_MANAGE_URL = "https://s1.xmcdn.com/yx/xiaoya-neo/last/build/?_default_share=0";
//    public static final String SMART_DEVISES_LIST_MANAGE_URL = "https://s1.uat.xmcdn.com/yx/xiaoya-neo/last/build/?_default_share=0";
    /***************智能硬件设备跳转 end *************/

    /**
     * 全部频道列表
     */
    public static final int PAGE_TO_CATEGORY_LIST = 279;

    /**
     * 标签频道落地页
     */
    public static final int PAGE_TO_TAG_CHANNEL = 280;

    /**
     * 单榜落地页
     */
    public static final int PAGE_TO_SINGLE_RANK = 293;

    /**
     * 新品推荐
     */
    public static final int PAGE_TO_NEW_PRODUCT = 296;

    /**
     * 老年模式专辑页
     */
    public static final int PAGE_TO_ELDERLY_ALBUM = 300;

    /**
     * 老年模式播放页
     */
    public static final int PAGE_TO_ELDERLY_PLAY = 301;


    /**
     * 账号页入口聚合页
     */
    public static final int PAGE_TO_MINE_MODULE_ENTRANCE = 302;

    /**
     * 新用户排行榜
     */
    public static final int PAGE_TO_NEW_USER_RANK = 303;

    /**
     * 亲子画像选择页
     */
    public static final int PAGE_TO_EDIT_CHILD_INFO = 304;

    /**
     * 时刻文稿跳转播放声音页的某一进度
     */
    public static final int PAGE_TO_TRACK_PROGRESS = 305;

    /**
     * 家庭会员-推荐集合页
     */
    public static final int PAGE_TO_FAMILY_RECOMMEND_DETAIL = 306;

    /**
     * 播客页-新版
     */
    public static final int PAGE_TO_PODCAST = 308;

    // 打开我听订阅独立页面
    public static final int PAGE_TO_MY_SUBSCRIBE = 307;

    /**
     * 播客排行榜
     */
    public static final int PAGE_TO_PODCAST_RANK = 309;

    /**
     * 评论活动结果页
     */
    public static final int PAGE_TO_COMMENT_THEME_PAGE = 311;

    /**
     * 小满游戏
     */
    public static final int OPEN_XIAOMAN_GAME = 312;

    /**
     * 会员购买浮层
     */
    public static final int PAGE_TO_VIP_DIALOG = 313;

    /**
     * rn动态详情页
     */
    public static final int PAGE_RN_DYNAMIC_DETAIL = 314;

    /**
     * 处理语音唤醒的指令，目前只在驾驶模式
     */
    public static final int DEAL_INSTRUCTION_TO_VOICE_WAKE = 318;

    /**
     * 豹趣游戏
     */
    public static final int OPEN_BAO_QU_GAME = 315;

    /**
     * 创建录音语聊房
     */
    public static final int PAGE_TO_RECORD_CREATE_CHAT_ROOM = 316;

    /**
     * 打开搜索结果页面
     */
    public static final int PAGE_TO_SEARCH_RESULT_PAGE = 324;

    /**
     * 打开文稿页面
     */
    public static final int PAGE_TO_AUDIO_DOC = 319;

    /**
     * 新版我的喜欢
     */
    public static final int OPEN_MY_LIKE_V2 = 320;

    /**
     * 广告下载中心iting ： AdDownloadListFragment
     */
    public static final int OPEN_AD_DOWNLOAD_PAGE = 321;


    /**
     * 跳转到聊天室 UGC/PGC 房间
     */
    public static final int PAGE_TO_CHATROOM_LIVE_ROOM = 323;

    /**
     * 进入建行SDK
     */
    public static final int OPEN_CCB_SDK = 326;

    /**
     * 打开p拉c分享面板
     */
    public static final int PAGE_TO_PLC_SHARE_DIALOG = 327;

    /**
     * 首页获取推荐的直播间
     */
    public static final int PAGE_TO_QUERY_RECOMMEND_LIVE_ROOM = 330;

    /**
     * 直播间-跳转指定礼物或背包
     */
    public static final int PAGE_TO_LIVE_GIFT_PACKAGE = 329;

    /**
     * 打开喜花花借钱
     */
    public static final int PAGE_TO_LOAN_SDK = 331;

    /**
     * 根据tabid跳转首页tab
     */
    public static final int PAGE_TO_HOME_PAGE_WITH_TAB_ID = 332;

    /**
     * 打开全部服务页面
     */
    public static final int PAGE_TO_MINE_ALL_SERVICES = 334;

    /**
     * 打开新用户推荐榜单页
     */
    public static final int PAGE_TO_RECOMMEND_NEW_USER_RANK = 335;

    /**
     * 打开MyClub房间页
     */
    public static final int PAGE_TO_MYCLUB_CHAT_ROOM = 337;
    public static final int PAGE_TO_MYCLUB_NEW_CHAT_ROOM = 343;

    /*
     * 跳转到 Pia 戏剧本详情页
     */
    public static final int PAGE_TO_PIA_SCRIPT_DETAIL = 338;

    /**
     * 跳转到 Pia 戏作者作品列表详情页
     */
    public static final int PAGE_TO_PIA_AUTHOR_DETAIL = 339;

    /**
     * 打赏（喜点）弹框
     */
    public static final int PAGE_TO_REWARD_DIAN_DIALOG = 336;

    public static final HashSet<Integer> ITING_GAME_LIST = new HashSet<Integer>() {
        {
            add(OPEN_XIAOMAN_GAME);
            add(OPEN_BAO_QU_GAME);
        }
    };

    /**
     * 打开新版编辑资料页
     */
    public static final int PAGE_TO_MY_DETAIL = 317;

    /**
     * 打开直播-我的-功能页（我的勋章，我的装扮，我的贵族等）
     */
    public static final int PAGE_TO_LIVE_MINE_FUNCTION = 340;

    /**
     * 我的付费直播
     */
    public static final int PAGE_TO_LIVE_MINE_PAID_LIST = 341;

    /**
     * 跳转聊天室开播页
     */
    public static final int PAGE_TO_NEW_CHAT_ROOM_CREATE_PAGE = 344;

    /**
     * 跳到短剧播放页
     */
    public static final int PAGE_TO_PLAYLET_PLAY_PAGE = 345;

    /**
     * 跳到短剧聚合页
     */
    public static final int PAGE_TO_PLAYLET_POLYMERIZE_PAGE = 346;

    /**
     * 打开卖货开关弹窗
     */
    public static final int PAGE_TO_SELL_TOGGLE = 347;


    /**
     * 跳到Myclub日程编辑页
     */
    public static final int START_MYCLUB_OTHER_TASKS = 348;

    /**
     * 跳到兴趣卡片选择页
     */
    public static final int PAGE_TO_CHOOSE_LIKE = 349;

    /**
     * 退出app
     */
    public static final int ITING_KEY_EXIT_APP = 350;

    /**
     * 跳转更多糖葫芦页面
     */
    public static final int PAGE_TO_MORE_CALABASH = 351;

    /**
     * 切换账号
     */
    public static final int PAGE_TO_SWITCH_ACCOUNT = 352;

    /**
     * 跳转糖葫芦聚合页
     */
    public static final int PAGE_TO_TING_LIST_GROUP_PAGE = 353;

    /**
     * 随机打开直播间，同时打开背包并定位到具体物品
     */
    public static final int PAGE_TO_LIVE_ROOM_LOCATE_PACKAGE = 354;

    /**
     * 选中地址后将地址id返回
     */
    public static final int PAGE_TO_SELECT_ADDRESS_RESULT = 516;

    /**
     * 跳转抽奖详情页
     */
    public static final int PAGE_TO_DYNAMIC_LOTTERY_RESULT = 517;

    /**
     * 打开ai文稿主播侧专辑列表，可显示特定专辑
     */
    public static final int PAGE_TO_RECORD_AI_ALBUM_LIST = 355;

    /**
     *  打开ai文稿主播侧专辑详细页
     */
    public static final int PAGE_TO_RECORD_AI_ALBUM = 356;

    /**
     *  RN广播频道页跳转广播详情页
     */
    public static final int PAGE_TO_RADIO_SQUARE_PAGE = 357;
    /**
     * 处理官方直播间的跳转
     */
    public static final int PAGE_TO_LIVE_TYPE_OFFICIAL = 358;

    /**
     *  直播频道糖葫芦-更多
     */
    public static final int PAGE_TO_LIVE_CHANNEL_MORE = 359;

    /**
     * 音效面板
     * */
    public static final int START_SOUND_EFFECT_CHOOSE_DIALOG = 362;

    /**
     * 打开新版榜单列表页
     */
    public static final int PAGE_TO_NEW_AGGREGATE_RANK_LIST = 366;

    /**
     * 讨论浮层
     */
    public static final int PAGE_TO_TALK_PANEL = 363;

    /**
     * 月票投票列表
     * */
    public static final int MONTHLY_VOTE_LIST_PAGE = 365;
    /**b
     * 微信公众号测试通道，仅仅为了保留给测试使用，所以用个比较大的号，不要惊讶
     */
    public static final int OPEN_WECHAT_RESERVE_DIALOG = 10000;


    /**
     *  频道页模板化预览iting
     */
    public static final int PAGE_TO_CHANNEL_FRAGMENT_PREVIEW = 360;

    /**
     *  频道页二级分类iting
     */
    public static final int PAGE_TO_CHANNEL_FRAGMENT_CHILD_CATEGORY = 361;

    /**
     * 打开「投稿箱」信件列表页
     */
    public static final int PAGE_TO_MAILBOX_MAIL_LIST = 367;

    /**
     * 打开「投稿箱」信件详情页
     */
    public static final int PAGE_TO_MAILBOX_MAIL_DETAIL = 368;

    /**
     * 打开「投稿箱」写信页
     */
    public static final int PAGE_TO_MAILBOX_WRITE_MAIL = 369;

    /**
     * 打开投票浮层
     */
    public static final int PAGE_TO_VOTE_PANEL = 370;

    /**
     * 新手引导，从积分中心，返回后打开月票浮层
     */
    public static final int SIGN_TO_VOTE_PANEL = 371;

    /**
     * 打开我的投稿列表页面
     */
    public static final int PAGE_TO_MY_MAIL_LIST = 372;

    /**
     * 打开互动卡片浮层
     */
    public static final int PAGE_TO_INTERACTIVE_CARD_PANNEL = 373;

    /**
     * 文字转声音，然后朗读
     */
    public static final int PAGE_TO_TEXT_TO_SOUND_AND_PLAY = 374;

    /**
     * 打开直播 PGC 房间编辑页
     */
    public static final int PAGE_TO_LIVE_PGC_ROOM_EDIT = 375;

    /**
     * 打开 月票排行榜 页面
     * */
    public static final int PAGE_TO_MONTH_TICKET_CONTRIBUTE_RANK_PAGE = 376;
    /**
     * 直播关注列表
     */
    public static final int PAGE_TO_LIVE_FOLLOW_LIST = 377;

    /**
     * 打开搜索结果页面，和ios对齐
     */
    public static final int PAGE_TO_SEARCH_RESULT_PAGE_1 = 508;

    /**
     * 打开主播的全部声音
     */
    public static final int PAGE_TO_ANCHOR_TRACK_LIST = 601;

    /**
     * 打开儿童故事落地页
     */
    public static final int PAGE_TO_KID_STORY = 379;
    /**
     * 打开儿童陪伴页
     */
    public static final int PAGE_TO_KID_ACCOMPANY = 380;

    /**
     * 打开儿童会员精选页
     */
    public static final int PAGE_TO_KID_VIP = 381;
    /**
     * 儿童全部分类
     */
    public static final int PAGE_TO_KID_ALL_COLUMN = 382;
    /**
     * 儿童定向限免
     */
    public static final int PAGE_TO_KID_LIMIT_FREE = 384;

    /**
     * 儿童内容推荐页
     */
    public static final int PAGE_TO_KID_CONTENT_RECOMMEND = 385;

    /**
     * 预览青少年弹窗
     */
    public static final int TO_PREVIEW_CHILD_PROTECT_DIALOG = 386;

    public static final int PAGE_TO_PODCAST_ALL_CATEGORY = 383;

    public static final int PAGE_TO_PERSONAL_QR_CODE_PAGE = 387;

    public static final int PAGE_TO_CHARACTER_DETAIL = 388;

    public static final int PAGE_TO_HALF_SCREEN_LOGIN_AND_GET_PRIZE = 389; // 半屏登录并且登录成功后领奖

    /**
     * 儿童 青少年模式 密码修改
     */
    public static final int PAGE_TO_KID_TEEN_RESET_PWD_PAGE = 390;

    /**
     * 儿童 宝贝哄睡设置
     */
    public static final int PAGE_TO_KID_PUT_TO_BED_SETTING = 391;

    //大师课会员购买弹窗
    public static final int PAGE_TO_MASTER_VIP_PURCHASE_DIALOG = 392;

    /**
     * 爸妈讲故事
     */
    public static final int PAGE_TO_KID_AI_SOUNDE = 393;

    /**
     * 儿童知识页
     */
    public static final int PAGE_TO_KID_KNOWLEDGE = 395;

    public static final int PAGE_TO_TRACK_COMMENT_PANEL = 394;

    public static final int PAGE_TO_QQ_GAME_CENTER = 396;

    public static final int PAGE_TO_REPORT = 397;

    /**
     * 书荒话题详情页
     */
    public static final int PAGE_TO_SHUHUANG_TOPIC = 398;

    /**
     * 儿童内容查询二级页
     */
    public static final int PAGE_TO_KID_CONTENT_QUERY = 399;

    /**
     * 打开无障碍模式
     */
    public static final int PAGE_TO_OPEN_ACCESSIBILITY_MODE = 403;

    /**
     * 勋章分享海报
     */
    public static final int PAGE_TO_SHARE_MEDAL_POSTER = 404;

    public static final int PAGE_TO_ANCHOR_CHAT_GROUP = 401; // 主播粉丝群聚合页

    public static final int PAGE_TO_ANCHOR_CHAT_GROUP_SHAPE = 402; // 主播粉丝群单个页，只包含一个群聊，只包含聚合页中的一条数据

    /**
     * 跳转播客决策流
     */
    public static final int PAGE_TO_POD_CAST_IMMERSIVE = 405;

    public static final int PAGE_TO_RECORD_INTERACTIVE_RECORD = 400;
    /**
     * 下载/打开喜马拉雅儿童app
     */
    public static final int PAGE_TO_KID_APP = 406;

    public static final int PAGE_TO_RECORD_EDIT = 407;
    public static final int PAGE_TO_LISTEN_TIME_FRAGMENT = 408;

    public static final int PAGE_TO_SCAN_QR = 409; // 打开扫一扫

    public static final int PAGE_TO_BOOK_TOPIC_LIST = 410; // 打开书荒话题列表页


//    public static final int PAGE_TO_SKETCH_CATEGORY = 412; // 短剧频道页


    public static final int PAGE_TO_MINE_ANCHOR_AND_RECOMMEND_GROUP_LIST = 411;

    public static final int PLAY_TO_TING_LIST_OR_CONTENT_LIST = 413; // 播放听单或者内容池
    public static final int PAGE_TO_SEARCH_WITH_HINT = 414; // 搜索页 带hint
    public static final int PAGE_TO_SEARCH_SPEECH_RECOGNITION = 415; // 跳转语音助手
    public static final int PAGE_TO_TAB_FREE_LISTEN = 416; // 跳转免费听tab
    public static final int PAGE_TO_KID_NEW_USERS_ONBOARDING = 417; // 跳转到


    public static final int PAGE_TO_TTS_PARSE_VOICE = 419; // 文字转音频
    public static final int PAGE_TO_DAILY_NEWS_PLAY_PAGE = 420; // 今日热点声音直接进播放页
    public static final int PAGE_TO_LOGIN_FROM_OUT_SITE = 421; // 站外授权登录
    public static final int PAGE_TO_PLAY_ALBUM_LAST_TRACK = 422; // 播放专辑最后一首

    public static final int PAGE_TO_KID_CHANNEL_PAGE = 418; //儿童新二级频道页

    public static final int PAGE_TO_DOC_READ_PAGE = 424; // 文档阅读器界面

    public static final int PAGE_TO_KID_COMMON_CHANNEL = 425; //儿童通用频道页

    public static final int PAGE_TO_CHAT_XMLY_DIALOG = 426; // 语音助手弹窗
    public static final int PAGE_TO_CHAT_XMLY_SETTING = 427; // 语音助手设置
    public static final int PAGE_TO_KID_RECOMMEND_PAGE = 429; // 儿童通用专辑列表页
    public static final int PAGE_TO_KID_IP_COLLECTION = 431; // 儿童进入ip合集页

    public static final int PAGE_TO_KID_SEGMENT_DETAIL = 432; // 儿童配音片段详情页

    public static final int PAGE_TO_KID_DUBBING_WORK = 433; // 儿童配音片段作品页
    public static final int PAGE_TO_AI_BROKER_TOOLS = 428; // Ai经纪人工具
    public static final int PAGE_TO_RECOMMEND_HOT_LIVE_LANDING_PAGE = 430; // 新推荐首页跳转热门直播落地页

    public static final int PAGE_TO_KID_DUBBING_RECORD = 434; // 儿童进入ip配音录音页
    public static final int PAGE_TO_KID_MY_DUBBING = 435; // 儿童进入配音秀作品页
    public static final int PAGE_TO_KID_CHILD_MODE= 533; // 打开开启青少年模式页

    public static final int PAGE_TO_TO_LISTEN = 436; // 待播相关
    public static final int PAGE_TO_TO_NEW_SHOW_NOTES = 438; // ShowNotes落地页

    public static final int PAGE_TO_TO_PLAYER_PAGE_SHOP_DIALOG = 439; // 播放页购物车弹窗

    public static final int PAGE_TO_RECORD_UGC_HOME_PAGE = 440; // ugc页面
    public static final int PAGE_TO_RECORD_UGC_READ_PAGE = 441; // ugc朗读广场页面

    public static final int PAGE_TO_CHAT_XMLY = 437; // chatxmly AI助手

    public static final int PAGE_TO_LIVE_DISCOVER_PAGE = 442; // 直播发现页

    public static final int PAGE_TO_AD_SETTING_SHAKE_PAGE = 443; // 广告设置摇一摇页面

    public static final int PAGE_TO_ALL_CATEGORY = 444; // 频道页  全部二级分类iting
    public static final int PAGE_TO_PLAY_CONTENT_POOL_TRACK_LIST = 446; // 播放内容池声音列表

    public static final int PAGE_TO_ALL_CATEGORY_FILTER_PAGE = 445; // 跳转分类二级筛选页

    public static final int PAGE_TO_CATEGROY_V2 = 447; // 跳转新版分类页

    public static final int PAGE_TO_BATCH_DOWNLOAD = 448; // 批量下载

    public static final int PAGE_TO_INTEREST_PAGE_NEW = 450; // 性别年龄+兴趣选择(AB)

    public static final int THIRD_APP_AUTHORIZE = 451; // 高德吊起归因

    public static final int PAGE_TO_RECORD_FROM_CLOUD_EDIT = 453; // 云剪辑跳转录音

    public static final int PAGE_TO_MS_BIZ_LIST = 452; // 蜜声商单列表
    public static final int PAGE_TO_UGC_STORY = 454; // 故事连载

    public static final int PAGE_TO_PlanTerminal = 520; // 定时关闭

    public static final int PAGE_TO_MINI_DRAMA_HOME_PAGE = 521; // 短剧上下滑刷剧页面
    public static final int PAGE_TO_MINI_DRAMA_DETAIL_PAGE = 522; // 短剧上下滑刷集页面
    public static final int PAGE_TO_MINI_DRAMA_HISTORY_PAGE = 423; //短剧播放历史

    public static final int PAGE_TO_LISTEN_PERMISSION = 523; // 定时关闭

    public static final int DIRECT_TO_FREE_LISTEN_REWARD = 526; // 直接进入时长模式激励视频广告
    public static final int DIRECT_TO_SETTING_PAGE = 527; // 跳转设置页iting
    public static final int DIRECT_TO_CHAT_PAGE = 528; // 跳转聊天页iting
    public static final int DIRECT_TO_TIMER_OPEN_PAGE = 529; // 跳转打开定时页iting
    public static final int DIRECT_TO_MODE_SWITCH_DIALOG = 530; // 打开模式切换弹窗
    public static final int DIRECT_SMART_DEVICE_PAGE = 531; // 智能硬件

    public static final int SHARE_TO_FREE_LISTEN = 532; // 通过畅听分享进入app

    public static final int MINE_LINE_RECORD_ENTRY = 534; // 个人页 录音/直播入口
    public static final int FIND_FRIENDS_SEARCH_PAGE = 535; // 通讯录-搜索
    public static final int FIND_FRIENDS_TXL_PAGE = 536; // 发现好友-通讯录

    public static final int GOTO_LOCAL_READ_PAGE = 602;
    public static final int GET_VIP_RETENTION_GIFT = 610;//领取用户挽留礼包

    public static final int PAGE_TO_SHOW_PLATINUM_VIP_VOUCHER_DIALOG = 613;
    public static final int PAGE_TO_SHOW_PLATINUM_VIP_AHEADLISTEN_SCHEDULE_DIALOG = 614;

    public static final int PAGE_TO_ALBUM_RESERVATION_LIST_NEW = 615; // 新专辑预约列表

    public static final int GOTO_DUAL_TRANSFER_PAGE = 603;

    //热搜榜分类页
    public static final int HOT_SEARCH_CATEGORY = 604;


    public static final int HALF_WEB_DIALOG = 605; // 半屏web弹窗
    public static final int RESERVE_ALBUM = 606; // 预约

    //儿童限免听单页
    public static final int GOTO_KID_TING_LIMIT_FREE_PAGE = 537;

    public static final int PAGE_TO_CATEGROY_V2_PREVIEW = 1000; // 新版分类页预览

    /***************通知使用的常量*************/
    public static final int ID_ALARM_LATER = 6;
    public static final int ID_ALARM_TIME_GONE = 7;
    public static final int INFORM_NET_ERR = 8;
    public static final int ID_XCHAT_KICK = 9;
    public static final int ID_DAILYNEWS_ALARM = 10;
    public static final int ID_BACKGROUND_PLAY_VIDEO = 11;
    //id : funs
    public static final int IDENTIFIER_FUNS = 0x1000;
    //id : letter
    public static final int IDENTIFIER_LETTER = 0x1001;
    //id : comment
    public static final int IDENTIFIER_COMMENTS = 0x1002;
    //id : new event
    public static final int IDENTIFIER_NEW_EVENT = 0x1003;
    //id : friends
    public static final String ALARM_RECEIVER = "alarm_receiver";
    public static final String ALARM_FROM_NOTIFICATION = "alarm_from_notification";
    public static final String DAILYNEWS_ALARM_FROM_NOTIFICATION = "dailynews_alarm_from_notification";
    public static final String ALARM_FROM_NOTIFICATION_LATER = "alarm_from_notification_later";
    public static final String NO_UPLOAD_SOUND_FLAG = "no_upload_sound_flag";
    public static final String SSO_RESPONSE_TYPE = "response_type";
    public static final String SSO_APP_KEY = "app_key";
    public static final String SSO_PACKAGE_ID = "pack_id";
    public static final String SSO_CLIENT_ID = "client_id";
    public static final String SSO_DEVICE_ID = "device_id";
    public static final String SSO_REDIRECT_URL = "redirect_uri";
    public static final String SSO_STATE = "state";
    public static final String SSO_CLIENT_OS_TYPE = "client_os_type";
    public static final String SSO_OBTAIN_AUTH_TYPE = "obtain_auth_type";
    public static final String SSO_THIRD_APP_NAME = "third_app_name";
    public static final String LOCAL_CITY_CODE = "City_Code";//本地听
    public static final String LOGIN_FROM_HOTLINE = "login_from_hotline";
    public static final int SOURCE_OTHER = 7;
    public static final int REQUEST_CODE_MESSAGE_SHARE = 6532;
    public final static String RADIO_YESTERDAY = "yesterdaySchedules";
    public final static String RADIO_TODAY = "todaySchedules";
    public final static String RADIO_TOMORROW = "tomorrowSchedules";
    public static final String[] DAY_TYPES = new String[]{RADIO_YESTERDAY, RADIO_TODAY,
            RADIO_TOMORROW};
    public static final String WELCOME_IS_INIT = "isInit";//欢迎页面初始化
    public static final int MAX_SYNC_LOAD_AD_TIME = 3000; // 最长的广告的加载时间
    //    public static final int FROM_UNBIND_PAGE = 1;//主动解绑页面
    public static final int FROM_LOGIN_PAGE = 2;//登陆页面
    public static final int FROM_BIND_PAGE = 3;//绑定页面(通用绑定逻辑)
    public static final int SHARE_TYPE_SUICHETING = 4;
    public static final int SHARE_TYPE_QSUICHETING = 5;
    public static final int SHARE_TYPE_MSUICHETING = 6;
    public static final int TYPE_TRACK = 0;
    public static final int TYPE_ALBUM = 1;
    public static final int TYPE_ANCHOR = 2;
    public static final int WORK_TYPE_READ_HALL = 3;
    public static final String TYPE_TRACK_STR = "track";
    public static final String TYPE_ALBUM_STR = "album";
    public static final String TYPE_ANCHOR_STR = "anchor";
    public static final int REFRESH_TING_NUM = 0X001;
    public static final String SOUND_REPORT_SHOW_TIMES = "sound_report_show_times";
    public static final String EXTRA_IMAGE_FROM = "extra_image_from";

    public static boolean isDebugSvrTest = false;
    public static boolean isPad = false;
    /**
     * SDL只支持3.1以后的。系统USBManager自3.1开始添加进Android SDK中
     */
    public static boolean isSupportSDL = Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB_MR1;
    /**
     * 发现页推荐应用开关
     */
    public static boolean sEnableAppAds = true;
    //注意ting目录已经被废弃，我们后续内容全部会存入对于包名下的私有目录中，
    // 该变量会在Application初始化中修改，注意新增目录在本类的resetBaseDir方法中重新赋值

    public static String EXIT_APP = "exit_app";
    //    播放来源(APP必须传，1:主播个人首页，2:推送， 3:主播直播首页，4:焦点图, 5:直播播放页，6:h5跳转，7:其他)
    /**
     * 为了应对工信部和某些电子市场app审查，要求像联网这样的请求提示用户， </p>此变量判断起始时是不是弹出提示框
     */
    public static boolean IS_TO_ASK_3G_AUTHORITY = true;
    /**
     * 上传通讯录是否弹窗提示
     */
    public static boolean IS_NOTIFY_UPLOAD_CONTACTS = false;
    /**
     * 用于onNewIntent
     */
    public static String FRAGMENT_CLASS_NAME = "fra_className";
    public static String FROM_VIEW = "from_view";
    public static String FRAGMENT_BUNDLE = "fra_bundle";
    public static final String INTENT_PARCEL_CLASS_NAME = "intent_parcel_class_name";

    /**
     * 未读消息数量
     *
     * <AUTHOR>
     */
    public static class MsgCount {
        public static int sCount_Fans = 0;
        public static int sCount_Letter = 0;
        public static int sCount_Comments = 0;
        public static int sCount_Events = 0;
        public static int sCount_Friends = 0;
        public static int sCount_ZoneComments = 0;
    }

    /**
     * 来源标示(0:发现流 1:话题时间排序 2:话题热度排序 3:用户作品列表
     * 8 我的趣配音作品页
     * 4 趣配音素材广场首页推荐 5 趣配音素材广场分类子tab页面
     * 6 趣配音素材广场热词页面 7 趣配音素材广场热词页面来自iting
     * 9 趣配音素材广场全部素材页面   10 趣配音素材广场首页最新素材
     * 11 动态
     * 12 挑战话题 - 最新tab
     * 13 挑战话题 - 我的作品
     * 14 挑战话题 - 挑战tab排行榜
     * 15 素材落地页 - 周榜
     * 16 素材落地页 - 总榜
     * 17 feed流 - 编辑精选
     * 18 话题挑战 - 模板
     * 19 素材广场 - 合配
     */
    public static final int TYPE_FINDING = 0;
    public static final int TYPE_TOPIC_TIME = 1;
    public static final int TYPE_TOPIC_HOT = 2;
    public static final int TYPE_MY_PLANT = 3;
    public static final int TYPE_OTHER_PLANT = 8;

    public static final int TYPE_MATERIAL_SQUARE_RECOMMEND = 4;
    public static final int TYPE_MATERIAL_SUB_CATEGORY = 5;
    public static final int TYPE_MATERIAL_HOT_WORD = 6;
    public static final int TYPE_MATERIAL_HOT_WORD_ITING = 7;
    public static final int TYPE_MATERIAL_ALL = 9;
    public static final int TYPE_MATERIAL_SQUARE_LATEST = 10;

    public static final int TYPE_DYNAMIC = 11;
    public static final int TYPE_TOPIC_RECENT_WORK = 12;
    public static final int TYPE_TOPIC_MY_WORK = 13;
    public static final int TYPE_TOPIC_CHALLENGE_RANK = 14;
    public static final int TYPE_MATERIAL_LANDING_WEEK_RANK = 15;
    public static final int TYPE_MATERIAL_LANDING_GENERAL_RANK = 16;
    public static final int TYPE_DUB_FEED_EDITOR_CHOSEN = 17;
    public static final int TYPE_TOPIC_CHALLENGE_TEMPLATE = 18;
    public static final int TYPE_DUB_COOPERATE_MATERIAL = 19;
    public static final int TYPE_MATERIAL_DUAL_DUB_DETAIL = 20;
    public static final int TYPE_MATERIAL_LANDING_DUAL_DUB = 21;

    public static final int TYPE_AGGREGATE_PAGE = 100;

    // 配音秀发送广播action
    public static final String TYPE_DUBBING_ACTION_LIKE = "type_dubbing_action_like";
    public static final String TYPE_DUBBING_ACTION_SHARE = "type_dubbing_action_share";
    public static final String TYPE_DUBBING_ACTION_COMMENT = "type_dubbing_action_comment";
    public static final String TYPE_RN_ACTION_FRAGMENT_FINISH = "type_rn_action_fragment_finish";

    public static final String TYPE_NO_CONTENT_ACTION_GUIDE = "type_no_content_action_guide";
    public static final String DATA_IS_SHOW_GUIDE = "DATA_IS_SHOW_GUIDE";

    public static final String DATA_DUBBING_FEED_ID = "feedID";
    public static final String DATA_DUBBING_TRACK_ID = "trackId";
    public static final String DATA_DUBBING_IS_LIKE = "isLike";
    public static final String DATA_DUBBING_LIKE_COUNT = "likeCount";
    public static final String DATA_DUBBING_COMMENT_COUNT = "commentCount";
    public static final String DATA_DUBBING_SHARE_COUNT = "shareCount";

    public static final String TYPE_VIP_CLUB_BOUGHT = "type_vip_club_bought";
    public static final String DATA_COMMUNITY_ID = "community_id";
    public static final String TYPE_FEED_CHOOSE_VIDEO_DATA = "type_feed_choose_video_data";
    public static final String TYPE_FEED_VIDEO_INFO_DATA = "type_feed_video_info_data";
    public static final String INTEREST_CARD_SWITCH_INFO_KEY = "interest_card_switch_info_key";
    public static final String TYPE_FEED_HOME_TAB_TYPE = "type_feed_home_tab_index";
    public static final String EXTRA_DATA_4_BACK_USER_OPEN_APP = "extra_data_4_back_user_open_app";

    public static final String RN_DETAIL_FINFISH_ACTION = "community_RNDynamicFinish";

    public final static int STICKY_OPTION_NONE = 0;
    public final static int STICKY_OPTION_DO_STICKY = 1;
    public final static int STICKY_OPTION_CANCEL_STICKY = 2;

    // 已购
    public static final int REFOUND_ERROE = 0;
    public static final int REFOUND_OUT_OF_DATE = 1;
    public static final int REFOUND_NOT_SUPPORT_ANCHOR = 2;
    public static final int REFOUND_NOT_SUPPORT_REDEEM_CODE = 3;
    public static final int REFOUND_NOT_SUPPORT_BUY_SECOND = 4;
    public static final int REFOUND_SUPPORT_REFOUND = 5;
    public static final int REFOUND_REFUNDING = 6;
    public static final int REFOUND_REFUNDED = 7;
    public static final int NO_COMMENT = 1;
    public static final int DISABLE_COMMENT = 2;
    public static final int HAD_COMMENT = 3;

    // refund info
    public static final int REFUND_STATUS_DEFAULT = 0;
    public static final int REFUND_STATUS_REFUNDING = 1;
    public static final int REFUND_STATUS_REFUND_CANCEL = 2;
    public static final int REFUND_STATUS_REFUND_SUCCESS = 3;

    // Download
    public static final String DOWNLOAD_FROM_MY_LISTEN = "myListen"; //我听
    public static final String DOWNLOAD_FROM_HOME_PAGE = "homePage"; //首页

    public static final int ALBUM_SLEEP_MODE_ID_FOR_HISTORY = 1; // 和后台协商 albumId为1的表示助眠模式声音
    public static final int ALBUM_ONE_KEY_ID_FOR_HISTORY = 2; // 和后台协商 albumId为2的表示一键听的声音

    public static final int CONTENT_AUDIO = 0; // 音频
    public static final int CONTENT_VIDEO = 1; // 视频

    public static final String ACTION_EVERYDAY_UPDATE_ACTION_CHANGE = "action_everyday_update_action_change";

    public final static String KEY_PATCH_REPORTED = "key_patch_reported";
    public final static String KEY_PATCH_REPORTED_STR = "key_patch_reported_str";

    // 专辑型听单
    public static final int TYPE_TINGLIST_ALBUM = 3;
    // 声音型听单
    public static final int TYPE_TINGLIST_TRACK = 2;

    public static final String KEY_LOCKSCREEN_AUTHORITY_OPEN_GUIDE_SHOW = "key_lockscreen_authority_open_guide_confirm_dialog_showed";

    public static final String GOTO_TOP_BROAD_CAST = "goto_top_broad_cast";

    public static final String KEY_SEARCH_TYPE = "key_search_type";
    public static final int KEY_SEARCH_PLAY_HIS = 0;
    public static final int KEY_SEARCH_READ_HIS = 1;

    public static final int KEY_SEARCH_DOWNLOAD_ALBUM = 2;
    public static final int KEY_SEARCH_DOWNLOAD_TRACK = 3;

    public static final String KEY_EVERYDAY_UPDATE_CLOSE_RECOMMEND_TIME = "key_everyday_update_close_recommend_time";

    public static final String DATA_SKIP_AD_SPLASH = "guiyin=no_ggzs";

    public static final String UBT_TRACE_ID = "ubtTraceId";

    public static final String CHILD_PRIVACY_POLICY_PAGE_URL = "https://passport.ximalaya.com/page/information_protection";

    public static final String ONE_KEY_LISTEN_PAGE_URL = "iting://open?msg_type=74&channelGroupId=1";

    public static String sAllServiceModuleIds = "";

    public static final int PAGE_LOCK_SCREEN_SETTING = 364;

    public static final int REQUEST_CODE_VIDEO_CAPTURE = 6875;

    public static final String ACTION_PAY_DIALOG_FINISH = "com.ximalaya.ting.android.ACTION_PAY_DIALOG_FINISH";

    // RN听单收藏广播
    public static final String LISTEN_ACTION_TYPE_TINGLIST_COLLECT = "LISTEN_ACTION_TYPE_TINGLIST_COLLECT";
    public static final String LISTEN_KEY_TINGLIST_LISTENID = "listenId";
    public static final String LISTEN_KEY_TINGLIST_COLLECT = "collect";

    public static final int REQUEST_CODE_QUERY_FILE = 1080;

    public static final int IM_SESSION_MSG_SYNC_DONE_REQUEST_LIMET = 5;
}
