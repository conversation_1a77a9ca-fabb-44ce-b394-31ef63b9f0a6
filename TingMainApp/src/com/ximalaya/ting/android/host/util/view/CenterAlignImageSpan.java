package com.ximalaya.ting.android.host.util.view;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.style.ImageSpan;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.util.common.ToolUtil;

/**
 * <AUTHOR> on 2018/2/28.
 */

public class CenterAlignImageSpan extends ImageSpan {
    private int marginRightDp;

    public CenterAlignImageSpan(Drawable drawable) {
        super(drawable);

    }

    public CenterAlignImageSpan(Drawable drawable, int marginRight) {
        super(drawable);
        marginRightDp = marginRight;
    }

    public CenterAlignImageSpan(Bitmap b) {
        super(b);
    }

    @Override
    public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, @Nullable Paint.FontMetricsInt fm) {
        Rect bounds = getDrawable().getBounds();
        return bounds.right + marginRightDp;
    }

    @Override
    public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom,
                     @NonNull Paint paint) {

        Drawable b = getDrawable();
        Paint.FontMetricsInt fm = paint.getFontMetricsInt();
//        int transY = (y + fm.descent + y + fm.ascent- b.getBounds().bottom) / 2 ;/ /计算y方向的位移, 这是不对的
        int transY = ((bottom - top) - b.getBounds().bottom) / 2 + top;
        canvas.save();
        canvas.translate(x, transY);//绘制图片位移一段距离
        b.draw(canvas);
        canvas.restore();
    }
}
