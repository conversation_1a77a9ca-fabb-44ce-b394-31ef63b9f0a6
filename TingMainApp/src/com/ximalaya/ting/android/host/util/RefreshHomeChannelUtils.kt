package com.ximalaya.ting.android.host.util

import android.util.Log
import com.ximalaya.ting.android.framework.BaseApplication
import com.ximalaya.ting.android.host.activity.MainActivity
import com.ximalaya.ting.android.host.data.request.HomePageTabRequestTask
import com.ximalaya.ting.android.host.listener.ILoginStatusChangeListener
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction
import com.ximalaya.ting.android.host.manager.login.model.LoginInfoModelNew
import com.ximalaya.ting.android.host.manager.request.CommonRequestM
import com.ximalaya.ting.android.host.util.common.ToolUtil
import com.ximalaya.ting.android.host.util.constant.UrlConstants
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack
import com.ximalaya.ting.android.read.utils.LogUtils
import org.json.JSONObject

object RefreshHomeChannelUtils {

    private const val TAG = "RefreshHomeChannelUtils"

    private var mLoginListener: ILoginStatusChangeListener? = null

    @JvmStatic
    fun init() {
        mLoginListener = object : ILoginStatusChangeListener {

            override fun onLogout(olderUser: LoginInfoModelNew?) {
            }

            override fun onLogin(model: LoginInfoModelNew?) {
                checkRefreshHomeChannel()
            }

            override fun onUserChange(oldModel: LoginInfoModelNew?, newModel: LoginInfoModelNew?) {
            }
        }
        UserInfoMannage.getInstance().addLoginStatusChangeListener(mLoginListener)
    }

    @JvmStatic
    fun destroy() {
        UserInfoMannage.getInstance().removeLoginStatusChangeListener(mLoginListener)
        mLoginListener = null
    }

    private fun checkRefreshHomeChannel() {
        if (!UserInfoMannage.hasLogined()) {
            return
        }
        val map = mutableMapOf<String, String>()
        CommonRequestM.baseGetRequest(UrlConstants.getInstanse().queryRefreshChannelUrl(), map,
            object : IDataCallBack<Boolean?> {
                override fun onSuccess(data: Boolean?) {
                    printLog("checkRefreshHomeChannel success:$data")
                    if (data == true) {
                        refreshHomeChannel()
                    }
                }

                override fun onError(code: Int, message: String?) {
                    printLog("checkRefreshHomeChannel onError code:$code message:$message")
                }

            }
        ) {
            val json = JSONObject(it)
            val data = json.optJSONObject("data")
            return@baseGetRequest data?.optBoolean("isMatch", false) == true
        }
    }

    private var needLoadTabData = false

    @JvmStatic
    fun onMyResume() {

        if (!needLoadTabData) {
            return
        }

        val homeFragment = getHomeFragment()

        if (homeFragment?.canUpdateUi() == true) {
            printLog("onMyResume 刷新tab数据")
            homeFragment.loadTabData()
            needLoadTabData = false
        }
    }

    private fun refreshHomeChannel() {
        val homeFragment = getHomeFragment()

        if (homeFragment == null) {
            HomePageTabRequestTask({
                printLog("未找到首页,记录等会刷新tab数据")
                needLoadTabData = true
            }, null, false).myexec()
            return
        }

        printLog("refreshHomeChannel 开始请求频道:$homeFragment")

        HomePageTabRequestTask({
            if (homeFragment.canUpdateUi()) {
                printLog("首页可见, 直接加载tab数据")
                homeFragment.loadTabData()
            } else {
                printLog("首页不可见,记录等会加载tab数据")
                needLoadTabData = true
            }
        }, null, false).myexec()

    }

    private fun getHomeFragment(): IMainFunctionAction.AbstractHomePageFragment? {
        val activity = BaseApplication.getMainActivity() as? MainActivity

        val homeFragment = activity?.tabFragmentManager?.homePageFragmentFragment

        return homeFragment as? IMainFunctionAction.AbstractHomePageFragment
    }

    private fun printLog(message: String) {
//        LogUtils.d(TAG, message)
    }
}