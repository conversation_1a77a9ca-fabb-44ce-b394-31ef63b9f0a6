package com.ximalaya.ting.android.host.util.constant;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import com.sina.util.dnscache.DNSCache;
import com.ximalaya.ting.android.configurecenter.ConfigureCenter;
import com.ximalaya.ting.android.configurecenter.base.IConfigureCenter;
import com.ximalaya.ting.android.firework.FireworkApi;
import com.ximalaya.ting.android.framework.BaseApplication;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.constant.SpConstants;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.xmlog.XmLogManager;
import com.ximalaya.ting.android.host.util.CategoryCardUtil;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.im.base.constants.IMDevelopeEnviromentConstants;
import com.ximalaya.ting.android.liveim.lib.IMLiveDevelopeEnviromentConstants;
import com.ximalaya.ting.android.loginservice.ConstantsForLogin;
import com.ximalaya.ting.android.opensdk.constants.BaseConstants;
import com.ximalaya.ting.android.opensdk.constants.ConstantsOpenSdk;
import com.ximalaya.ting.android.opensdk.constants.PreferenceConstantsInOpenSdk;
import com.ximalaya.ting.android.opensdk.player.XmPlayerManager;
import com.ximalaya.ting.android.opensdk.util.BaseUtil;
import com.ximalaya.ting.android.opensdk.util.MmkvCommonUtil;
import com.ximalaya.ting.android.routeservice.RouterServiceManager;
import com.ximalaya.ting.android.routeservice.service.xdcs.IXdcsPost;
import com.ximalaya.ting.android.upload.common.XmUploaderConstants;
import com.ximalaya.ting.android.xmgrowth.XmGrowthEnvironment;
import com.ximalaya.ting.android.xmgrowth.XmGrowthManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.net.URL;
import java.util.Locale;

/**
 * Http请求相关的常量
 */

/**
 * TODO
 * 原因：子项目化开发，需要拆分
 * 涉及类：UrlConstants
 * 涉及分支：live2
 * 时间：2017年2月20日
 * 注意：各子项目需要集成这个类，并将归属业务的方法剥离
 *
 * <AUTHOR>
 */
public class UrlConstants {
    public static final String HTTP_DNS_CONFIG_HOST = "http://dns.ximalaya.com/";//获取dns 配置url
    /**
     * http error code
     */
    public static final int REQUEST_ERROR = 0x0001;// 网络异常
    public static final int ANALYSIS_ERROR = 0x0002;// 解析失败
    public static final int REQUEST_SUCCESS = 0x0003;// 请求成功
    public static final int REQUEST_FAIL = 0x0004;// 请求成功
    /**
     * 获取dns 配置url
     */
    public static final String HTTP_DNS_CONFIG = "http://dns.ximalaya.com/xdns/iplist";
    private static final String LIVE_SERVER_ADDRESS = "http://live.ximalaya.com/";// 直播 host
    private static final String KSONG_SERVER_ADDRESS = "http://live.ximalaya.com"; // k歌房host
    private static final String SERVER_SKIN = "http://mobile.ximalaya.com/";
    private static final String SERVER_COUPON_RPC = "http://adcouponrpc.ximalaya.com/";
    private static final String SERVER_LINK_EYE = "http://linkeye.ximalaya.com/";
    private static final String SERVER_VIP_URL = "http://vip.ximalaya.com/";
    /*线上正式 HOST END*/

    private static final String THIRD_PARTY_ADDRESS = " http://3rd.ximalaya.com";
    private static final String THIRD_PARTY_ADDRESS_DEBUG = " http://3rd.test.ximalaya.com";
    private static final String PERSONAL_LIVE_PLAY_STATISTICS = "http://play.ximalaya.com";
    private static final String TRACK_PAY_URI = "https://mpay.ximalaya.com/";
    private static final String TRACK_PAY_URI_DEBUG = "http://mpay.dev.test.ximalaya.com/";
    private static final String TRACK_PAY_URI_UAT = "https://mpay.uat.ximalaya.com/";
    private static final String TRACK_AUDIO_PAY_URI = "http://audio.pay.xmcdn.com/";
    private static final String BUSINESS_HOST = "http://e.ximalaya.com/";
    private static final String BUSINESS_HOST_S = "https://e.ximalaya.com/";
    private static final String AD_WEB_HOST = "http://adweb.ximalaya.com/";
    private static final String OPEN_HOST = "https://open.ximalaya.com/";
    private static final String AD_WEB_MIC_TASK = "http://adweb.ximalaya.com/microtask/app/homepage";
    /**
     * 播放统计
     */
    private static final String PLAY_RECORD = "http://play.ximalaya.com/";
    /*线下DEBUG HOST END*/

    // ******************* 获取各种Host的所有方法 ************************/
    public static String XDCS_COLLECT_ADDRESS = "http://xdcs-collector.ximalaya.com/";
    public static String XDCS_COLLECT_FOR_AD_ADDRESS = "http://adbehavior.ximalaya.com/";
    public static String XDCS_COLLECT_ADDRESS_HTTPS = "https://xdcs-collector.ximalaya.com/";
    public static String XDCS_COLLECT_FOR_AD_ADDRESS_HTTPS = "https://adbehavior.ximalaya.com/";
    public static String SHARE_TRACK = "track";
    public static String SHARE_ALBUM = "album";
    public static String SHARE_USER = "user";
    public static String SHARE_MEMBER = "member";
    public static String SHARE_ACTIVITY = "activity";
    public static String SHARE_ACTIVITY_VOTE = "activity/vote";
    public static String SHARE_ACTIVITY_TRACK = "activity/track";
    public static String SHARE_SPECIAL = "special";
    public static String SHARE_LINK = "link";

    public static String SHARE_RANK = "rank";
    public static String SHARE_PERSONALLIVE = "personallive";
    public static String URL_GET_DUIBAURL = "http://3rd.ximalaya.com/point-thirdparty/duiba/url/create";
    private volatile static UrlConstants singleton;
    /*线上正式 HOST START*/
    private final String SERVER_NET_PUpushReceiveSH = "http://pns.ximalaya.com/";
    private final String SERVER_NET_PUSH = "http://pns.ximalaya.com/";
    private final String SERVER_NET_ADDRESS = "http://mobile.ximalaya.com/";
    private final String SERVER_NET_ADDRESS_S = "https://mobile.ximalaya.com/";
    private final String SERVER_XDCS_XIMALAYA = "http://xdcs.ximalaya.com/";
    private final String SERVER_NET_ADDRESS_M = "http://m.ximalaya.com/";
    protected final String SERVER_NET_ADDRESS_M_S = "https://m.ximalaya.com/";
    public static String SERVER_XIMALAYA_AD = "http://adse.ximalaya.com/";
    private final String SERVER_XIMALAYA_ACT = "http://ad.ximalaya.com/";
    private final String SERVER_ACTIVITY_ADDRESS = "http://activity.ximalaya.com/";
    private final String SERVER_USERINFO_ADDRESS = "http://hybrid.ximalaya.com/";
    private final String SERVER_USERINFO_ADDRESS_S = "https://hybrid.ximalaya.com/";
    private final String SERVER_USERINFO_ADDRESS_UAT = "http://hybrid.uat.ximalaya.com/";
    private final String SERVER_USERINFO_ADDRESS_DEBUG = "http://hybrid.test.ximalaya.com/";
    private final String UPLOAD_NET_ADDRESS = "http://upload.ximalaya.com/";
    private final String SERVER_ZHUBO_HOST = "http://zhubo.ximalaya.com/";
    private final String SERVER_API_ADDRESS_S = "https://api.ximalaya.com/";
    private final String SERVER_NET_ADDRESS_MCD = "http://mres.ximalaya.com/";
    private final String SERVER_NET_PDS_S = "https://pds.ximalaya.com/";
    private final String SERVER_NET_PASSPORT_S = "https://passport.ximalaya.com/";
    private final String SERVER_NET_PASSPORT = "http://passport.ximalaya.com/";
    private final String SERVER_WEIXIN_DAKA_ADDRESS = "http://daka.ximalaya.com/";
    private final String SERVER_WEIXIN_CAMP_ADDRESS = "http://camp.ximalaya.com/";
    private final String SERVER_VIP_COLLECTOR_ADDRESS = "http://vip-collector.ximalaya.com/";
    private final String SERVER_AUDIO_AI_ADDRESS_S = "https://audio-ai.ximalaya.com/";
    private final String SERVER_AUDIO_AI_ADDRESS_WS = "ws://audio-ai.ximalaya.com/";
    private final String SERVER_NEW_CITY_RECOMMEND_ADDRESS_S = "https://citywave.ximalaya.com/";
    private final String SERVER_NET_CHANNEL_S = "https://channel.ximalaya.com/";
    private final String SERVER_NET_KIDS_ADDRESS_S = "https://xxm.ximalaya.com/";
    private final String PAGE_NET_ADDRESS_S = "https://pages.ximalaya.com/";
    private final String SERVER_NET_PUSH_PULL_ADDRESS = "https://pull.ximalaya.com/";
    protected final String SERVER_NET_ANCHOR_AI_ADDRESS = "https://anchorai.ximalaya.com/";
    /******************* 获取URL的所有方法 ************************/
    private final String SEARCH = "http://search.ximalaya.com/";
    public final String SEARCH_URL_HUB = "hub/";    //搜索接口迁移用

    /*******************
     * 获取URL的所有方法
     ************************/
    private final String URL_LOCATION_SERVICE = "http://location.ximalaya.com/";
    private final String SERVER_NET_ADDRESS_AR_TEST = "http://ar.test.ximalaya.com/";
    private final String SERVER_NET_ADDRESS_AR = "http://ar.ximalaya.com/";
    private final String SERVER_NET_ADDRESS_AR_UAT = "http://ar.uat.ximalaya.com/";
    //	private final String SERVER_COMMENT = "http://mobile.ximalaya.com/comment-mobile/";
    private final String SERVER_NET_HOST = "http://www.ximalaya.com/";
    private final String SERVER_NET_HOST_S = "https://www.ximalaya.com/";

    private final String SERVER_MP_ADDRESS = "http://mp.ximalaya.com/";
    private final String SERVER_ADWELFARE = "http://adwelfare.ximalaya.com/";
    private final String SERVER_HOTLINE_ADDRESS = "http://hotline.ximalaya.com/";
    private final String SERVER_RECOMMEND_STREAM = "http://ifm.ximalaya.com/recsys-stream-query/";
    private final String SERVER_RECOMMEND_STREAM_TEST = "http://ifm.test.ximalaya.com/recsys-stream-query/";
    private final String SERVER_RECOMMEND_STREAM_UAT = "http://ifm.uat.ximalaya.com/recsys-stream-query/";
    private final String SERVER_RECOMMEND_NEGATIVE = "http://ifm.ximalaya.com/recsys-negative-service/";
    /*线下DEBUG HOST START*/

    private final String SERVER_ZHUBO_HOST_DEBUG = "http://zhubo.test.ximalaya.com/";
    private final String SERVER_ZHUBO_NEW_HOST_DEBUG = "http://m.test.ximalaya.com/";
    private final String SERVER_TEAMBITION = "http://teambition.ximalaya.com/";
    private final String SERVER_TEAMBITIONFILE = "http://teambitionfile.ximalaya.com/";
    private final String SERVER_PASSPORT_TEST_S = "https://passport.test.ximalaya.com/";
    private final String SERVER_PASSPORT_UAT_S = "https://passport.uat.ximalaya.com/";

    private final String SERVER_MP_ADDRESS_S = "https://mp.ximalaya.com/";
    private final String SERVER_MyClub_MOBILE_HTTP = "http://joinchitchat.com/";
    private final String SERVER_MyClub_MOBILE_HTTP_S = "https://joinchitchat.com/";
    /**
     * 生产环境直播客户端 HTTP 接口域名
     */
    private final String SERVER_LIVE_MOBILE_HTTP = "http://live.ximalaya.com/";
    /**
     * 生产环境直播客户端 HTTPS 接口域名
     */
    private final String SERVER_LIVE_MOBILE_HTTPS = "https://live.ximalaya.com/";

    /**
     * 生产环境直播客户端 HTTPS 接口域名 2
     */
    private final String SERVER_MLIVE_MOBILE_HTTPS = "https://mlive.ximalaya.com/";
    /*
     *获取魅族第三方账号登录url
     */
    private final String MEIZU_TOKEN_URL = "https://open-api.flyme.cn/oauth/token?";

    private final String SERVER_NET_ADDRESS_QF = "http://qf.ximalaya.com/";

    private final String SERVER_NET_AUDIO_AI = "https://audio-ai.ximalaya.com/";

    //文字转声音并朗读功能的接口地址
    private final String SERVER_NET_TEXT_TO_SOUND = "http://ops.ximalaya.com/";

    private final String SERVER_NET_OPS = "https://ops.ximalaya.com/";

    private final String SERVER_NET_NLPALI = "https://nlpali.ximalaya.com/";

    /**
     * 听单详情的url包含
     */
    public static final String SUBJECT_URL = "explore/subject_detail?id=";

    public static UrlConstants getInstanse() {
        if (singleton == null) {
            synchronized (UrlConstants.class) {
                if (singleton == null) {
                    singleton = new UrlConstants();
                }
            }
            if (MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_AD_USE_HTTPS, false)) {
                SERVER_XIMALAYA_AD = "https://adse.ximalaya.com/";
            }
        }
        return singleton;
    }

    public static String getHttpDnsConfigUrl() {
        return BaseUtil.chooseEnvironmentUrl(HTTP_DNS_CONFIG_HOST + "xdns/iplist");
    }

    public String getPlayPageInfoNew() {
        return getServerNetAddressHost() + "mobile-track/playpage";
    }

    public String getPlayPageTabAndInfoUrlFormat() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/tabs/v2/%d/";
    }

    public String getPlayPageRecommendDataUrl() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/recommendInfo/";
    }

    /**
     * 播放页推荐单列流接口
     */
    public String getPlayPageRecommendInfoFeedUrl() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/recommendInfoFeed/";
    }

    public String getPlayPageQualityListUrlFormat() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/track/quality/%d/";
    }

    public String getPlayPageQualityAndEffectListUrlFormat() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/track/qualityAndEffect/%d/";
    }

    public String getPlayPageSoundEffectListUrl(long trackId) {
        return getServerNetAddressHost() + "mobile-playpage/playpage/sound/effects/" + trackId + "/ts-" + System.currentTimeMillis();
    }

    /**
     * 播放地址防盗链获取获取视频加密信息地址
     *
     * @return
     */
    public static String getVideoPlayUrl() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI + "mobile/track/pay/",
                TRACK_PAY_URI_DEBUG + "mobile/track/pay/",
                TRACK_PAY_URI_UAT + "mobile/track/pay/");

    }

    /**
     * 播放地址防盗链获取获取声音加密信息地址
     *
     * @return
     */
    public static String getTrackPayV2Url() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI + getTrackPayPath(),
                TRACK_PAY_URI_DEBUG + getTrackPayPath(),
                TRACK_PAY_URI_UAT + getTrackPayPath());

    }

    public static String getTrackPayPath() {
        return "mobile/track/pay/v2/";
    }

    //获取文件详情接口，下载文档
    public static String getFileUrl(String feedIdString) {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return "http://mpay.ximalaya.com/" + "chaos/v1/feeds/" + feedIdString + "/files";
        } else if (BaseConstants.ENVIRONMENT_UAT == BaseConstants.environmentId) {
            return "http://mpay.uat.ximalaya.com/" + "chaos/v1/feeds/" + feedIdString + "/files";
        } else {
            return "http://*************/" + "chaos/v1/feeds/" + feedIdString + "/files";
        }
    }

    /**
     * 播放地址防盗链获取获取声音加密信息下载地址
     * 比播放地址多了个"download"
     *
     * @return
     */
    public static String getTrackDownloadV2Url() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI + "mobile/track/pay/download/v2/",
                TRACK_PAY_URI_DEBUG + "mobile/track/pay/download/v2/",
                TRACK_PAY_URI_UAT + "mobile/track/pay/download/v2/");
    }

    public static String getVideoInfoUrl() {
        // TODO: uat?
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI + "product/album/",
                TRACK_PAY_URI_FOR_WEIKE_TEST + "product/album/", "");
    }

    public String getRichAudioInfo() {
        return getServerNetAddressHost() + "mobile-playpage/track/richAudioInfo";
    }

    /**
     * 播放地址防盗链获取获取声音加密信息地址
     *
     * @return
     */
    public static String getTrackPayHost() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI,
                TRACK_PAY_URI_DEBUG,
                TRACK_PAY_URI_UAT);
    }

    // 老版听头条专辑获取地址
    public String getHeadLineList() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/trackItems/ts-";
    }

    // 新版听头条专辑获取地址
    public String getHeadLineListNew() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v3/trackItems/ts-";
    }

    // 今日热点新接口 PushGuardPlayerManager也用到了
    public String getDailyNews3() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v5/trackItems/ts-";
    }

    // 今日热点推荐主播列表接口
    public String getDailyNewsRecommendAnchors() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/rec/anchors";
    }

    // 今日热点推荐专辑列表接口
    public String getDailyNewsRecommendAlbums() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/rec/albums";
    }

    // 今日热点推荐主播列表接口
    public String getDailyNewsUpdateAnchorFollow() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/record/update";
    }

    // push推荐新接口 PushGuardPlayerManager也用到了
    public String getPushRecommendTracks() {
        return getServerNetAddressHost() + "keep-alive-player-mobile/playNotifyContent/query/ts-";
    }

    // 今日热点ab接口
    public String getDailyNews3Ab() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/queryAB/ts-";
    }

    // 今日热点置顶声音不喜欢接口
    public String getDailyNews3Dislike() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/disLike/ts-";
    }

    // 今日热点置顶声音不喜欢接口V2
    public String getDailyNews3DislikeV2() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v2/disLike/ts-";
    }

    // 今日热点专题接口
    public String updateDailyNews3Topic() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/channelHeadComponentItems/ts-";
    }

    // 流播放页接口
    public String getUnLimitedTrackFeed() {
        return getServerNetAddressHost() + "discovery-feed/unlimitedTrackFeed/ts-";
    }

    // 听头条更新数量获取地址
    public String getDailyNewsUpdateCount() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/getCountByStartTime";
    }

    /**
     * 该方法无测试地址，故可以写死
     *
     * @return
     */
    public static String getTrackPayDownloadUrl() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_AUDIO_PAY_URI + "download/");
    }

    public static String getPlayRecordHost() {
        return BaseUtil.chooseEnvironmentUrl(PLAY_RECORD);
    }

    public static String getPlayRecordUrl() {
        return getPlayRecordHost() + "mobile/tracks/record/t";
    }

    public static String getPlayLiveRecordUrl() {
        return getPlayRecordHost() + "live/listened/record";
    }

    public void switchOnline(int environment) {
        AppConstants.environmentId = environment;
        DNSCache.environmentId = environment;
        XmUploaderConstants.mEnvironmentId = environment;
        IMDevelopeEnviromentConstants.switchDevelopEnvironment(environment);
        IMLiveDevelopeEnviromentConstants.switchDevelopEnvironment(environment);


        if (BaseUtil.isMainProcess(BaseApplication.getMyApplicationContext())) {

            XmPlayerManager.setPlayerProcessRequestEnvironment(AppConstants.environmentId);

            if (environment == AppConstants.ENVIRONMENT_ON_LINE) {
                ConfigureCenter.getInstance().switchEnvironment(BaseApplication.getMyApplicationContext(), IConfigureCenter.Environment.ENVIRONMENT_ON_LINE);
                FireworkApi.getInstance().setEnvironment(com.ximalaya.ting.android.firework.UrlConstants.Environment.ENVIRONMENT_ON_LINE);
                XmGrowthManager.INSTANCE.setEnvironment(XmGrowthEnvironment.ENVIRONMENT_ON_LINE);
            } else if (environment == AppConstants.ENVIRONMENT_TEST) {
                ConfigureCenter.getInstance().switchEnvironment(BaseApplication.getMyApplicationContext(), IConfigureCenter.Environment.ENVIRONMENT_TEST);
                FireworkApi.getInstance().setEnvironment(com.ximalaya.ting.android.firework.UrlConstants.Environment.ENVIRONMENT_TEST);
                XmGrowthManager.INSTANCE.setEnvironment(XmGrowthEnvironment.ENVIRONMENT_TEST);
            } else {
                ConfigureCenter.getInstance().switchEnvironment(BaseApplication.getMyApplicationContext(), IConfigureCenter.Environment.ENVIRONMENT_UAT);
                FireworkApi.getInstance().setEnvironment(com.ximalaya.ting.android.firework.UrlConstants.Environment.ENVIRONMENT_UAT);
                XmGrowthManager.INSTANCE.setEnvironment(XmGrowthEnvironment.ENVIRONMENT_UAT);
            }
        } else if (BaseUtil.isPlayerProcess(BaseApplication.getMyApplicationContext())) {
            if (environment == AppConstants.ENVIRONMENT_ON_LINE) {
                ConfigureCenter.getInstance().switchEnvironment(BaseApplication.getMyApplicationContext(), IConfigureCenter.Environment.ENVIRONMENT_ON_LINE);
            } else if (environment == AppConstants.ENVIRONMENT_TEST) {
                ConfigureCenter.getInstance().switchEnvironment(BaseApplication.getMyApplicationContext(), IConfigureCenter.Environment.ENVIRONMENT_TEST);
            } else {
                ConfigureCenter.getInstance().switchEnvironment(BaseApplication.getMyApplicationContext(), IConfigureCenter.Environment.ENVIRONMENT_UAT);
            }
        }


//        FireworkApi.getInstance().setEnvironment(FireworkApi.Environment.ENVIRONMENT_TEST);// 测试 上线时去掉

        if (environment == AppConstants.ENVIRONMENT_ON_LINE) {
            ConstantsForLogin.environmentId = ConstantsForLogin.ENVIRONMENT_ON_LINE;
        } else if (environment == AppConstants.ENVIRONMENT_TEST) {
            ConstantsForLogin.environmentId = ConstantsForLogin.ENVIRONMENT_TEST;
        } else if (environment == AppConstants.ENVIRONMENT_UAT) {
            ConstantsForLogin.environmentId = ConstantsForLogin.ENVIRONMENT_UAT;
        }

        XmLogManager.setEnvironment();
    }

    public String getPushPullAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PUSH_PULL_ADDRESS);
    }

    /**
     * 获取移动端host地址，http://mobile.ximalaya.com/
     */
    public String getServerNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS);
    }

    public String getServerXdcsAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_XDCS_XIMALAYA);
    }

    /**
     * http://daka.ximalaya.com/
     */
    public String getServerDakaAddress() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_WEIXIN_DAKA_ADDRESS);
    }

    /**
     * http://camp.ximalaya.com/
     */
    public String getServerCampAddress() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_WEIXIN_CAMP_ADDRESS);
    }

    /**
     * 获取新的插件，RN，静态资源服务器地址，http://mcd.ximalaya.com/
     */
    public String getServerNetMcdAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_MCD);
    }

    public String getMineLiveUrl() {
        return getLiveServerHost() + "lamia/v1/subscribe/status";
    }

    public String getMcMineLiveUrl() {
        return getMyClubMobileHttpsHost() + "chitchat-mobile-web/api/v1/sdk_query/following_living_status";
    }

    /**
     * 获取移动端host地址，http://mobile.ximalaya.com/
     */
    public String getServerNetSAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_S);
    }

    /**
     * 奇妙思维后端host地址, https://channel.ximalaya.com/
     */
    public String getServerNetSChannelHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_CHANNEL_S);
    }

    /**
     * 亲子儿童后端host地址，https://xxm.ximalaya.com/
     */
    public String getServerNetKidsAddressHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_KIDS_ADDRESS_S);
    }

    /**
     * https://pages.ximalaya.com/
     */
    public String getPageNetSHost() {
        return BaseUtil.chooseEnvironmentUrl(PAGE_NET_ADDRESS_S);
    }

    /**
     * 获取主播服务host地址，http://zhubo.test.ximalaya.com/
     */
    public String getZhuBoServerHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_USERINFO_ADDRESS, SERVER_ZHUBO_HOST_DEBUG, SERVER_USERINFO_ADDRESS_UAT);
    }

    /**
     * 获取新的主播服务host地址，http://m.test.ximalaya.com/
     * 配乐后台的项目全部迁到barge了，自动配置得域名和以前手动配置得不一样了
     */
    public String getNewZhuBoServerHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_USERINFO_ADDRESS, SERVER_ZHUBO_NEW_HOST_DEBUG, SERVER_USERINFO_ADDRESS_UAT);
    }

    public String getQfServerNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_QF);
    }

    public String getAudioAIAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_AUDIO_AI);
    }

    public String getVipNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_VIP_URL);
    }

    /**
     * 推送域名
     */
    public String getServerPushHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PUSH);
    }

    public String getSERVER_XIMALAYA_AD() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_XIMALAYA_AD);
    }

    /**
     * 获取adse 域名，因为test环境和 online环境不一致，需要单独处理
     *
     * @return
     */
    public String getSERVER_XIMALAYA_ADSE() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return BaseUtil.chooseEnvironmentUrl(SERVER_XIMALAYA_AD);
        } else if (BaseConstants.ENVIRONMENT_TEST == BaseConstants.environmentId) {
            return "http://ad.test.ximalaya.com/";
        }
        return BaseUtil.chooseEnvironmentUrl(SERVER_XIMALAYA_AD);
    }

    public String getPureSERVER_XIMALAYA_ADSE() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_XIMALAYA_AD);
    }


    public String getSERVER_XIMALAYA_ACT() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_XIMALAYA_ACT);
    }

    public String getUploadNetAddress() {
        return BaseUtil.chooseEnvironmentUrl(UPLOAD_NET_ADDRESS);
    }

    /**
     * 获取活动的host地址
     */
    public String getActivitiesHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_ACTIVITY_ADDRESS);
    }

    public String getRedeemCodeWebUrl(String redeemCode) {
        if (TextUtils.isEmpty(redeemCode)) {
            return getMNetAddressHostS() + "gatekeeper/hybrid-cdkeys-new/exchange";
        } else {
            return getMNetAddressHostS() + "gatekeeper/hybrid-cdkeys-new/exchange?redeemCode=" + redeemCode;
        }
    }

    /**
     * 获取用户信息的host地址
     */
    public String getHybridHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_USERINFO_ADDRESS);
    }

    public String getHybridHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_USERINFO_ADDRESS_S);
    }

    /**
     * 获取用户信息的host地址
     */
    public String getHybridHostEnv() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_USERINFO_ADDRESS, SERVER_USERINFO_ADDRESS_DEBUG, SERVER_USERINFO_ADDRESS_UAT);
    }

    /*
     * 录音tcp上传，获取ip的host.
     */
    public String getServerLinkEyeHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_LINK_EYE);
    }

    /**
     * 检查jssdk的host地址
     */
    public String getCheckJsApiHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_API_ADDRESS_S + "v2/app/check_js_api");
    }

    /**
     * 获取电台信息host地址
     */
    public String getServerRadioHost() {
        return getLiveServerHost() + "live-web/v1/";
    }

    /**
     * 获取定位host,17/04/27修改,{ http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/location/web-api.md}
     *
     * @return
     */
    public String getLocationHost() {
        return BaseUtil.chooseEnvironmentUrl(URL_LOCATION_SERVICE + "location-web/");
    }

    /**
     * 获取搜索host地址
     */
    public String getSearchHost() {
        return BaseUtil.chooseEnvironmentUrl(SEARCH);
    }

    public String getARHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_AR, SERVER_NET_ADDRESS_AR_TEST, SERVER_NET_ADDRESS_AR_UAT);
    }

    public String getMNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_M);
    }

    public String getMNetAddressHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_M_S);
    }

    public String getAudioAiNetAddressHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_AUDIO_AI_ADDRESS_S);
    }

    public String getAudioAiNetAddressHostWS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_AUDIO_AI_ADDRESS_WS);
    }

    public String getNewCityRecommendAddressHost() {
        if (CategoryCardUtil.isUseTestDomainInFormalEnvironment()) {
            return "http://citywave.test.ximalaya.com/";
        }
        return BaseUtil.chooseEnvironmentUrl(SERVER_NEW_CITY_RECOMMEND_ADDRESS_S);
    }

    public String getNewCitySceneryAddressHost() {
        if (CategoryCardUtil.isUseTestDomainInFormalEnvironment()) {
            return "http://mobile.test.ximalaya.com/";
        }
        return BaseUtil.chooseEnvironmentUrl(SERVER_NEW_CITY_RECOMMEND_ADDRESS_S);
    }

    public String getVipCollectorAddress() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_VIP_COLLECTOR_ADDRESS);
    }

    public String getXDCSCollectAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_ADDRESS);
    }

    public String getXDCSCollectForAdAddressHost() {
        if (MmkvCommonUtil.getInstance(BaseApplication.getMyApplicationContext()).getBoolean(PreferenceConstantsInOpenSdk.KEY_AD_USE_HTTPS, false)) {
            return BaseUtil.chooseEnvironmentUrl(BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_FOR_AD_ADDRESS_HTTPS),
                    BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_ADDRESS_HTTPS), // 2020-12-18 测试环境 用 http://xdcs-collector.test.ximalaya.com/
                    BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_FOR_AD_ADDRESS_HTTPS));
        } else {
            return BaseUtil.chooseEnvironmentUrl(BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_FOR_AD_ADDRESS),
                    BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_ADDRESS), // 2020-12-18 测试环境 用 http://xdcs-collector.test.ximalaya.com/
                    BaseUtil.chooseEnvironmentUrl(XDCS_COLLECT_FOR_AD_ADDRESS));
        }
    }

    /**
     * https://passport.ximalaya.com
     */
    public String getServerPassportAddressHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PASSPORT_S);
    }

    /**
     * http://passport.ximalaya.com
     */
    public String getServerPassportAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PASSPORT);
    }

    /**
     * https://pds.ximalaya.com
     */
    public String getPdsHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PDS_S);
    }

    /**
     * https://passport.ximalaya.com
     * https://passport.test.ximalaya.com/
     * https://passport.uat.ximalaya.com/
     */
    public String getServerPassportHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PASSPORT_S, SERVER_PASSPORT_TEST_S, SERVER_PASSPORT_UAT_S);
    }

    /**
     * 主播会员host
     *
     * @return
     */
    public String getMemberAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_MP_ADDRESS);
    }

    /**
     * 获取热线的host地址
     */
    public String getHotLineHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_HOTLINE_ADDRESS);
    }

    /**
     * 充值接口host
     *
     * @return
     */
    public String getMpAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_MP_ADDRESS);
    }

    /**
     * ops 接口
     *
     * @return
     */
    public String getOPSNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_OPS);
    }

    public String getNlpNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_NLPALI);
    }
    public String getMpAddressHostS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_MP_ADDRESS_S);
    }

    public String getRecommendFlowHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_RECOMMEND_STREAM, SERVER_RECOMMEND_STREAM_TEST, SERVER_RECOMMEND_STREAM_UAT);
    }

    public String getRecommendNegative() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_RECOMMEND_NEGATIVE);
    }

    public String getCommentBaseUrl() {
        return getCommentHost() + "comment-mobile/";
    }

    public String getCommentHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS);
    }

    public String getPassportAddressHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PASSPORT);
    }

    public String getPassportAddressHosts() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_PASSPORT_S);
    }

    /**
     * 优惠券
     */
    public String getAdWelfAreHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_ADWELFARE);
    }

    public String getServerCouponRpc() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_COUPON_RPC);
    }

    // ******************* 获取URL的所有方法 ************************/

    /**
     * 新版广播页host
     */
    public String getRadioHostV2() {
        return getLiveServerHost() + "live-web/v2/";
    }

    /**
     * 新版广播页host
     */
    public String getRadioHostV4() {
        return getLiveServerHost() + "live-web/v4/";
    }

    /**
     * 新版广播页host 201801版本加
     */
    public String getRadioHostV5() {
        return getLiveServerHost() + "live-web/v5/";
    }

    /**
     * 强制绑定手机号url
     */
    public String getForceBindPhoneUrl() {
        return getHybridHost() + "api/bind/force_bind_phone?";
    }

    /**
     * 绑定手机号url
     */
    public String getBindPhoneUrl() {
        return getHybridHost() + "api/bind/bind_phone";
    }

    public String getCheckUpdateUrl() {
        return getServerNetAddressHost() + "v1/mobile/version";
    }

    public String getCheckUpdateUrlV2() {
        return getServerNetSAddressHost() + "v1/mobile/version2";
    }

    public String getCheckUpdateNewUrl() {
        return getServerNetAddressHost() + "butler-portal/versionCheck/ts-" + System.currentTimeMillis();
    }

    public String getNoticeAfterVersionUpdateUrl() {
        return getServerNetAddressHost() + "butler-portal/report/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取地址
     */
    public String getLocation() {
        return getServerNetAddressHost() + "mobile/discovery/v2/location";
    }

    /**
     * 获取用户积分
     */
    public String getUserPointsUrl() {
        return getServerNetAddressHost() + "mobile/" + AppConstants.API_VERSION
                + "/point/query";
    }

    /**
     * 收藏专辑同步
     */
    public String getPostCollectAlbumsCollect() {
        return getHybridHost() + "mobile/album/subscribe/batch";
    }

    /**
     * 赚积分配置获取
     */
    public String getBehaviorScore() {
        return getServerNetAddressHost() + "mobile/" + AppConstants.API_VERSION
                + "/point/config";
    }

    public String getAlbumData() {
        return getServerNetAddressHost() + "mobile/others/ca/album/track";
    }

    public String getAlbumDataForCount() {
        return getServerNetAddressHost() + "mobile/others/album/track";
    }

    public String getAlbumMyData() {
        return getServerNetAddressHost() + "mobile/my/album/track";
    }

    public String getAlbumHomePage() {
        return getServerNetAddressHost() + "mobile/v1/album";
    }

    public String getAlbumHomePageNew() { // 新的请求专辑接口
        return getServerNetAddressHost() + "mobile-album/album/page";
    }

    public String getTrackTopRecord() { // 声音置顶
        return getServerNetAddressHost() + "album-mobile-writer/studio/album/topRecord";
    }

    public String getTrackCancelTopRecord() { // 声音取消置顶
        return getServerNetAddressHost() + "album-mobile-writer/studio/album/cancelTopRecord";
    }

    public String getAlbumTrackList() {
        return getServerNetAddressHost() + "mobile/v1/album/track";
    }

    public String getAlbumTrackListV2() {
        return getServerNetAddressHost() + "mobile/v1/album/track/v2";
    }

    public String getAlbumPlayList() {
        return getServerNetAddressHost() + "mobile/playlist/album/new";
    }

    public String getNoPrivacyXdcsUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/first/xdcs";
    }

    public String getRecommendPlayList(long trackId) {
        return getRecommendPlayListBaseUrl() + trackId;
    }

    public String getRecommendPlayListBaseUrl() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/queryRecommendTrack/";
    }

    public String getTrainingCampPlayList() {
        return getMNetAddressHost() + "business-trainingcamp-mobile-web/play/audit/list";
    }

    public String getTrainingCampVideoList() {
        return getMNetAddressHost() + "business-trainingcamp-mobile-web/play/video/list";
    }

    public String getTrainingCampVideoListByTrackId() {
        return getMNetAddressHost() + "business-trainingcamp-mobile-web/play/video/listById";
    }

    public String getTrainingCampCourseReportUrl() {
        return getMNetAddressHost() + "business-trainingcamp-mobile-web/client/clock/completeContent";
    }

    public String getAlbumDetail() {
        return getServerNetAddressHost() + "mobile/v1/album/detail";
    }

    public String getAlbumPage3Data() {
        return getServerNetAddressHost() + "mobile-album/album/plant/grass";
    }

    public String getReserveAlbumData() {
        return getServerNetAddressHost() + "mobile-album/album/preserve/detail";
    }

    public String getActivities() {
        return getActivitiesHost() + "activity-web/activity/activityList";
    }

    public String getActivity() {
        return getActivitiesHost() + "activity-web/activity";
    }

    public String getSearchSuggestUrl() {
        return getSearchHost() + getSearchHub() + AppConstants.SUGGEST + "/v2/2.3";
    }

    public String getSearchHub() {
        //搜索接口域名迁移
        if (ConfigureCenter.getInstance().getBool("toc", "search_service_interface_new", true)) {
            return SEARCH_URL_HUB;
        } else {
            return "";
        }
    }

    public String getSearchUrl() {
        return getSearchHost() + getSearchHub() + AppConstants.FRONT_VERSION;
    }

    public String getSearchConfig() {
        return getSearchHost() + getSearchHub() + "searchConfig/v2";
    }

    public String getSearchFeedBackTypesUrl() {
        return getSearchHost() + "feedback/noResultType";
    }

    public String getSearchFeedBackUrl() {
        return getSearchHost() + "feedback/noResult";
    }

    public String getAiSearchFeedBackUrl() {
        return getNlpNetAddressHost() + "nlp-llm-mid-core/llm/feedback";
    }

    public String getAiSearchCancelFeedBackUrl() {
        return getNlpNetAddressHost() + "nlp-llm-mid-core/llm/cancel_feedback";
    }

    public String getAiSearchCloseUrl() {
        return getSearchHost() + "hub/starArch/off";
    }

    public String getSearchCategoryUrl(String path) {
        return getSearchHost() + "front/" + path + "/category";
    }

    public String getSearchHotWordUrl() {
        return getSearchHost() + getSearchHub() + "hotWordV3/1.0";
    }

    public String getSearchGuideUrl() {
        return getSearchHost() + getSearchHub() + "guideWordV3/1.2";
    }

    /**
     * 获取电台排行榜
     */
    public String getRadioTopListUrl() {
        return getServerRadioHost() + AppConstants.RADIO_TOP_LIST;
    }

    /**
     * 获取电台列表
     */
    public String getRadioListByTypeUrl() {
        return getServerRadioHost() + AppConstants.RADIO_LIST_BYTYPE;
    }

    /**
     * 获取省份列表
     */
    public String getRadioProvinceListUrl() {
        return getServerRadioHost() + AppConstants.RADIO_PROVINCE_LIST;
    }

    /**
     * 获取首页运营推荐电台和排行榜电台
     */
    public String getRadioHomePageListUrl() {
        return getServerRadioHost() + AppConstants.RADIO_HOME_PAGE_LIST;
    }

    /**
     * 获得弹幕
     */
    public String getDanMu() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/list/ts-" + System.currentTimeMillis();
    }

    public String getDanMuV2() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/v2/list";
    }

    /**
     * 获取弹幕点赞信息
     */
    public String getDanmuLikeInfoUrl() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/like/info";
    }

    /**
     * 获得音频的详细信息
     */
    public String getTrackInfo() {
        return getServerNetAddressHost() + getTrackInfoPath() + System.currentTimeMillis();
    }

    public String getTrackInfoV4() {
        return getServerNetAddressHost() + getTrackV4BaseInfoPath();
    }

    public String characterConfess() {
        return getMNetAddressHost() + "community-widget/api/v1/confession/characterConfess";
    }

    public String getTrackInfoPath() {
        return "mobile-playpage/track/v3/baseInfo/";
    }

    public String getTrackV4BaseInfoPath() {
        return "mobile-playpage/track/v4/baseInfo/";
    }

    /**
     * 批量查询声音信息
     * /mobile/track/baseInfo/list
     */
    public String getTrackListInfo() {
        return getServerNetAddressHost() + "mobile/track/baseInfo/list/" + System.currentTimeMillis();
    }

    public String getTrackListInfoV2() {
        return getServerNetAddressHost() + "mobile/track/baseInfo/v2/list/" + System.currentTimeMillis();
    }

    /**
     * 获取当前用户是否是会员
     */
    public String getIsVip() {
        return getMNetAddressHost() + "vip/check/user/ts-" + System.currentTimeMillis();
    }

    public String fetchUserVipInfo(){
        return getServerNetAddressHost() + "business-vip-center-mobile-web/user/vip-info/v1/" + System.currentTimeMillis();
    }

    /**
     * 获得音频的详细信息
     * car分支专用
     */
    public String getTrackInfoForCar() {
        return getServerNetAddressHost() + "v1/track/baseInfo";
    }

    /**
     * 818弹窗曝光（频控用）
     */
    public String getAlbum818WebDialogExploreUrl() {
        return getMNetAddressHostS() + "business-pgc-adapter-mobile-web/popup/expose";
    }

    /**
     * 获得打赏的人
     */
    public String getReWardList() {
        return getHybridHost() + "ting-shang-mobile-web/v1/rewardOrders/track/";
    }

    /**
     * 获得榜单页面
     */
    public String getRankList() {
        return getServerNetAddressHost()
                + "mobile/discovery/v2/rankingList/group";
    }

    /**
     * 获得主播页面
     */
    public String getRecommendAnchorList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/anchor/recommend";
    }

    /**
     * 榜单声音
     */
    public String getRankTrackList() {
        return getServerNetAddressHost()
                + "mobile/discovery/v2/rankingList/track";
    }

    /**
     * 榜单声音
     */
    public String getRankTrackListV3() {
        return getServerNetAddressHost()
                + "mobile/discovery/v3/rankingList/track";
    }

    /**
     * 榜单专辑
     */
    public String getRankAlbumList() {
        return getServerNetAddressHost()
                + "mobile/discovery/v1/rank/album";
    }

    /**
     * 榜单专辑
     */
    public String getRankAlbumListV3() {
        return getServerNetAddressHost()
                + "mobile/discovery/v3/rankingList/album";
    }

    /**
     * 榜单主播
     */
    public String getRankAnchorList() {
        return getServerNetAddressHost()
                + "mobile/discovery/v2/rankingList/anchor";
    }

    /**
     * 榜单主播
     */
    public String getRankAnchorListV3() {
        return getServerNetAddressHost()
                + "mobile/discovery/v3/rankingList/anchor";
    }

    /**
     * 获得某一类别下主播列表
     */
    public String getCommonAnchorList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/anchor/normal";
    }

    /**
     * 获得某一类别下名人主播列表
     */
    public String getFamousList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/anchor/famous";
    }

    public String getAnchorAllTrack() {
//		return getServerNetAddressHost() + "mobile/others/ca/track";
        return getServerNetAddressHost() + "mobile/v1/artist/tracks";
    }

    public String getAnchorAllTrackNew() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/tracks";
    }

    /**
     * 获得主播详细信息
     */
    public String getAnchorDetail() {
        return getServerNetAddressHost() + "mobile/others/ca/homePage";
    }

    /**
     * 获得主播的直播
     */
    public String getAnchorRadio() {
        return getServerNetAddressHost() + "mobile/others/live";
    }


    /**
     * 获得主播所有的专辑
     */
    public String getAnchorAllAlbum() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/albums";
    }

    /**
     * 获得我的所有的专辑
     */
    public String getMyAllAlbum() {
//		return getServerNetAddressHost() + "mobile/others/ca/album";
        return getServerNetAddressHost() + "mobile/v1/my/albums";
    }

    /**
     * 获得我的所有的专辑-新
     */
    public String getMyAllAlbumNew() {
        return getServerNetAddressHost() + "mobile-user/my/albums";
    }

    public String getMyTracksInfoNew() {
        return getServerNetAddressHost() + "mobile-user/my";
    }

    /**
     * 获得主播关注的人
     */
    public String getUserFollowPeople() {
        return getServerNetAddressHost() + "mobile/others/following";
    }

    /**
     * 获得用户的粉丝
     */
    public String getUserFensPeople() {
        return getServerNetAddressHost() + "mobile/others/follower";
    }

    /**
     * 获得用户点赞的声音
     */
    public String getUserFavoritTrack() {
        return getServerNetAddressHost() + "favourite-business/favorite";
    }

    /**
     * 我的喜欢推荐声音
     */
    public String getMyLikeRecommendData() {
        return getServerNetAddressHost() + "favourite-business/recommendTrack/query/ts-";
    }

    /**
     * 添加收藏 (POST请求)
     */
    public String collectAlbumAdd() {
        return getServerNetAddressHost() + "subscribe/album/subscribe/create";
    }

    /**
     * 添加收藏 (POST请求)
     */
    public String collectAlbumAddAndAnchor() {
        return getServerNetAddressHost() + "subscribe/album-subscribe/and/ask-anchor-info";
    }

    /**
     * 批量订阅专辑
     */
    public String batchAlbumSubscribe() {
        return getServerNetAddressHost() + "subscribe/album/subscribe/batch";
    }

    public String batchCreateAlbumSubscribe() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/batch-create";
    }

    /**
     * 删除收藏 (POST请求)
     */
    public String collectAlbumDel() {
        return getServerNetAddressHost() + "subscribe/album/subscribe/delete";
    }

    /**
     * 评论喜欢或不喜欢(POST请求)
     */
    public String commentLike() {
        return getServerNetAddressHost() + "comment-mobile/like";
    }

    /**
     * 弹幕喜欢或不喜欢(POST请求)
     */
    public String getCommentBulletLikeUrl() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/v2/like";
    }

    /**
     * 评论删除
     */
    public String commentDel() {
        return getServerNetAddressHost() + "mobile/comment/delete";
    }

    public String getGrowAndPublicityEnter() {
        return getMNetAddressHost() + "anchor-record-web/myPage/growAndPublicityEnter";
    }

    /**
     * 获取主页信息
     */
    public String getHomePage() {
        return getServerNetAddressHost() + "mobile/homePage";
    }

    /**
     * 获取我页入口配置
     */
    public String getHomePageEntrance() {
        return getServerNetAddressHost() + "mobile-user/homePage/entrance";
    }

    /**
     * 查询指定模块下的入口配置
     */
    public String getModuleEntrance() {
        return getServerNetAddressHost() + "mobile-user/homePage/module/entrance";
    }


    //    public String getCustomFeed() {
//        return getServerNetAddressHost() + "feed/v4/feed/dynamic";
//    }
    public String getCustomFeed() {
        return getServerNetAddressHost() + "subscribe/v6/subscribe/dynamic/ts-";
    }


    public String follow() {
        return getServerNetAddressHost() + "mobile/follow";
    }

    /**
     * 分享信息获取
     */
    public String getShareSetting() {
        return getServerNetAddressHost() + "mobile/sync/get";
    }

    /**
     * 评论
     */
    public String getSendCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/create";
    }

    /**
     * 讨论下的评论
     */
    public String getSendTalkCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/talk/comments/create";
    }

    public String getSendVoteCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/vote/comments/create";
    }

    public String getSendSequentialVoteCommentUrl() {
        return getServerNetAddressHost() + "comment-mobile/sequentialVote/comments/create";
    }

    public String createCommentUniversal() {
        return getServerNetAddressHost() + "universal-comment-mobile/comment/create";
    }

    public String getQuickCommentTemplateUrl(long timeMs) {
        return getServerNetAddressHost() + "comment-mobile/getQuickCommentTemplate/" + timeMs;
    }

    public String getQuickBulletTemplateUrl(long timeMs) {
        return getServerNetAddressHost() + "barrage-mobile/barrage/getBarrageTemplate/" + timeMs;
    }

    /**
     * 发送弹幕
     */
    public String getSendDanmuUrl() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/create";
    }

    /**
     * 分页获取弹幕列表，弹幕V2接口
     */
    public String getDanmuListUrl(long timeMs) {
        return getServerNetAddressHost() + "barrage-mobile/barrage/v2/layer/list/" + timeMs;
    }

    /**
     * 删除一条弹幕
     */
    public String getDeleteDanmuUrl() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/v2/remove";
    }

    /**
     * 发送ai文稿的反馈
     */
    public String getSendAiFeedbackUrl() {
        return getAudioAiNetAddressHostS() + "has/asr/api/v1/feedback/add";
    }

    public String getAsrSseUrl() {
        return getAudioAiNetAddressHostS() + "has/asr/stream/api/v1/sync/sse";
    }

    public String getAsrWebSocketUrl() {
        return getAudioAiNetAddressHostWS() + "has/asr/stream/api/v1/ws";
    }

    /**
     * 主播关注(POST 请求)
     */
    public String AnchorFollowUrl() {
        return getServerNetAddressHost() + "mobile/follow";
    }

    /**
     * 关注主播(POST 请求) - 8.3.30 版本后加入，增加数据上报
     */
    public String getFollowUrl() {
        return getServerNetAddressHost() + "fans/follow";
    }

    /**
     * 批量关注主播
     */
    public String getMultiFollowUrl() {
        return getServerNetAddressHost() + "fans/v2/multi_follow";
    }

    /**
     * 获取推荐收藏数据列表
     */
    public String getCollectionUrl() {
        return getServerNetAddressHost() + "subscribe/v3/subscribe/list";
    }

    public String matchDriveModeBluetoothDeviceName() {
        return getServerNetAddressHost() + "mobile-driving-mode/mobile/drivingMode/match";
    }

    public String matchDriveModeBluetoothDeviceNameV2() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/match";
    }

    public String recordDriveModeBluetoothDeviceName() {
        return getServerNetAddressHost() + "mobile-driving-mode/mobile/drivingMode/record";
    }

    public String recordDriveModeBluetoothDeviceNameV2() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/save";
    }

    // 一键听保存子频道
    public String getSaveSubChannels() {
        return getServerNetAddressHost() + "discovery-feed/related/onekey/sub/saveChannels/";
    }

    // 一键听 加载 声音列表url
    public String getOneKeyListenNewPlusQuery() {
        return getServerNetAddressHost() + "discovery-feed/related/onekey/recTrack/ts-";
    }

    // 专辑视频列表加载
    public String getAlbumVideoList() {
        return getServerNetAddressHost() + "mobile-album/album/video";
    }

    // 专辑推荐视频
    public String getAlbumRecommendVideoList() {
        return getServerNetAddressHost() + "mobile-playpage/playpage/recommend/video/list";
    }

    // 专辑视频列表加载
    public String getAlbumVideoListV2() {
        return getServerNetAddressHost() + "mobile-album/v2/album/video";
    }


    /**
     * 获得专辑相关推荐
     */
    public String getRelaComment() {
        return getARHost() + "rec-association/recommend/album";
    }

    /**
     * 根据Tag的名字获得专辑列表
     */
    public String getAlbumListByTag() {
        return getServerNetAddressHost() + "mobile/discovery/m/tags/get_albums";
    }

    /**
     * 同步是否喜欢此音频(POST 请求)
     */
    public String syncTrackLikeOrUnLick() {
        return getServerNetAddressHost() + "mobile/sync/";
    }

    /**
     * 赞歌曲
     */
    public String likeSound() {
        return getServerNetAddressHost() + "favourite-business/favorite/track";
    }

    /**
     * 开始分享(POST 请求)
     */
    public String startShare() {
        return getServerNetAddressHost() + "mobile/v1/share/client/stat";
    }

    /**
     * 新版开始分享(POST 请求)
     */
    public String startShareNew() {
        return getServerNetAddressHost() + "thirdparty-share/share/stat";
    }


    /**
     * 分享 which 分一下几个值 1 track 2 album 3 user 4 activity 5 activity/vote 6
     * activity/track 7 special 8 link
     */
    public String getShareContent(String which) {
        if (SHARE_RANK.equalsIgnoreCase(which)) {
            return getShareContentFromRank();
        }
        if (SHARE_MEMBER.equals(which)) {
            return getShareContentFromMember();
        }
        if (SHARE_PERSONALLIVE.equalsIgnoreCase(which)) {
            return getShareContentFromPersonal();
        }
        return getServerNetAddressHost() + "mobile/v1/" + which
                + "/share/content";
    }

    /**
     * 赚取积分
     */
    public String earnIntegral() {
        return getServerNetAddressHost() + "mobile/" + AppConstants.API_VERSION
                + "/point/query/multi/earn/rest";
    }

    /**
     * 扣取积分
     */
    public String deductIntegral() {
        return getServerNetAddressHost() + "mobile/" + AppConstants.API_VERSION
                + "/point/query/deduct/rest";
    }

    /**
     * 分享
     */
    public String shareContent() {
        return getServerNetAddressHost() + "mobile/v1/auth/feed";
    }


    /**
     * 新浪访问令牌 (POST)
     */
    public String getSinaAccessToken() {
        return "https://api.weibo.com/oauth2/access_token";
    }

    /**
     * 新浪发布微博 (POST)
     */
    public String getSinaWeiboUrlWithPic() {
        return "https://api.weibo.com/2/statuses/upload_url_text.json";
    }

    /**
     * 新浪发布微博不带图片 (POST)
     */
    public String getSinaWeiboUrl() {
        return "https://api.weibo.com/2/statuses/update.json";
    }

    /**
     * qq访问令牌
     */
    public String getQQAccessToken() {
        return "https://graph.qq.com/oauth2.0/token?"
                + "grant_type=authorization_code" + "&client_id="
                + AppConfigConstants.QQ_APP_ID + "&client_secret="
                + AppConfigConstants.QQ_CLIENT_SECRET
                + "&redirect_uri=http%3A%2F%2Fwww.ximalaya.com"
                + "&display=mobile" + "&code=";
    }

    /**
     * qq访问令牌之后还要再次请求不知道干啥用
     */
    public String getQQAccessTokenSecond() {
        return "https://graph.qq.com/oauth2.0/me";
    }

    /**
     * 根据trackid播放当个音频
     *
     * @return
     */
    public String playSoundByTrackId() {
        return getServerNetAddressHost() + "mobile/track";
    }

    /**
     * 获得关注的状态
     */
    public String getFollowStatu() {
        return getServerNetAddressHost() + "m/follow_status";
    }

    /**
     * 获得用户临时的声音列表(试听)
     */
    public String getAnchorTempTracks() {
        return getServerNetAddressHost() + "m/prelisten";
    }

    /**
     * 定制听-我的关注-获取新鲜事
     */
    public String getFeedEvents() {
        return getServerNetAddressHost() + "feed/v1/feed/event";
    }

    /**
     * 找相似 新接口
     */
    public String getSimilarAlbumList() {
        return getServerNetAddressHost() + "album-mobile/album/similar/querySimilarAlbumList/ts-";
    }

    /**
     * 首页
     */
    public String getRecommendFeedStreamUrl() {
        return getServerNetAddressHost() + "discovery-feed/v3/mix";
    }

    /**
     * 首页
     */
    public String getRecommendFeedStreamV4Url() {
        return getServerNetAddressHost() + "discovery-feed/v4/mix";
    }

    /**
     * 获得专辑相关推荐by trackId
     */
    public String getRelaCommentByAlbumId() {
        return getARHost() + "rec-association/recommend/album/by_album";
    }

    /**
     * 获得专辑系列
     */
    public String getAlbumSeriesUrl() {
        return getServerNetAddressHost()
                + "album-mobile/album/set/queryAlbumSets/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取小编推荐的数据
     *
     * @return
     */
    public String getEditorRecommend() {
        return getServerNetAddressHost()
                + "mobile/discovery/v2/recommend/editor";
    }

    /**
     * 优选列表
     *
     * @return
     */
    public String getPreferredList() {
        return getServerNetAddressHost() + "discovery-category/more/weekyouxuan_list";
    }

    /**
     * 获取猜你喜欢的数据
     *
     * @return
     */
    public String getGuessYouLikeLogin() {
        return getServerNetAddressHost()
                + "discovery-firstpage/guessYouLike/list";
    }

    /**
     * 获取猜你喜欢换一换数据
     *
     * @return
     */
    public String getGuessYouLikeRefresh() {
        return getServerNetAddressHost()
                + "mobile/discovery/v3/guessYouLike/firstpage";
    }

    /**
     * 获得用户关注的状态
     */
    public String getUseFollowStatue() {
        return getServerNetAddressHost() + "mobile/user/relation";
    }

    /**
     * 根据声音所属用户id和声音id获取下载声音的数据
     */
    public String getTrackDownloadInfo(String uid, String trackId) {
        return getServerNetAddressHost() + "mobile/download/" + uid + "/track/"
                + trackId;
    }

    public String getAlbumBatchDownloadInfo() {
        return getServerNetAddressHost() + "mobile/api1/download/album/";
    }

    /**
     * 获得电台的详细信息
     */
    public String getRadioDetail() {
        return getServerRadioHost() + "getProgramDetail";
    }

    /**
     * 获得电台的今天昨天明天的时间表
     */
    public String getProgressSchedules() {
        return getServerRadioHost() + "getProgramSchedules";
    }

    /**
     * 获得电台的今天昨天明天的时间表
     */
    public String getProgressSchedulesNew() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/getPlayPageInfo";
    }

    /**
     * 获取账户绑定状态
     *
     * @return
     */
    public String getAccountBindStatus() {
        return getServerNetAddressHost() + "mobile/auth/bindStatus";
    }

    /**
     * 获取设置
     */
    public String getSettingGetUrl() {
        return getServerNetAddressHost() + "mobile/settings/get";
    }

    public String getMobileSettings() {
        return getServerNetAddressHost() + "mobile-settings/get";
    }

    public String getMobileSettingTypes() {
        return getServerNetAddressHost() + "mobile-user/setting/gettypes";
    }

    public String getClearRecommendHistory() {
        return getServerNetAddressHost() + "mobile-user/setting/off7/personal/rec";
    }

    public String getMobileSettingTypesForDevice() {
        return getServerNetAddressHost() + "mobile-user/setting/getTypesForDevice";
    }

    public String setMobileSettings() {
        return getServerNetAddressHost() + "mobile-settings/v2/set";
    }

    public String logout() {
        return getPassportAddressHosts() + "mobile/logout";
    }

    /**
     * 获取收到的评论
     */
    public String getReceiveComment() {
        return getServerNetAddressHost() + "comment-mobile/in";
    }

    /**
     * 获取发送的评论
     */
    public String getSendComment() {
        return getServerNetAddressHost() + "mobile/message/out";
    }

    /**
     * 查询音频列表是否是喜欢的
     */
    public String getTracksIsLike() {
        return getServerNetAddressHost() + "mobile/track/relation";
    }

    /**
     * 获取联系人
     *
     * @return
     */
    public String getCarePersons() {
        return getServerNetAddressHost() + "mobile/following/user";
    }

    /**
     * 录音-我的专辑列表
     *
     * @return
     */
    public String getTrackUploadAlbums() {
        return getServerNetAddressHost() + "mobile-track-write/upload/albums";
    }

    public String getTrackUploadAlbumsAndTips() {
        return getServerNetAddressHost() + "mobile-track-write/upload/track/info";
    }

    /**
     * 录音-我的专辑列表-创建新专辑
     */
    public String getCreateAlbum() {
        return getServerNetAddressHost() + "mobile/api1/upload/album_form";
    }

    /**
     * 喜马拉雅大学
     *
     * @return
     */
    public String getCollegeHost() {
        return "http://blog.ximalaya.com/college/";
    }

    public String uploadTrack() {
        return getServerNetAddressHost() + "mobile-track-write/upload/trackForm";
    }

    public String uploadSubmit() {
        return getServerNetAddressHost() + "mobile-track-write/upload/createTrack";
    }

    /**
     * 收益中心
     *
     * @return
     */
    public String getBusinessHost() {
        return BaseUtil.chooseEnvironmentUrl(BUSINESS_HOST);
    }

    public String getBusinessHostS() {
        return BaseUtil.chooseEnvironmentUrl(BUSINESS_HOST_S);
    }

    /**
     * 有声化平台
     *
     * @return
     */
    public String getAudioPlusHost() {
        return BaseUtil.chooseEnvironmentUrl("http://ma.ximalaya.com/", "http://ma2.test.ximalaya.com/",
                "http://ma.uat.ximalaya.com/");
    }

    /*
     * 微任务
     *
     * */
    public String getMicTaskHost() {
        return BaseUtil.chooseEnvironmentUrl(AD_WEB_MIC_TASK);
    }

    /**
     * 广告服务
     *
     * @return
     */
    public String getAdvertiseHost() {
        return BaseUtil.chooseEnvironmentUrl(AD_WEB_HOST + "broadcaster/getAdTipPage");
    }

    /**
     * 广告服务
     *
     * @return
     */
    public String getAdvertiseHostV2() {
        return BaseUtil.chooseEnvironmentUrl(AD_WEB_HOST);
    }

    // 开发平台
    public String getOpenHost() {
        return BaseUtil.chooseEnvironmentUrl(OPEN_HOST);
    }

    /**
     * 音频转采
     */
    public String TrackRelay() {
        return getServerNetAddressHost() + "mobile/track/relay";
    }

    /**
     * 加载举报配置信息
     */
    public String loadReportProperty() {
        return getServerNetAddressHost() + "mobile/report/content/query";
    }

    /**
     * 音频举报
     */
    public String reportTrack() {
        return getServerNetAddressHost() + "mobile/report/track/create";
    }

    /**
     * 举报专辑
     */
    public String reportAlbum() {
        return getServerNetAddressHost() + "mobile/report/album/create";
    }

    /**
     * 修改用户信息
     *
     * @return
     */
    public String getChangeAccountInfo() {
        return getServerNetAddressHost() + "mobile/user/account";
    }

    /**
     * 获取兑吧url
     *
     * @return
     */
    public String getDuiBaUrl() {
        // 没有测试环境
        return BaseUtil.chooseEnvironmentUrl("http://3rd.ximalaya.com/point-thirdparty/duiba/url/create",
                "http://3rd.ximalaya.com/point-thirdparty/duiba/url/create",
                "http://3rd.uat.ximalaya.com/point-thirdparty/duiba/url/create");
    }

    /**
     * 录音获取声音的分类列表
     *
     * @return
     */
    public String getSoundCategories() {
        return getServerNetAddressHost() + "mobile/category/upload";
    }

    public String getNeedRealNameVerifyUrl() {
        return getServerNetAddressHost() + "album-mobile/album/anchor/needRealNameAuth/" + System.currentTimeMillis();
    }

    public String getInviteThird() {
        return getServerNetAddressHost() + "mobile/v1/auth/invite";
    }

    public String getContentMsg() {
        return getServerNetAddressHost() + "mobile/message/invite/content";
    }

    /**
     * 获取未注册好友
     *
     * @return
     */
    public String getUnRegister() {
        return getServerNetAddressHost() + "mobile/friendship/unregister";
    }

    public String getMyAlbumData() {
        return getServerNetAddressHost() + "mobile/my/albums";
    }

    public String getMyTracks() {
        return getServerNetAddressHost() + "mobile/my_tracks";
    }

    /**
     * 付费收听项目 用户声音列表新接口
     *
     * @return
     */
    public String getMyTracksNew() {
        return getServerNetAddressHost() + "mobile/v1/my/tracks";
    }


    public String getNickNameNonceUrl() {
        return getPassportAddressHosts() + "mobile/nonce/" + System.currentTimeMillis();
    }

    public String getCheckNickNameIllegalUrl() {
        return getPassportAddressHosts() + "mobile/v1/nickname/update";
    }

    public String doModifyNicknameAndIntro() {
        return getServerNetAddressHost() + "mobile/user/profile";
    }

    /**
     * 推送反馈
     */
    public String getPushCallBackUrl() {
        return getServerNetAddressHost() + "mobile/pns/cb";
    }

    /**
     * xdcs upload iting
     */
    public String getPostIting() {
        return getXDCSCollectAddressHost() + "api/v1/statistics";
    }

    public String getPostCDN() {
        return getXDCSCollectAddressHost() + "api/v1/cdnAndroid";
    }

    public String getPostAppInfo() {
        return getXDCSCollectAddressHost() + "api/v1/endata";
    }


    public String collectError() {
        return getXDCSCollectAddressHost() + "api/v1/frontEnd";
    }

    // 广告xdcs
    public String getPostOnlineAd() {
        return getXDCSCollectForAdAddressHost() + "api/v1/adRealTime";
    }

    // 客户端错误
    public String getPostErrorInfo() {
        return getXDCSCollectAddressHost() + "api/v1/realtime";
    }

    // 流量包注册信息上报
    public String getPostRegisterFlow() {
        return getXDCSCollectAddressHost() + "api/info/registerFlow";
    }

    public String getPostOfflineData() {
        //return getXDCSCollectAddressHost() + "api/v1/offline";
        return getXDCSCollectAddressHost() + "nyx/v1/offline/track/statistic/android";

    }

    public String getPostTrafficData() {
        return getXDCSCollectAddressHost() + "api/info/traffic";
    }


    /**
     * 更改头像
     *
     * @return
     */
    public String updataHead() {
        return getServerNetAddressHost() + "mobile/header/upload";
    }

    /**
     * 更改背景
     *
     * @return
     */
    public String updataBackground() {
        return getServerNetAddressHost() + "mobile/appBackground/upload";
    }

    /**
     * 通讯录邀请好友
     *
     * @return
     */
    public String uploadContacts() {
        return getPassportAddressHosts() + "friendship-mobile/v2/contacts/upload";
    }


    public String sendGiuid() {
        return "http://mobile.ximalaya.com/persona-web/addTags";
    }

    /**
     * 更新app的配置
     */
    public String updateAppConfig() {
        return getServerNetAddressHost() + "mobile/switch/app_set";
    }

    // 每日更新－更新数
    public String geUpdateTrackCount() {
        return getServerNetAddressHost() + "track-feed/v1/new-feed/count";
    }

    // 每日更新－更新数
    public String geUpdateTrackCountV2() {
        return getServerNetAddressHost() + "track-feed/v2/new-feed/count";
    }

    // 每日更新
    public String getEverydaytUpdate() {
        return getServerNetAddressHost() + "track-feed/v1/track-feed/dynamic";
    }

    // 每日更新
    public String getEverydaytUpdateV2() {
        return getServerNetAddressHost() + "track-feed/v2/track-feed/dynamic";
    }

    // 根据任务分组和标签获取任务列表
    public String getTaskRecords() {
        return getMNetAddressHost() + "starwar/lottery/task/record";
    }

    //删除小说阅读历史
    public String getEbookDeleteHistoryUrl() {
        return getServerNetAddressHost() + "mobile-ebook/ebook/delHistory/" + System.currentTimeMillis();
    }

    //阅读历史列表url
    public String getEbookReadHistoryUrl() {
        return getServerNetAddressHost() + "mobile-ebook/ebook/historyList/" + System.currentTimeMillis();
    }

    //推荐书籍url
    public String getEbookRecommendUrl() {
        return getServerNetAddressHost() + "mobile-ebook/ebook/recommend/" + System.currentTimeMillis();
    }

    /**
     * XDCS数据收集
     */
    public String getXDCSCollect() {
        return getXDCSCollectAddressHost() + "api/v1/cdnAndroid";
    }

    /**
     * 用户再次返回到app
     */
    public String mobileResume() {
        return getServerNetAddressHost() + "mobile/resume";
    }

    /**
     * 推荐的的Track
     *
     * @return
     */
    public String recommentTrack() {
        return getServerNetAddressHost() + "m/explore_track_list";
    }

    /**
     * 皮肤信息
     *
     * @return
     */
    public String skinInfo() {
        return getServerSkinHost() + "appskin/skinUrl";
    }

    /**
     * 获取主播打赏服务开通状态
     *
     * @return
     */
    public String getRewardStatus() {
        return getServerNetAddressHost() + "mobile/anchor_backend";
    }

    /**
     * 上传用户位置信息
     */
    public String postUserLocationInfo() {
        return getLocationHost() + "collectPosInfo";
    }

    public String getAlbumCollect() {
        return getServerNetAddressHost() + "subscribe/album/subscribe-status";
    }

    public String getForgetPassword() {
        return getHybridHost() + "api/bind/forget_password";
    }

    public String setPassword() {
        return getServerNetAddressHost() + "passport/register/password";
    }

    public String checkNickname() {
        return getServerNetAddressHost() + "passport/register/check-nickname";
    }

    public String setNickname() {
        return getServerNetAddressHost() + "passport/register/nickname";
    }

    /**
     * 关注的新鲜事置顶
     *
     * @return
     */
    public String feedTop() {
        return getServerNetAddressHost() + "subscribe/v1/feed/top/create";//subscribe
    }

    /**
     * 关注的新鲜事取消置顶
     *
     * @return
     */
    public String feedTopCancel() {
        return getServerNetAddressHost() + "subscribe/v2/feed/top/delete";
    }

    /**
     * 专辑中声音播放列表
     *
     * @return
     */
    public String getPlayListInAlbum() {
        return getServerNetAddressHost() + "mobile/playlist/album";
    }

    /**
     * 获取订阅专辑列表
     */
    public String getSubscribeAlbumList() {
        return getServerNetAddressHost() + "subscribe/v3/subscribe/list";
    }

    /**
     * 获取订阅专辑列表-全部
     */
    public String getAlbumListForEveryDayUpdateSetting() {
        return getServerNetAddressHost() + "track-feed/v1/subscribe/list/ts-" + System.currentTimeMillis();
    }

    public String getAlbumListForPodUpdateSetting() {
        return getServerNetAddressHost() + "track-feed/v3/chase/list/" + System.currentTimeMillis();
    }

    /**
     * 获取订阅专辑列表-已追更
     */
    public String getSubscribeAlbumListForEveryDayUpdateSetting() {
        return getServerNetAddressHost() + "track-feed/v1/chase/list/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取订阅专辑列表-分类 后台分要分俩 sb
     */
    public String getAlbumListByCategoryForEveryDayUpdateSetting() {
        return getServerNetAddressHost() + "track-feed/v1/subscribe/certain-category/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取订阅专辑列表
     */
    public String getSubscribeAlbumListV4() {
        return getServerNetAddressHost() + "subscribe/v6/subscribe/list/ts-";
    }

    /**
     * 订阅搜素
     */
    public String getSubscribeSearchAlbumList() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/search";
    }

    /**
     * 订阅专辑批量删除
     */
    public String getSubscribeBatchDetele() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/multi/delete";
    }

    /**
     * 获取订阅专辑列表---有分类时的请求
     *
     * @return
     */
    public String getMySubscribeCertainCategoryV6() {
        return getServerNetAddressHost() + "subscribe/v6/subscribe/certain-category/ts-" + System.currentTimeMillis();
    }

    public String getSearchCustomHomeUrl() {
        return getServerNetAddressHost() + "mobile-album/hm/searchContent";
    }

    public String getSearchCustomHomeDifyUrl() {
//        return getServerNetAddressHost() + "mobile-album/hm/content";
        return "https://mobile.test.ximalaya.com/mobile-album/hm/content";
    }

    /**
     * 获取订阅专辑列表---有分类时的请求--20423活动接口
     *
     * @return
     */
    public String getMySubscribeCertainCategoryV1ForActivity20423() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/activity123/ts-" + System.currentTimeMillis();
    }


    public String getNewUserWelfareParticipateInfo() {
        return getHybridHost() + "hybrid/api/newUserWelfare/participateInfo";
    }

    /**
     * 追更专辑-日更需求
     *
     * @return
     */
    public String chaseAlbumForEveryDayUpdate() {
        return getServerNetAddressHost() + "track-feed/v1/chase/create";
    }

    /**
     * 取消追更专辑-日更需求
     *
     * @return
     */
    public String unChaseAlbumForEveryDayUpdate() {
        return getServerNetAddressHost() + "track-feed/v1/chase/delete";
    }

    public String getSubscribeCartEntryV1() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/cart-entry/ts-" + System.currentTimeMillis();
    }

    /**
     * 订阅-分类筛选的url
     *
     * @return
     */
    public String getSubscribeCategorySortV1() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/all-categories/ts-" + System.currentTimeMillis();
    }

    /**
     * myclub 订阅 取消订阅
     *
     * @param isSubscribe
     * @return
     */
    public String getMyclubSubscribe(boolean isSubscribe) {
        return getMNetAddressHost() +
                (isSubscribe ? "chitchat-mobile-web/api/v1/schedule/subscribe" : "chitchat-mobile-web/api/v1/schedule/unSubscribe");
    }

    public String getSubscribeRecommendV4(boolean hasLogin) {
        if (hasLogin) {
            return getServerNetAddressHost() + "subscribe/v4/subscribe/recommend/ts-";
        } else {
            return getServerNetAddressHost() + "subscribe/v4/unlogin/subscribe/recommend/ts-";
        }
    }

    public String getSubscribeRecommendV6() {
        return getServerNetAddressHost() + "subscribe/v6/subscribe/tagAlbumList";
    }

    public String getOneKeySubscribeTagAlbumV6() {
        return getServerNetAddressHost() + "subscribe/v6/subscribe/tagSubscribe";
    }

    public String getSubscribeRecommendChangeV6() {
        return getServerNetAddressHost() + "subscribe/v6/subscribe/update";
    }

    public String getSubscribeRecommend(boolean hasLogin) {
        if (hasLogin) {
            return getServerNetAddressHost() + "subscribe/v3/subscribe/recommend";
        } else {
            return getServerNetAddressHost() + "subscribe/v2/subscribe/recommend/unlogin";
        }
    }

    public String getPodcastUpdateUrl() {
        return getServerNetAddressHost() + "track-feed/v6/track-feed/dynamic/" + System.currentTimeMillis();
    }

    public String getSubscribeHotUpdateV2() {
        if (UserInfoMannage.hasLogined()) {
            return getServerNetAddressHost() + "track-feed/v2/chase/recommend/ts-" + System.currentTimeMillis();
        } else {
            return getServerNetAddressHost() + "track-feed/v2/unlogin/chase/recommend/ts-" + System.currentTimeMillis();
        }
    }

    public String getSubscribeHotUpdate() {
        return getServerNetAddressHost() + "track-feed/v1/chase/recommend";
    }

    //是否交叉人群
    public String getGuideUserTypeUrl() {
        return getServerNetAddressHost() + "hoverball-mobile/guide/user";
    }

    //书籍批量上报
    public String getBookBatchReportUrl() {
        return getServerNetAddressHost() + "hoverball-mobile/book/batch/report";
    }

    //书籍列表接口
    public String getLocalBookListUrl() {
        return getServerNetAddressHost() + "hoverball-mobile/book/list";
    }

    //书籍详情
    public String getLocalBookDetailUrl() {
        return getServerNetAddressHost() + "hoverball-mobile/book/detail";
    }

    //删除书籍
    public String getDeleteBookUrl(long bookId) {
        return getServerNetAddressHost() + "hoverball-mobile/book/delete/" + bookId;
    }

    //搜索站内内容
    public String getSearchBookContentUrl() {
        return getServerNetAddressHost() + "hoverball-mobile/search/content/v2";
    }

    /**
     * 按综合排序获取专辑列表
     *
     * @return
     */
    public String getSubscribeComprehensive() {
        return getServerNetAddressHost() + "subscribe/v2/subscribe/comprehensive/rank/ts-";
    }

    /**
     * 第一个是最近订阅
     * 后三个按综合排序获取专辑列表
     *
     * @return
     */
    public String getSubscribeForWidget() {
        return getServerNetAddressHost() + "subscribe/subscribe/widget/list/ts-";
    }

    public String getSignInForWidget() {
        return getMNetAddressHostS() + "x-web-activity/signIn/v2/widget";
    }

    public String getXNpsQueryUrl() {
        return getServerNetAddressHost() + "questionnaire-mobile/questionnaire/query";
    }

    public String exploreXNps() {
        return getServerNetAddressHost() + "questionnaire-mobile/questionnaire/exposure";
    }

    public String submitXNps() {
        return getServerNetAddressHost() + "questionnaire-mobile/questionnaire/submit";
    }

    public String getSearchForWidget() {
        return getSearchHost() + "hub/widget";
    }

    /**
     * 删除新鲜事
     *
     * @return
     */
    public String deleteFeed() {
        return getServerNetAddressHost() + "feed/v1/feed/event/delete";
    }

    public String getBindAppXiaomi() {
        return getServerNetAddressHost() + "mobile/pns/xiaomi/bind";
    }

    public String getBindAppGetui() {
        return getServerNetAddressHost() + "mobile/pns/gt/bind";
    }

    public String getEndPoint() {
        return "https://api.weibo.com/2/friendships/create.json";
    }

    public String pushReceive() {
        return getServerPushHost() + "pns-portal/push/receive";
    }

    public String pushClick() {

        return getServerPushHost() + "pns-portal/push/click";
    }

    /**
     * 热门专辑
     */
    public String getHotAlbum() {
        return getServerNetAddressHost() + "m/explore_album_list";
    }

    /**
     * 获取推送设置
     *
     * @return
     */
    public String getPushSet(String appKey) {
        return getServerPushHost() + "pns-portal/push/settings/" + appKey + "/get";
    }

    /**
     * 推送设置
     *
     * @return
     */
    public String setPushSet(String appKey) {
        return getServerPushHost() + "pns-portal/push/settings/" + appKey + "/set";
    }

    public String getBulletTrackList() {
        return getServerNetAddressHost()
                + "mobile/discovery/v1/recommend/bulletArea";
    }

    /**
     * 声音贴片信息上传
     */
    public String uploadAdPlayData() {
        return getXDCSCollectAddressHost() + "api/v1/realtime";
    }

    public String getFeedRecommend() {
        return getServerNetAddressHost() + "feed/v1/recommend/classic";
    }

    public String getFeedRecommendUnlogin() {
        return getServerNetAddressHost() + "feed/v1/recommend/classic/unlogin";
    }

    /**
     * 获得智能硬件列表
     */
    public String getProductData() {
        return getServerNetAddressHost() + "mobile/mall/products";
    }

    public String getFindTabModel() {
        return getServerNetAddressHost() + "mobile/discovery/v1/tabs";
    }

    public String getFindTabModelV2() {
        return getServerNetAddressHost() + "mobile/discovery/v2/tabs";
    }

    /**
     * 请求用户首页展示的tab，和所有tab（也叫分类）
     */
    public String getHomepageTabsAndAllCategoriesUrl() {
//        return getServerNetAddressHost() + "discovery-category/v2/customCategories/";
        return getServerNetSAddressHost() + "discovery-category/customCategories/";
    }

    public String getBackUserPullUpData() {
        return getServerNetAddressHost() + "discovery-feed/isRecurringUser";
    }

    public String getCategorySubfields() {
        return getServerNetAddressHost()
                + "mobile/discovery/v1/category/subfield/albums";
    }

    //下载开始统计
    public String getDownloadRecordBatchStart() {
        return getServerNetAddressHost() + "mobile/api1/download/record/start";
    }

    //下载暂停统计
    public String getDownloadStop() {
        return getServerNetAddressHost() + "mobile/v1/download/record";
    }

    /**
     * 获取我的关注
     *
     * @return
     */
    public String getMyFollowing() {
        return getServerNetAddressHost() + "mobile/following";
    }

    /**
     * 获取关注的主播列表
     */
    public String getMyAnchorFollowing() {
        return getServerNetAddressHost() + "mobile-message-center/following/list/ts-" + System.currentTimeMillis();
    }

    /**
     * 获得我的粉丝
     */
    public String getMyFans() {
        return getServerNetAddressHost() + "mobile/follower";
    }

    /**
     * 获取综艺娱乐推荐
     *
     * @return
     */
    public String getSubjectAlbums() {
        return getServerNetAddressHost()
                + "mobile/discovery/v2/category/subjects";
    }

    /**
     * 删除我的声音
     *
     * @return
     */
    public String getDeleteMyTrack() {
        return getServerNetAddressHost() + "mobile-track-write/track/delete";
    }

    /**
     * 焦点图多跳转
     *
     * @return
     */
    public String getFocusData() {
        return getServerNetAddressHost() + "m/focus_list";
    }

    public String getPlayHistory() {
        return getServerNetAddressHost() + "mobile/playlist/album/page";
    }

    /**
     * 充值接口
     *
     * @return
     */
    public String recharge() {
        return getMpAddressHost() + "xmacc/recharge/prepare/v2";
    }

    public String rechargeDiamond() {
        return getMpAddressHost() + "business-xi-bean-mobile-web/xibean/recharge/placeorderandmakepayment/";
    }

    /**
     * 获取可用余额接口
     *
     * @return
     */
    public String getMyDifference() {
        return getMpAddressHost() + "xmacc/mysubaccount/v1";
    }

    /**
     * 消费记录和充值
     *
     * @return
     */
    public String getBuyOrConsume() {
        return getMpAddressHost() + "xmacc/traderecord/v2/";
    }

    /**
     * 支付后通知服务端
     *
     * @return
     */
    public String noticeServerAfterPay() {
        return getMpAddressHost() + "xmacc/recharge/place/v1";
    }

    /**
     * 常见问题
     *
     * @return
     */
    public String getWebProblem() {
        return getMpAddressHostS() + "xmacc/help/index/android";
    }

    /**
     * 获取单声音或者全集的价格信息
     *
     * @return
     */
    public String getXiOrderPrice() {
        return getMpAddressHost() + "payable/order/context/v1/";
    }

    /**
     * 已购声音
     *
     * @return
     */
    public String getMyBuyedSounds() {
        //pageid/{pageId}/pagesize/{pageSize}
        return getMpAddressHost() + "payable/myprivilege/v2/";
    }

    /**
     * 确认购买声音
     *
     * @return
     */
    public String buyTrack() {
        return getMpAddressHost() + "payable/order/placeorder/v1";
    }

    public String buyFreeAlbumPaidTrack() {
        return getMNetAddressHostS() + "trade/placeorderandmakepayment";
    }

    /**
     * 确认购买专辑
     *
     * @return
     */
    public String buyAlbum() {
        return getMpAddressHost() + "payable/order/placeorder/album/v2";
    }


    /**
     * 购买剩余所有声音接口
     * http://gitlab.ximalaya.com/business/business-payable/wikis/trade-order#6购买剩余所有声音接口-post
     *
     * @return
     */
    public String buyAlbumV2() {
        return getMpAddressHost() + "payable/order/placeorder/album/v2";
    }

    /**
     * 已更未购n集
     *
     * @return
     */
    public String buyAlbumV3() {
        return getMpAddressHost() + "payable/order/placeorder/album/v3";
    }

    /**
     * 已更未购n集
     *
     * @return
     */
    public String buySingleAlbumRemain() {
        return getServerNetAddressHost() + "product/promotion/v1/single/track/purchase/whole/ts-" + System.currentTimeMillis();
    }

    /**
     * 查询订单购买状态
     *
     * @return
     */
    public String queryOrderStatus() {
        return getMpAddressHost() + "payable/order/context/orderstatus/v2/";
    }

    public String getCouponList() {
        return getMpAddressHost() + "payable/order/coupon/list/v2/";
    }

    /**
     * 获取充值套餐列表
     *
     * @return
     */
    public String getRechargeProducts() {
        // return getMpAddressHost() + "xmacc/recharge/products/v3";
        return getMNetAddressHostS() + "common-recharge/products";
    }

    /**
     * 获取喜钻充值套餐列表
     *
     * @return
     */
    @Deprecated
    public String getRechargeDiamondProducts() {
        long timestamp = System.currentTimeMillis();
        return getMpAddressHost() +
                "business-xi-bean-mobile-web/xibean/recharge/product/v2/" + timestamp;
    }

    /**
     * 批量购买声音列表
     *
     * @return
     */
    public String batchBuyTrackList() {
        return getMpAddressHost() + "payable/order/context/v2";
    }

    /**
     * 获取分类筛选元数据
     *
     * @return
     */
    public String getCategoryMetadatas() {
//		return getServerNetAddressHost() + "mobile/discovery/v1/category/metadatas";
        return getServerNetAddressHost() + "mobile/discovery/v2/category/metadatas";
    }

    public String getCategoryMetadatasV2() {
        return getServerNetAddressHost() + "mobile-category/v1/meta/searchParams";
    }

    public String getChannelMetadatas() {
        return getServerNetAddressHost() + "discovery-category/channel/metadatas";
    }

    /**
     * 获取标签筛选元数据
     */
    public String getTagMetadatasUrl() {
        return getServerNetAddressHost() + "discovery-category/channel";
    }

    /**
     * 获取分类筛选元数据(虚拟分类-元数据)
     *
     * @return
     */
    public String getVirtualCategoryMetadatas() {
        return getServerNetAddressHost() + "product/v1/category/metadatas";
    }

    /**
     * 精选列表&热词 付费筛选接口
     *
     * @return
     */
    public String getCategoryFilterMetadatas() {
        return getServerNetAddressHost() + "mobile/discovery/v2/category/filter/albums";
    }

    /**
     * 精选列表&热词 付费筛选接口(虚拟分类-更多)
     *
     * @return
     */
    public String getVirtualCategoryFilterMetadatas() {
        return getServerNetAddressHost() + "product/v1/category/filter/albums";
    }

    /**
     * 获取分类筛选元数据条件下的专辑列表
     *
     * @return
     */
    public String getAlbumsByMetadata() {
//		return getServerNetAddressHost() + "mobile/discovery/v1/category/metadata/albums";
        return getServerNetAddressHost() + "mobile/discovery/v2/category/metadata/albums";
    }

    /**
     * 获取新落地页筛选元数据下的专辑
     */
    public String getChannelAlbumsByMetadata() {
        return getServerNetAddressHost() + "discovery-category/channel/albums";
    }

    public String getChannelAlbumsByMetadataV2() {
        return getServerNetAddressHost() + "mobile-category/v1/search";
    }

    /**
     * 获取标签筛选元数据条件下的专辑列表
     */
    public String getAlbumsByTagMetadata() {
        return getServerNetAddressHost() + "discovery-category/channel/metadata/albums";
    }

    /**
     * 获取分类筛选元数据条件下的专辑列表(虚拟分类-单个元数据专辑列表)
     *
     * @return
     */
    public String getVirtualAlbumsByMetadata() {
        return getServerNetAddressHost() + "product/v1/category/metadata/albums";
    }

    public String getRechargeStatus() {
        return getMpAddressHost() + "xmacc/recharge/status/v1/";
    }

    public String getRechargeDiamondStatus() {
        return getMpAddressHost() + "business-xi-bean-mobile-web/xibean/recharge/trade/order/status/";
    }

    public String getAlbumAutoBuyTip() {
        return getMpAddressHost() + "payable/autobuy/query/v1/albumid/";
    }

    public String postAlbumAutoBuyTip() {
        return getMpAddressHost() + "payable/autobuy/v1/albumid/";
    }

    /**
     * 创建一条专辑评论
     *
     * @return
     */
    public String postAlbumComment() {
        return getCommentBaseUrl() + "create/album/comment";
    }

    /**
     * 删除一条专辑评论
     *
     * @return
     */
    public String deleteAlbumComment() {
        return getCommentBaseUrl() + "delete/album/comment";
    }

    /**
     * 查询发件箱消息列表
     *
     * @return
     */
    public String getSendMsgBox() {
        return getCommentBaseUrl() + "out";
    }

    /**
     * 查询收件箱消息列表
     *
     * @return
     */
    public String getReceiveMsgBox() {
        return getCommentBaseUrl() + "in";
    }

    /**
     * 查询专辑评论回复
     *
     * @return
     */
    public String getCommentReplies() {
        return getCommentBaseUrl() + "album/reply/ts-" + System.currentTimeMillis();
    }

    /**
     * 赞评论
     *
     * @return
     */
    public String likeComment() {
        return getCommentBaseUrl() + "album/like";
    }

    /**
     * 取消赞评论
     *
     * @return
     */
    public String dislikeComment() {
        return getCommentBaseUrl() + "album/dislike";
    }

    /**
     * 创建评论回复
     *
     * @return
     */
    public String createReply() {
        return getCommentBaseUrl() + "create/album/comment/reply";
    }

    /**
     * 删除评论回复
     *
     * @return
     */
    public String deleteReply() {
        return getCommentBaseUrl() + "delete/album/comment/reply";
    }

    /**
     * 删除评论回复收件箱
     *
     * @return
     */
    public String deleteReceiveBox() {
        return getCommentBaseUrl() + "in/delete/album/comment/reply";
    }

    /**
     * 删除评论回复发件箱
     *
     * @return
     */
    public String deleteSendBox() {
        return getCommentBaseUrl() + "out/delete/album/comment/reply";
    }

    public String getAlbumCommentList() {
        return getCommentBaseUrl() + "album/ts-" + System.currentTimeMillis();
    }

    public String getAnchorAlbumCommentList() {
        return getCommentBaseUrl() + "album/v2";
    }

    /**
     * 获取已购未下载的声音列表
     */
    public String getBuyedWithoutDownloadTracks() {
        return getServerNetAddressHost() + "v1/download/album/paid";
    }

    /**
     * 上传上传错误日志
     */
    public String uploadErrorInfo() {
        return getXDCSCollectAddressHost() + "/api/v1/frontEnd";
    }

    /**
     * 榜单分享
     */
    public String getShareContentFromRank() {
        return getServerNetAddressHost() + "mobile/discovery/v3/rankingList/shareContent";
    }

    /**
     * 聚合榜单分享
     *
     * @return
     */
    public String getShareContentFromGroupRank() {
        return getServerNetAddressHost() + "mobile/discovery/v1/rankGroup/shareContent";
    }

    public String getShareContentFromGroupRankNew() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/shareContent";
    }

    public String getShareContentFromMember() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/document/memberFind";
    }

    public String getShareContentFromPersonal() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/document/liveFind";
    }

    public String getUids2NicknameUrl() {
        return getServerNetAddressHost() + "mobile/user/infos";
    }

    public String getQueryUserInfoUrl() {
        return getServerNetAddressHost() + "mobile-user/user/baseinfo/ts-" + System.currentTimeMillis();
    }

    /**
     * 专辑评论和回复举报
     */
    public String reportAlbumComment() {
        return getServerNetAddressHost() + "mobile/report/album/commentAndReply";
    }

//    /**
//     * 解绑手机账号
//     *
//     * @return
//     */
//    public String getUbindPhone() {
//        return getServerNetAddressHost() + "passport/unBindMobile";
//    }

//    /**
//     * 解绑第三方账号
//     *
//     * @return
//     */
//    public String getUbindThird(int thirdpartyId) {
//        return getServerNetAddressHost() + "passport/auth/" + thirdpartyId + "/unbind";
//    }

    /**
     * 获取城市列表
     *
     * @return
     */
    public String getCityList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/city/list";
    }

    /**
     * 切换城市
     *
     * @return
     */
    public String changeCity() {
        return getServerNetAddressHost() + "mobile/discovery/v1/city/change";
    }

    /**
     * 本地听tab
     *
     * @return
     */
    public String getCityTabs() {
        return getServerNetAddressHost() + "mobile/discovery/v1/city/tab";
    }

    /**
     * 本地听推荐数据
     *
     * @return
     */
    public String getCityRecommendAlbum() {
        return getServerNetAddressHost() + "discovery-category/city/recommendContent";
    }

    /**
     * 新的本地听推荐数据
     * http://citywave.test.ximalaya.com/culture-citywave-api/citywave/recommendContent?code=43_130000_1301
     *
     * @return
     */
    public String getNewCityRecommendAlbum() {
        return getNewCityRecommendAddressHost() + "culture-citywave-api/citywave/recommendContent";
    }

    /**
     * 查询景点听单
     */
    public String getNewCitySceneryListenUrl() {
        return getNewCitySceneryAddressHost() + "culture-citywave-api/citywave/scenery/defaultscenery";
    }

    /**
     * 查询景点导览数据
     */
    public String getNewCityQuerySceneryUrl() {
        return getNewCitySceneryAddressHost() + "culture-citywave-api/citywave/scenery/queryscenery";
    }

    /**
     * 本地听运营位点击
     *
     * @return
     */
    public String getCityBubbleAdd() {
        return getServerNetAddressHost() + "discovery-category/city/bubbleAdd";
    }

    /**
     * 根据metadata获取本地听专辑列表
     *
     * @return
     */
    public String getCityAlbumByMetadata() {
        return getServerNetAddressHost() + "mobile/discovery/v1/city/metadataAlbum";
    }

    /**
     * 本地听电台列表
     *
     * @return
     */
    public String getCityRadioList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/city/radio";
    }

    public String ginsightReceive() {
        return "http://openapi.gi.igexin.com/getTag";
    }

    /**
     * 获得当前播放的广播节目
     *
     * @return
     */
    public String getCurrentRadioProgramUrl() {
        return getServerRadioHost() + "radio";
    }

    /**
     * 获取直播推荐信息列表
     */
    public String getRadioRecommendLiveListUrl() {
        return getServerNetAddressHost() + "lamia" + "/v1/recommend/radio";
    }

    /**
     * 获取直播播放历史
     */
    public String getLiveHistoryListUrl() {
        return getServerNetAddressHost() + "lamia" + "/v1/live/play/history";
    }

    /**
     * 未登陆用户登陆领奖需要同步优惠码 从 deviceId-》userId
     */
    public String adSyncpromo() {
        return getAdWelfAreHost() + "syncpromo";
    }

    public String getWholeAlbumPrice() {
        return getMpAddressHost() + "payable/order/context/whole/v2";
    }

    //同步儿童画像
    public String getSyncBabyInfoUrl() {
        return getServerNetAddressHost() + "persona/syncBabyInfo";
    }

    public String buyWholeAlbum() {
        return getMpAddressHost() + "payable/order/placeorder/whole/album/v3";
    }

    /**
     * 新版广播页
     */
    public String getHomePageRadioUrl() {
        return getRadioHostV5() + "homepage";
    }

    /**
     * 新版广播排行列表
     */
    public String getRadioRankUrl() {
        return getRadioHostV2() + "radio/hot";
    }

    /**
     * 获取声音图片列表
     */
    public String getTrackImages() {
        return getServerNetAddressHost() + "mobile/track/images";
    }

    /**
     * 获取猜你喜欢的数据
     *
     * @return
     */
    public String getGuessYouLikeUnlogin() {
        return getServerNetAddressHost()
                + "mobile/discovery/v3/recommend/guessYouLike/unlogin";
    }

    /**
     * 获取所有分类
     *
     * @return
     */
    public String getAllCategories() {
        return getServerNetAddressHost() + "mobile/discovery/v1/categories";
    }

    public String getSketchTabData() {
        return getServerNetAddressHost() + "mobile-category/v1/shortVideoAlbum/tagList";
    }

    public String getSketchListData() {
        return getServerNetAddressHost() + "mobile-category/v1/shortVideoAlbum/feedList";
    }
    public String getAllCategoriesV3() {
        return getServerNetAddressHost() + "mobile/discovery/v3/categories";
    }

    public String getTrackRichIntro() {
        return getServerNetAddressHost() + "mobile-playpage/richIntro";
    }

    public String getTrackInfoForOneKey() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v1/trackItem";
    }

    public String getTrackExtendInfo() {
        return getServerNetAddressHost() + "v1/track/extendInfo";
    }

    /**
     * 获取没有封面的tag
     *
     * @return
     */
    public String getCategoryKeywords() {
        return getServerNetAddressHost() + "discovery-category/keyword/all/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取虚拟分类的所有热词
     *
     * @return
     */
    public String getVirtualCategoryKeywords() {
        return getServerNetAddressHost()
                + "product/v1/category/recommends/keywords";
    }

    /**
     * 获取专辑型分类下的内容(严格缓存控制)
     */
    public String getCategoryAlbums() {
        return getServerNetAddressHost() + "mobile/discovery/v3/category/keyword/albums";//新
    }

    /**
     * 获取虚拟专辑型分类下的内容(严格缓存控制)
     */
    public String getVirtualCategoryAlbums() {
        return getServerNetAddressHost() + "product/v1/category/keyword/albums";
    }

    /**
     * 获取分类推荐页内容V2版
     */
    public String getCategoryRecommends() {
        return getServerNetAddressHost()
                + "mobile/discovery/v5/category/recommends";
    }

    /**
     * 获取精品听单数据
     *
     * @return
     */
    public String getSpecialListenList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/special/list";
    }

    /**
     * 获取精品听单详情数据
     *
     * @return
     */
    public String getSubjectDetail() {
        return getServerNetAddressHost() + "m/subject_detail";
    }

    /**
     * 获取精品听单详情数据
     *
     * @return
     */
    public String getSubjectList() {
        return getMNetAddressHost() + "explore/subject_tracks";
    }

    public String getAnchorCategory() {
        return getServerNetAddressHost() + "mobile/discovery/v1/anchor/categoryWithFamous";
    }

    /**
     * 新版分类广播列表
     */
    public String getRadioByCategoryUrl() {
        return getRadioHostV2() + "radio/category";
    }

    /**
     * 推荐广播列表
     */
    public String getRadioReccomendUrl() {
        return getServerRadioHost() + "radio/local";
    }

    /**
     * 收藏广播列表
     */
    public String getRadioFavoriteUrl() {
        return getRadioHostV2() + "radio/favoritelist";
    }

    /**
     * 收藏广播列表
     */
    public String getRadioNationalUrl() {
        return getRadioHostV2() + "radio/national";
    }

    /**
     * 收藏广播列表
     */
    public String getRadioProvinceUrl() {
        return getRadioHostV2() + "radio/province";
    }

    /**
     * 收藏广播列表
     */
    public String getRadioNetUrl() {
        return getRadioHostV2() + "radio/network";
    }

    public String getLiveServerHost() {
        return BaseUtil.chooseEnvironmentUrl(LIVE_SERVER_ADDRESS);
    }

    /**
     * k歌房host
     *
     * @return
     */
    public String getKsongServerHost() {
        return BaseUtil.chooseEnvironmentUrl(KSONG_SERVER_ADDRESS);
    }


    /**
     * 个人直播 Service Base Url
     */
    public final String getPersonLiveBaseUrl() {
        return getServerNetAddressHost() + "lamia";
    }

    /**
     * 获取直播客户端接口域名
     */
    public final String getLiveServerMobileHttpHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_LIVE_MOBILE_HTTP);
    }

    /**
     * 获取MyClub接口域名
     */
    public final String getMyClubMobileHttpHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_MyClub_MOBILE_HTTP);
    }

    /**
     * 获取MyClub接口域名https
     */
    public final String getMyClubMobileHttpsHost() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return BaseUtil.chooseEnvironmentUrl(SERVER_MyClub_MOBILE_HTTP_S);
        } else if (BaseConstants.ENVIRONMENT_TEST == BaseConstants.environmentId) {
            return "http://test.joinchitchat.com/";
        }
        return BaseUtil.chooseEnvironmentUrl(SERVER_MyClub_MOBILE_HTTP_S);
    }

    /**
     * 获取直播客户端接口域名
     */
    public final String getLiveServerMobileHttpsHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_LIVE_MOBILE_HTTPS);
    }

    /**
     * 获取直播客户端接口域名2
     */
    public final String getMLiveServerMobileHttpsHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_MLIVE_MOBILE_HTTPS);
    }

    /**
     * 获取youzan带货地址
     *
     * @return
     */
    public String getYouZanOpenIdUrl() {
        return getMLiveServerMobileHttpsHost() + "/frozen-goods-web/v1/youzan/userOpenId";
    }

    /**
     * 获取youzan 绑定地址
     *
     * @return
     */
    public String getYouZanBindingUrl() {
        return getMLiveServerMobileHttpsHost() + "/frozen-goods-web/v1/youzan/uid/binding";
    }

    /**
     * 直播统计 Service Base Url
     */
    public final String getPersonalLivePlayStatisticsHost() {
        return BaseUtil.chooseEnvironmentUrl(PERSONAL_LIVE_PLAY_STATISTICS);
    }

    /**
     * 获取举报的接口（成都给出）
     */
    public final String getReportToServerHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS_M + "ops-audit-report-app/");
    }

    public final String getThirdPartHost() {
        return BaseUtil.chooseEnvironmentUrl(THIRD_PARTY_ADDRESS);
    }

    /**
     * 本地听专辑列表
     */
    public String getCityAlbumList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/city/album";
    }

    public String getNewsContentCategory() {
        return getServerNetAddressHost() + "mobile/discovery/v1/news/tab";
    }


    /**
     * 某些第三方登录需要获取验证url 目前小米和魅族需要
     * 如果再添加第三方登录也需要的话，应该使用主host去注册，这样便于统一
     *
     * @return
     */
    public String getRedirectUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_HOST);
    }

    public String getRedirectUrlS() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_HOST_S);
    }

    public String getServerSkinHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_SKIN);
    }

    public String getMeiZuTokenUrl() {
        return MEIZU_TOKEN_URL;
    }

    public String getRecordChangeFileUrl() {
        return getServerNetSAddressHost() + "mobile-track-write/track/changeFile";
    }

    public String getRecordOfflineReasonUrl() {
        return getServerNetAddressHost() + "mobile-track-write/studio/track/offline/detail";
    }

    /*
     * 更改声音前查询地址
     */
    public String getRequestRecordUrl() {
        return getServerNetAddressHost() + "mobile-track-write/upload/track/query";
    }

    /*
     * 查询声音创建页面是不是展现商店入口
     */
    public String getRequestRecordStoreUrl() {
        return getMNetAddressHost() + "anchor-sell/auth/hasTrackAuth";
    }

    public String getTrackDataUrl(long trackId) {
        return String.format(Locale.getDefault(), "%sgatekeeper/data-center-h5/sounds/%d",
                UrlConstants.getInstanse().getMNetAddressHost(), trackId);
    }

    public String getAlbumDataUrl(long albumId) {
        return String.format(Locale.getDefault(), "%sgatekeeper/data-center-h5/album-analysis/%d",
                UrlConstants.getInstanse().getMNetAddressHost(), albumId);
    }


    /*
     *删除专辑
     */

    /**
     * 专辑未购页
     * <p>
     * 2017/11/10 :
     * 专辑未购页的接口地址有变化，需要改一下，新的地址：http://mobile.test.ximalaya.com/product/v{version}/album/rich，这版version=2
     *
     * @return
     */
    public String getAlbumPageNewContents() {
        return getServerNetAddressHost() + "product/v9/album/rich";
    }

    /*
     * 2019.5.22 更新到v10
     * http://gitlab.ximalaya.com/business/business-payable-discovery/wikis/albumdetail
     * */
    public String getAlbumPageNewContentsNew() {
        return getServerNetAddressHost() + "product/v12/album/rich";
    }

    public String getAlbumSimpleInfo() {
        return getServerNetAddressHost() + "mobile/album/paid/info";
    }

    public String getAlbumSimpleInfoForDownload() {
        return getServerNetAddressHost() + "mobile/v1/album/info";

    }

    /*
     * 声音信息修改
     */
    public String getUpdateRecordUrl() {
        return getServerNetAddressHost() + "mobile-track-write/upload/track/update";
    }

    public String getDeleteRecordUrl() {
        return getServerNetAddressHost() + "mobile/studio/album/delete";
    }

    /*
     *查询专辑信息
     */
    public String getRequestAlbumUrl() {
        return getServerNetAddressHost() + "mobile/studio/album/edit";
    }

    public String getIsTagNewVersion() {
        return getServerNetAddressHost() + "metadatav2-mobile/metadata/switch/" + System.currentTimeMillis();
    }

    public String getAlbumForEditUrl() {
        return getServerNetAddressHost() + "album-mobile-writer/studio/album/forEdit";
    }

    /*
     * 创建专辑页面个人认证
     */
    public String getAlbumAuthenticationUrl() {
        return getHybridHost() + "hybrid/api/layout/addv/person";
    }

    /*
     * 文章类型映射的默认专辑分类和标签
     */
    public String getRequestMappingCategoryTagUrl() {
        return getMNetAddressHost() + "anchor-read-web/app/getAlbumTag";
    }

    /*
     *修改专辑信息
     */
    public String getUpdateAlbumUrl() {
        return getServerNetAddressHost() + "mobile/studio/album/update";
    }

    /**
     * 获取主播会员会员卡详情
     */
    public String getMemberCardListUrl() {
        return getMemberAddressHost() + "membership/membercard/v1/";
    }

    public String getNonceUrl() {
        // 测试监控nonce app接口
        if (ConstantsOpenSdk.isDebug) {
            IXdcsPost xdcsPost = RouterServiceManager.getInstance().getService(
                    IXdcsPost.class);
            if (xdcsPost != null) {
                xdcsPost.statErrorToXDCS("NonceAppFilter", Log.getStackTraceString(new Throwable("nonceApp")));
            }
        }
        return getServerNetAddressHost() + "mobile/nonce/app";
    }

    public String getMultiNonceUrl() {
        return getServerNetAddressHost() + "mobile-nonce/nonce/multi/query/" + System.currentTimeMillis();
    }

    public String getPassportNonceUrl() {
        return getServerPassportAddressHostS() + "xthirdparty-toolkit-web/nonce/";
    }

    public String getSendMesssageToWeixinUrl() {
        return getServerPassportAddressHostS() + "xthirdparty-toolkit-web/weixin/10/sendMessage";
    }

    public String getMemberListUrl() {
        return getServerNetAddressHost() + "mobile/discovery/v1/member/list";
    }

    /**
     * 获得广场数据
     */
    public String getSquareListUrl() {
        return getServerNetAddressHost() + "mobile/discovery/v1/square/list";
    }

    /**
     * 更新广场tab的时间
     */
    public String updateSquareTabTimeUrl() {
        return getServerNetAddressHost() + "mobile/v1/no_read";
    }

    public String updateUnReadMsgUrl() {
        return getServerNetAddressHost() + "mobile-user/unread/" + System.currentTimeMillis();
    }

    public String getHomeUnreadUrl() {
        return getServerNetAddressHost() + "mobile/home/<USER>/" + System.currentTimeMillis();
    }

    /**
     * 是否显示主播页会员条
     */
    public String getIsShowMemberInfoUrl() {
        return getMemberAddressHost() + "membership/memberproduct/show/v1/owner/";
    }

    /**
     * 主播会员确认支付页面
     */
    public String getMemberPayDetailUrl() {
        return getMemberAddressHost() + "membership/memberorder/context/confirmation/v1/memberproductid/";
    }

    /**
     * 获得主播会员的支付链接
     */
    public String getMemberPayUrl() {
        return getMemberAddressHost() + "membership/order/placeorder/v1";
    }

    /**
     * 主播会员会员卡详情
     * 参数是memberCardId
     */
    public String getMemberCardDetailUrl() {
        return getMemberAddressHost() + "membership/memberinfo/card/detail/v1/id/";
    }

    /**
     * 主播会员会员卡详情
     * 参数是memberProductId
     */
    public String getMemberProductIdUrl() {
        return getMemberAddressHost() + "membership/memberinfo/card/detail/v1/productid/";
    }

    /**
     * 主播会员支付成功以后
     */
    public String getMemberPaySuccessUrl() {
        return getMemberAddressHost() + "membership/memberorder/context/succeed/v1/memberproductid/";
    }

    /**
     * 主播会员会员页详情
     */
    public String getMemberPageDetailUrl() {
        return getMemberAddressHost() + "membership/memberinfo/v1/owner/";
    }


    /**
     * 听友圈host
     *
     * @return
     */
    private String getTingAddressHost() {
        return getServerNetAddressHost() + "chaos/";
    }

    /**
     * 获取粉丝动态列表
     *
     * @return
     */
    public String getFansDynamicUrl() {
        return getTingAddressHost() + "v2/feed/list/followings";
    }


    /**
     * 听友圈动态的视频地址详情
     *
     * @return
     */
    public String getDynamicVideoAddressInfo() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return "http://mpay.ximalaya.com/" + "chaos/v1/video/detail";
        } else if (BaseConstants.ENVIRONMENT_UAT == BaseConstants.environmentId) {
            return "http://mpay.uat.ximalaya.com/" + "chaos/v1/video/detail";
        } else {
            return "http://*************/" + "chaos/v1/video/detail";
        }
    }

    public String getDirectVideoPlayUrl() {
        return getServerNetAddressHost() + "discovery-feed/v1/getPlayUrl";
    }

    public String getDynamicVideoAd(long feedId) {
        return getTingAddressHost() + "v1/ad/feed/" + feedId;
    }

    public String getVideoAdList() {
        return getTingAddressHost() + "v1/ad/ownedProducts";
    }

    public String getRecommendUserList() {
        return getServerNetAddressHost() + "nexus/v1/recommend/authors/query";
//        return "http://ops.ximalaya.com/api-manager-open-api/apiMock/getMockUrl/60/1/mock/v1/recommend/authors/query";
    }

    public String closeRecommendAuthors() {
        return getServerNetAddressHost() + "nexus/v1/recommend/authors/close";
//        return "http://ops.ximalaya.com/api-manager-open-api/apiMock/getMockUrl/60/1/mock/v1/recommend/authors/close";
    }

    public String getVideoAdBtn() {
        return getTingAddressHost() + "v1/ad/enable";
    }


    /**
     * 拍摄工具埋点上报
     *
     * @return
     */
    public String uploadShootVideoRecords() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/records/add" + "?device=android";
    }

    /**
     * 拍摄工具埋点上报
     *
     * @return
     */
    public String uploadShootVideoMaterials() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/videos/add" + "?device=android";
    }

    //参与投票
    public String getCommunityVoteUrl(long communityId, long voteId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId
                + "/votes/" + voteId;
    }

    /**
     * 创建动态
     *
     * @return
     */
    public String createDynamicUrl() {
        return getTingAddressHost() + "v3/feed/create" + "?device=android";
    }

    /**
     * 删除动态
     *
     * @return
     */
    public String delDynamicUrl() {
        return getTingAddressHost() + "v2/feed/delete";
    }

    public String getRecordSharedUrl(long communityId, long articleId) {
        return getMNetAddressHost() + "community/v1/communities/" + communityId + "/articles/" + articleId + "/shared";
    }

    /**
     * 沉浸式播放页跳转的背景音聚合进入拍摄页前要查询素材
     */
    public String getQueryDynamicCollectUrl(long feedId) {
        return getMNetAddressHost() + "community/v1/user/articles/" + feedId + "/is-collect";
    }

    /**
     * 推荐
     */
    public String recommendDynamicUrl(long communityId, long articleId) {
        return getMNetAddressHost() + "community/v1/communities/" + communityId + "/articles/" + articleId + "/recommend";
    }

    /**
     * 赞动态
     *
     * @return
     */
    public String zanDynamicUrl() {
        return getTingAddressHost() + "v2/feed/praise/create";
    }


    /**
     * 取消赞
     *
     * @return
     */
    public String cancleZanDynamicUrl() {
        return getTingAddressHost() + "v2/feed/praise/delete";
    }


    /**
     * 踩动态
     */
    public String caiDynamicUrl(long feedId) {
        return getTingAddressHost() + "v1/feeds/" + feedId + "/dislike/create";
    }

    public String soundPatchAllResource() {
        return getServerNetAddressHost() + "sound-guide-portal/sound/resource/all/" + System.currentTimeMillis();
    }

    /**
     * 取消踩
     */
    public String cancelCaiDynamicUrl(long feedId) {
        return getTingAddressHost() + "v1/feeds/" + feedId + "/dislike/delete";
    }

    /**
     * 赞动态的评论
     *
     * @return
     */
    public String zanDynamicCommentUrl() {
        return getTingAddressHost() + "v3/feed/comment/praise/create";
    }

    public String topicRedPacketGainUrl() {
        return getServerNetAddressHost() + "social-award-web/v1/topicRedPacket/gain";
    }

    /**
     * 取消赞动态的评论
     *
     * @return
     */
    public String cancleZanDyanmicCommentUrl() {
        return getTingAddressHost() + "v3/feed/comment/praise/delete";
    }


    public String getUserDynamicListUrl() {
        return getTingAddressHost() + "v2/feed/list/user";
    }

    public String getOtherUserDynamicListUrl() {
        return getTingAddressHost() + "v2/feed/list/other";
    }

    public String getPersonDynamicListUrl(long uid) {

        return getServerNetAddressHost() + "chaos-discovery-web/v2/personal/" + uid + "/feed/list/"
                + System.currentTimeMillis();
    }

    public String getPersonDynamicListUrlNew(long uid) {
        return getServerNetAddressHost() + "chaos-discovery-web/v3/personal/" + uid + "/feed/list/" + System.currentTimeMillis();
    }

    /**
     * 听友圈动态详情
     *
     * @return
     */
    public String dynamicDetailUrl() {
        return getTingAddressHost() + "v2/feed/detail";
    }

    /**
     * 动态评论回复列表
     *
     * @return
     */
    public String dynamicCommentDetailUrl() {
        return getTingAddressHost() + "v3/comment/reply/list";
    }

    /**
     * 查询动态的评论列表
     *
     * @return
     */
    public String dynamicRequestCommentUrl() {
        return getTingAddressHost() + "v3/comment/list";
    }

    /**
     * 查询动态的点赞列表
     *
     * @return
     */
    public String dynamicUpVoteUrl() {
        return getTingAddressHost() + "v1/feed/praise/list";
    }

    /**
     * 听友圈评论一条动态
     */
    public String dynamicReplyCommentUrl() {
        return getTingAddressHost() + "v3/feed/comment/create";
    }

    /**
     * 听友圈删除一条评论
     */
    public String dynamicDeleteCommentUrl() {
        return getTingAddressHost() + "v2/feed/comment/delete";
    }

    /**
     * 听友圈举报一条评论
     */
    public String dynamicReportCommentUrl() {
        return getTingAddressHost() + "v1/feed/comment/report";
    }

    /**
     * 听友圈举报一条动态
     */
    public String dynamicReportUrl() {
        return getTingAddressHost() + "v1/feed/report";
    }

    /**
     * 听友圈查询接收的评论列表
     */
    public String dynamicMessageCommentUrl() {
        return getTingAddressHost() + "v2/notice/comment/rec";
    }

    /**
     * 听友圈查询接收的点赞列表
     */
    public String dynamicMessageUpvoteUrl() {
        return getTingAddressHost() + "v2/notice/praise/rec";
    }

    public String getTopicFollowingCount() {
        return getServerNetAddressHostForTopic() + "nexus/v1/follower/topics-count";
    }

    public String getServerNetAddressHostForTopic() {
        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "http://liveroom.ximalaya.com/";
        } else if (AppConstants.environmentId == BaseConstants.ENVIRONMENT_UAT) {
            return "http://liveroom.uat.ximalaya.com/";
        } else {
            return "http://m.test.ximalaya.com/";
        }
    }

    public String getAnchorInfo() {
        return getServerNetAddressHost() + "mobile-user/artist/intro";
    }


    /**
     * 查询听友圈消息未读数
     */
    public String dynamicMessageNumUrl() {
        return getTingAddressHost() + "v1/notice/unread/rec";
    }


    public String shareApp() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/document/inviteFind";
    }

    /**
     * H5页面喜点支付
     *
     * @return
     */
    public String xiPay() {
        return getMpAddressHost() + "xmacc/xipay";
    }

    /**
     * 查询会员购买信息
     *
     * @return
     */
    public String getSimpleMemberInfo() {
        return getMpAddressHost() + "membership/memberinfo/card/v1/ownerid/";
    }


    public String getQuoraDetail(int questionId, boolean playNow) {
        if (playNow) {
            return getHotLineHost() + "hotline/question/" + questionId + "?audition=play";
        } else {
            return getHotLineHost() + "hotline/question/" + questionId;
        }
    }

    public String payForQuora(int answerId, double price) {
        return getHotLineHost() + "hotline/pay/answer?" + "answerId=" + answerId + "&price=" + price;
    }

    /**
     * 获取推荐好友
     *
     * @return
     */
    public String getRecommendUsers() {
        return getServerNetAddressHost() + "fans/user/recommend";
    }

    /**
     * 推荐流加载刷新。1、新建应用进程时候调用;2、进程存活情况,在非推荐流页面时间超过6小时调用
     *
     * @return
     */
    public String getRecommendFlowListByLoad() {
        return getRecommendFlowHost() + "recsys/stream/load";
    }

    public String getAlbumPayParamsUrl() {
        return getMpAddressHost() + "payable/order/thirdpartypay/native/prepare/album/v2";
    }

    public String getMemberPayParamsUrl() {
        return getMpAddressHost() + "membership/order/thirdpartypay/prepare/membership/v2";
    }

    public String checkIsMemberAuthorized() {
        return getMpAddressHost() + "membership/memberinfo/card/v1/ownerid/";
    }

    public String getDownloadQualityInfo() {
        return getServerNetAddressHost() + "mobile/download/track/quality/list";
    }

    /**
     * 推荐流上拉加载更多。
     *
     * @return
     */
    public String getRecommendFlowListByMore() {
        return getRecommendFlowHost() + "recsys/stream/query";
    }

    /**
     * 插入刷新。1、用户下拉刷新;2、用户点击“以上为刚更新的...条推荐，点击获取更多”一栏
     * 3）用户刷到整个列表底部（没有更多的内容了），点击“点击获取更多推荐”
     *
     * @return
     */
    public String getRecommendFlowListByFeed() {
        return getRecommendFlowHost() + "recsys/stream/feed";
    }

    /**
     * 讨厌该推荐流
     *
     * @return
     */
    public String hateRecommendFlow() {
        return getRecommendFlowHost() + "recsys/stream/dislike";
    }

    /**
     * 全民朗读文稿
     */
    public String getRecordPaperUrl() {
        return BaseUtil.chooseEnvironmentUrl("http://a.ximalaya.com/m/all/people/list",
                "http://hybrid.test.ximalaya.com/m/all/people/list",
                "http://a.test.ximalaya.com/m/all/people/list");
    }

    /**
     * 录音创作活动
     */
    public String getRecordCreateActivityUrl() {
        return BaseUtil.chooseEnvironmentUrl("https://m.ximalaya.com/encourage-activity-web/h5CreateActivity",
                "http://m.test.ximalaya.com/encourage-activity-web/h5CreateActivity",
                "http://m.test.ximalaya.com/encourage-activity-web/h5CreateActivity");
    }

    /**
     * 喜马拉雅送礼 Service Base Url
     *
     * @wiki http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/gift/gift-web-api.md
     */
    public String getLiveGiftBaseUrl() {
        return getLiveServerMobileHttpHost() + "gift/";
    }

    /*******  新的专辑下载声音排序接口 ********/
    /**
     * 获取付费专辑已购未下载声音列表信息v1
     *
     * @return
     */
    public String getBuyedWithoutDownloadTracksV1() {
        return getServerNetAddressHost() + "mobile/download/v1/album/paid";
    }

    /**
     * 专辑声音批量下载获取下载信息接口v1
     *
     * @return
     */
    public String getAlbumBatchDownloadInfoV1() {
        return getServerNetAddressHost() + "mobile/download/v1/album/";
    }

    /**
     * 专辑声音批量下载获取下载信息接口v2
     *
     * @return
     */
    public String getAlbumBatchDownloadInfoV2() {
        return getServerNetAddressHost() + "mobile/download/v2/album/";
    }

    /**
     * 获取单条声音下载信息新接口
     *
     * @param trackId
     * @return
     */
    public String getTrackDownloadInfoV2(String trackId) {
        return getServerNetAddressHost() + "mobile/download/v2/track/"
                + trackId + "/ts-" + System.currentTimeMillis();
    }

    /**
     * vip-专辑剩余声音批量下载获取下载信息接口
     *
     * @return
     */
    public String getAlbumLeftDownloadInfo() {
        return getServerNetAddressHost() + "mobile/download/rest/";
    }

    /**
     * 获取单条视频下载信息新接口
     *
     * @param uid
     * @param trackId
     * @return
     */
    public String getVideoDownloadInfo(String uid, String trackId) {
        return getServerNetAddressHost() + "mobile/download/v1/" + uid + "/video/"
                + trackId + "/ts-" + System.currentTimeMillis();
    }

    /**
     * 更新专辑声音ordernum
     *
     * @return
     */
    public String getAlbumTracksOrderNum() {
        return getServerNetAddressHost() + "mobile/download/v2/order/sync" + "/ts-" + System.currentTimeMillis();
    }

    /**
     * 获取激活防重放token
     *
     * @return
     */
    public String getActiveToken() {
        return getServerNetAddressHost() + "device-mobile/nonce/active";
    }

    public String getModifyImeiUrl() {

        return getServerNetAddressHost() + "device-mobile/v1/update/android/authorization";
    }

    /**
     * 获取新的激活接口
     *
     * @return
     */
    public String activateApp() {
        return getServerNetAddressHost() + "device-mobile/v1/active/android";
    }

    /**
     * 如果激活接口上报时没有上报oaid 需要将oaid上报下
     *
     * @return
     */
    public String fetchOaid() {
        return getServerNetAddressHost() + "device-mobile/v1/update/android/delay";
    }

    public String getCouponsCount() {
        //return getServerCouponRpc() + "getUsableCouponCount";
        return getServerCouponRpc() + "getUsableCouponCount/v2";
    }

    public String getRedEnvelopeList() {
//        return getServerCouponRpc() + "ad-coupon-rpc/getUsableRedEnvelope";
        return getServerCouponRpc() + "getUsableRedEnvelope";
    }

    public String getMyCouponList() {
        return getMNetAddressHostS() + "promotion/coupon/user/index/list";
    }


    /**
     * 获取分享专辑优惠券文案
     *
     * @return
     */
    public String shareCouponForAlbum() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/document/coupon/album";
    }

    /**
     * 获取分享专辑优惠券文案
     *
     * @return
     */
    public String shareCouponForActivity() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/document/coupon/activity";
    }

    /**
     * 获取分享红包文案
     *
     * @return
     */
    public String shareRedEnvelop() {
        return getServerNetAddressHost() + "thirdparty-share/share";
    }

    /**
     * 获取插件更新
     * //http://mobile.test.ximalaya.com/nuwa-portal/check/xxx?signature=xxx
     * //http://gitlab.ximalaya.com/x-fm/nuwa/tree/master/nuwa-portal
     * xxx:timestamp
     * signature:签名
     */
    public String getLastestPluginInfoListUrl(@Nullable BundleModel bundleModel) {
        if (bundleModel != null && Configure.carBundleModel.bundleName.equals(bundleModel.bundleName)) {
            boolean boo = ConfigureCenter.getInstance().getBool("android", "new_car_plugin_server", true);
            if (!boo) {
                return getServerNetAddressHost() + "nuwa-portal/check/plugin/";
            }
            return getDogPortalHost() + "dog-portal/checkOld/plugin/";
        } else {
            if (useNewPluginServer()) {
                return getDogPortalHost() + "dog-portal/checkOld/plugin/";
            }

            return getServerNetAddressHost() + "nuwa-portal/check/plugin/";
        }
    }

    public String getPluginAndPatchInfoUrl() {

        return getDogPortalHost() + "dog-portal/checkOld2/all/" + System.currentTimeMillis();
    }

    public String getCheckResourceUrl() {
        return getDogPortalHost() + "dog-portal/check/resource/";
    }

    public String getCheckSchemeUrl() {
        return getDogPortalHost() + "dog-portal/check/scheme/";
    }

    private boolean hasInitUserNewDogPortal = false;
    private boolean userNewDogPortal = true;

    private String getDogPortalHost() {

        if (!hasInitUserNewDogPortal) {

            userNewDogPortal = !ConfigureCenter.getInstance().getBool("sys", "is_use_old_dog_portal_server", false);

            hasInitUserNewDogPortal = true;
        }

        if (!userNewDogPortal) {

            return getServerNetAddressHost();
        } else {

            return getServerNetMcdAddressHost();
        }

    }

    private boolean mHasInitUserNewPluginServer = false;
    private boolean mUseNewPluginServer = false;

    /**
     * 确保在拉取插件和patch时使用的是同一个后台
     */
    private synchronized boolean useNewPluginServer() {
        if (mHasInitUserNewPluginServer) {
            return mUseNewPluginServer;
        }

        ConfigureCenter.getInstance().registerConfigFetchCallback(new IConfigureCenter.ConfigFetchCallback() {
            @Override
            public void onUpdateSuccess() {
                boolean boo = ConfigureCenter.getInstance().getBool("android", "newPluginServer", false);
                SharedPreferences preferences = BaseApplication.getMyApplicationContext().getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE + DeviceUtil.getAppVersionCode(BaseApplication.getMyApplicationContext()), Context.MODE_MULTI_PROCESS);
                preferences.edit().putBoolean(SpConstants.KEY_USE_NEW_PLUGIN_SERVER, boo).apply();
            }

            @Override
            public void onRequestError() {

            }
        });

        SharedPreferences preferences = BaseApplication.getMyApplicationContext().getSharedPreferences(SpConstants.FILE_PLUGIN_SHARE_FILE + DeviceUtil.getAppVersionCode(BaseApplication.getMyApplicationContext()), Context.MODE_MULTI_PROCESS);

        mUseNewPluginServer = preferences.getBoolean(SpConstants.KEY_USE_NEW_PLUGIN_SERVER, true);

        mHasInitUserNewPluginServer = true;
        return mUseNewPluginServer;
    }

    /**
     * 获取最新的插件补丁
     */
    public String getLastestPluginPatchUrl(@Nullable BundleModel bundleModel) {
        if (bundleModel != null && Configure.carBundleModel.bundleName.equals(bundleModel.bundleName)) {
            boolean boo = ConfigureCenter.getInstance().getBool("android", "new_car_plugin_server", true);
            if (!boo) {
                return getServerNetAddressHost() + "nuwa-portal/check/pluginJar/";
            }
            return getDogPortalHost() + "dog-portal/checkOld/pluginJar/";
        } else {
            if (useNewPluginServer()) {
                return getDogPortalHost() + "dog-portal/checkOld/pluginJar/";
            }
            return getServerNetAddressHost() + "nuwa-portal/check/pluginJar/";
        }
    }

    /**
     * 插件下载统计
     * http://mobile.test.ximalaya.com/nuwa-portal/collect/xxx?downloads=xxx&installs=xxx&signature=xxx
     *
     * @return
     */
    public String getPluginDownloadStatisticsUrl() {
        return getServerNetAddressHost() + "nuwa-portal/collect/";
    }

    /**
     * 根据优惠券id查询专辑列表或者会员列表
     *
     * @return
     */
    public String searchByCouponId() {
        return getSearchHost() + "template/search/coupon";
    }

    public String clickCommentAlert() {
        return getServerNetAddressHost() + "comment-mobile/comment/clickAlert";
    }

    /**
     * 查询云历史纪录ids
     */
    public String getCloudHistoryIdsUrl() {
        return getServerNetAddressHost() + "nyx/history/query/id/list";
    }

    /**
     * 根据id查询云历史详细信息
     */
    public String getCloudHistoryDetailUrl() {
        return getServerNetAddressHost() + "nyx/history/query/detail";
    }


    /**
     * 查询云历史纪录
     */
    public String getCloudHistoryUrl() {
        return getServerNetAddressHost() + "nyx/v1/history/query/android";
    }

    /**
     * 合并云历史纪录
     */
    public String mergeCloudHistoryUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/history/merge/android";
    }

    public String getTTSArticle(String id) {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS) + "hoverball-mobile/article/query/" + id;
    }

    /**
     * 删除云历史纪录
     */
    public String deleteCloudHistoryUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/history/delete/android";
    }

    /**
     * 批量删除历史记录
     */
    public String batchDeleteCloudHistoryUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/history/batchDelete/android";
    }

    /**
     * 清空云历史纪录
     */
    public String clearCloudHistoryUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/history/clear/android";
    }

    /**
     * 添加离线云历史
     */
    public String addOfflineCloudHistoryUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/history/offlineAdd/android";
    }


    /**
     * 获取隐私设置的url
     *
     * @return
     */
    public String getPrivateSettingUrl() {
        //return getServerSkinHost() + "m/get_common_private_settings";
        return getServerSkinHost() + "mobile-settings/switch/private/get";
    }

    /**
     * 进行隐私设置的url
     *
     * @return
     */
    public String getSetPrivateUrl() {
        //return getServerSkinHost() + "m/set_common_private_settings";
        return getServerSkinHost() + "mobile-settings/switch/private/set";

    }

    /**
     * 声音计数
     *
     * @return
     */
    public String getTrackCountUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/track/count/android";
    }

    public String getTrackCountUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/track/count/android";
    }

    public String getTrackCountUrlV3() {
        return getXDCSCollectAddressHost() + "nyx/v3/track/count/android";
    }

    public String getMyClubReplayTrackCountUrl() {
        return getServerNetAddressHost() + "nyx/common/count";
    }

    public String getMyclubReplayStatisticsUrl() {
        return getServerNetAddressHost() + "nyx/common/statistic/android";
    }

    /**
     * 声音信息统计
     *
     * @return
     */
    public String getTrackStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/track/statistic/android";
    }

    public String getTrackStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/track/statistic/android";
    }

    public String getLiveStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/anchorlive/statistic/android";
    }

    public String getLiveStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/anchorlive/statistic/android";
    }

    /**
     * @return 播放页文稿阅读时长上报
     */
    public String getManuscriptStatisticUrl() {
        return getServerNetAddressHost() + "nyx/manuscript/statistic/android";
    }

    /**
     * 广播计数
     *
     * @return
     */
    public String getRadioCountUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/radio/count/android";
    }

    public String getRadioCountUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/radio/count/android";
    }

    /**
     * 主播收费专辑带评价列表
     *
     * @return
     */
    public String getPayCommentAlbum() {
        return getServerNetAddressHost() + "mobile/v1/artist/myPayAlbums";
    }

    /**
     * 广播信息统计
     *
     * @return
     */
    public String getRadioStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/radio/statistic/android";
    }

    public String getRadioStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/radio/statistic/android";
    }

    public String getVideoStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/video/statistic/android";
    }

    public String getVideoStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/video/statistic/android";
    }

    public String getDubShowStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/show/statistic/android";
    }

    public String getDubShowStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/show/statistic/android";
    }

    public String getChaosVideoStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/chaos/video/statistic/android";
    }

    public String getContentVideoStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v2/content/video/statistic/android";
    }


    /**
     * 现场直播计数
     *
     * @return
     */
    public String getActivityCountUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/activitylive/count/android";
    }

    public String getActivityCountUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/activitylive/count/android";
    }

    /**
     * 现场直播统计
     *
     * @return
     */
    public String getActivityStatisticsUrl() {
        return getXDCSCollectAddressHost() + "nyx/v1/activitylive/statistic/android";
    }

    public String getActivityStatisticsUrlV2() {
        return getXDCSCollectAddressHost() + "nyx/v2/activitylive/statistic/android";
    }


    public String getRankGroupInfo() {
        return getServerNetAddressHost() + "mobile/discovery/v1/rankGroup/info";
    }

    public String getSimpleCategoryUrl() {
        return getServerNetAddressHost() + "mobile/discovery/v1/rankGroup/categories";
    }

    public String getRankGroupAlbumList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/rank/album";
    }

    public String getCategoryRankGroupInfo() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/category/cluster/tabs";
    }

    public String getCategoryRankGroupInfoNew() {
        return getServerNetAddressHost() + "discovery-ranking-web/v4/category/cluster/tabs";
    }

    public String getCategoryRankGroupInfoNewForElderly() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/category/cluster/tabs";
    }

    public String getCategoryRankGroupAlbumListNew() {
        return getServerNetAddressHost() + "discovery-ranking-web/v3/category/concreteRankList";
    }

    public String getCategoryConcreteRankListUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/v4/category/concreteRankList";
    }

    public String getSingleRankListUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/v4/ranking/singleRankList";
    }

    public String getSimpleAggregateRankUrl() {
        return getServerNetAddressHost() + "discovery-category/aggregateRankList/v2/AggregateRankListTabs";
    }


    public String getAggregateRankGroupAlbumList() {
        return getServerNetAddressHost() + "discovery-category/aggregateRankList/concreteRankList";
    }

    public String getAggregateRankGroupAnchorAlbumList() {
        return getServerNetAddressHost() + "discovery-category/aggregateRankList/anchorRankList";
    }

    /**
     * 配置用户聊天设置信息
     */
    public String setTalkSettingUrl() {
        return getServerNetAddressHost() + "mobile/settings/switch/set";
    }

    /**
     * 拉黑用户黑名单
     */
    public String setBlackListUrl() {
        return getServerNetAddressHost() + "mobile-user/setting/pullblack";
    }

    /**
     * 获取用户聊天设置信息（用于私信聊天列表）
     */
    public String getTalkSettingInfoUrl() {
        return getServerNetAddressHost() + "mobile/user/infos";
    }

    public String getAllTalkSettingInfoUrl() {
        return getServerNetAddressHost() + "mobile/chatlist/infos";
    }


    public String getRankGroupTrackList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/rank/track";
    }

    public String getRankGroupAnchorList() {
        return getServerNetAddressHost() + "mobile/discovery/v1/rank/anchor";
    }

    public String getFreeListenList() {
        return getMpAddressHost() + "payable/wiretap/records";
    }

    //请求参数：trackId：声音ID，需要用户登录状态  需要签名
    public String shareFreeListenSuccess() {
        return getMpAddressHost() + "payable/wiretap";
    }


    /**
     * 获取分享文案新url
     *
     * @return
     */
    public String shareContentUrl() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/document/query";
    }

    public String listenCalendarUrl() {
        return getMemberAddressHost() + "payable/expertbook";
    }

    public String getSkinAndSuperGiftHost() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS);
    }

    public String newUserQueryDataUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS)
                + "discovery-feed/newUser/exit/queryData/" + System.currentTimeMillis();
    }

    public String newUserSubmitFeedbackUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS)
                + "discovery-feed/newUser/exit/submitFeedback/" + System.currentTimeMillis();
    }

    public String unlockTrackUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS) + "mobile-playpage/track/unlock";
    }

    public String freeUnlockV3TargetUserUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS) + "mobile/free_listen/exp/v3";
    }

    /**
     * 录音tcp上传的dispatcher服务器地址。
     */
    public String getDispatcherNetAddress() {
        return getUploadNetAddress() + "nupload-dispatcher/ticket/";
    }

    //录音用户引导
    public String getRecordGuideUrl() {
        return getMNetAddressHost() + "third/android/record.html";
    }

    //录音全名朗读引导
    public String getReadGuideUrl() {
        return getMNetAddressHost() + "third/android/read.html";
    }

    public String getLinkeyeUrl() {
        return getServerLinkEyeHost() + "xmdns/get";
    }

    public String getDomainString() {
        return BaseUtil.chooseEnvironmentUrl("nupload.ximalaya.com");
    }

    //退款相关
    public String getRefundDataByOrderNo(long albumId) {
        return getMpAddressHost() + "rfd/order/{orderNo}/" + albumId + "/refund";
    }

    public String getRefundDataByRefundId() {
        return getMpAddressHost() + "rfd/refund/{refundId}";
    }

    public String getRefundRequestUrl() {
        return getMpAddressHost() + "rfd/order/{merchantOrderNo}/refund";
    }

    public String getRefundCancelUrl() {
        return getMpAddressHost() + "rfd/refund/{refundId}/cancel";
    }

    // 首页数据刷新
    public String getRecommendItemRefresh() {
        return getServerNetAddressHost() + "mobile/discovery/v4/albums";
    }

    // 首页更多数据
    public String getRecommendMoreData() {
        return getServerNetAddressHost() + "mobile/discovery/v4/recommend/albums";
    }

    public String getDiscoveryFeedRecommendDataUrl() {
        return getServerNetAddressHost() + "discovery-feed/recommend";
    }

    // 首页本地听刷新数据
    public String getRecommendCityRefreshData() {
        return getServerNetAddressHost() + "/mobile/discovery/v2/city/albums";
    }

    // 首页付费精品刷新数据
    public String getRecommendPaidRefreshData() {
        return getServerNetAddressHost() + "/mobile/discovery/v1/guessYouLike/paidCategory";
    }

    /**
     * 获取当前地区国际区号
     *
     * @return
     */
    public String getInternationalCode() {
        return getLocationHost() + "v1/locateCallNum";
    }

    //每日必听相关接口================begin========================

    public String getDailyRecommendLoadUrl() {
        return getRecommendFlowHost() + "recsys/daily/rec/load";
    }

    //每日必听新接口
    public String getNewDailyRecommendLoadUrl() {
        return getRecommendFlowHost() + "recsys/daily/v2/rec/load";
    }

    public String getDailyRecommendDislikeFeedbackUrl() {
        return getRecommendFlowHost() + "recsys/daily/rec/dislike";
    }

    //每日必听相关接口================end========================

    public String getCreateAlbumTitleGuideUrl() {
        return getHybridHost() + "api/datacenter/guide_article/8";
    }

    public String getCreateAlbumCoverGuideUrl() {
        return getHybridHost() + "api/datacenter/guide_article/9";
    }

    public String getSoundReportUrl() {
        return getMNetAddressHost() + "carnival/voice_detection/index";
    }


    /**
     * "我听"页喜欢和购买数接口，升级v3，不返还喜欢，只返回购买数。
     */
    public String getFavoriteAndPurchasedCountUrl() {
        return getServerNetAddressHost() + "subscribe/v3/subscribe/paylike/statcount";
    }

    public String getSearchAppConfig() {//获取搜索ABtest
        return getSearchHost() + getSearchHub() + "front/appConfig";
    }

    /**
     * 用户请求奖券列表接口
     */
    public String getAdWelfare() {
        return getAdWelfAreHost() + "coupon";
    }

    /**
     * 热线问答消息列表
     *
     * @return
     */
    public String getHotLineMessage() {
        return getHotLineHost() + "message";
    }

    /**
     * 获取H5组件包更新
     */
    public String getLastestCompInfoUrl() {
        return getDogPortalHost() + "dog-portal/checkOld/h5/" + System.currentTimeMillis();
    }

    /* 热线问答主播页
     *
     * @param uid
     * @return
     */
    public String getAnchorHotLine(long uid) {
        return getHotLineHost() + "hotline/answerer/" + uid;
    }

    /**
     * 热线问答主播页
     *
     * @param uid
     * @return
     */
    public String getAnchorHotLine(long uid, long trackId) {
        return getHotLineHost() + "hotline/answerer/" + uid + "?trackId=" + trackId;
    }

    /**
     * 上传xdcs错误日志
     *
     * @return
     */
    public String postXdcsNetWorkError() {
        return getXDCSCollectAddressHost() + "/neterror/feedback";
    }

    /**
     * 二维码分享url转换成内部schema
     *
     * @return
     * @url http://gitlab.ximalaya.com/ting/thirdparty/wikis/document_new#应用内扫描二维码
     */
    public String getQRTransfer() {
        return getServerNetAddressHost() + "thirdparty-share/transfer";
    }

    public static String addTimeStampOnUrl(String url) {
        if (TextUtils.isEmpty(url)) return "";
        if (url.endsWith("?")) {
            return url.substring(0, url.length() - 1) + "/ts-" + System.currentTimeMillis() + "?";
        } else if (url.endsWith("/")) {
            return url + "ts-" + System.currentTimeMillis() + "/";
        } else {
            return url + "/ts-" + System.currentTimeMillis();
        }
    }

    //获取当前登录用户的黑名单列表
    public String getBlacklist() {
        return getServerNetAddressHost() + "mobile-settings/blacklist";
    }


    /**
     * 获取官方账户的session列表
     * <p>
     * http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/message-center/notice-web-v1.md
     */
    public String getOfficeSessionListUrl() {
        return getServerNetAddressHost() + "chaos-notice-web/v1/message/preview/list";
    }

    /**
     * 获取官方账户ID列表
     * <p>
     * http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/message-center/notice-web-v1.md
     */
    public String getOfficeIdListUrl() {
        return getServerNetAddressHost() + "mobile/user/official/list";
    }

    /**
     * 新用户首次启动App是否弹出引导页(请求参数为deviceId)
     * http://gitlab.ximalaya.com/x-fm/persona/wikis/InterestCard-Client
     */
    public String getIsInterestCardOfHomepage() {
        return getServerNetAddressHost() + "persona/traitCollect/queryShow/v2";
    }

    /**
     * 我要做主播h5
     *
     * @return
     */
    public String getWebOfBeCompere() {
        return getHybridHost() + "hybrid/api/layout/addv/banner";
    }

    public String getWebOfCompereLevel() {
        // 加全屏参数
        return getMNetAddressHostS() + "grade-web/views/grade" + "?_full_with_transparent_bar=1";
    }

    public String getVAuthenticationUrl() {
        return getHybridHostS() + "hybrid/api/layout/addv/entry";
    }

    public String getUserGradeTaskListUrl() {
        return getMNetAddressHost() + "grade-web/views/task";
    }

    //POST请求
    public String getAnchorMissionScore() {
        return getMNetAddressHost() + "mission-web/api/missions/score/acquire";
    }

    /**
     * 获取取消拼团url
     *
     * @param albumId
     * @return
     */
    public String getRevokeGroupBuyUrl(long albumId) {
        return getMNetAddressHost() + "groupon/albumId/" + albumId + "/cancel/app";
    }

    public String getWebOfVerify() {
        return getHybridHostS() + "hybrid/api/layout/addv/entry";
    }

    public String getChildProtectRealNameVerify() {
        return getHybridHostS() + "hybrid/api/layout/faceLogin";
    }

    public String getWebOfVerifyForRecommend() {
        return getHybridHostS() + "hybrid/api/layout/addv/entry";
    }

    public String getWebOfAnchorShare() {
        return getHybridHost() + "hybrid/api/layout/grade/anchor-grade-share";
    }

    public String getWebOfAnchorProfit() {
        return UrlConstants.getInstanse().getBusinessHost() + "manage/user/";
    }

    /**
     * 获取购买限购券页url
     *
     * @param productItemId
     * @return
     */
    public String getLimitTicketUrl(long productItemId) {
        return getServerCouponRpc() + "paidcoupon/" + productItemId + "/context";
    }

    /**
     * 获取购买限购券页支付url
     *
     * @return
     */
    public String getPayLimitTicketUrl() {
        return getServerCouponRpc() + "paidcoupon/placeorder";
    }

    /**
     * 查询限购券订单购买状态url
     *
     * @return
     */
    public String getPaidCouponOrderStatusUrl(long timestamp) {
        return getServerCouponRpc() + "paidcoupon/orderstatus/" + timestamp + "";
    }

    /**
     * 获取代金券url
     *
     * @param albumId
     * @return
     */
    public String getVoucherUrl(long albumId, long timestamp) {
        return getMNetAddressHost() + "voucher/receivable/album/" + albumId + "/" + timestamp + "";
    }

    /**
     * 领取代金券url
     *
     * @return
     */
    public String receiveVoucherUrl() {
        return getMNetAddressHost() + "voucher/receive";
    }

    /**
     * 会员音色试用
     *
     * @return
     */
    public String vipTimbreGiftUrl() {
        return getServerNetSAddressHost() + "business-sale-promotion-guide-mobile-web/gift/receive/v1";
    }

    /**
     * 登录(post) , 使用喜马拉雅uid,token 换取access_token
     *
     * @return
     */
    public String getAccessToken() {
        return getServerNetAddressHost() + "thirdparty-portal/v1/xm/auth/login";
    }

    /**
     * 长链接登陆接口
     */
    public String getLongConnectLoginUrl() {

        if (AppConstants.environmentId == AppConstants.ENVIRONMENT_ON_LINE) {
            return "http://live.ximalaya.com/xchat-login/queryaddr";
        } else {
            return "http://live.test.ximalaya.com/xchat-login/queryaddr";
        }
    }

    public String getReportBgMusicDownloadOrUseUrl() {
        return getNewZhuBoServerHost() + "music-web/client/useOrDownMusic";
    }


    // 直播微课 地址防盗链获取

    private static final String TRACK_PAY_URI_FOR_WEIKE = "http://mpay.weike.ximalaya.com/";
    private static final String TRACK_PAY_URI_FOR_WEIKE_TEST = "http://mpay.dev.test.ximalaya.com/";

    /**
     * 播放 直播微课 地址防盗链获取获取声音加密信息地址
     *
     * @return 加密信息访问 地址
     */
    public String getWeikeLiveVoicePayUrl() {

        if (BaseConstants.ENVIRONMENT_ON_LINE == AppConstants.environmentId) {
            return TRACK_PAY_URI_FOR_WEIKE + "weikemsg-web/v1/audio/queryaddr";
        } else {
            return TRACK_PAY_URI_FOR_WEIKE_TEST + "weikemsg-web/v1/audio/queryaddr";
        }

    }

    /**
     * 获取分享文案新url
     *
     * @return
     */
    public String shareThirdPartyContentUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share";
    }

    /**
     * 获取分块上传所需token的请求地址
     *
     * @param fileName   文件名
     * @param fileSize   文件大小
     * @param uploadType 文件类型
     * @return 分块上传所需token的请求地址
     */
    public String getUploadTokenUrl(String fileName, long fileSize, String uploadType) {
        return String.format(getUploadNetAddress()
                        + "clamper-token/token?fileName=%s&fileSize=%d&uploadType=%s"
                , fileName, fileSize, uploadType);
    }

    /**
     * 获取分块上传所需请求地址
     *
     * @return 分块上传请求地址
     */
    public String getUploadUrl() {
        return getUploadNetAddress() + "clamper-server/mkblk/";
    }

    /**
     * 获取分块上传后创建文件所需请求地址(除视频外的其他文件)
     *
     * @param fileSize  文件大小
     * @param extString 文件后缀名，比如MP3，png
     * @return 创建文件所需请求地址
     */
    public String getMakeNoVideoFileUrl(long fileSize, String extString) {
        return String.format(getUploadNetAddress()
                + "clamper-server/mkfile/%d/ext/%s/", fileSize, extString);
    }

    /**
     * 获取分块上传后创建文件所需请求地址(除视频外的其他文件)
     *
     * @param fileSize  文件大小
     * @param extString 文件后缀名，比如MP3，png
     * @return 创建文件所需请求地址
     */
    public String getMakeVideoFileUrl(long fileSize, String extString) {
        return String.format(getUploadNetAddress() + "clamper-server/mkfile/video/%d/ext/%s/", fileSize, extString);
    }

    /**
     * 付费精品 为你推荐更多
     *
     * @return
     */
    public String getBoutiqueRecommendForYouMore() {
        return getServerNetAddressHost() + "product/v1/category/recommend/albums/more";
    }

    /**
     * 根据请求参数的不同来获取视频播放地址或者下载地址
     *
     * @param trackId 声音id
     */
    public String getVideoInfo(long trackId) {
        StringBuilder sb = new StringBuilder();
        sb.append(getVideoPlayUrl()).append("video/").append(trackId).append("/").append("ts-").append(System.currentTimeMillis());
        return sb.toString();
    }

    /**
     * 分享领好书url
     */
    public String getShareAndGainBookUrl() {
        return getHybridHost() + "hybrid/api/layout/freeShare/myReceived?pType=";
    }

    public String receiveVipShareGift() {
        return getMNetAddressHost() + "business-universal-gift-mobile-web/divide/receive";
    }

    /**
     * 分享领好书url
     */
    public String getShareAndGainBookUrlNew() {
        return getHybridHost() + "hybrid/api/layout/freeShare/";
    }

    /**
     * 配音秀-我的作品
     *
     * @return
     */
    public String myDubPrograms() {
        return getServerNetAddressHost() + "mobile-dub-track/dubTrack/query/myworks/" + System.currentTimeMillis();
    }

    public String delMyDub() {
        return getServerNetAddressHost() + "mobile-dub-track/dubTrack/option/delete";
    }

    /**
     * 查询联通联名卡免流
     *
     * @return
     */
    public String queryUnicomFreeFlow() {
        return getServerNetAddressHost() + "freeflow/unicom/query";
    }

    /**
     * 配音秀-图片模版tab
     *
     * @return
     */
    public String onlinePictureTemplateTabs() {
        StringBuffer url = new StringBuffer(getDubServerNetAddressHost());
        url.append("dub/query/materialTypeAll/ts-");
        url.append(System.currentTimeMillis());
        return url.toString();
    }

    /**
     * 配音秀-视频模版tab
     *
     * @return
     */
    public String onlineVideoTemplateTabs() {
        StringBuilder sb = new StringBuilder();
        sb.append(getDubServerNetAddressHost());
        sb.append("video/toc/query/getAllVideoTypes/ts_");
        sb.append(System.currentTimeMillis());
        return sb.toString();
    }

    /**
     * 配音秀-图片模版分类列表
     */
    public String onlineVideoTemplateTypeList(boolean isHot, long typeId, int pageNo, int pageSize) {
        StringBuilder sb = new StringBuilder();
        sb.append(getDubServerNetAddressHost());
        sb.append("video/toc/query/");
        if (isHot) {
            sb.append("getHotVideoInfos/");
        } else {
            sb.append("getVideoInfosByType/");
            sb.append(typeId);
            sb.append("/");
        }
        sb.append(pageNo);
        sb.append("/");
        sb.append(pageSize);
        sb.append("/ts_");
        sb.append(System.currentTimeMillis());
        return sb.toString();
    }


    /**
     * 配音秀-图片模版热门分类列表
     *
     * @return
     */
    public String onlinePictureTemplateHotTypeList() {
        StringBuffer url = new StringBuffer(getDubServerNetAddressHost());
        url.append("dub/query/hotMaterialDetails/ts-");
        url.append(System.currentTimeMillis());
        return url.toString();
    }

    /**
     * 配音秀-图片模版分类列表
     *
     * @return
     */
    public String onlinePictureTemplateTypeList() {
        StringBuffer url = new StringBuffer(getDubServerNetAddressHost());
        url.append("dub/query/materialInfosByType/ts-");
        url.append(System.currentTimeMillis());
        return url.toString();
    }

    /**
     * 配音秀-图片模版详情
     *
     * @return
     */
    public String onlinePictureTemplateDetails() {
        StringBuffer url = new StringBuffer(getDubServerNetAddressHost());
        url.append("dub/query/hotMaterialDetails/ts-");
        url.append(System.currentTimeMillis());
        return url.toString();
    }

    /**
     * 配音秀-图片模版搜索
     *
     * @return
     */
    public String onlinePictureTemplateSearch(String keyWord, int pageNo, int pageSize) {
        String url = getDubServerNetAddressHost() + "square/search/template/%s/%d/%d/ts-%d";
        return String.format(url, keyWord, pageNo, pageSize, System.currentTimeMillis());
    }

    /**
     * 挑战模式-挑战列表
     *
     * @return String
     */
    public String dubChallengeListUrl(int pageNo, int pageSize) {
        String url = getDubServerNetAddressHost() + "topic/toc/query/getTopicInfoList/%d/%d/ts-%d";
        return String.format(url, pageNo, pageSize, System.currentTimeMillis());
    }

    /**
     * 挑战模式-挑战搜索
     *
     * @return String
     */
    public String dubChallengeSearchUrl(String keyWord, int pageNo, int pageSize) {
        String url = getDubServerNetAddressHost() + "topic/toc/query/searchTopicInfoList/%s/%d/%d/ts-%d";
        return String.format(url, keyWord, pageNo, pageSize, System.currentTimeMillis());
    }

    /**
     * 推荐话题列表
     *
     * @param pageNo
     * @param pageSize
     */
    public String dubTopicRecommendListUrl(int pageNo, int pageSize) {
        return getDubServerNetAddressHost() + "theme/v2/queryRecommendPage?pageNo=" + pageNo + "&pageSize=" + pageSize;
    }

    /**
     * 最近话题列表
     */
    public String dubTopicLatestListUrl() {
        return getDubServerNetAddressHost() + "theme/queryRecentPage";
    }

    /**
     * 话题查询结果
     */
    public String dubTopicResultUrl() {
        return getDubServerNetAddressHost() + "theme/getThemeResult";
    }


    /**
     * 挑战模式-新建挑战
     *
     * @return String
     */
    public String createDubChallenge() {
        StringBuffer url = new StringBuffer(getDubServerNetAddressHost());
        url.append("topic/toc/save/topicInfo/ts-");
        url.append(System.currentTimeMillis());
        return url.toString();
    }

    private String getDubServerNetAddressHost() {
        return BaseUtil.chooseEnvironmentUrl("http://hybrid.ximalaya.com/dub-web/");
    }

    public String getShareContentNew() {
        return getServerNetAddressHost() + "thirdparty-share/share";
    }

    public String getShareAiContent() {
        return getServerNetAddressHost() + "content-handle-web/ai/content/gen";
    }

    public String getPlayCompleteShareInfo() {
        return getServerNetAddressHost() + "thirdparty-share/share/scene/album/play/completed/poster/" + System.currentTimeMillis();
    }

    /**
     * 新版分享内容接口
     */
    public String getShareContentUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share/content";
    }

    /**
     * 判断专辑是否是播客专辑
     */
    public String judgePodCastAlbum() {
        return getServerNetAddressHost() + "nyx/history/query/album";
    }

    /**
     * 新版分享h5内容接口
     *
     * @return
     */
    public String getShareLinkUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share/link";
    }

    public String getShareContentOfCheckInActivity(String albumId, String activityId) {
        return getMNetAddressHost() + "business-activity-checkin-mobile/share/" + activityId + "/" + albumId;
    }

    //用户喜欢的听过的专辑列表
    public String getRecommendAlbumIds() {
        return getServerNetAddressHost() + "mobile/album/recommend/list/user";
    }

    public String getDubChallengeDetailsUrl(long topicId) {
        return getMNetAddressHost() + "mobile-dub-track/dubTrack/query/topicIndex?topicId=" + topicId + "&ts=" + System.currentTimeMillis();
    }

    //分块上传文件上传日志手机地址
    public String getUploadFileLogPostUrl() {
        return getUploadNetAddress() + "clamper-token/stat/file";
    }

    //分块上传块上传日志手机地址
    public String getUploadBlockLogPostUrl() {
        return getUploadNetAddress() + "clamper-token/stat/block";
    }

    public String queryRecommendAlbumCard() {
        return getServerNetAddressHost() + "discovery-feed/newUserFeed/ts-" + System.currentTimeMillis();
    }

    private String getMicroLessonBaseUrlV1() {

        return getServerNetAddressHost() + "microlesson-web/v1/";
    }

    //分享直播微课 赚钱查询文案和短链
    public String getRewardShareWeikeContentUrl(long courseId, int courseType) {

        if (courseType == 1) {//系列课
            return getMicroLessonBaseUrlV1() + "cps/series/" + courseId;
        } else {//单课
            return getMicroLessonBaseUrlV1() + "cps/lesson/" + courseId;
        }
    }

    /**
     * 表情云相关接口
     * http://api-doc.dongtu.com/dongtu/
     */
    private String getEmotionBaseUrl() {

        return "http://open-api.dongtu.com/open-api/";
    }

    //搜索表情
    public String getSearchEmotionUrl() {

        return getEmotionBaseUrl() + "emojis/net/search";
    }

    //搜索热门标签
    public String getSearchHotTagsUrl() {

        return getEmotionBaseUrl() + "netword/hot";
    }

    //流行动图
    public String getTrendingEmotionUrl() {

        return getEmotionBaseUrl() + "trending";
    }

    public String getSoundList() {
        return getNewZhuBoServerHost() + "music-web/client/getSoundList";
    }

    /**
     * H5分享串码解密
     */
    public String getShareCommandUrl() {
        return getServerNetAddressHost() + "thirdparty-share/shareCommand";
    }

    /**
     * 表情云相关接口
     */

    /**
     * 签约代扣结果页
     *
     * @return
     */
    public String getWebOfEntrustResult() {
        return getMNetAddressHostS() + "vip/alipay/callback";
    }

    public String getMyPrivilegedProductsHttps() {
        return getMpAddressHostS() + "payable/myprivilegedproducts/ts-" + System.currentTimeMillis();
    }

    public String getListenPackageUrl() {
        return getMpAddressHostS() + "payable/myPurchases/contents/" + System.currentTimeMillis();
    }

    public String getSearchBoughtsUrl() {
        return getSearchHost() + "vertical/myproducts/search";
    }

    /**
     * 风控处理进行图形验证相关接口
     * http://gitlab.ximalaya.com/x-fm/xm-captcha/wikis/upgrade-captcha
     */
    private static final String PIC_IDENTIFY_CODE_URL = "https://ximalaya.com/";
    private static final String PIC_IDENTIFY_CODE_URL_TEST = "https://test.ximalaya.com/";

    //获取图形验证码信息info
    public String getPicIdentifyCodeInfoUrl() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return PIC_IDENTIFY_CODE_URL + "xmcaptcha-service/getCaptcha/ts-" + System.currentTimeMillis();
        } else {
            return PIC_IDENTIFY_CODE_URL_TEST + "xmcaptcha-service/getCaptcha/ts-" + System.currentTimeMillis();
        }
    }

    //获取图形验证码
    public String getPicIdentifyCodeUrl() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return PIC_IDENTIFY_CODE_URL + "xmcaptcha-service/captchaImg/";
        } else {
            return PIC_IDENTIFY_CODE_URL_TEST + "xmcaptcha-service/captchaImg/";
        }
    }

    //校验验证码
    public String getPicIdentifyCodeVerifyUrl() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return PIC_IDENTIFY_CODE_URL + "xmcaptcha-service/checkCaptcha/ts-" + System.currentTimeMillis();
        } else {
            return PIC_IDENTIFY_CODE_URL_TEST + "xmcaptcha-service/checkCaptcha/ts-" + System.currentTimeMillis();
        }
    }

    /**
     * 风控处理进行图形验证相关接口 end=========
     */

    /**
     * 用户(设备）隐私策略
     */
    public String postAgreePrivacyUrl() {
        return getServerNetAddressHost() + "mobile/privacy/policy/agree";
    }

    public String getAgreePrivacyUrl() {
        return getServerNetAddressHost() + "mobile/privacy/policy/query/";
    }

    public String getSearchHotListNew() {
        return getSearchHost() + getSearchHub() + "hotWordBillboard/list/3.2";
    }

    //完播推荐相关排行榜
    public String getRelatedRankAlbumListUrl() {
        return getServerNetAddressHost() + "discovery-category/aggregateRankList/playPageRankList";
    }

    // 获取标记声音信息
    public String getTingMarInfoUrl() {
        return getServerNetAddressHost() + "track-mark-mobile/trackmark/count/";
    }

    public String getTinglistCollectors() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/collectorList";
    }

    // 更新听单内容推荐语
    public String updateTingListContentRecommendUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/record/updateRecText";
    }

    // 听单详情（不含内容列表）
    public String getTingListDetailUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/detail";
    }

    // 听单详情（内容列表）
    public String getTingListDetailContentUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/playlist/all";
    }

    // 听单专辑搜索
    public String getTingListSearchAlbum() {
        return getServerNetAddressHost() + "mobile/listenlist/search/list";
    }

    // 听单是否被收藏
    public String isTingListCollectedUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/isCollected";
    }

    // 收藏听单
    public String collectTingList() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/add";
    }

    // 取消收藏听单
    public String cancelCollectTingList() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/delete";
    }

    // 我收藏的听单列表
    public String getCollectTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/list";
    }

    public String getRecommendTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/queryRecommend/ts-" + System.currentTimeMillis();
    }

    // 我的喜欢-我喜欢的音乐
    public String getMyLikeV2MusicUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/playlist/myMusic";
    }

    // 我的喜欢-我喜欢的声音
    public String getMyLikeUserFavoriteTrack() {
        return getServerNetAddressHost() + "favourite-business/favorite";
    }

    // 我的喜欢-我收藏的听单列表（新加了专辑创建者名称）
    public String getMyLikeV2CollectTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/myCollect/list";
    }

    // 我的喜欢-推荐音乐
    public String getMyLikeV2RecommendMusicUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/recommend/music/list";
    }

    // 我的喜欢-听单推荐
    public String getMyLikeV2RecommendListenListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/recommend/listenlist/list";
    }

    // 我的喜欢-批量删除我收藏的听单
    public String getMyLikeV2BatchDeleteTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/multidelete";
    }


    //TA收藏的听单列表
    public String getOtherCollectTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/anchorList";
    }

    //获取标记声音列表
    public String getMarkListUrl() {
        return getServerNetAddressHost() + "track-mark-mobile/trackmark/myMarkTracklist/";
    }

    //批量删除标记声音
    public String getBatchDeleteMarksUrl() {
        return getServerNetAddressHost() + "track-mark-mobile/trackmark/batchDelete/";
    }

    // 添加精彩声音
    public String addTrackMarkUrl() {
        return getServerNetAddressHost() + "track-mark-mobile/trackmark/add/";
    }

    // 删除精彩声音
    public String deleteTrackMarkUrl() {
        return getServerNetAddressHost() + "track-mark-mobile/trackmark/delete/";
    }

    /**
     * 听单数据接口
     *
     * @return
     */
    public String getListenListPlaylistPage() {
        return getServerNetAddressHost() + "mobile/listenlist/playlist/page";
    }

    public String addToLaterListen() {
        return getServerNetAddressHost() + "listen-list-web/listenList/add";
    }

    public String getListenListMyListCnt() {
        return getServerNetAddressHost() + "mobile/listenlist/mylist/cnt";
    }

    public String getMyTingList() {
        return getServerNetAddressHost() + "mobile/listenlist/mylist/detail";
    }

    public String getMySelfBuildTingList() {
        return getServerNetAddressHost() + "mobile/listenlist/myListenlist";
    }

    public String getCollectTingListNew() {
        return getServerNetAddressHost() + "mobile/listenlist/queryListenlist";
    }

    public String getCollectTingListHasUpdate() {
        return getServerNetAddressHost() + "mobile/listenlist/collect/hasCollectUpdate";
    }

    public String getListenMyCollectTrackList() {
        return getServerNetAddressHost() + "general-relation-service/track/collect/page/query";
    }

    public String getSearchMyCollectTrackList() {
        return getServerNetAddressHost() + "general-relation-service/track/collect/page/search";
    }

    public String getTingListUnLikeUrl() {
        return getServerNetAddressHost() + "general-relation-service/general/relation/delete";
    }

    public String getTingListLikeUrl() {
        return getServerNetAddressHost() + "general-relation-service/general/relation/add";
    }

    public String getOtherTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/anchorlist/detail";
    }

    public String getListenListDetailWithTrack() {
        return getServerNetAddressHost() + "mobile/listenlist//detailWithTrack";
    }

    public String getListenListPlaylistAll() {
        return getServerNetAddressHost() + "mobile/listenlist/playlist/all";
    }

    public String getListenListMyListSimple() {
        return getServerNetAddressHost() + "mobile/listenlist/mylist/simple";
    }

    public String getListenListDelete() {
        return getServerNetAddressHost() + "mobile/listenlist/delete";
    }

    public String getListenListTrackDelete() {
        return getServerNetAddressHost() + "mobile/listenlist/track/delete";
    }

    public String getListenListTrackMultiDelete() {
        return getServerNetAddressHost() + "mobile/listenlist/track/multidelete";
    }

    public String getListenListTrackReorder() {
        return getServerNetAddressHost() + "mobile/listenlist/track/reorder";
    }

    public String getListenListAlbumReorder() {
        return getServerNetAddressHost() + "mobile/listenlist/album/reorder";
    }

    public String getListenListTrackRepost() {
        return getServerNetAddressHost() + "mobile/listenlist/track/repost";
    }

    public String getListenListCreate() {
        return getServerNetAddressHost() + "mobile/listenlist/create";
    }

    public String getListenListUpdate() {
        return getServerNetAddressHost() + "mobile/listenlist/update";
    }

    public String getListenListDetail() {
        return getServerNetAddressHost() + "mobile/listenlist/detail";
    }

    public String getAllDubMaterialTemplates(int pageNo, int pageSize, int type) {
        String url = getHybridHost() + "dub-web/square/query/allTemplates/%d/%d/%d/ts-%d";
        return String.format(url, pageNo, pageSize, type, System.currentTimeMillis());
    }

    public String getDubCategorySubTypeMaterial() {
        return getHybridHost() + "dub-web/square/query/templateByType/ts-" + System.currentTimeMillis();
    }

    public String getDubHotWordMaterial() {
        return getHybridHost() + "dub-web/square/query/hotWordTemplate/ts-" + System.currentTimeMillis();
    }

    public String getDubMaterialListDataByTagIds(int type, int pageNo, int pageSize) {
        String url = getHybridHost() + "dub-web/square/query/templateByTags/%d/%d/%d/ts-%d";
        return String.format(url, pageNo, pageSize, type, System.currentTimeMillis());
    }

    public String getMoreDubMaterialTemplates() {
        return getHybridHost() + "dub-web/square/query/newTemplate/ts-" + System.currentTimeMillis();
    }

    public String getSelfRecTemplates() {
        // 改为个性化推荐url
        return getHybridHost() + "dub-web/square/query/selfRecTemplate/ts-" + System.currentTimeMillis();
    }

    public String getRecommendTrackListUrl() {
        return getServerNetAddressHost() + "mobile-album/playlist/recommend";
    }

    public String getToListenRecommendTrackList() {
        return getServerNetAddressHost() + "mobile-playpage/planTrack/recommendContent/ts-" + System.currentTimeMillis();
    }

    public String getActivityUrl() {
        return getServerNetAddressHost() + "thirdparty-share/queryActivityUrl";
    }

    public String getSearchOfflineAlbumUrl() {
        return getSearchHost() + "recommend/offlineAlbum";
    }

    public String getSearchWantListenUrl() {
        return getSearchHost() + "feedback/wantListen";
    }

    // 获取移动免流信息
    public String getCmccProxyInfo() {
        return getServerNetAddressHost() + "freeflow/chinaMobile/flowPkgInfo/query";
    }

    public String getDynamicTopicRecentTrackUrl() {
        return getHybridHost() + "dub-web" + "/theme/getRecentTrack";
    }

    public String getDynamicTopicTrackRankingUrl() {
        return getHybridHost() + "dub-web" + "/theme/queryTemplate";
    }

    public String getDynamicMyTopicWorksUrl() {
        return getServerNetAddressHost() + "mobile-dub-track" + "/dubTrack/query/myworks";
    }


    public String getSearchRelativeAlbumUrl() {
        return getSearchHost() + "recommend/relativeAlbum";
    }

    public String getMaterialLandingRankUrl() {
        return getHybridHost() + "dub-web/square/query/rankData/ts-" + System.currentTimeMillis();
    }

    public String getMaterialLandingDualDataUrl() {
        return getHybridHost() + "dub-web/cooperate/query/cooperatesByTemplate/ts-" + System.currentTimeMillis();
    }

    public String getSkipHeadTailUrl() {
        return getServerNetAddressHost() + "mobile-settings/v1/skipsegment/get/";
    }

    public String setSkipHeadTailUrl() {
        return getServerNetAddressHost() + "mobile-settings/v2/skipsegment/set/";
    }

    // 保存首次打开时的时间
    public String saveAppOpenTime() {
        return getHybridHost() + "hybrid/api/newUserCheckIn/saveAppOpenTime";
    }

    public String getTeambitionAccessToken() {
        return SERVER_TEAMBITION + "api/oauth2/access_token";
    }

    public String getTeambitionProjects() {
        return SERVER_TEAMBITION + "api/v2/projects";
    }

    public String getTeambitionMembers() {
        return SERVER_TEAMBITION + "api/v2/organizations/5a0bd6dae09f123e2596f81a/members/search";
    }

    public String getTeambitionDefectElements(String projectId) {
        return SERVER_TEAMBITION + "api/projects/" + projectId + "/scenariofieldconfigs";
    }

    public String getTeambitionIterations(String projectId) {
        return SERVER_TEAMBITION + "api/projects/" + projectId + "/sprints";
    }

    public String getCreateDefectElements(String taskflowId) {
        return SERVER_TEAMBITION + "api/taskflows/" + taskflowId + "/tasks";
    }

    public String getUploadUrlToTeambition() {
        return SERVER_TEAMBITIONFILE + "upload";
    }

    public String getAuthorizeToken() {
        return SERVER_TEAMBITIONFILE + "authorize";
    }

    public String queryChildProtectPwd() {
        return getServerNetAddressHost() + "minor-protection-web/user/pwd/query";
    }

    public String queryChildProtectStatus() {
        return getServerNetAddressHost() + "minor-protection-web/minorProtection/status";
    }

    public String openChildProtectStatus() {
        return getServerNetAddressHost() + "minor-protection-web/minorProtection/open";
    }

    public String closeChildProtectStatus() {
        return getServerNetAddressHost() + "minor-protection-web/minorProtection/closeWithPW";
    }

    public String closeChildProtectWithoutPW() {
        return getServerNetAddressHost() + "minor-protection-web/minorProtection/closeWithoutPW";
    }

    public String verifyChildProtectPwd() {
        return getServerNetAddressHost() + "minor-protection-web/minorProtection/verify";
    }

    public String getChildProtectModifyAgeUrl() {
        return getServerNetAddressHost() + "minor-protection-web/minorProtection/age/update";
    }

    public String queryBindChildren() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/parentVision/query";
    }

    public String queryBindStatus() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/childVision/query";
    }

    public String bindChildWithParent() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/bind";
    }

    public String unbindChild() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/unbind";
    }

    public String openChildProtectByParent() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/minor/open";
    }

    public String closeChildProtectByParent() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/minor/close";
    }

    public String updateChildAgeRangeByParent() {
        return getServerNetAddressHost() + "minor-protection-web/parentChild/minor/age/update";
    }

    // 亲子平台申述Url
    public String getChildPlatformRepresentationUrl() {
        return getMNetAddressHost() + "custom-service-app/feedback/submit?type=5";
    }

    public String getChildPlatformAgreement() {
        return getMNetAddressHost() + "marketing/activity2/7215";
    }

    // 商品订单管理
    public String orderManager() {
        return getMNetAddressHostS() + "business-trade-material-aftersale-web/index/order/user";
    }

    public String getBookListUrl() {
        return "http://book.ximalaya.com/?_fullscreen=1&sonic=no/#/purchase";
    }

    public String getMyBuyedSoundsNewHttps() {
        return this.getMpAddressHostS() + "payable/myprivilege/v3/";
    }

    public String getAppSwitchSettings() {
        return getServerNetAddressHost() + "mobile/settings/switch/get/ts-" + System.currentTimeMillis();
    }

    public String setAppSwitchSettings() {
        return getServerNetAddressHost() + "mobile/settings/switch/set";
    }

    public String getNonceNew() {
        return getPassportAddressHosts() + "friendship-mobile/nonce";
    }

    public String getNonceProfile() {
        return getPassportAddressHosts() + "profile-http-app/nonce";
    }

    public String getUploadFriend() {
        return getServerNetAddressHost() + "thirdparty-share/share/friend";
    }

    // 通用APP开关设置
    public String setCommonAppSwitchSettings() {
        return getServerNetAddressHost() + "mobile/settings/set";
    }

    public String queryKingCardStatusByIp() {
        return getServerNetAddressHost() + "tencent-kingcard-web/freeflow/tencentKingcard/queryStatusByIP/" + System.currentTimeMillis();
    }

    public String getUserPortrait() {
        return getServerNetAddressHost() + "discovery-feed/queryUserTraitByUid";
    }

    public String getSignInfo() {
        return getMNetAddressHost() + "starwar/task/listen/score";
    }

    /**
     * 获取金币数量信息接口
     */
    public String getCoinInfo() {
        return getMNetAddressHost() + "starwar/lottery/task/gold-coin";
    }

    public String getSignJumpUrl() {
        return getMNetAddressHostS() + "starwar/task/listen/layout/home";
    }

    //跳转金币H5
    public String getGoldJumpUrl() {
        return getMNetAddressHostS() + "starwar/task/listen/layout/center/home";
    }


    //TODO 新版消息中心新的批量获取用户、群信息的接口
    public String getAllTalkSettingInfoUrlV2() {
        return getServerNetAddressHost() + "mobile-message-center/chatlist/infos/";
    }

    public String getListenTime() {
        return getMNetAddressHost() + "starwar/task/listen/listenTime";
    }

    public String getNewUserGuideContent() {
        return getServerNetAddressHost() + "discovery-feed/new/user/query";
    }

    public String getOrderNumRequestUrl() {
        return getServerNetAddressHost() + "mobile/download/v1/order/sync";
    }

    public String setVipBulletColorUrl() {
        return getServerNetAddressHost() + "barrage-mobile/barrage/color";
    }

    public String getEmergencyPlayUrl() {
        String host;
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            host = "http://mobile.tx.ximalaya.com/";
        } else if (BaseConstants.ENVIRONMENT_UAT == BaseConstants.environmentId) {
            host = "http://mobile.tx.uat.ximalaya.com/";
        } else {
            host = "http://mobile.test.ximalaya.com/";
        }
        return host + "mobile-accident/accident/announcement/client/query";
    }

    public String queryIting() {
        return getServerNetAddressHost() + "device-deeplink/v2/query/iting";
    }

    public String queryItingV3() {
        return getServerNetAddressHost() + "device-deeplink/v3/query/iting";
    }

    public String getRegisterRule() {
        return "https://passport.ximalaya.com/page/register_rule";
    }

    public String getPrivacyRule() {
        return "https://passport.ximalaya.com/page/privacy_policy";
    }

    public String getPrivacyRuleContent() {
        return getServerPassportHostS() + "page/privacy_policy_summary";
    }

    public String getChildPrivacyRule() {
        return "https://passport.ximalaya.com/page/information_protection";
    }

    public String getPersonalInfoCollectRule() {
        return getMNetAddressHostS() + "gatekeeper/user-info-list/";
    }

    // 获取小雅动画入口
    public String getUserActionJson() {
        return getHybridHostEnv() + "adopt/api/userActionJson";
    }

    public String getUserAgeUrl() {
        return getServerNetAddressHost() + "mobile-user/user/age";
    }

    public String getChildProtectDialogInfoUrl() {
        return getServerNetAddressHost() + "mobile-user/minorProtection/popup";
    }

    // 新版一键听url
    public String getOneKeyListenChannelsNewPlus() {
        return getServerNetAddressHost() + "discovery-feed/related/onekey/loadSceneById/ts-" + System.currentTimeMillis();
    }

    // 新版今日热点tab url
    public String getDailyNewsTabsData() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v6/queryGroups/ts-" + System.currentTimeMillis();
    }

    // 一键听9.0 tab url
    public String getDailyNewsTabsDataV9() {
        return getServerNetAddressHost() + "discovery-firstpage/headline/v2/queryGroupById/ts-" + System.currentTimeMillis();
    }

    // 设置收听模式是一键听或者睡眠模式
    public String settingOneKeyListenModeOrSleepMode() {
        return getServerNetAddressHost() + "mobile/settings/set";
    }

    // 新版一键听自定义频道
    public String saveOneKeyNewPlusCustomChannels() {
        return getServerNetAddressHost() + "discovery-feed/related/onekey/saveChannels";
    }

    // 新版今日热点自定义频道
    public String saveDailyNewsCustomChannels() {
        return getServerNetAddressHost() + "discovery-feed/discovery/radio/personal/config";
    }


    public String getMoreDualDubDetail() {
        String url = getHybridHost() + "dub-web/cooperate/query/cooperateRank/ts-%d";
        return String.format(url, System.currentTimeMillis());
    }

    /**
     * iting播放风控开关校验接口地址
     */
    public String getItingRiskControlUrl() {
        return getServerNetAddressHost() + "mobile-track/moon/play/ts-" + System.currentTimeMillis();
    }

    //新用户推荐页tab
    public String getNewRecommendTabsUrl() {
        return getServerNetAddressHost() + "discovery-category/category/newRecommendTabs";
    }

    //新用户推荐页数据
    public String getNewRecommendUrl() {
        return getServerNetAddressHost() + "discovery-category/category/newRecommend";
    }

    //发现页播放统计上传
    public String getFeedVideoPlayRecordUrl() {
        return getServerNetAddressHost() + "chaos/v1/video/play/record";
    }

    //发现页完成视频任务
    public String getCompleteFeedVideoTaskUrl() {
        return getServerNetAddressHost() + "chaos/v1/video/task/finish";
    }

    // 获取天气预报下载的url
    public String getWeatherForecastDownloadUrl() {
        return getServerNetAddressHost() + "mobile/weather/play/ts-";
    }

    public String getNewUserGuideAlbumListUrl() {
        return getServerNetAddressHost() + "discovery-feed/newUserListenList";
    }

    //周播剧
    public String getWeeklyAlbumUrl() {
        return getServerNetAddressHost() + "discovery-category/queryWeeklyAlbum";
    }

    // 获取今天是否有可展示的积分
    public String getListenTaskRecord() {
        return getMNetAddressHost() + "starwar/lottery/check-in/record";
    }

    // 取消收藏音乐
    public String cancleStarMaterial() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/stars/remove";
    }

    // 收藏音乐
    public String starMaterial() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/stars/add";
    }

    // 获取素材收藏列表
    public String getMaterialStarList() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/stars/list";
    }

    // 获取推荐的视频信息
    public String getRecommendVideo() {
        return getServerNetAddressHost() + "nexus/v1/realtime/recommend/video/" + System.currentTimeMillis();
    }

    // 获取推荐的动态
    public String getRecommendDynamic() {
        return getServerNetAddressHost() + "nexus-web/v1/realtime/recommend/feeds/" + System.currentTimeMillis();
    }

    /**
     * 推荐动态曝光id上报
     */
    public String uploadFindRecommendDisplayItemUrl() {
        return getServerNetAddressHost() + "nexus/v1/realtime/filter";
    }

    // 获取素材列表
    public String getMaterialsList() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/list";
    }

    //获取广播首页信息
    public String getRadioHomeDataUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/homePage/v2";
    }

    //老版本获取广播首页信息 老年模式使用
    public String getRadioHomeDataUrlV1() {
        return getServerNetAddressHost() + "radio-first-page-app/homePage";
    }

    //获取广播频道页查询广播接口
    public String getRadioFeedDataUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/search";
    }

    //取消订阅电台节目
    public String unSubscribeRadioUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/unSubscribe";
    }

    //订阅电台节目
    public String subscribeRadioUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/subscribe";
    }

    //查询广播对应的直播间信息
    public String getRadioLiveRoomInfo() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/getLiveRoom";
    }

    public String getFavorRadioUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/favorite";
    }

    public String getUnFavorRadioUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/unFavorite";
    }

    public String getChangeRadioListUrl() {
        return getServerNetAddressHost() + "radio-first-page-app/radio/getChangeRadioList";
    }

    public String getDailyNewsRadioList() {
        return getServerNetAddressHost() + "discovery-firstpage/hotRadios/v1/queryTodayHotRadios";
    }

    /**
     * 获取带有专辑id信息的vip商品页url
     */
    public String vipProductsWebUrl(@Nullable String specifyUrl, long albumId) {
        if (!TextUtils.isEmpty(specifyUrl)) {
            if (specifyUrl.contains("albumId")) {
                return specifyUrl;
            } else {
                try {
                    URL url = new URL(specifyUrl);
                    String protocol = url.getProtocol();
                    String host = url.getHost();
                    String path = url.getPath();
                    String query = url.getQuery();

                    StringBuilder urlBuilder = new StringBuilder();
                    urlBuilder.append(protocol).append("://");
                    urlBuilder.append(host);
                    urlBuilder.append(path);
                    urlBuilder.append("?");
                    if (TextUtils.isEmpty(query)) {
                        urlBuilder.append("albumId=").append(albumId);
                    } else {
                        urlBuilder.append(query);
                        urlBuilder.append("&");
                        urlBuilder.append("albumId=").append(albumId);
                    }
                    return urlBuilder.toString();
                } catch (Exception e) {
                    e.printStackTrace();
                    return getMNetAddressHostS() + "vip/product/ts-" + System.currentTimeMillis() + "?albumId=" + albumId;
                }
            }
        } else {
            return getMNetAddressHostS() + "vip/product/ts-" + System.currentTimeMillis() + "?albumId=" + albumId;
        }
    }

    /**
     * 获取带有专辑id和声音id信息的vip商品页url
     */
    public String vipProductsWebUrl(@Nullable String specifyUrl, long albumId, long trackId) {
        String url = vipProductsWebUrl(specifyUrl, albumId);
        return url + "&trackId=" + trackId;
    }


    /**
     * 喜马拉雅VIP会员服务协议
     */
    public String getVipServiceAgreementUrl() {
        return getMNetAddressHostS() + "gatekeeper/member-agreement-html/ts-" + System.currentTimeMillis() + "?_default_share=0&_ka=1";
    }

    /**
     * 自动续费服务规则
     */
    public String getAutoRenewRuleAgreementUrl() {
        return getPageNetSHost() + "mkt/act/8e7818e03c5f7509?_default_share=0&_ka=1";
    }

    /**
     * 儿童会员相关协议
     */
    public String getChildVipAgreementUrl(String key) {
        return getMNetAddressHostS() + "xmkp-fe-service/policy/" + key;
    }

    /**
     * 当前连接蓝牙设置信息
     */
    public String getBlueToothSettingInfo() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/setting/info";
    }

    public String settingDriveModeUpdate() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/setting/update";
    }

    public String driveModeSave() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/save";
    }

    public String getTingListActivityTagsUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/activity/detail";
    }

    public String getTingListWithTagUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/search/listenlist/byActivity";
    }

    // 删除听单内容
    public String deleteContentFromTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/track/delete";
    }

    // 批量添加专辑到听单
    public String multiAddContentToTingListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/record/multiRepost";
    }

    public String getVipAlbumReserveUrl() {
        return getServerNetAddressHost() + "vip/new/album/reserve/v1";
    }

    public String getCancelAlbumReserveUrl() {
        return getServerNetAddressHost() + "vip/new/album/cancel/v1";
    }
    /**
     * 查询驾驶模式订阅列表
     */
    public String getDriveModeSubscribeListUrl() {
        return getServerNetAddressHost() + "subscribe/v1/subscribe/driving_mode";
    }

    public String getSettingDriveModeEntry() {
        return getServerNetAddressHost() + "mobile-driving-mode/bluetooth/hassetting";
    }

    /**
     * 清除专辑未读数
     */
    public String driveModeClearUnread() {
        return getServerNetAddressHost() + "subscribe/album/unread/clear";
    }

    /**
     * 随心听
     */
    public String getEnjoyModeUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/drive/tracks";
    }


    public String queryMyListenSquares() {
        return getServerNetAddressHost() + "discovery-firstpage/squares/query/ts-";
    }

    public String getSkinSettingInfoUrl() {
        return getServerNetAddressHost() + "app-skin-service/skin/setting/info/";
    }

    public String getVipBottomTabIconInfoUrl() {
        return getMNetAddressHostS() + "business-vip-channel-web/vipbottomicon/ts-";
    }

    public String getVipBottomTabIconExposure() {
        return getMNetAddressHostS() + "business-vip-channel-web/vipbottomicon/exposure";
    }

    //获取nonce（个人页资料修改需要）
    public String getMyDetailNonce() {
        return getServerPassportHostS() + "mobile/nonce";
    }

    //修改个人简介
    public String updatePersonalBrief() {
        return getPassportAddressHosts() + "mobile/profile/updatePersonalSignature/v1";
    }

    //修改地区
    public String updateLocation() {
        return getPassportAddressHosts() + "mobile/profile/updateLocation/v1";
    }

    //修改生日
    public String updatePersonalBirth() {
        return getPassportAddressHosts() + "mobile/profile/updateBirthDay/v1";
    }

    //修改星座
    public String updateConstellation() {
        return getPassportAddressHosts() + "mobile/profile/updateConstellation/v1";
    }

    public String chatGroupNotSendPush() {
        return getServerNetAddressHost() + "imc-group-web/message/acknowledge";
    }

    public String getTrackHighlightsUrl() {
        return getServerNetAddressHost() + "shortcontent-web/shortcontent/public/page/bysourcetrack";
    }

    public String getTrackHighlightsLikeUrl() {
        return getServerNetAddressHost() + "shortcontent-web/shortcontent/like";
    }

    public String getTrackHighlightsCancelLikeUrl() {
        return getServerNetAddressHost() + "shortcontent-web/shortcontent/cancellike";
    }

    public String getAddCollectTrackUrl() {
        return getServerNetAddressHost() + "general-relation-service/track/collect/add";
    }

    public String getDeleteCollectTrackUrl() {
        return getServerNetAddressHost() + "general-relation-service/track/collect/delete";
    }

    public String getKidsAlbumTracks() {
        return getServerNetAddressHost() + "mobile-album/album/track/ts-";
    }

    public String getCommentActivityThemeUrl() {
        return getServerNetAddressHost() + "comment-mobile/v1/track/comment/activity";
    }

    // 儿童品类分享url
    public String getChildAchievementShareUrl() {
        if (BaseConstants.ENVIRONMENT_TEST == BaseConstants.environmentId) {
            return getMNetAddressHost() + "hybrid-hy-share/baby";
        } else {
            return getMNetAddressHostS() + "hybrid-hy-share/baby";
        }
    }

    public String getFeedbackUrl() {
        return getMNetAddressHostS() + "custom-service-app/feedback/submit";
    }

    public String getChapterPlayInfoUrl() {
        return getTrackPayHost() + "mobile-book-reader/security/chapter/play/query";
    }

    public String getCatalogInfoUrl() {
        return getServerNetAddressHost() + "mobile-book-reader/reader/catalog/query";
    }

    public String getChapterInfoUrl() {
        return getServerNetAddressHost() + "mobile-book-reader/reader/chapter/query";
    }

    public String getBookInfoUrl() {
        return getServerNetAddressHost() + "mobile-book-reader/reader/book/query/";
    }

    public String getBookReadCountUploadUrl() {
        return getServerNetAddressHost() + "nyx/v2/ebook/count/android";
    }

    //客服h5页面
    public String h5OfService() {
        return getMNetAddressHostS() + "cs-bridge-web/page/contact-cs?systemNum=HYqg4ZckW7D8D0NUcFqgNA&_fix_keyboard=1";
    }

    /**
     * 查询是否开通自动支付
     */
    public String getAutoRechargeStatusUrl() {
        return getMpAddressHost() + "payable/autopay/ts-" + System.currentTimeMillis();
    }

    /**
     * 发起自动充值
     */
    public String getEngageAutoRechargeUrl() {
        return getMpAddressHost() + "payable/autopay/recharge";
    }

    /**
     * 查询自动充值的结果
     */
    public String getAutoRechargeResultUrl(String orderId) {
        return getMpAddressHost() + "payable/autopay/orderstaus/" + orderId + "/ts-" + System.currentTimeMillis();
    }

    /**
     *
     * 圈子，发现页模块公用url  begin
     * */

    /**
     * 圈子相关
     */
    public String getCommunityBaseUrlV1() {

        return getMNetAddressHost() + "community/v1/";
    }

    /**
     * 圈子相关v2
     */
    public String getCommunityBaseUrlV2() {

        return getMNetAddressHost() + "community/v2/";
    }

    /**
     * 付费私享圈相关
     */
    public String getPaidCommunityBaseUrlV1() {
        return getMNetAddressHost() + "community-vipclub-web/v1/";
    }

    //置顶帖子
    public String getTopPostUrl(long communityId, long articleId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId
                + "/articles/" + articleId + "/top";
    }

    //取消置顶
    public String getCancelTopPostUrl(long communityId, long articleId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId
                + "/articles/" + articleId + "/cancel-top";
    }

    //加精帖子
    public String getEssencePostUrl(long communityId, long articleId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId
                + "/articles/" + articleId + "/essence";
    }

    //取消加精
    public String getCancelEssencePostUrl(long communityId, long articleId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId
                + "/articles/" + articleId + "/cancel-essence";
    }

    //添加禁言成员
    public String getAddBannedMemberUrl(long communityId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId + "/blacklists/add";
    }

    public String getRemoveBannedMemberUrl(long communityId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId + "/blacklists/remove";
    }

    //修改帖子分类
    public String getModifyPostCategoryUrl(long communityId, long articleId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId
                + "/articles/" + articleId + "/category/modify";
    }

    //收藏帖子
    public String getCollectPostUrl() {

        return getCommunityBaseUrlV2() + "user/articles/" + "collect";
    }

    //取消收藏帖子
    public String getCancelCollectPostUrl() {

        return getCommunityBaseUrlV2() + "user/articles/" + "cancel-collect";
    }

    //记录圈子被分享
    public String getRecordShareCommunityUrl(long communityId) {

        return getCommunityBaseUrlV1() + "communities/" + communityId + "/shared";
    }

    public String getQueryOwnCommunityUrl(long uid) {

        return getCommunityBaseUrlV1() + "communities/owners/" + uid;
    }

    //加入圈子
    public String getJoinCommunityUrl() {

        return getCommunityBaseUrlV1() + "user/communities/join";
    }

    /**
     * 获取帖子原数据
     */
    public String getEditContentUrl(long communityId, long articleId) {
        return getCommunityBaseUrlV1() + "communities/" + communityId + "/articles/" + articleId + "/richContent";
    }

    //加入的圈子列表
    public String getQueryJoinedCommunitiesUrl() {

        return getCommunityBaseUrlV1() + "user/communities/joined";
    }

    /**
     * 私享圈相关配置参数
     */
    public String getPaidZoneConfig(long communityId) {
        return getPaidCommunityBaseUrlV1() + "vipclub/communities/" + communityId + "/permission";
    }

    /**
     * 私密圈售前页
     */
    public String getZonePreSalesPage(long communityId) {
        return getMNetAddressHost() + "business-vip-club-mobile-web/page/" + communityId;
    }

    /**
     * 私享圈帖子详情售前页
     */
    public String getZonePreSalesPostDetailPage(long communityId, long articleId) {
        return getMNetAddressHost() + "business-vip-club-mobile-web/page/" + communityId + "/post-preview/" + articleId;
    }

    /**
     * 未读消息
     */
    public String getUnreadMessageUrl() {
        return getServerNetAddressHost() + "chaos-notice-web/v1/tyq/unreadcount/" + System.currentTimeMillis();
    }

    /**
     * 圈子，发现页模块公用url  end
     */
    public String getKachaPreferUrl() {
        return getServerNetAddressHost() + "shortcontent-web/shortcontent/query/related/";
    }

    /**
     * 圈子，发现页模块公用url  end
     */
    public String getKachaRecommendHomeUrl() {
        return getServerNetAddressHost() + "shortcontent-web/firstPagePool/list/query/";
    }

    /**
     * /**
     * 账号页新接口(林文波要求进行迁移)
     * 老：mobile/homePage
     * 新：mobile-user/homePage
     */
    public String getAccountHomePageUrl() {
        return getServerNetAddressHost() + "mobile-user/homePage";
    }

    /**
     * 账号页新接口(叶纬要求进行迁移)
     * 老：mobile/homePage
     * 新：mobile-user/homePage
     * 新新：mobile-user/v1/homePage
     */
    public String getHomePageUrlNew() {
        return getServerNetAddressHost() + "mobile-user/v1/homePage";
    }

    public String getHomePageUrlV9() {
        return getServerNetAddressHost() + "mobile-user/v2/homePage";
    }

    /**
     * 查询老年模式开关
     */
    public String getElderlyMode() {
        return getServerNetAddressHost() + "aged-mobile/aged/mode/query/ts-" + System.currentTimeMillis();
    }

    /**
     * 开启老年模式开关
     */
    public String openElderlyMode() {
        return getServerNetAddressHost() + "aged-mobile/aged/mode/open/ts-" + System.currentTimeMillis();
    }


    /**
     * 关闭老年模式开关
     */
    public String closeElderlyMode() {
        return getServerNetAddressHost() + "aged-mobile/aged/mode/close/ts-" + System.currentTimeMillis();
    }

    /**
     * 老年模式首页接口
     */
    public String getElderlyHomeData() {
        return getServerNetAddressHost() + "elderly-aged-api/api/home/<USER>";
    }

    /**
     * 老年模式课程上报进度接口
     */
    public String reportElderlyCoursePlayData() {
        return getServerNetAddressHost() + "aged-mobile/selection/track/playreport";
    }

    public String getCourseTrackList() {
        return getServerNetAddressHost() + "aged-mobile/selection/track/list/ts-";
    }


    /**
     * 老年模式首页自定义选品列表接口
     */
    public String getCustomSelectionAlbums() {
        return getServerNetAddressHost() + "discovery-feed/elders/custom/selection/ts-" + System.currentTimeMillis();
    }


    /**
     * 和mainurlConstants的getVipProductPageUrl一致
     */
    public String getVipProductPageUrl() {
        return getMNetAddressHostS() + "vip/product/ts-" + System.currentTimeMillis();
    }

    /**
     * 查询播放页新版分享面板分享卡片
     */
    public String getShareCardListUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share/multicard";
    }

    /**
     * 查询播放页自定义海报分享面板海报列表
     */
    public String getSharePosterListUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share/poster/list";
    }

    /**
     * 查询播放页P拉C海报分享面板海报列表
     */
    public String getSharePPosterListUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share/p_poster/list";
    }

    /**
     * 查询播放页分享数据以及vip标记
     */
    public String getPlayShareDataUrl() {
        return getServerNetAddressHost() + "discovery-category/shareAct/queryShareData";
    }

    /**
     * 查询分享任务列表
     */
    public String getShareTaskUrl() {
        return getServerNetAddressHost() + "discovery-category/shareAct/queryShareAct";
    }

    /**
     * 分享数据同步
     */
    public String getSyncShareDataUrl() {
        return getServerNetAddressHost() + "thirdparty-share/data/backflowSync";
    }

    /**
     * 领取奖励
     */
    public String getReceiveVipUrl() {
        return getServerNetAddressHost() + "discovery-category/shareAct/getAward";
    }


    public String getElderlyShareUrl() {
        if (BaseConstants.ENVIRONMENT_ON_LINE == BaseConstants.environmentId) {
            return "http://m.ximalaya.com/gatekeeper/m-elderly-guide";
        } else {
            //测试环境
            return "http://m.test.ximalaya.com/gatekeeper/m-elderly-guide";
        }
    }

    /**
     * 用户是否需要修改昵称
     */
    public String getNeedChangeNickNameStatusUrl() {
        return getPassportAddressHosts() + "user-http-app/v1/nickname/info";
    }


    //儿童模式-开启
    public String getKidModeOpenUrl() {
        return getServerNetAddressHost() + "child-mobile/child/mode/open";
    }

    //儿童模式-关闭
    public String getKidModeCloseUrl() {
        return getServerNetAddressHost() + "child-mobile/child/mode/close";
    }

    //查询儿童模式状态
    public String getKidModeStatusUrl() {
        return getServerNetAddressHost() + "child-mobile/child/mode/query";
    }

    // 是否展示登录引导
    public String canShowLoginGuide() {
        return getServerNetAddressHost() + "pizza-category/lite/index/football2?key=forceLogin";
    }

    // 推送自建通道pull接口
    public String xiMaPipePullPushData() {
        return getPushPullAddressHost() + "pns-pull-mobile/pull/query/ts-" + System.currentTimeMillis();
    }

    // 推送自建通道pull接口
    public String pushReceiveV2() {
        return getServerNetAddressHost() + "pns-portal/push/receive/v2";
    }

    public String getFirstUserUpdateInfoUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/first/updateInfo";
    }

    // 是否可以引入mobtech sdk
    public String canImportMobSdk() {
        return getServerNetAddressHost() + "pizza-category/lite/index/football2?key=mobSdk";
    }

    public String getAlbumSimilarVideos() {
        return getServerNetAddressHost() + "album-http/album/similarVideo";
    }

    public String getSettingNonceUrl() {
        return getServerNetAddressHost() + "mobile-settings/nonce";
    }

    // 获取家庭会员家庭信息
    public String getFamilyRelationshipInfoUrl() {
        return getServerNetAddressHost() + "family-vip-relationship-mobile/vipFamily/query";
    }

    /**
     * 家庭会员买赠弹窗
     */
    // 获取家庭会员家庭信息（新）
    public String getFamilyMemberInfoUrl() {
        return getMNetAddressHostS() + "business-family-vip-mobile-web/share";
    }

    // 推荐给家人听
    public String getRecommendToFamilyUrl() {
        return getMNetAddressHost() + "business-family-vip-mobile-web/recommendation";
    }

    public String getListenTaskAwardUrl() {
        return getHybridHost() + "web-hybrid-server/api/welfareCenter/listenTaskInfo";
    }

    // 订购状态及免流权限查询
    public String qryFreeFlowRights() {
        return getOpenHost() + "omp-customized-app/cbp/unicom/woMusic/app/freeTraffic/qryFreeFlowRights";
    }

    // 订购状态及免流权限查询(通过device token)
    public String queryUserRightByDeviceId() {
        return getOpenHost() + "omp-customized-app/cbp/unicom/woMusic/app/freeTraffic/queryUserRightByDeviceId";
    }

    // 免流API服务节点接口
    public String unicomServiceNodes() {
        return getOpenHost() + "omp-customized-app//cbp/unicom/woMusic/app/freeTraffic/serviceNodes";
    }

    public String getPromotionModelUrl() {
        return getServerNetAddressHost() + "sound-guide-portal/sound/guide/batch/ts-" + System.currentTimeMillis();
    }

    public String getPromotionSoundItemReportUrl() {
        return getServerNetAddressHost() + "sound-guide-portal/sound/report";
    }

    public String getCancelAdListenPermissionUrl() {
        return getMNetAddressHost() + "business-free-listen-mobile-web/counted";
    }

    public String getAdUnlockVipTargetUserUrl() {
        return getServerNetAddressHost() + "mobile-album/album/replaceConfig";
    }

    public String getListenAchievementUrl() {
        return getServerNetAddressHost() + "mobile-medal/medal/checkRealtimeMedalInfo";
    }

    public String getRewardVideoConfigUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/incentivePositionConfig/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenConfigUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/durationConfig/ts-" + System.currentTimeMillis();
    }

    public String getShortPlayFreeListenConfigUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/playletDurationConfig/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenAlbumListUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/recAlbumInfo/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenPointsTaskUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/queryListenTaskInfo/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenTabAlbumListUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/recAlbumInfoByTag/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenDialogInfoUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/popupDurationInfo/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenDialogCoverUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/popupCover/ts-" + System.currentTimeMillis();
    }

    public String getAddListenTimeUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardDuration/ts-" + System.currentTimeMillis();
    }

    public String getPopupDialogUrl() {
        return getPopupDialogPath() + "/ts-" + System.currentTimeMillis();
    }

    public String getPopupDialogPath() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/queryHomePopup";
    }

    public String getDecreaseListenTimeUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/decreaseDuration/ts-" + System.currentTimeMillis();
    }

    public String getQueryListenTimeUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/currentDuration/ts-" + System.currentTimeMillis();
    }

    public String getQueryListenTaskUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/welfare/queryListenTask/ts-" + System.currentTimeMillis();
    }

    public String getCustomerServiceConfigUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/showFeedbackEntry/ts-" + System.currentTimeMillis();
    }

    public String welfareGetAddListenTimeUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/welfare/syncListenTime/ts-" + System.currentTimeMillis();
    }

    public String welfareGetQueryListenTimeUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/welfare/queryListenTime/ts-" + System.currentTimeMillis();
    }

    public String getDurationLayerUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/durationLayer/ts-" + System.currentTimeMillis();
    }

    public String getDownloadConfigUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/downloadConfig/ts-" + System.currentTimeMillis();
    }

    public String getDownloadInfoUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/downloadInfo/ts-" + System.currentTimeMillis();
    }

    public String getRewardDownloadTimesUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardDownloadTimes/ts-" + System.currentTimeMillis();
    }

    public String getDecreaseDownloadTimesUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/decreaseDownloadTimes/ts-" + System.currentTimeMillis();
    }

    public String getRewardExperienceVipUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/rewardExperienceVip/ts-" + System.currentTimeMillis();
    }

    public String getFreeListenDialogShowUrl() {
        return getSERVER_XIMALAYA_AD() + "incentive/ting/popupDisplay/ts-" + System.currentTimeMillis();
    }
    public String getAdPreCheckUrl() {
        return getPureSERVER_XIMALAYA_ADSE() + "incentive/ting/preAd/ts-" + System.currentTimeMillis();
    }

    /**
     * 签约代扣下单
     * <p>
     * 2021.5.12 从main移动至host
     *
     * @return
     */
    public String orderOfAutoRenew() {
        return getMNetAddressHost() + "business-sign-mobile-web/subscription/v1/sign";
    }

    /**
     * 云闪付签约码上传接口
     */
    public String uploadUnionPayAuthCodeToSign() {
        return getMNetAddressHost() + "business-sign-mobile-web/subscription/v1/putAuthCodeToSign";
    }

    /**
     * 查询视频广告任务列表
     */
    public final String getQueryTaskRecordsUrl() {
        return getHybridHost() + "web-activity/task/queryTaskRecords";
    }

    /**
     * 刷新视频广告任务
     */
    public final String getRefreshClientTaskUrl() {
        return getHybridHost() + "web-activity/task/refreshClientTask";
    }

    /**
     * 刷新广告任务V2
     */
    public final String getRefreshClientTaskUrlV2() {
        return getHybridHost() + "web-activity/task/v2/refreshClientTask";
    }

    /**
     * 刷新评论积分任务
     */
    public final String getMainCommentScoreTask() {
        return getMNetAddressHost() + "web-activity/task/v2/refreshClientTask";
    }

    /**
     * 是否可参加视频免广告活动
     */
    public final String getJoinActivityCheckUrl() {
        return getMNetAddressHost() + "xfm-integral-mall-activity-web/activity/avoidAd/joinActivityCheck";
    }

    /**
     * 兑换活动商品
     */
    public final String getConvertActivityProductUrl() {
        return getMNetAddressHostS() + "xfm-integral-mall-activity-web/activity/convert/convert";
    }

    /**
     * 根据商品id（itemId）获取在购物车中状态
     */
    public String getCheckGoodsInCartUrl() {
        return getMNetAddressHost() + "business-shopping-cart-mobile-web/cart/get";
    }

    /**
     * 添加商品进入购物车
     */
    public String getAddCartUrl() {
        return getMNetAddressHost() + "business-shopping-cart-mobile-web/cart/add";
    }

    public String getCheckShareToInviteUrl() {
        return getMNetAddressHostS() + "/business-family-vip-mobile-web/share";
    }

    public String getAlbumTrackListFromCurrentTrackUrl() {
        return getServerNetSAddressHost() + "mobile-album/playlist/album/new";
    }

    // 游戏bundle-start

    public String getGameCurrentUserAuthInfo() {
        return getPassportAddressHosts() + "mobile/third/auth/getCurrentUserAuthInfo";
    }

    public String hotGameList() {
        return getPassportAddressHosts() + "mobile/third/game/hotGameRank?ts=" + System.currentTimeMillis();
    }

    public String recentGames() {
        return getPassportAddressHosts() + "mobile/third/game/userRecentPlayInfoV2?ts=" + System.currentTimeMillis();
    }

    public String userRecentPlayCardInfo() {
        return getPassportAddressHosts() + "mobile/third/game/userRecentPlayCardInfo?ts=" + System.currentTimeMillis();
    }

    public String getWxAuthAccessTokenByCode() {
//        return "https://passport.test.ximalaya.com/thirdparty-mobile/v1/auth/4/getAccessTokenByCode";
        return getPassportAddressHosts() + "thirdparty-mobile/v1/auth/4/getAccessTokenByCode";
    }

    public String wxRefreshToken(String appId, String refreshToken) {
        return "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=" + appId + "&grant_type=refresh_token&refresh_token=" + refreshToken;
    }

    public String userGameInfo() {
        return getPassportAddressHosts() + "mobile/third/game/userPlayInfo?ts=" + System.currentTimeMillis();
    }

    // 游戏bundle-end

    /**
     * 建行鉴权接口
     */
    public String getTempXmCcbUrl() {
        return getOpenHost() + "omp-customized-app/ccb/merchant_auth/getMerchantSign";
    }

    /**
     * 查询uid对应的手机号
     */
    public String getTempCorrespondingPhNumber() {
        return getOpenHost() + "omp-customized-app/ccb/app/getUserInfoByUid";
    }

    /**
     * 上报建行二类户开户成功接口
     */
    public String getTempReportCcbOpenedAccountUrl() {
        return getOpenHost() + "omp-customized-app/ccb/app/distributeVipWithUserInfo";
    }

    /**
     * 签到V2
     */
    public String getVipPunchInUrl() {
        return getHybridHost() + "web-activity/signIn/v2/signIn";
    }


    /**
     * 查询用户直播状态接口
     */
    public String getCheckUserLiveStatusListUrl() {
        return getServerNetAddressHost() + "lamia" + "/v1/user/live/status";
    }

    /**
     * 个人页/节目/专辑 设置置顶
     */
    public String getAnchorSetAlbumTopUrl() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/albums/top";
    }

    /**
     * 个人页/节目/专辑 取消置顶
     */
    public String getAnchorCancelAlbumTopUrl() {
        return getServerNetAddressHost() + "mobile-user/v2/artist/albums/top/cancel";
    }

    public String getQueryShareRewardUrl() {
        return getServerNetAddressHost() + "thirdparty-share/shareRight/sharePanel/info/query/" + System.currentTimeMillis();
    }

    public String getShareRewardReceiveUrl() {
        return getServerNetAddressHost() + "thirdparty-share/shareRight/sharePanel/award/receive/" + System.currentTimeMillis();
    }

    /**
     * 客户端启动app(用于清除推送红点标志)
     */
    public String clearBadgeNum() {
        return getServerPushHost() + "pns-portal/pns/startUp";
    }

    /**
     * 推送拉活后 是否拉播放进程
     */
    public String guardPullPlayer() {
        return getServerNetAddressHost() + "keep-alive-player-mobile/playNotify/query/ts-" + System.currentTimeMillis();
    }

    public String hwGuardPullPlayer() {
        return getServerNetAddressHost() + "keep-alive-player-mobile/playNotify/query/huawei/ts-" + System.currentTimeMillis();
    }

    public String getDynamicPushRulesUrl() {
        return getServerNetAddressHost() + "mobile/app/innerPushConfig/list/ts-" + System.currentTimeMillis();
    }

    public String getShortEpisodeCollectUrl() {
        return getServerNetAddressHost() + "general-follow-service/follow/playlet/list/query/" + System.currentTimeMillis();
    }

    public String getListenTimeForClient() {
        return getServerNetAddressHost() + "achievement-listen-web/listenDuration/listenTimeForClient";
    }

    public String getDyncListenPraiseUrl() {
        return getServerNetAddressHost() + "general-relation-service/general/relation/add/" + System.currentTimeMillis();
    }

    public String getDyncListenCancelPraiseUrl() {
        return getServerNetAddressHost() + "general-relation-service/general/relation/delete/" + System.currentTimeMillis();
    }

    public String getDiscoveryFirstPageUrl() {
        return getServerNetSAddressHost() + "rms-resource-home/squares/queryMore/" + System.currentTimeMillis();
    }

    public String wholeAlbumPriceInfoDetail(long albumId) {
        return getServerNetAddressHost()
                + "product/promotion/v9/whole/album/"
                + albumId
                + "/price/detail/ts-"
                + System.currentTimeMillis();
    }

    public String wholeAlbumPriceInfoDynamic(long albumId) {
        return getServerNetAddressHost()
                + "product/promotion/v9/whole/album/"
                + albumId
                + "/price/dynamic/ts-"
                + System.currentTimeMillis();
    }

    /**
     * 下单并跳转收银台支付所需数据的接口
     */
    public String getGoCheckoutInfoUrl() {
        return getMNetAddressHostS() + "trade-v3/placeorder";
    }

    /**
     * 我的足迹页面 查询专辑浏览历史
     */
    public String getMyFootPrintQueryUrl() {
        return getServerNetAddressHost() + "browsing-history-business/browsing/history/query/ts-" + System.currentTimeMillis();
    }

    /**
     * 我的足迹页面 添加专辑浏览历史
     */
    public String getAddMyFootPrintQueryUrl() {
        return getServerNetAddressHost() + "browsing-history-business/browsing/history/add/ts-" + System.currentTimeMillis();
    }

    /**
     * 我的足迹页面 删除专辑浏览历史
     */
    public String getRemoveMyFootPrintQueryUrl() {
        return getServerNetAddressHost() + "browsing-history-business/browsing/history/delete/ts-" + System.currentTimeMillis();
    }

    /**
     * 动态入口
     *
     * @return
     */
    public String getDynamicEntrance() {
        return getServerNetAddressHost() + "social-web/bottomTabs/dynamicEntrance/status/" + System.currentTimeMillis();
    }

    /**
     * 焦点图单模块刷新接口
     */
    public String getFocusBannerData() {
        return getServerNetAddressHost() + "discovery-feed/focus/queryFocus";
    }

    public String getAlbumAiPermissionUrl() {
        return getMNetAddressHost() + "anchor-works-web/aiDoc/albumPermission";
    }

    /**
     * 获取组件化分享面版固定资源位
     */
    public String getResourceNicheInfoUrl() {
        return getServerNetAddressHost() + "thirdparty-share/sharePanel/resourceNiche/info/query/" + System.currentTimeMillis();
    }

    public String getVipGiftCardInfo() {
        return getMNetAddressHostS() + "business-vip-level-h5-web/api/gift/card";
    }

    /**
     * 获取知识红包分享面版资源
     * */
    public String getKnowledgeRedPackageInfoUrl() {
        return getServerNetAddressHost() + "thirdparty-share/sharePanel/knowledgeRedPackage/info/query/" + System.currentTimeMillis();
    }

    public String querySubscriptionGuide() {
        return getServerNetAddressHost() + "discovery-feed/subscriptionGuide/albums/";
    }

    public String queryRecallUserRightsUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/newUserSt/recall/query";
    }

    //召回用户领取礼包接口
    public String getRecallUserReceiveGiftUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/newUserSt/recall/send";
    }

    /**
     * 青少年模式banner图片
     */
    public String getChildProtectBannerUrl() {
        return getServerNetAddressHost() + "minor-protection-web/resourceNiche/query/ts-" + System.currentTimeMillis();
    }

    // 完播明细接口
    public String getPlayCompleteDetailUrl() {
        return getARHost() + "rec-association/playend/detail";
    }

    public String getPlayCompleteDetailUrlV2() {
        return getServerNetSAddressHost() + "rec-association/playend/v2/detail";
    }

    public String getPlayCompleteGuideUrl() {
        return getServerNetAddressHost() + "thirdparty-share/general/playCompletedPage/subscribeGuide/query/" + System.currentTimeMillis();
    }

    public String uplodPlayCompleteUrl() {
        return getServerNetAddressHost() + "album-listen-mobile/api/v1/upload/finish/album/info";
    }

    public String getPlayCompleteShareUrl() {
        return getServerNetAddressHost() + "thirdparty-share/share/scene/album/play/completed/achievement/" + System.currentTimeMillis();
    }

    public String getMyClubScheduleUrl() {
        return getMyClubMobileHttpsHost() + "chitchat-mobile-web/api/v1/schedule/sdk/subscribeOrUnSubscribe";
    }

    /**
     * 直播预约接口
     *
     * @return
     */
    public String liveSchedule() {
        return getLiveServerHost() + "lamia/v1/live/book";
    }

    /**
     * 直播预约接口
     *
     * @return
     */
    public String liveClassSchedule() {
        return getLiveServerHost() + "diablo-web/v1/live/booking/add";
    }

    /**
     * 直播取消预约接口
     *
     * @return
     */
    public String liveClassRemoveSchedule() {
        return getLiveServerHost() + "diablo-web/v1/live/booking/remove";
    }

    /**
     * 兴趣标签弹框
     */
    public String queryInterestTagGuide() {
        return getServerNetAddressHost() + "discovery-feed/interestTagGuide/" + System.currentTimeMillis();
    }

    public String postInterestTagTraits() {
        return getServerNetAddressHost() + "discovery-feed/interestTagTraits";
    }

    // 资源位免广告
    public String resourcePositionAdvertisFree() {
        return getServerNetAddressHost() + "mobile-resource-position/resource/advertising/free";
    }

    // 资源位免广告
    public String adFreeInfo() {
        return getServerNetAddressHost() + "mobile-resource-position/resource/advertising/free/v2";
    }


    public String getCustomEmojisDictionary() {
        return getServerNetAddressHost() + "comment-mobile/customEmojiInfo/list/%d";
    }

    public String getAlbumOrTrackStatus() {
        return getServerNetAddressHost() + "mobile-category/contentAuth";
    }

    /**
     * 动态入口
     *
     * @return
     */
    public String getAggregateRankAb() {
        return getServerNetAddressHost() + "discovery-ranking-web/v5/ranking/ABResForCategoryMerge/" + System.currentTimeMillis();
    }

    public String monthlyVoteList() {
        return getServerNetAddressHost() + "monthly-ticket-mobile/votelist/";
    }

    public String getTalkCardDetail() {
        return getServerNetAddressHost() + "comment-mobile/talk/binding/%s";
    }

    public String getVoteCardDetail() {
        return getServerNetAddressHost() + "comment-mobile/vote/binding/%s";
    }

    public String getSketchVideoList() {
        return getMNetAddressHost() + "mobile-category/v1/shortVideoAlbum/feedList";
    }

    public String getPersonaCardDetail(String cardId) {
        return getMNetAddressHostS() + "community-widget/api/v1/persona/binding/" + cardId + "/" + System.currentTimeMillis();
    }

    public String getMailCardDetail() {
        return getServerNetAddressHost() + "comment-mobile/mailBox/binding/%s";
    }

    public String getJumpCardDetail(long trackId, String cardId) {
        return getMNetAddressHostS() + "community-widget/api/v1/forwarding/binding/" + trackId + "/" + cardId;
    }

    public String getSequentialVoteCardDetail() {
        return getMNetAddressHost() + "community-widget/api/v1/sequentialVote/binding/%s";
    }

    public String getSequentialVoteDetail() {
        return getMNetAddressHost() + "community-widget/api/v1/sequentialVote/detail/%d";
    }

    public String getFeedLotteryDetail(long lotteryId) {
        return getServerNetSAddressHost() + "social-web/feedLottery/" + lotteryId;
    }

    public String getVirtualHumanDetail() {
        return getServerNetKidsAddressHostS() + "xmkp-xm-provider-mobile/virtual/character/show";
    }

    /**
     * 儿童专辑AI换声
     */
    public String covertAiSoundTrack() {
        return getServerNetKidsAddressHostS() + "xmkp-xm-provider-mobile/tellStory/convertTrack";
    }

    /**
     * 领取哄睡音体验权益
     */
    public String getSleepModeVipExperience() {
        return getServerNetKidsAddressHostS() + "xmkp-xm-provider-mobile/sleepmode/audiosticker/receiveGift";
    }

    /**
     * 请声音转音状态
     */
    public String queryTrackRoleState() {
        return getServerNetKidsAddressHostS() + "xmkp-xm-provider-mobile/tellStory/queryUserAiPermission";
    }

    /**
     * 查询纠错记录
     */
    public String getDocErrorsRecord(long trackId) {
        return getServerNetAddressHost() + "mobile-playpage/ai/doc/errors/" + trackId + "/" + System.currentTimeMillis();
    }

    public String getContentPoolRecommendTrackList(long trackId) {
        return getServerNetAddressHost() + "mobile-playpage/playpage/queryBusinessResource/" + trackId + "/" + System.currentTimeMillis();
    }


    /**
     * @return 查询文稿推荐数据
     */
    public String getDocRecommendDataUrl(long trackId) {
        return getSearchHost() + "nlp/ai_text/entity_info?id=" + trackId;
    }

    /**
     * 提交纠错记录
     */
    public String getAiDocCorrectSubmit() {
        return getServerNetAddressHost() + "mobile-playpage/ai/doc/correct/submit";
    }

    public String getCheckRateLimitsUrl() {
        return getMNetAddressHost() + "compass-monitor/ratelimit/getResult";
    }

    public String getToListenPatch() {
        return getServerNetAddressHost() + "sound-guide-portal/sound/tts/" + System.currentTimeMillis();
    }

    /**
     * 评论box
     */
    public String getBoxComment() {
        return getServerNetAddressHost() + "comment-mobile/v1/box/comment/" + System.currentTimeMillis();
    }

    public String monthlyVotePreInfo() {
        return getServerNetAddressHost() + "monthly-ticket-mobile/prevote/";
    }

    public String monthlyVote() {
        return getServerNetAddressHost() + "monthly-ticket-mobile/vote/";
    }

    public String getOnlyNewFocusPicUrl() {
        return getServerNetAddressHost() + "focus-mobile/focusPic/info";
    }

    public String monthlyTicketGuide() {
        return getMNetAddressHost() + "x-web-activity/monthlyTicket/guide";
    }

    // 获取字节素材列表
    public String getBytesMaterialsListUrl() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/bytedance/list";
    }

    // 获取字节素材列表v2版本
    public String getBytesMaterialsListUrlV2(){
        return getServerNetAddressHost() + "vtool-web/v1/materials/bytedance/list/v2";
    }


    /**
     * 定时上报AI礼物是否可用条件
     */
    public String getPostReportAiGiftUrl(){
        return getServerNetAddressHost() + "vtool-web/v1/materials/bytedance/aigift/report";
    }

    // 获取字节素材列表
    public String getBytesMaterialsStarListUrl() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/stars/bytedance/list";
    }

    //添加收藏资源
    public String getStarBytesMaterialUrl() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/stars/bytedance/add";
    }

    //删除收藏资源
    public String getUnStarBytesMaterialUrl() {
        return getServerNetAddressHost() + "vtool-web/v1/materials/stars/bytedance/remove";
    }

    public String selectVoteUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/vote/select";
    }

    public String unselectVoteUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/vote/unSelect";
    }

    // 一键表白（请求结构同vote/select接口）
    public String voteConfessUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/confession/voteConfess";
    }

    public String selectSequentialVoteUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/sequentialVote/subVote/select";
    }

    public String submitAllSequentialVoteUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/sequentialVote/select";
    }

    /**
     * 追更顶部引导
     */
    public String getEverydayChaseTipUrl() {
        return getServerNetAddressHost() + "track-feed/chase/tips/" + System.currentTimeMillis();
    }

    /**
     * 查询活动解说 - 邀请好友，解锁声音
     */
    public String getShareUnlockTrackInfoUrl() {
        return getServerNetAddressHost() + "thirdparty-share/sharePanel/unlockTrackWithInvitation/info/query/" + System.currentTimeMillis();
    }

    public String getShareVerifyInputUrl() {
        return getServerNetSAddressHost() + "content-handle-web/ai/content/check";
    }

    public String getShareActivityPosterDataUrl() {
        return getServerNetSAddressHost() + "business-marketing-poster/poster";
    }

    /**
     * 追更头部专辑列表
     */
    public String getEverydayChaseListUrl() {
        return getServerNetAddressHost() + "track-feed/v2/chase/list/" + System.currentTimeMillis();
    }

    /**
     * 追更列表信息流 （全部&单个专辑）
     */
    public String getEverydayTrackFeedDynamicUrl() {
        return getServerNetAddressHost() + "track-feed/v5/track-feed/dynamic/" + System.currentTimeMillis();
    }

    /**
     * 追更列表信息流 （推荐）
     */
    public String getEverydayTrackFeedRecommendUrl() {
        return getServerNetAddressHost() + "track-feed/v5/track-feed/filter/" + System.currentTimeMillis();
    }

    /**
     * 订阅页追更引导
     */
    public String getEverydayNewFeedInfo() {
        return getServerNetAddressHost() + "track-feed/v3/new-feed/info/" + System.currentTimeMillis();
    }

    /**
     * 追更清专辑红点
     */
    public String getEveryDayResetUrl() {
        return getServerNetAddressHost() + "track-feed/v3/new-feed/reset";
    }

    /**
     * 获取用于给华为捐赠的数据
     */
    public String getDonateDataForHwUrl() {
        return getMNetAddressHost() + "web-config/api/jc/queryAllData?app=web&group=web-lite&key=cardDonate";
    }

    public String getHwDonateTrackInfo() {
        return getServerNetSAddressHost() + "mobile-playpage/hm/view";
    }


    public String getNewUserActivityResult() {
        return getServerNetSAddressHost() + "discovery-firstpage/first/request/" + System.currentTimeMillis();
    }

    public String getActivityResult() {
        return getServerNetSAddressHost() + "discovery-firstpage/first/activity/" + System.currentTimeMillis();
    }

    public String getDoShareAssistUrl() {
        return getMNetAddressHostS() + "web-activity/share/v2/assist";
    }

    /**
     * 付费统一弹窗接口
     */
    public String getUniversalPaymentUrl() {
        return getServerNetSAddressHost() + "product/promotion/v1/album/price/ts-" + System.currentTimeMillis();
    }

    public String getUniversalPaymentWithPurchaseUrl() {
        Logger.d("mark112234", "getUniversalPaymentWithPurchaseUrl:\n"+Log.getStackTraceString(new Throwable()));
        return getServerNetSAddressHost() + "product/promotion/v1/ad/unlock/ts-" + System.currentTimeMillis();
    }

    public String getNewUserNoPlayTipResult() {
        return getServerNetSAddressHost() + "discovery-firstpage/newUserSt/no_play/tip";
    }

    /**
     * 上报直播美颜业务数据便于产品研究
     */
    public String getPostLiveBeautifyUrl() {
        return getLiveServerMobileHttpHost() + "lamia/v1/common/business/report";
    }

    /**
     * 批量领券接口
     */
    public String getMultiRequestCouponUrl() {
        return getMNetAddressHost() + "promotion/coupon/user/multiAllocate";
    }

    public String getCommunityTinyUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/tinyUrl/generate";
    }

    /**
     * 月票贡献榜
     */
    public String getMonthlyTicketRankUrl() {
        return getServerNetAddressHost() + "monthly-ticket-mobile/rankList/" + System.currentTimeMillis();
    }


    // 通用赞评论V2
    public String likeCommentUniversalUrl() {
        return getServerNetAddressHost() + "universal-comment-mobile/like";
    }

    // 通用取消赞评论V2
    public String unlikeCommentUniversalUrl() {
        return getServerNetAddressHost() + "universal-comment-mobile/cancelLike";
    }

    // 通用踩评论
    public String hateCommentUniversalUrl() {
        return getServerNetAddressHost() + "universal-comment-mobile/hate";
    }

    // 通用取消踩评论
    public String cancelHateCommentUniversalUrl() {
        return getServerNetAddressHost() + "universal-comment-mobile/cancelHate";
    }

    public String checkCanUploadColdStartUpTraceUrl() {
        return getServerNetSAddressHost() + "keep-alive-player-mobile/keepLive/query/ts-" + System.currentTimeMillis();
    }

    public String setPushGuardEnable() {
        return getServerNetSAddressHost() + "keep-alive-player-mobile/keepLive/query/keepalive/status";
    }

    // 获取tts的token
    public String getTtsTokenUrl() {
        return getServerNetAddressHost() + "hoverball-mobile/audio/base";
    }

    //新用户参与抽奖
    public String getLotteryJoinActUrl() {
        return getMNetAddressHost() + "x-web-activity/lottery/joinAct?actName=newUserLottery";
    }

    // 查询畅听信息接口
    public String getQueryFreeListenInfoUrl() {
        return getServerNetSAddressHost() + "mobile/free_listen/guide/info/ts-" + System.currentTimeMillis();
    }

    // 查询全站畅听首页弹窗素材
    public String queryAdUnlockFireworkMaterial() {
        return getServerNetAddressHost() + "mobile-user/freeListen/material/" + System.currentTimeMillis();
    }

    // 全站畅听糖葫芦活动页默认地址
    public String getAdUnlockActLink() {
        return "https://pages.ximalaya.com/mkt/act/01d3fa6fb4911527";
    }

    /**
     * 获取用户关注推荐的url
     *
     * @return
     */
    public String getSubscribeRecommendUrl() {
        return getARHost() + "rec-association/recommend/album/sub";
    }

    public String getSubscribeExpiringSoonUrl() {
        return getServerNetSAddressHost() + "mobile-album/album/expiringSoon";
    }

    // 新用户参与抽奖
    public String getSplashScreenImageTypeUrl() {
        return getServerNetSAddressHost() + "discovery-feed/openPicMatch";
    }

    public String getDownloadTrackFix() {
        return getServerNetSAddressHost() + "mobile/download/track/fix";
    }

    // 创建讨论卡片
    public String createTalkCard() {
        return getMNetAddressHost() + "community-widget/api/v1/author/admin/user/widget/talk/create";
    }

    // 创建投票卡片
    public String createVoteCard() {
        return getMNetAddressHost() + "community-widget/api/v1/author/admin/user/widget/vote/create";
    }


    public String getAlbumsByAnchorUrl() {
        return getServerNetSAddressHost() + "rec-association/recommend/album/by_anchor";
    }

    public String getAlbumsByListenUrl() {
        return getServerNetSAddressHost() + "rec-association/recommend/album/by_listen";
    }

    // 一键参与抽奖
    public String dynamicFeedLotteryJoiner(long lotteryId) {
        return getServerNetAddressHost() + "social-web/feedLottery/" + lotteryId + "/joiners";
    }

    public String getSubscribeFollowStatusUrl() {
        return getServerNetSAddressHost() + "subscribe/subscribe/guide/popup/" + System.currentTimeMillis();
    }

    public String collectItingForGrowthUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_API_ADDRESS_S)
                + "openapi-feeds-stream-app/activity/complete/copartner/task";
    }

    public String createAlbumRateUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/create";
    }

    public String createOrUpdateAlbumRateUrlV2() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/v2";
    }

    public String getMyAlbumRateIdUrl() {
        return getServerNetAddressHost() + "album-comment-mobile/album/comment/query";
    }

    public String getRefreshLimitAlbumUrl() {
        return BaseUtil.chooseEnvironmentUrl(SERVER_NET_ADDRESS) + "discovery-feed/module/oneKeyListenScene";
    }

    public String getUserPageShowUrl() {
        return getMNetAddressHostS() + "mobile-anchor-web/api/v1/userPage/getUserPageShow";
    }

    public String getUserVipInfo() {
        return getServerNetSAddressHost() + "mobile-user/my/vip";
    }

    public String getDriveModeFindUrl() {
        return getServerNetAddressHost() + "mobile-category/v1/scene/query?namespaceName=mainAppCarModel";
    }

    public String getDriveModeFollowUpTrackListUrl() {
        return getServerNetAddressHost() + "track-feed/v2/track-feed/dynamic/ts-1686551022389?size=50";
    }

    /**
     * 请求青少年弹窗专辑推荐信息
     */
    public String getProtectDialogAlbumInfoUrl() {
        return getServerNetAddressHost() + "minor-protection-web/screen/show";
    }

    /**
     * 请求青少年弹窗专辑推荐预览信息
     */
    public String getProtectDialogPreviewUrl() {
        return getServerNetAddressHost() + "minor-protection-web/screen/pre/view";
    }

    public String queryUserVerifyState() {
        return getMNetAddressHostS() + "anchor-verify-web/platformCelebrity/queryUserVerifyState";
    }

    public String getHimalayaOverseasPushUrl() {
        return getServerNetAddressHost() + "app-skin-service/inner/push/himalaya/download/" + System.currentTimeMillis();
    }

    public String verifyPersonalH5Url() {
        return getMNetAddressHostS() + "anchor-verify-web/index/personal";
    }

    public String getNotAgreePrivacyReportUrl() {
        return getServerNetSAddressHost() + "discovery-firstpage/first/xdcs";
    }

    //查询通知未读数 （包含 评论 和 点赞 的未读消息）
    public String getCheckNotifyUnreadCountUrl() {
        return getServerNetAddressHost() + "messenger-web/v1/unread/number/";
    }

    /**
     * 领奖接口
     *
     * @return 领奖接口url
     */
    public String getWinGetPrizeNewUrl() {
        return getServerNetAddressHost() + "persona/lottery/winGetPrize";
    }

    public String getPopPlaycardUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/pop/playcard";
    }

    public String getGiftSendUrl() {
        return getServerNetAddressHost() + "discovery-firstpage/pop/giftSend";
    }

    public String getMineAnchorUrl() {
        return getMNetAddressHost() + "anchor-ximi-web/fans-center/joined";
    }

    public String getGo2ManagerUrl() {
        return getMNetAddressHost() + "gatekeeper/ximi-personal-center/home?app=iting&version=" +
                DeviceUtil.getVersion(BaseApplication.getMyApplicationContext()) + "&impl=com.gemd.iting&channel=my_ximi_list&_full_with_transparent_bar=1";
    }

    public String getGo2LookUrl(long anchorId) {
        return getMNetAddressHost() + "anchor-ximi-web/page/fans-center/member/" + anchorId + "?_full_with_transparent_bar=1";
    }

    public String getGo2RenewalUrl(long anchorId) {
        return getMNetAddressHost() + "anchor-ximi-web/page/fans-center/member/" + anchorId + "?_full_with_transparent_bar=1&sku=true";
    }

    // 判断内网wifi环境url
    public String getWifiIntranetUrl() {
        return "http://search.test.ximalaya.com/hub/guideWordV3/1.0";
    }

    public String getPurchaseTabUrl() {
        return getServerNetSAddressHost() + "mobile-user/v2/purchased/recommend/" + System.currentTimeMillis();
    }

    public String getHistorySubscribeStatusUrl() {
        return getServerNetSAddressHost() + "subscribe/v6/subscribe/status/query";
    }

    public String getMultiVipServiceAgreementUrl(String ids) {
        return getMNetAddressHost() + "gatekeeper/agreement-union-member?_default_share=0&_ka=1&presentIds=" + ids;
    }

    /**
     * @return 获取收藏配乐的url
     */
    public String getMarkedBgMusicListUrl() {
        return this.getHybridHost() + "music-web/client/mark/list";
    }

    /**
     * 评论
     * /**
     * 创建大师课笔记
     */
    public String getCreateMasterNoteUrl() {
        return getMNetAddressHost() + "business-user-operation-mobile-web/play_note/add/note";
    }

    /**
     * 创建大师课评论、回复
     */
    public String getCreateMasterNoteReplyUrl() {
        return getMNetAddressHost() + "business-user-operation-mobile-web/play_note/reply/note";
    }

    // 查询私信屏幕状态
    public String getUserBlockStatusUrl() {
        return getServerNetAddressHost() + "messenger-web/messenger/block/status/" + System.currentTimeMillis();
    }

    // 设置私信屏蔽状态
    public String getUserBlockUrl() {
        return getServerNetAddressHost() + "messenger-web/messenger/block";
    }

    public String getUserVoteUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/vote/select";
    }

    public String getUserVoteInfoUrl() {
        return getMNetAddressHost() + "community-widget/api/v1/vote/userVoteInfo";
    }

    public String getRnListenListTrackListUrl() {
        return getServerNetAddressHost() + "thirdparty-share/contentPool/playList/query/";
    }

    public String getRankTrackListUrl() {
        return getServerNetAddressHost() + "discovery-ranking-web/ranking/playListInfo/";
    }

    // 话题详情页头部信息 http://ops.ximalaya.com/api-manager-backend/router-page/projectApiLook/1763/101381
    public String getTopicDetailHeaderUrl() {
        return getMNetAddressHost() + "anchor-content-produce-web/api/v2/topicMobile/detailPage/header";
    }

    public String getSearchFollowUrl() {
        return getSearchHost() + "vertical/user/followings";
    }

    /**
     * 查询我的底tab标签
     */
    public String getUserBottomBarTagUrl() {
        return getServerNetAddressHost() + "mobile-user/scene/user/bottom/tag/" + System.currentTimeMillis();
    }

    public String getSceneQuerySingleUrl() {
        return getServerNetAddressHost() + "mobile-category/v2/scene/querySingle";
    }

    public String getAccessibilityModeFeedUrl() {
        return getServerNetAddressHost() + "mobile-category/sceneExt/querySingleMang";
    }

    public String getAccessibilityModeExposeHistoryAppendUrl() {
        return getServerNetAddressHost() + "mobile-category/history/append";
    }


    /**
     * 内push亮屏上报接口
     */
    public String getPostScreenOnUrl() {
        return getLiveServerMobileHttpHost() + "live-data-web/v1/app/push/screen";
    }


    /**
     * 上报内puh直播消息的接口
     */
    public String getPostLivePushCollectUrl() {
        return getLiveServerMobileHttpHost() + "live-data-web/v1/app/push/collect";
    }

    public String getTingListOrContentListUrl() {
        return getServerNetAddressHost() + "mobile/listenlist/listen/content/page";
    }


    public String getNextSoundPatchUrl() {
        return getServerNetAddressHost() + "sound-guide-portal/sound/tts";
    }

    public String getChatXmlyTipsUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/tips-info";
    }

    public String getChatXmlyBubbleInfoUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/bubble-info";
    }

    public String getUpdateWakeupSwitchUrl() {
        return "https://api.xiaoyastar.com/v2/app/service/chatxmly/update-wakeup-switch";
    }

    public String getChatXmly2TipsUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/ai-section/tips";
    }

    public String getChatXmlyTagsUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/ai-section/tags";
    }

    public String getChatXmlyAISectionUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/ai-section/page";
    }

    public String getChatXmlyAISectionByIdUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/ai-section/%d";
    }

    public String getChatXmlySpeakersUrl() {
        return "https://api.ximalaya.com/xyos-api-service/chatxmly/ai-section/speakers";
    }

    public String getFreeFlowDialogDataUrl(){
        return getMNetAddressHost() + "omp-operator-open-api/activity/free/flow/popup/info";
    }

    public String getAuthCodeAuthLoginUrl() {
        return getPassportAddressHosts() + "xpassport-mobile/auth/code/auth";
    }

    public String getNewUserFirstWeekBenefitActiveUrl() {
        return getMNetAddressHost() + "x-web-activity/newUserFirstWeekBenefit/activate";
    }

    public String getAlbumNoticeMarkShowUrl(){
        return getServerNetSAddressHost() + "business-sale-promotion-guide-mobile-web/display/confirm/v1";
    }

    // 设备扩展信息更新
    public String deviceUpdate() {
        return getServerNetAddressHost() + "device-mobile/v1/ext/update";
    }

    public String dislikeForNewRecommendPage() {
        return getRecommendNegative() + "recsys/batch-dislike";
    }

    public String removeCardForNewRecommendPage() {
        return getRecommendNegative() + "recsys/card-management";
    }

    /**
     * 商业化统一频控弹窗物料信息接口
     * @return
     */
    public String getCommercialDialogFrequencyUrl(){
        return getServerNetAddressHost() + "business-sale-promotion-guide-mobile-web/popup/info/v1";
    }

    /**
     * 商业化统一频控弹窗展示上报
     * @return
     */
    public String getReportCommercialDialogShowUrl(){
        return getServerNetAddressHost() + "business-sale-promotion-guide-mobile-web/popup/report";
    }

    public String getFeedBackDialogDataUrl() {
        return getServerNetAddressHost() + "discovery-feed/sceneCard/v2/cardTabChangeData/elementsAndPanel";
    }

    public String getCmccDdrUserActivityInfoUrl() {
        return getMNetAddressHostS() + "omp-operator-open-api/cmccxdr/userActivityInfo";
    }

    public String getCmccxdrUpsertTrait(String pushId,String contactId) {
        return getMNetAddressHostS() + "omp-operator-open-api/cmccxdr/upsertTrait?pushId="+pushId+"&contactId="+contactId;
    }

    public String getBookDetailUrl() {
        return getMNetAddressHostS() + "qiji-mobile/xima/v1/book/detail";
    }

    public String getBookRelativeTrackVoUrl() {
        return getMNetAddressHostS() + "qiji-mobile/xima/v1/chapter/detail";
    }

    public String getTrackRelativeBookInfoUrl() {
        return getMNetAddressHostS() + "qiji-mobile/xima/relative/chapter";
    }

    public String getPublicAccountSubscribeStatus() {
        return getServerNetSAddressHost() + "mobile/public_accounts/subscribe/status";
    }

    public String thirdUidBind() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI, TRACK_PAY_URI_DEBUG + "openapi-payfacade-app/", TRACK_PAY_URI_UAT) + "open_pay/third_uid_bind?device=android";
    }

    public String thirdUidUnBind() {
        return BaseUtil.chooseEnvironmentUrl(TRACK_PAY_URI, TRACK_PAY_URI_DEBUG + "openapi-payfacade-app/", TRACK_PAY_URI_UAT) + "open_pay/third_uid_unbind_v2?device=android";
    }

    public String getAnchorAbUrl() {
        return getServerNetAddressHost() + "mobile-user/v3/artist/ab";
    }

    public String getGuessLikeForHwIntent() {
        return getServerNetAddressHost() + "discovery-feed/v1/social/listen/more/v2";
    }

    public String getAuthorCorrectUrl() {
        return getServerNetSAddressHost() + "mobile/album/author/correct";
    }

    public String getSubscribeAllCategoriesUrl() {
        return getServerNetSAddressHost() + "subscribe/broad/all-categories";
    }

    public String getSubscribeSetTopUrl() {
        return getServerNetSAddressHost() + "subscribe/broad/feed/top/create";
    }

    public String getSubscribeCancelTopUrl() {
        return getServerNetSAddressHost() + "subscribe/broad/feed/top/delete";
    }

    public String getSubscribeBroadListUrl() {
        return getServerNetSAddressHost() + "subscribe/broad/list";
    }

    public String getSubscribeBatchDeleteUrl() {
        return getServerNetAddressHost() + "subscribe/broad/multi/delete";
    }

    public String getUpdateAgentConfigUrl() {
        return getServerNetSAddressHost() + "xy-listening-content-web/config/aiVoice/update";
    }

    public String getAgentConfigUrl() {
        return getServerNetSAddressHost() + "xy-listening-content-web/config/agentConfig";
    }

    public String getAgentConfigUrlWithoutAb() {
        return getServerNetSAddressHost() + "xy-listening-content-web/config/aiVoice/get";
    }

    public String getlistDailySugUrl() {
        return getServerNetSAddressHost() + "mobile-rag/sug/listDailySug";
    }

    public String getReportNotShowChildProtectDialogUrl() {
        return getServerNetSAddressHost() + "mobile-user/minorProtection/no/popup/distrub";
    }

    public String vehicleSwitchModeUrl() {
        return getServerNetSAddressHost() + "mobile/vehicle/switch_mode/set";
    }

    /**
     * 付费挽留弹窗物料信息接口
     */
    public String getHomePaidRetentionDialogUrl(){
        return getServerNetAddressHost() + "business-user-device-info-mobile-web/popUp/info";
    }

    public String reportHomePaidRetentionDialogSuccessUrl(){
        return getServerNetAddressHost() + "business-user-device-info-mobile-web/popUp/report";
    }

    public String shortContentFeed() {
        return getServerNetSAddressHost() + "mobile-category/v2/short/content/feed";
    }

    public String queryTrackCollection(long trackId) {
        return getServerNetAddressHost() + "mobile-category/v1/short/content/queryTrackCollection?trackId=" + trackId;
    }

    public String queryVipJumpPageUrl() {
        return getPageNetSHost() + "business-pgc-adapter-mobile-web/short_vip/show/recommendation";
    }

    public String aiRadioSubscribe() {
        return getServerNetAddressHost() + "xy-listening-content-web/aiRadio/subscribe";
    }

    public String queryRefreshChannelUrl() {
        return getServerNetAddressHost() + "discovery-feed/sceneMatch?sceneId=7345";
    }
}
