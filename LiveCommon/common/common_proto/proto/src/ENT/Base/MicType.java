// Code generated by Wire protocol buffer compiler, do not edit.
// Source file: Users/nali/work2024/XmMainApp2024/LiveBundle/LiveCommon/common/common_proto/pb/ENT.Base.proto at 43:1
package ENT.Base;

import com.squareup.wire.ProtoAdapter;
import com.squareup.wire.WireEnum;
import java.lang.Override;

public enum MicType implements WireEnum {
  /**
   * 排队上麦
   */
  MIC_WAITING(0),

  /**
   * 快速上麦
   */
  MIC_FAST(1);

  public static final ProtoAdapter<MicType> ADAPTER = ProtoAdapter.newEnumAdapter(MicType.class);

  private final int value;

  MicType(int value) {
    this.value = value;
  }

  /**
   * Return the constant for {@code value} or null.
   */
  public static MicType fromValue(int value) {
    switch (value) {
      case 0: return MIC_WAITING;
      case 1: return MIC_FAST;
      default: return null;
    }
  }

  @Override
  public int getValue() {
    return value;
  }
}
