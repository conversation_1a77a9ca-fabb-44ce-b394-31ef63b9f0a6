package com.ximalaya.ting.android.live.common.lib.gift.panel.multireceiver;


import android.app.Activity;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.fragment.BaseFragment;
import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.toast.ToastManager;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.web.nativeweb.NativeHybridFragment;
import com.ximalaya.ting.android.host.util.common.ToolUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.lib.configcenter.LiveSettingManager;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.CommonGiftPage;
import com.ximalaya.ting.android.live.common.lib.gift.panel.SendGiftDialog;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.BaseGiftPanelItem;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.BaseItem;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.LiveBrocadeBagBindInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.LiveGiftTopViewModel;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.PackageInfo;
import com.ximalaya.ting.android.live.common.lib.gift.panel.view.LiveGiftTopView;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveBaseAttributeRecord;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.view.SimpleSwitchButton;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 多人互动礼物面板基类.
 *
 * <AUTHOR>
 */
public abstract class MultiReceiverGiftDialog<T extends BaseGiftLoader> extends SendGiftDialog<T> implements SimpleSwitchButton.OnCheckChangeListener {

    protected long mRoomUid;
    /**
     * 点击礼物面板上的资料，显示对应用户资料卡片
     */
    protected OnShowUserInfo mOnShowUserInfo;

    /**
     * 交友模式麦上人的 id
     */

    protected SimpleSwitchButton mSwitchAllMic;

    protected TextView mTvReceiverInfo;

    protected ConstraintLayout mSelectReceiverLayout;

    protected MultiReceiverAdapter mReceiversAdapter;

    protected List<GiftReceiverItem> mReceiverItems;

    protected long mDefaultUserId;

    protected boolean mReceiverListShouldRefresh = true;

    protected boolean mIsSingleMode;

    @Keep
    protected MultiReceiverGiftDialog(Activity context) {
        super(context, STYLE_TRANSPARENT);
    }

    @Keep
    protected MultiReceiverGiftDialog(Activity context, int themeRes) {
        super(context, themeRes);
    }

    @Override
    protected void initView() {
        super.initView();

        mSelectReceiverLayout = (ConstraintLayout) LayoutInflater.from(mContext).inflate(R.layout.live_include_gift_dialog_top, null);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, BaseUtil.dp2px(mContext, 55));

        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        mSelectReceiverLayout.setLayoutParams(layoutParams);

        showOrHideReceiverState(true);

        mSwitchAllMic = mSelectReceiverLayout.findViewById(R.id.live_hall_choose_all);
        mSwitchAllMic.setOnCheckChangeListener(this);

        RecyclerView mReceiverListView =
                mSelectReceiverLayout.findViewById(R.id.live_hall_rv_users);
        mTvReceiverInfo = mSelectReceiverLayout.findViewById(R.id.live_hall_selected_user_info);
        mTvReceiverInfo.getPaint().setFlags(Paint.UNDERLINE_TEXT_FLAG);
        if (null == mReceiverItems) {
            mReceiverItems = new ArrayList<>();
        }
        mReceiversAdapter = new MultiReceiverAdapter(getContext(), mReceiverItems);

        mReceiverListView.setLayoutManager(new LinearLayoutManager(getContext(),
                LinearLayoutManager.HORIZONTAL, false));
        mReceiverListView.setHasFixedSize(true);
        RecyclerView.RecycledViewPool recycledViewPool = mReceiverListView.getRecycledViewPool();
        recycledViewPool.setMaxRecycledViews(0, 9);
        mReceiverListView.setAdapter(mReceiversAdapter);

        mReceiversAdapter.setOnItemClickListener(new MultiReceiverAdapter.OnItemClickListener() {
            @Override
            public void onClicked(int position, GiftReceiverItem item, boolean isSingleCheckMode) {
                notifyReceiverList();
                onTargetUserChanged();
            }
        });

        mTvReceiverInfo.setOnClickListener(this);

        setOnMicUsers(mReceiverItems, mDefaultUserId);
    }

    @Override
    public long getSingleSelectedUid() {
        List<GiftReceiverItem> users = getSelectedUsers();
        if (users.isEmpty()) return 0;
        return users.get(0).uid;
    }

    /**
     * 设置麦上用户列表
     *
     * @param onMicUsers 麦上用户列表
     */
    public void setOnMicUsers(List<GiftReceiverItem> onMicUsers) {
        setOnMicUsers(onMicUsers, 0);
    }

    public long getRoomUid() {
        return mRoomUid;
    }

    protected boolean isReceiverUserSame(List<GiftReceiverItem> oldList, List<GiftReceiverItem> newList) {
        if (oldList == null && newList == null) {
            return true;
        }

        int oldSize = oldList != null ? oldList.size() : 0;

        int newSize = newList != null ? newList.size() : 0;

        if (oldSize != newSize) {
            return false;
        }
        for (int i = 0; i < oldSize; i++) {
            if (oldList.get(i).uid != newList.get(i).uid) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void onChecked(boolean check, SimpleSwitchButton button) {
        List<GiftReceiverItem> receiverItemList = mReceiversAdapter.getList();

        for (GiftReceiverItem item : receiverItemList) {
            item.setSelected(check);
        }

        mReceiversAdapter.notifyDataSetChanged();
        notifyReceiverList();

        String state = check ? "selected" : "unselected";
        sendUserTracking("全麦按钮", state, "7017");
    }

    private void notifyReceiverList() {

        List<GiftReceiverItem> receiverItemList = mReceiversAdapter.getList();

        if (receiverItemList.isEmpty()) {
            showOrHideReceiverState(false);
            return;
        }
        // 通过选中收礼人的数量来更新宝箱是否可批量赠送的逻辑
        mutexMultiBtnBatch();

        boolean allMic = true;
        int selectedCount = 0;
        for (int i = 0; i < receiverItemList.size(); i++) {
            GiftReceiverItem item = receiverItemList.get(i);
            if (!item.isSelected()) {
                allMic = false;
            } else {
                selectedCount++;
            }
        }

        if (null != mSwitchAllMic) {
            mSwitchAllMic.setCheck(allMic);
        }

        if (null != mTvReceiverInfo) {
            boolean enable = 1 == selectedCount;
            mTvReceiverInfo.setEnabled(enable);
            if (enable) {
                mTvReceiverInfo.setPaintFlags(mTvReceiverInfo.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
            } else {
                mTvReceiverInfo.setPaintFlags(mTvReceiverInfo.getPaintFlags() & (~Paint.UNDERLINE_TEXT_FLAG));
            }
        }
    }

    /**
     * 互斥麦上选择多人的时候，更新批量选择逻辑
     */
    private void mutexMultiBtnBatch() {
        CommonLiveLogger.d(TAG, "getSelectedUsers():" + getSelectedUsers().size());
        if (getSelectedUsers().size() > 1) {
            // 麦上人数大于1个，如果是宝箱礼物，则设置可批量选择的按钮不可操作
            if (mBeanSelected instanceof GiftInfoCombine.GiftInfo && ((GiftInfoCombine.GiftInfo) mBeanSelected).isBoxGift()) {
                enableGiftBatch(false);
            }
        } else {
            if (mBeanSelected instanceof GiftInfoCombine.GiftInfo && ((GiftInfoCombine.GiftInfo) mBeanSelected).isBoxGift()) {
                enableGiftBatch(isSupportBatchGift((GiftInfoCombine.GiftInfo) mBeanSelected));
            }
        }
    }

    /**
     * 全选按钮是否展示
     *
     * @param multiBtnIsShow true: 隐藏， false展示
     */
    @Override
    protected void mutexBatchMultiBtn(boolean multiBtnIsShow) {
        if (mBeanSelected instanceof GiftInfoCombine.GiftInfo && ((GiftInfoCombine.GiftInfo) mBeanSelected).isBoxGift()) {
            checkGiftCanSendMultiReceive(mBeanSelected, multiBtnIsShow);
        }
    }

    /**
     * 展示资料卡片
     */
    public interface OnShowUserInfo {
        /**
         * 展示回调
         *
         * @param uid 待展示用户 uid
         */
        void showUserInfo(long uid);
    }

    protected void sendUserTracking(String srcModule, String itemId, String id) {

        String selectedAll = mSwitchAllMic.isSelected() ? "selected" : "unselected";
        new UserTracking().setSrcPage("room")
                .setSrcModule(srcModule)
                .setItem("button")
                .setItemId(itemId)
                .setId(id)
                .setSrcPageId(mRoomId)
                .putParam("wholeMic", selectedAll)
                .statIting(XDCSCollectUtil.APP_NAME_EVENT, XDCSCollectUtil.SERVICE_PAGE_CLICK);
    }

    @Override
    protected void goToCharge(int chargeType) {
        super.goToCharge(chargeType);
        trackClickXiDiamond();
    }

    private void trackClickXiDiamond() {
        if (LiveBaseAttributeRecord.getInstance().hasBaseAttributeData()) {
            LiveBaseAttributeRecord.getInstance().getBaseTrace()
                    .click(15798)
                    .put("currPage", "fmMainScreen")
                    .createTrace();
        }
    }


    @Override
    protected void sendGift(GiftInfoCombine.GiftInfo giftInfo) {
        super.sendGift(giftInfo);

        sendUserTracking("打赏面板", "送礼", "7021");
        trackClickSendGift();
    }

    private void trackClickSendGift() {
        if (LiveBaseAttributeRecord.getInstance().hasBaseAttributeData()) {
            LiveBaseAttributeRecord.getInstance().getBaseTrace()
                    .click(15800)
                    .put("currPage", "fmMainScreen")
                    .createTrace();
        }
    }

    /**
     * 幸运礼物，不允许多选
     * 宝箱，允许多选
     *
     * @param info           礼物
     * @param multiBtnIsShow 是否显示批量送
     */
    private void checkGiftCanSendMultiReceive(BaseItem info, boolean multiBtnIsShow) {
        boolean isSingleMode = false;
        // todo 这里是为了保证大活动上线安全增加的开关，后续可以去掉，默认给打开
        boolean allowLotMultipleMicSwitch = LiveSettingManager.livePgcAllowLotMultipleMicSwitch();
        if (info instanceof GiftInfoCombine.GiftInfo) {
            GiftInfoCombine.GiftInfo giftInfo = (GiftInfoCombine.GiftInfo) info;
            isSingleMode = giftInfo.isSingleTargetUserGift();
            if (giftInfo.isLotGift()) {
                isSingleMode = isSingleMode || !allowLotMultipleMicSwitch;
            }
        } else if (info instanceof PackageInfo.Item) {
            PackageInfo.Item item = (PackageInfo.Item) info;
            isSingleMode = !PackageInfo.Item.isPackageGiftType(item);
        }

        changeReceiverSelectMode(isSingleMode || multiBtnIsShow);
    }

    /**
     * 修改收礼人是否可以多选
     *
     * @param isSingleMode true: 隐藏全麦按钮，单选；false: 显示全麦按钮
     */
    private void changeReceiverSelectMode(boolean isSingleMode) {
        LiveHelper.Log.i("changeReceiverSelectMode: " + isSingleMode
                + ",mIsSingleMode:  " + mIsSingleMode);

        if (mIsSingleMode != isSingleMode) {
            UIStateUtil.showViewsIfTrue(!isSingleMode, mSwitchAllMic);

            mIsSingleMode = isSingleMode;
            mReceiversAdapter.setSingleCheck(isSingleMode);
            notifyReceiverList();
        }
    }

    private void showOnMicList() {
        showOrHideReceiverState(null != mReceiverItems && !mReceiverItems.isEmpty());
    }

    @Override
    public void onGiftSelected(BaseItem info) {
        super.onGiftSelected(info);

        // 情况发生在背包为空时
        if (null == info) {
            mGiftTopView.removeAndClearAllViews();
            return;
        }
        BaseGiftPanelItem item = (BaseGiftPanelItem) info;
        if (item.isGiftPanelActivityItem()) {
            //一元首充等活动入口，不显示收礼列表
            showOrHideReceiverState(false);
            return;
        }

        if (info instanceof PackageInfo.Item) {
            if (getIfNeedMicUidParam()) {
                showOnMicList();
            } else {
                showOrHideReceiverState(false);
            }
        } else if (info instanceof GiftInfoCombine.GiftInfo) {
            GiftInfoCombine.GiftInfo giftInfo = (GiftInfoCombine.GiftInfo) info;
            hideOtherViewsShowOnlyReceiverView(giftInfo);
            showOnMicList();
        }
        checkGiftCanSendMultiReceive(info, false);
        mutexMultiBtnBatch();
    }

    @Override
    protected void requestBrocadeBagTips() {
        if (mLiveGiftViewModel != null) {
            mLiveGiftViewModel.requestBrocadeBagBindInfo(getRoomId());
        }
    }

    @Override
    protected void observeBrocadeBagInfo(Fragment fragment) {
        if (mLiveGiftViewModel != null) {
            mLiveGiftViewModel.getLiveBrocadeBagBindInfoData().observe(fragment, liveBrocadeBagBindInfo -> {
                if (mGiftTopView == null) {
                    return;
                }
                if (mLiveBrocadeBagView == null) {
                    return;
                }
                mLiveBrocadeBagView.setBrocadeBagContent(liveBrocadeBagBindInfo.getAnchorUid(),
                        mReceiverItems,
                        fragment.getChildFragmentManager(),
                        getRoomId(),
                        mRoomUid);
                mLiveBrocadeBagView.setGiftViewModel(mLiveGiftViewModel);
                mGiftTopView.addView(new LiveGiftTopViewModel(LiveGiftTopView.ORDER_O, mLiveBrocadeBagView));
            });
            mLiveGiftViewModel.getLiveBrocadeBagBindInfoDataError().observe(fragment, ToastManager::showFailToast);
        }
    }

    @Override
    protected void observeBindInfo(Fragment fragment) {
        if (mLiveGiftViewModel != null) {
            mLiveGiftViewModel.getBindInfoResultData().observe(fragment, result -> {
                if (mLiveBrocadeBagView == null) {
                    return;
                }
                mLiveBrocadeBagView.bindInfoResult(result);
            });
        }
    }

    /**
     * 是否在PGC房间收礼人上面显示，根据礼物类型的白名单
     */
    private void hideOtherViewsShowOnlyReceiverView(GiftInfoCombine.GiftInfo giftInfo) {
        if (!giftInfo.isNobleGift()
                && !giftInfo.isLuckyGift()
                && !giftInfo.isMagicGift()
                && !giftInfo.isAnnualGift()
                && !giftInfo.isCelebrationGift()
                && !giftInfo.isFansGift()
                && !giftInfo.isRelationGift()
                && !giftInfo.isCpRelationshipGift()
                && !giftInfo.isTxtGift()
                && !giftInfo.isInteractiveGift()
                && !giftInfo.isIntimacyGift()
        ) {
            mGiftTopView.removeAndClearAllViews();
        }
    }

    /**
     * 显示或者隐藏收礼人列表
     *
     * @param show true 显示收礼人列表 false 隐藏收礼人列表
     */
    private void showOrHideReceiverState(boolean show) {
        if (show) {
            if (mGiftTopView != null) {
                mGiftTopView.addView(new LiveGiftTopViewModel(LiveGiftTopView.ORDER_2, mSelectReceiverLayout));
            }
        } else {
            if (mGiftTopView != null) {
                mGiftTopView.removeView(new LiveGiftTopViewModel(LiveGiftTopView.ORDER_2, mSelectReceiverLayout));
            }
        }
    }

    /**
     * 礼物接口文档，包括类型说明:<a href="http://gitlab.ximalaya.com/live-team/Cowboy-Bebop/blob/master/treasure/v1/treasure-package-web-api.md">类型说明</a>
     *
     * @param item 背包物品
     */
    @Override
    public void usePackageItem(PackageInfo.Item item) {
        if (null == item) {
            CustomToast.showFailToast("请选择物品");
            return;
        }

        //虚拟币跳转兑换商城
        if (PackageInfo.Item.TYPE_VIRTUAL_COIN == item.type) {
            if (!TextUtils.isEmpty(item.iting)) {
                BaseFragment baseFragment = NativeHybridFragment
                        .newInstance(item.iting
                                + "?_fullscreen=1&subType=" + item.subType, true);
                if (getOwnerActivity() != null && getOwnerActivity() instanceof MainActivity) {
                    ((MainActivity) getOwnerActivity()).startFragment(baseFragment);
                    dismiss();
                }
            } else {
                String message = TextUtils.isEmpty(item.msg) ? "配置错误，请重试" : item.msg;
                CustomToast.showFailToast(message);
            }

            return;
        }


        List<GiftReceiverItem> selectedUsers = getSelectedUsers();
        boolean needSelectedUser = getIfNeedMicUidParam();
        if (needSelectedUser) {
            if (ToolUtil.isEmptyCollects(selectedUsers)) {
                CustomToast.showFailToast("请选择赠送人");
                return;
            }
        }
        // 连击礼物类型
        if (PackageInfo.Item.isPackageGiftType(item) && mGiftLoader.isSupportPackageGiftBatter(item.referId)) {
            mGiftLoader.consecutiveUsePackageItem(item, mReceiverUid, mGiftNum, mRepeatHitHand, getWordGiftContent());
            return;
        }

        mGiftLoader.usePackageItem(item, mReceiverUid, mGiftNum, false, 0, null);
    }


    @Override
    public void onClick(View view) {
        super.onClick(view);

        int id = view.getId();
        if (id == R.id.live_hall_selected_user_info) {
            if (null != mOnShowUserInfo && null != getSelectedUsers() && !getSelectedUsers().isEmpty()) {
                mOnShowUserInfo.showUserInfo(getSelectedUsers().get(0).uid);
            }

            dismiss();
            sendUserTracking("打赏面板", "资料", "7018");
            trackUserInfoClick();
        }
    }

    @Override
    protected void intimacyInviteClick(GiftInfoCombine.GiftInfo intimacyGiftInfo) {
        if (!getSelectedUsers().isEmpty() && intimacyGiftInfo != null) {
            // 亲密关系礼物只能选择一个主播
            mLiveGiftViewModel.postRelationInvite(mBizType, getRoomId(), getSelectedUsers().get(0).uid, intimacyGiftInfo.id);
        }
    }

    @Override
    protected void requestIntimacyTips(GiftInfoCombine.GiftInfo giftInfo) {
        if (mLiveGiftViewModel != null && !getSelectedUsers().isEmpty() && giftInfo != null) {
            mLiveGiftViewModel.getRelationTips(giftInfo.id, getSelectedUsers().get(0).uid, mBizType);
        }
    }

    private void trackUserInfoClick() {
        if (LiveBaseAttributeRecord.getInstance().hasBaseAttributeData()) {
            LiveBaseAttributeRecord.getInstance().getBaseTrace()
                    .click(15796)
                    .put("currPage", "fmMainScreen")
                    .createTrace();

        }
    }

    @Override
    public void onTabIndicatorChanged(int position, CommonGiftPage.PageItemInfo itemInfo) {
        super.onTabIndicatorChanged(position, itemInfo);
        // 注意此方法切换tab会回调两次！！！
        sendUserTracking("打赏面板", itemInfo.pageTitle, "7016");
        trackTabChangeOrClick(itemInfo);
    }

    protected long mLastTrackTimeMs;//上次上传的时间戳，避免多次上传

    protected void trackTabChangeOrClick(CommonGiftPage.PageItemInfo itemInfo) {
        if (System.currentTimeMillis() - mLastTrackTimeMs < 100) {
            return;
        }

        if (itemInfo == null || TextUtils.isEmpty(itemInfo.pageTitle)) {
            return;
        }

        if (LiveBaseAttributeRecord.getInstance().hasBaseAttributeData()) {

            mLastTrackTimeMs = System.currentTimeMillis();

            LiveBaseAttributeRecord.getInstance().getBaseTrace()
                    .click(15793)
                    .put("currPage", "fmMainScreen")
                    .put("Item", itemInfo.pageTitle)
                    .createTrace();
        }

    }


    /**
     * 设置麦上用户列表 指定默认选中人
     *
     * @param onMicUsers    麦上用户列表
     * @param defaultUserId 默认选中人
     */
    public void setOnMicUsers(@NonNull List<GiftReceiverItem> onMicUsers, long defaultUserId) {

        if (isReceiverUserSame(mReceiverItems, onMicUsers) && mDefaultUserId != 0 && mDefaultUserId == defaultUserId && !mReceiverListShouldRefresh) {
            Logger.d(TAG, "isReceiverUserSame and defaultUserId same");
            return;
        }
        mReceiverItems = onMicUsers;
        mDefaultUserId = defaultUserId;

        if (null != mReceiversAdapter) {
            mReceiversAdapter.updateList(mReceiverItems, defaultUserId);
            mReceiverItems = mReceiversAdapter.getList();
            mReceiverListShouldRefresh = false;
            notifyReceiverList();
            if (mGiftPage != null) {
                onGiftSelected(mGiftPage.getCurrentSelectedBean());
            }
        }
    }

    /**
     * 更新选中麦上用户
     *
     * @param selectedUserId 被选中的麦上用户
     */
    public void updateSelectedUser(long selectedUserId) {
        if (null != mReceiversAdapter) {
            mReceiversAdapter.updateList(mReceiverItems, selectedUserId);
            notifyReceiverList();
            if (mGiftPage != null) {
                onGiftSelected(mGiftPage.getCurrentSelectedBean());
            }
        }
    }

    public List<GiftReceiverItem> getReceiverItems() {
        return mReceiverItems;
    }

    @NonNull
    public List<GiftReceiverItem> getSelectedUsers() {
        List<GiftReceiverItem> users = new ArrayList<>();

        if (null != mBeanSelected) {
            // 仅使用于房间的物品返回房主 id
            if (mBeanSelected instanceof PackageInfo.Item) {
                PackageInfo.Item item = (PackageInfo.Item) mBeanSelected;
                if (PackageInfo.Item.TYPE_PROP == item.type
                        && PackageInfo.Item.SUB_TYPE_CUSTOM_PROP == item.subType
                        && PackageInfo.Item.CUSTOM_INNER_TYPE_HOST == item.customInnerType) {
                    GiftReceiverItem giftReceiverItem = new GiftReceiverItem();

                    giftReceiverItem.uid = mRoomUid;

                    users.add(giftReceiverItem);

                    return users;
                }
            }
        }

        for (GiftReceiverItem item : mReceiversAdapter.getList()) {
            if (item.isSelected()) {
                users.add(item);
            }
        }

        return users;
    }

    public boolean getIfNeedMicUidParam() {
        if (null != mBeanSelected) {
            //在背包中，只有礼物和自定义道具中的指定麦上人使用类型需要选择麦上人，其他都不需要传micUid参数
            if (mBeanSelected instanceof PackageInfo.Item) {
                PackageInfo.Item item = (PackageInfo.Item) mBeanSelected;
                // 如果是需要展示提示，则这里直接返回true，显示收礼人
                if (item.hasTips && !item.isWishPackage()) {
                    //心愿商店不需要收礼人
                    return true;
                }

                if (PackageInfo.Item.TYPE_PROP == item.type) {

                    if (PackageInfo.Item.SUB_TYPE_GIFT == item.subType) {
                        return true;
                    }

                    return PackageInfo.Item.SUB_TYPE_CUSTOM_PROP == item.subType
                            && PackageInfo.Item.CUSTOM_INNER_TYPE_ON_MIC == item.customInnerType;
                }
            }
        }

        return false;
    }

}
