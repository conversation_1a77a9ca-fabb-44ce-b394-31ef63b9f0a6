package com.ximalaya.ting.android.live.common.lib.gift.panel.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import com.handmark.pulltorefresh.library.ILoadingLayout;
import com.handmark.pulltorefresh.library.PullToRefreshRecyclerView;
import com.handmark.pulltorefresh.library.internal.IRefreshPullProportion;
import com.handmark.pulltorefresh.library.internal.LoadingLayout;
import com.handmark.pulltorefresh.library.internal.XmLoadingLayout;
import com.ximalaya.ting.android.framework.activity.BaseFragmentActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * desc：
 * Created by xuxinkai on 2022-06-28
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 17602190319
 */
public class LivePullToRefreshRecyclerView extends PullToRefreshRecyclerView  {
    /**
     * 是否需要在加载的时候，隐藏文案
     */
    private boolean isHideLoadingText = false;

    public LivePullToRefreshRecyclerView(Context context) {
        super(context);
    }

    public LivePullToRefreshRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public LivePullToRefreshRecyclerView(Context context, Mode mode) {
        super(context, mode);
    }

    public LivePullToRefreshRecyclerView(Context context, Mode mode, AnimationStyle animStyle) {
        super(context, mode, animStyle);
    }

    public void setHideLoadingText(boolean hideLoadingText) {
        isHideLoadingText = hideLoadingText;
    }

    public TextView getFootTextView() {
        return footerLoadingTV;
    }

    public void startLoadingMore() {
       super.startLoadingMore();
       if (isHideLoadingText){
           footerLoadingTV.setText("");
       }

    }

    @Override
    protected LoadingLayout createLoadingLayout(Context context, Mode mode, TypedArray attrs) {
        return createLoadingLayoutImpl(context, mode, attrs);
    }


    private LoadingLayout createLoadingLayoutImpl(Context context, Mode mode, TypedArray attrs) {
        LoadingLayout loadingLayout = super.createLoadingLayout(context, mode, attrs);

        if (loadingLayout instanceof XmLoadingLayout) {

            XmLoadingLayout xmLoadingLayout = (XmLoadingLayout) loadingLayout;

            if (allHeaderViewColor == 0) {
                allHeaderViewColor = BaseFragmentActivity.sIsDarkMode ? 0xffcfcfcf : Color.BLACK;
            }
            xmLoadingLayout.setAllViewColor(allHeaderViewColor);
            // 因为其他地方会设置这个,所以在这里重置下
            xmLoadingLayout.setLoadingDrawable(null);

        }
        return loadingLayout;
    }

    private int allHeaderViewColor = BaseFragmentActivity.sIsDarkMode ? 0xffcfcfcf : Color.BLACK;

    private int lastColor = allHeaderViewColor;

    public void setAllHeaderViewColor(int color) {
        if (lastColor == color) {
            return;
        }
        lastColor = color;
        allHeaderViewColor = color;

        ILoadingLayout loadingLayoutProxy = getLoadingLayoutProxy();
        loadingLayoutProxy.setAllViewColor(color);
    }

    public int getHeaderViewColor() {
        return allHeaderViewColor;
    }




}
