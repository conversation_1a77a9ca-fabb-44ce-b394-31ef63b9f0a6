package com.ximalaya.ting.android.live.common.input.guide;

import android.content.DialogInterface;
import android.text.Html;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.ximalaya.ting.android.framework.manager.XDCSCollectUtil;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.util.common.DeviceUtil;
import com.ximalaya.ting.android.host.xdcs.usertracker.UserTracking;
import com.ximalaya.ting.android.live.common.R;
import com.ximalaya.ting.android.live.common.dialog.base.LiveBaseDialogFragment;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * 贵族弹幕引导开通贵族.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13162538925
 */

public class LiveNobleBulletGuideFragment extends LiveBaseDialogFragment
        implements View.OnClickListener {

    private View.OnClickListener mOpenNobleClickListener;

    public static LiveNobleBulletGuideFragment newInstance(View.OnClickListener onClickListener) {
        LiveNobleBulletGuideFragment fragment = new
                LiveNobleBulletGuideFragment();
        fragment.mOpenNobleClickListener = onClickListener;
        return fragment;
    }

    public static void show(FragmentManager fragmentManager, View.OnClickListener onClickListener) {
        if (fragmentManager == null) {
            return;
        }

        FragmentTransaction mFragTransaction = fragmentManager.beginTransaction();
        LiveNobleBulletGuideFragment nobleBulletGuideFragment =
                (LiveNobleBulletGuideFragment) fragmentManager.findFragmentByTag
                        ("LiveNobleBulletGuideFragment");
        if (nobleBulletGuideFragment != null) {
            // 防止重复显示对话框，移除正在显示的对话框
            mFragTransaction.remove(nobleBulletGuideFragment);
        }
        nobleBulletGuideFragment = LiveNobleBulletGuideFragment.newInstance(onClickListener);

        if (nobleBulletGuideFragment != null) {
            nobleBulletGuideFragment.show(mFragTransaction,
                    "LiveNobleBulletGuideFragment");
        }
    }

    @Override
    public void load() {

    }

    @Override
    public void init() {
        TextView tvTag = (TextView) findViewById(R.id.live_tv_noble_guide_msg);
        ImageView ivClose = (ImageView) findViewById(R.id.live_fans_guide_close);
        ImageButton ivOpenNoble = (ImageButton) findViewById(R.id.live_btn_open_noble);

        tvTag.setText(Html.fromHtml("贵族专属特权<br/>成为贵族即可<font color='#CBAB7E'>免费</font>发送贵族弹幕～"));

        ivClose.setOnClickListener(this);
        ivOpenNoble.setOnClickListener(this);

        AutoTraceHelper.bindData(ivClose, AutoTraceHelper.MODULE_DEFAULT, "");
        AutoTraceHelper.bindData(ivOpenNoble, AutoTraceHelper.MODULE_DEFAULT, "");
    }

    @Override
    protected int getLayoutId() {
        return R.layout.live_common_dialog_noble_bullet_guide;
    }

    @Override
    public LiveFragmentDialogParams getCustomLayoutParams() {
        LiveFragmentDialogParams params = new LiveFragmentDialogParams();
        params.style = R.style.LiveCommonTransparentDialog;
        params.gravity = Gravity.CENTER;
        params.width = BaseUtil.dp2px(getActivity(), 274);
        params.height = BaseUtil.dp2px(getActivity(), 264);
        params.canceledOnTouchOutside = true;

        return params;
    }

    @Override
    public void onClick(View v) {
        if (v == null || !OneClickHelper.getInstance().onClick(v)) {
            return;
        }

        if (v.getId() == R.id.live_fans_guide_close) {
            dismiss();
        } else if (v.getId() == R.id.live_btn_open_noble) {
            if (mOpenNobleClickListener != null) {
                mOpenNobleClickListener.onClick(v);
            }
            traceClickEvent();
            dismiss();
        }
    }

    @Override
    public void onShow(DialogInterface dialogInterface) {
        super.onShow(dialogInterface);
        traceShowEvent();
    }

    //埋点
    private void traceShowEvent() {
        // 直播间-加贵族弹窗  控件曝光
        new XMTraceApi.Trace()
                .setMetaId(52185)
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .setServiceId("slipPage") // 用户滑动停止、页面刷新、页面重新出现在屏幕内时上报，不去重
                .put("currPage", "liveRoom")
                .put("screenDirection", DeviceUtil.isLandscape(MainApplication.getTopActivity()) ? "1" : "2") // 1 表示横屏，2 表示竖屏
                .createTrace();
    }

    //埋点
    private void traceClickEvent() {
        // 直播间-加贵族弹窗  点击事件
        new XMTraceApi.Trace()
                .click(52184) // 用户点击时上报
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .put("currPage", "liveRoom")
                .put("screenDirection", DeviceUtil.isLandscape(MainApplication.getTopActivity()) ? "1" : "2") // 1 表示横屏，2 表示竖屏
                .put("Item", "开通贵族")
                .createTrace();
    }
}
