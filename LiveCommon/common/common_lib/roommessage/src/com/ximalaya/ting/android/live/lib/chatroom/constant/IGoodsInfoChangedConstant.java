package com.ximalaya.ting.android.live.lib.chatroom.constant;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 商品信息变更
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/27.
 */
@IntDef
@Retention(RetentionPolicy.SOURCE)
public @interface IGoodsInfoChangedConstant {

    /**
     * ADD(1, "新增商品"),
     * DELETE(2, "删除商品"),
     * EXPLAIN(3, "讲解中"),
     * UNEXPLAIN(4, "取消讲解"),
     * GONE(5, "抢光"),
     * SETHOT(6, "设置热门"),
     * CANCELHOT(7, "取消热门"),
     * SUPPLEMENT(8, "补货"),
     */
    int ADD = 1;
    int DELETE = 2;
    int EXPLAIN = 3;
    int UNEXPLAIN = 4;
    int GONE = 5;
    int SETHOT = 6;
    int CANCELHOT = 7;
    int SUPPLEMENT = 8;
}