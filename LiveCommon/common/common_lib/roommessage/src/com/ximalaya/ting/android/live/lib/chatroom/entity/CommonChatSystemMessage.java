package com.ximalaya.ting.android.live.lib.chatroom.entity;

import android.util.Log;

import androidx.annotation.NonNull;

import com.tencent.bugly.crashreport.CrashReport;
import com.ximalaya.ting.android.live.common.lib.entity.AnchorPilotUpdateMsg;
import com.ximalaya.ting.android.live.common.lib.entity.HeadAnchorInfo;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAiSoundPenalty;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorRelationBindMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnchorTaskMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAnimationLayerMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAuditNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveAwardGiftV2Msg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCarouselRankNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveCommonActGiftGuideMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveDanmuCountModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveDanmuFreeModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansClubStatusModel;
import com.ximalaya.ting.android.live.common.lib.entity.LiveFansProgressBean;
import com.ximalaya.ting.android.live.common.lib.entity.LiveGetAiGiftMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveGiftUnLockNotifyMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveGuardGuideBean;
import com.ximalaya.ting.android.live.common.lib.entity.LiveHostNoPushStreamMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveLuckySilkBagMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveMoreLiveNotifyMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveNewPkAnimMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveNewbieGuideMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveNobleExpireNotifyMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialNoticeMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialSwitchMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveOfficialTimerMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRecallGiftGuideMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRelationBindUserMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRelationBindUserSuccessMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRemindPkMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRoomAvatarDecorate;
import com.ximalaya.ting.android.live.common.lib.entity.LiveRoomPreviewInfoUpdateNotifyMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveSpringFestivalMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveSpringFestivalProgressMessage;
import com.ximalaya.ting.android.live.common.lib.entity.LiveStreamCdnChangedMsg;
import com.ximalaya.ting.android.live.common.lib.entity.LiveTaskNotify;
import com.ximalaya.ting.android.live.common.lib.entity.LiveWealCenterModel;
import com.ximalaya.ting.android.live.common.lib.entity.ai.LiveAnchorAiConfigInfo;
import com.ximalaya.ting.android.live.common.lib.entity.ai.LiveImAiHelperMsg;
import com.ximalaya.ting.android.live.common.lib.entity.premiere.LivePremiereMsg;
import com.ximalaya.ting.android.live.common.lib.entity.rank.FirstChargeNotify;
import com.ximalaya.ting.android.live.common.lib.utils.LiveGsonUtils;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.BaseSystemMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonBusinessMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatFansIntimacyMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAdminUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAlbumInfoMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomAnchorVerifyWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomBigSvgMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomCloseMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomComboBigGiftMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomCommonH5DialogMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomCompleteWishListMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomCoverChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansClubUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomFansRankMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomInviteMicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomKtvPreOrderMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomLoveValueChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNewTopicMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNobleClubUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNoticeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomNotifyBottomButtonMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOnlineUserListMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomOperationChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomRankMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomRedPacketOverMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomRuleInfoUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomSkinUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomStatusChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomTitleUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomToastMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomTopicUpdateMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomUserInfoUpdateMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonChatRoomWarningMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonCouponShowViewStatusMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonCourseShopMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonFansGroupMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGetNewCouponMsg;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoShoppingMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsInfoChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonGoodsOrderChangedMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonLiveVideoInfoChangeMessage;
import com.ximalaya.ting.android.live.lib.chatroom.entity.system.CommonPushJsData;
import com.ximalaya.ting.android.live.lib.livetopic.bean.LiveAreaConfigListBean;
import com.ximalaya.ting.android.xmutil.Logger;

/**
 * 系统消息。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @wiki <a href="http://gitlab.ximalaya.com/tandy.zhang/xchat-guide/blob/master/room_chat/rm-chat/rm-chat-system-msg.md">系统消息接口文档</a>
 * @server 沈斯
 * @since 2019/4/6
 */
public class CommonChatSystemMessage {

    private static final String TAG = "CommonChatSystemMessage";

    /**
     * 直播状态变化消息
     */
    public final static int THIRD_TYPE_SYS_MSG_LIVE_ROOM_STATUS_CHANGED = 4;
    /**
     * 添加管理员消息
     */
    public final static int THIRD_TYPE_SYS_MSG_ADD_ROOM_ADMIN = 5;
    /**
     * 移除管理员消息
     */
    public final static int THIRD_TYPE_SYS_MSG_REMOVE_ROOM_ADMIN = 6;
    /**
     * 警告消息
     */
    public final static int THIRD_TYPE_SYS_MSG_WARNING_THE_HOST = 8;
    /**
     * 直播间喜爱值变化
     */
    public final static int THIRD_TYPE_SYS_MSG_LOVE_VALUE_CHANGED = 9;
    /**
     * 红包抢光消息
     */
    public final static int THIRD_TYPE_SYS_MSG_RED_PACK_OVER = 12;
    /**
     * 发布或更新话题
     */
    public final static int THIRD_TYPE_SYS_MSG_PUBLISH_TOPIC = 13;
    /**
     * 直播间运营位有变化
     */
    public final static int THIRD_TYPE_SYS_MSG_OPERATION_CHANGE = 15;
    /**
     * 直播间换肤消息
     */
    public final static int THIRD_TYPE_SYS_MSG_CHANGE_SKIN = 17;
    /**
     * 直播间提示消息/公屏系统提示消息
     */
    public final static int THIRD_TYPE_SYS_MSG_CHAT_ROOM_NOTICE = 21;
    /**
     * 粉丝团人数
     */
    public final static int THIRD_TYPE_SYS_MSG_FANS_CLUB = 22;
    /**
     * 在线贵族人数
     */
    public final static int THIRD_TYPE_SYS_MSG_ONLINE_NOBLE = 23;
    /**
     * 直播间 Toast 提示，客户端直接展示
     */
    public final static int THIRD_TYPE_SYS_MSG_CHAT_ROOM_TOAST = 24;
    /**
     * 连击或批量送指定数量小礼物触发的特效
     */
    public final static int THIRD_TYPE_SYS_MSG_COMBO_BIG_GIFT = 25;
    /**
     * 更新余额（背包、喜钻、喜点）
     */
    public final static int THIRD_TYPE_SYS_MSG_BALANCE_UPDATE = 26;
    /**
     * 非送礼触发直播间特效，包括PK道具
     */
    public final static int THIRD_TYPE_SYS_MSG_BIG_SVG = 28;
    /**
     * 直播间流量卡信息有更新
     */
    public final static int THIRD_TYPE_SYS_MSG_QUERY_TRAFFIC_CARD_INFO = 29;
    /**
     * 语音房周榜
     */
    public final static int THIRD_TYPE_SYS_MSG_GIFT_WEEK_RANK_CHANGED = 31;
    /**
     * 心愿单弹窗消息
     */
    public final static int THIRD_TYPE_SYS_MSG_WISH_LIST = 32;
    /**
     * 审核警告弹窗：主播未互动、被警告、被隐藏
     */
    public final static int THIRD_TYPE_SYS_MSG_ANCHOR_VERIFY_WARNING = 33;
    /**
     * 商品信息变更
     */
    public final static int THIRD_TYPE_SYS_MSG_GOODS_INFO_CHANGED = 35;
    /**
     * 商品顺序变更
     */
    public final static int THIRD_TYPE_SYS_MSG_GOODS_ORDER_CHANGED = 36;
    /**
     * 购买效果
     */
    public final static int THIRD_TYPE_SYS_MSG_GO_SHOPPING = 37;
    /**
     * 关闭优惠券挂件通知
     */
    public final static int THIRD_TYPE_SYS_MSG_COUPON_SHOWVIEW_STATUS_CHANGE = 38;
    /**
     * 主播发券通知
     */
    public final static int THIRD_TYPE_SYS_MSG_GET_NEW_COUPON = 39;
    /**
     * 粉丝团消息
     */
    public final static int THIRD_TYPE_SYS_MSG_FANS_GROUP = 40;
    /**
     * 底部按钮刷新消息（同时刷新更多菜单功能）
     */
    public final static int THIRD_TYPE_SYS_MSG_NOTIFY_BOTTOM_BUTTON = 41;
    /**
     * 通用弹窗消息
     * 目前也用于 粉丝团升级消息，因为粉丝团升级弹框在 9.2.75版本移除，添加在了粉丝团头部
     */
    public final static int THIRD_TYPE_SYS_MSG_COMMON_H5_DIALOG = 42;

    /**
     * 用户在线列表更新消息消息
     */
    public static final int THIRD_TYPE_SYS_MSG_ONLINE_USER_LIST = 44;

    /**
     * 房间内用户头像框、座位框更新
     */
    public static final int THIRD_TYPE_SYS_MSG_ROOM_USER_INFO_UPDATE = 46;

    /**
     * 长连接支持活动类消息推送
     */
    public static final int THIRD_TYPE_SYS_MSG_PUSH_JS_DATA = 47;

    /**
     * 更新信号量：直播间通用业务消息，分别用于
     * 1. 福袋
     * 2. 投票
     * 3. 官方投票
     * 4. 天选红包
     * 5. ai助手小红点提醒
     */
    public static final int THIRD_TYPE_SYS_MSG_COMMON_BUSINESS = 48;

    /**
     * 专辑推荐消息
     */
    public static final int TYPE_SYSTEM_RECOMMEND_ALBUM_INFO = 49;

    /**
     * 当天首次评论有惊喜消息
     */
    public static final int TYPE_SYSTEM_FIRST_COMMENT_AWARD = 51;

    /**
     * 自打赏引导消息
     */
    public static final int TYPE_SELF_REWARD_GUIDANCE_MSG = 50;

    /**
     * 粉丝团提醒消息
     */
    public static final int TYPE_FANS_REMIND_MSG = 52;

    /**
     * 生日场抽奖消息
     */
    public static final int TYPE_BIRTHDAY_INFO_MSG = 53;

    /**
     * 榜单更新消息。个播使用 type=88 系统消息，PGC 使用本消息
     */
    public static final int THIRD_TYPE_SYS_MSG_LIVE_HOUR = 54;

    /**
     * UGC聊天室：房间话题更新消息
     */
    public static final int TYPE_SYS_MSG_ROOM_TOPIC_UPDATE = 55;

    /**
     * 房间特殊开关
     */
    public static final int TYPE_SPECIAL_MODE_SWITCH_MSG = 56;

    /**
     * 抢头条消息
     */
    public static final int TYPE_HEAD_LINES_MSG = 58;

    /**
     * 课程直播购物车显示隐藏消息
     */
    public static final int THIRD_TYPE_SYS_MSG_LIVE_COURSE = 57;

    /**
     * 弹幕体验消息
     */
    public static final int TYPE_DANMU_FREE_MSG = 59;

    /**
     * 预点歌
     */
    public static final int THIRD_TYPE_SYS_MSG_PRE_ORDER = 60;

    /**
     * 弹幕使用次数
     */
    public static final int TYPE_DANMU_COUNT_MSG = 61;

    /**
     * 直播热门话题更新消息
     */
    public static final int TYPE_LIVE_HOT_TOPIC = 62;

    /**
     * 课程直播间内部分信息变更
     */
    public static final int TYPE_LIVE_INFO_CHANGE = 65;

    /**
     * 激活粉丝团活跃状态
     */
    public static final int TYPE_LIVE_ACTIVE_FANS_CLUE = 66;
    /**
     * 官方直播间：定时器提示通道
     */
    public static final int TYPE_LIVE_OFFICIAL_LIVE_TIMER = 67;
    /**
     * 官方直播间：主播选择
     */
    public static final int TYPE_LIVE_OFFICIAL_LIVE_CHOOSE_ACTION = 68;
    /**
     * 官方直播间：直播间切换消息
     */
    public static final int TYPE_LIVE_OFFICIAL_LIVE_SWITCH_ACTION = 69;
    /**
     * 直播间检测无推流警告(不关闭直播)
     */
    public static final int TYPE_LIVE_HOST_NO_PUSH_STREAM = 70;

    /**
     * 送好运锦囊的弹窗消息
     */
    public static final int TYPE_LIVE_LUCKY_SILK_BAG_RESULT = 73;

    /**
     * 新人引导内容推送消息
     */
    public static final int TYPE_LIVE_NEWBIE_GUIDE = 74;

    /**
     * 主播任务和心愿单消息
     */
    public static final int TYPE_LIVE_ANCHOR_TASK_AND_WISH = 76;

    /**
     * 活动礼物引导推送消息，如：每日任务、限时任务、新人任务等
     */
    public static final int TYPE_LIVE_ACT_GIFT_GUIDE = 75;

    /**
     * 首映室相关信令消息
     */
    public static final int TYPE_LIVE_PREMIERE_MSG = 77;

    /**
     * 直播流地址切换消息
     */
    public static final int TYPE_LIVE_STREAM_CDN_CHANGED = 78;

    /**
     * 礼物解锁消息通知更新
     */
    public static final int TYPE_LIVE_GIFT_UN_LOCK_NOTIFY = 80;
    /**
     * 主播任务完成通知
     */
    public static final int TYPE_LIVE_HOST_TASK_COMPLETE_NOTIFY = 82;

    /**
     * 用户召回收听礼引导弹窗消息
     */
    public static final int TYPE_RECALL_GIFT_GUIDE_DIALOG = 83;

    /**
     * 主播收到观众送的AI礼物
     */
    public static final int TYPE_LIVE_REC_AI_GIFT = 85;

    /**
     * ai声音惩罚消息
     */
    public static final int TYPE_LIVE_AI_SOUND_PENALTY = 86;

    /**
     * 公告审核失败消息
     */
    public static final int TYPE_LIVE_ANNOUNCEMENT_AUDIT_FAILED = 87;

    /**
     * 个播榜单轮播展示
     */
    public static final int TYPE_LIVE_CAROUSEL_RANK = 88;

    /**
     * 新春年兽消息
     */
    public static final int TYPE_SPRING_FESTIVAL_ACTIVITY = 89;

    /**
     * 新春打年兽进度更新消息
     */
    public static final int TYPE_SPRING_FESTIVAL_ACTIVITY_PROGRESS = 90;

    /**
     * 助力榜更新消息，只有个播在用
     */
    public final static int THIRD_TYPE_SYS_MSG_RANK_AND_ONLINE = 91;

    /**
     * 直播间头像框更新消息
     */
    public static final int TYPE_LIVE_ROOM_AVATAR_DECORATE = 92;

    /**
     * 聊天区域AI助手 ：生成话题
     * wiki: <a href="https://alidocs.dingtalk.com/i/nodes/lyQod3RxJK3m7lXAUKR3BwMKJkb4Mw9r">AI提供话题系分</a>
     */
    public final static int TYPE_SYS_MSG_CHAT_AI_HELPER = 93;

    /**
     * 动画层消息
     */
    public final static int TYPE_SYS_MSG_ANIMATION_LAYER = 94;

    /**
     * 召回任务提示弹窗 V2 - H5 新人任务弹窗
     */
    public final static int TYPE_SYS_MSG_AWARD_GIFT_H5 = 95;

    /**
     * 心愿单+礼物墙任务进展
     */
    public final static int TYPE_SYS_MSG_WISH_OR_GIFT_WALL_PROGRESS = 96;

    /**
     * 粉丝百、千团进度
     */
    public final static int TYPE_SYS_MSG_FANS_NUMBER_OF_PROGRESS = 97;

    /**
     * 守护团 引导信息推送
     */
    public final static int TYPE_SYS_MSG_GUARD_GUIDE_MSG = 98;
    /**
     * 一元首冲提醒
     */
    public final static int TYPE_SYS_MSG_FIRST_CHARGE = 99;

    /**
     * ai配置更新 （主要用于礼物截屏开关和金额改变， 话题主要依赖后端信令推送，在线状态主要H5侧展示）
     */
    public final static int TYPE_SYS_MSG_AI_CONFIG_UPDATE = 103;

    /**
     * 平台红包展示结果信令，原使用42系统消息信令，由于压测压力大，新增信令，延迟[0，3) 秒展示iTing
     */
    public final static int TYPE_SYS_MSG_RED_PACKET_HEAVENLY_RESULT = 104;

    /**
     * 房间亲密度加成信令
     */
    public final static int TYPE_SYS_MSG_ROOM_FANS_INTIMACY_UPDATE = 105;

    /**
     * 「更多直播」内容更新下发系统消息：正在直播中、有红包、有福袋
     */
    public final static int TYPE_SYS_MSG_MORE_LIVE_NOTIFY = 106;

    /**
     * 直播预告有更新
     */
    public final static int TYPE_SYS_MSG_ROOM_PREVIEW_INFO_UPDATE = 107;

    /**
     * 贵族到期提醒
     */
    public final static int TYPE_SYS_MSG_NOBLE_EXPIRE = 108;

    /**
     * 新版PK道具动画消息
     */
    public final static int TYPE_SYS_MSG_NEW_PK_ANIM = 109;

    /**
     * 福利中心引导卡片
     */
    public final static int TYPE_SYS_MSG_WEAL_CENTER = 110;

    /**
     * 排位pk提醒主播pk消息
     */
    public final static int TYPE_SYS_MSG_REMIND_PK = 111;

    /**
     * 房间锦囊消息更新
     */
    public final static int TYPE_SYS_MSG_ROOM_BROCADE_BAG = 114;

    /**
     * 领航任务等级
     */
    public final static int TYPE_SYS_MSG_PILOT_LEVEL_UPDATE = 115;

    /**
     * 房间主视区 展示类型改变消息
     */
    public final static int TYPE_SYS_MSG_AREA_CONFIG_UPDATE = 116;

    /**
     * 用户亲密关系绑定和操作消息
     */
    public final static int TYPE_SYS_MSG_USER_BIND_RELATION = 117;

    /**
     * 主播亲密关系绑定和操作消息
     */
    public final static int TYPE_SYS_MSG_ANCHOR_BIND_RELATION = 118;

    /**
     * 绑定成功消息
     */
    public final static int TYPE_SYS_MSG_USER_BIND_RELATION_SUCCESS = 119;

    /**
     * 玩法更新消息
     */
    public final static int THIRD_TYPE_SYS_MSG_GAME_RULES_UPDATE = 600;
    /**
     * 标题更新消息
     */
    public final static int THIRD_TYPE_SYS_MSG_TITLE_UPDATE = 601;
    /**
     * 房间关闭消息（审核下架，直播踢人）
     */
    public final static int THIRD_TYPE_SYS_MSG_ROOM_CLOSE = 602;
    /**
     * 主持人开通了房间守护，需要刷新面板里的守护团信息
     */
    public final static int THIRD_SYS_MSG_OPEN_ROOM_GUARD = 603;
    /**
     * PGC聊天室：邀请上麦提示
     */
    public final static int THIRD_SYS_MSG_INVITE_MIC = 605;

    /**
     * PGC聊天室：封面更换消息
     */
    public final static int THIRD_TYPE_SYS_MSG_COVER_CHANGE = 609;


    /**
     * 消息类型
     */
    public int mType;
    /**
     * 消息内容，Json字符串
     */
    public String mContent;
    /**
     * 解析对象
     */
    public Object mParsedData;

    public static Object parseContent(CommonChatSystemMessage chatSystemMessage) {
        if (chatSystemMessage == null) {
            return null;
        }

        int type = chatSystemMessage.mType;
        String content = chatSystemMessage.mContent;

        Class<?> specifyClass = null;
        Object result = null;
        switch (type) {
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_LIVE_ROOM_STATUS_CHANGED:
                specifyClass = CommonChatRoomStatusChangeMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_ADD_ROOM_ADMIN:
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_REMOVE_ROOM_ADMIN:
                specifyClass = CommonChatRoomAdminUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_WARNING_THE_HOST:
                specifyClass = CommonChatRoomWarningMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_LOVE_VALUE_CHANGED:
                specifyClass = CommonChatRoomLoveValueChangeMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_GIFT_WEEK_RANK_CHANGED:
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_RANK_AND_ONLINE:
                specifyClass = CommonChatRoomFansRankMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_RED_PACK_OVER:
                specifyClass = CommonChatRoomRedPacketOverMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_PUBLISH_TOPIC:
                specifyClass = CommonChatRoomTopicUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_OPERATION_CHANGE:
                specifyClass = CommonChatRoomOperationChangeMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_CHANGE_SKIN:
                specifyClass = CommonChatRoomSkinUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_CHAT_ROOM_NOTICE:
                specifyClass = CommonChatRoomNoticeMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_FANS_CLUB:
                specifyClass = CommonChatRoomFansClubUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_ONLINE_NOBLE:
                specifyClass = CommonChatRoomNobleClubUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_CHAT_ROOM_TOAST:
                result = new CommonChatRoomToastMessage();
                ((CommonChatRoomToastMessage) result).content = content;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_COMBO_BIG_GIFT:
                specifyClass = CommonChatRoomComboBigGiftMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_BIG_SVG:
                specifyClass = CommonChatRoomBigSvgMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_WISH_LIST:
                specifyClass = CommonChatRoomCompleteWishListMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_GAME_RULES_UPDATE:
                specifyClass = CommonChatRoomRuleInfoUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_TITLE_UPDATE:
                specifyClass = CommonChatRoomTitleUpdateMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_ROOM_CLOSE:
                specifyClass = CommonChatRoomCloseMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_SYS_MSG_OPEN_ROOM_GUARD:
                specifyClass = Object.class;
                break;
            case CommonChatSystemMessage.THIRD_SYS_MSG_INVITE_MIC:
                specifyClass = CommonChatRoomInviteMicMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_ANCHOR_VERIFY_WARNING:
                specifyClass = CommonChatRoomAnchorVerifyWarningMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_COVER_CHANGE:
                specifyClass = CommonChatRoomCoverChangeMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_GOODS_INFO_CHANGED:
                specifyClass = CommonGoodsInfoChangedMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_GOODS_ORDER_CHANGED:
                specifyClass = CommonGoodsOrderChangedMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_GO_SHOPPING:
                specifyClass = CommonGoShoppingMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_COUPON_SHOWVIEW_STATUS_CHANGE:
                specifyClass = CommonCouponShowViewStatusMsg.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_GET_NEW_COUPON:
                specifyClass = CommonGetNewCouponMsg.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_FANS_GROUP:
                specifyClass = CommonFansGroupMsg.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_COMMON_H5_DIALOG:
            case CommonChatSystemMessage.TYPE_SYS_MSG_RED_PACKET_HEAVENLY_RESULT:
                specifyClass = CommonChatRoomCommonH5DialogMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_ROOM_FANS_INTIMACY_UPDATE:
                specifyClass = CommonChatFansIntimacyMsg.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_NOTIFY_BOTTOM_BUTTON:
                specifyClass = CommonChatRoomNotifyBottomButtonMsg.class;
                break;

            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_ONLINE_USER_LIST:
                specifyClass = CommonChatRoomOnlineUserListMsg.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_ROOM_USER_INFO_UPDATE:
                specifyClass = CommonChatRoomUserInfoUpdateMsg.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_PUSH_JS_DATA:
                specifyClass = CommonPushJsData.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_COMMON_BUSINESS:
                specifyClass = CommonBusinessMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYSTEM_RECOMMEND_ALBUM_INFO:
                specifyClass = CommonChatRoomAlbumInfoMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYSTEM_FIRST_COMMENT_AWARD:
                specifyClass = CommonFirstCommentAward.class;
                break;
            case CommonChatSystemMessage.TYPE_SELF_REWARD_GUIDANCE_MSG:
                specifyClass = SelfRewardGuidanceMessage.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_LIVE_HOUR:
                specifyClass = CommonChatRoomRankMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_ROOM_TOPIC_UPDATE:
                specifyClass = CommonChatRoomNewTopicMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_FANS_REMIND_MSG:
                specifyClass = LiveFansRemindMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_BIRTHDAY_INFO_MSG:
                specifyClass = CommonBirthInfoMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_SPECIAL_MODE_SWITCH_MSG:
                specifyClass = CommonRoomSpecialMode.class;
                break;
            case CommonChatSystemMessage.TYPE_HEAD_LINES_MSG:
                specifyClass = HeadAnchorInfo.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_LIVE_COURSE:
                result = CommonCourseShopMessage.parse(content);
                break;
            case CommonChatSystemMessage.TYPE_LIVE_HOT_TOPIC:
                specifyClass = CommonChatHotTopicMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_DANMU_FREE_MSG:
                specifyClass = LiveDanmuFreeModel.class;
                break;
            case CommonChatSystemMessage.TYPE_DANMU_COUNT_MSG:
                specifyClass = LiveDanmuCountModel.class;
                break;
            case CommonChatSystemMessage.THIRD_TYPE_SYS_MSG_PRE_ORDER:
                specifyClass = CommonChatRoomKtvPreOrderMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_INFO_CHANGE:
                result = CommonLiveVideoInfoChangeMessage.parse(content);
                break;
            case CommonChatSystemMessage.TYPE_LIVE_ACTIVE_FANS_CLUE:
                specifyClass = LiveFansClubStatusModel.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_OFFICIAL_LIVE_TIMER:
                specifyClass = LiveOfficialTimerMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_OFFICIAL_LIVE_CHOOSE_ACTION:
                specifyClass = LiveOfficialNoticeMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_OFFICIAL_LIVE_SWITCH_ACTION:
                specifyClass = LiveOfficialSwitchMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_HOST_NO_PUSH_STREAM:
                specifyClass = LiveHostNoPushStreamMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_LUCKY_SILK_BAG_RESULT:
                specifyClass = LiveLuckySilkBagMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_NEWBIE_GUIDE:
                result = LiveNewbieGuideMsg.parse(content);
                break;
            case CommonChatSystemMessage.TYPE_LIVE_ANCHOR_TASK_AND_WISH:
            case CommonChatSystemMessage.TYPE_SYS_MSG_WISH_OR_GIFT_WALL_PROGRESS:
                specifyClass = LiveAnchorTaskMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_ACT_GIFT_GUIDE:
                specifyClass = LiveCommonActGiftGuideMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_PREMIERE_MSG:
                specifyClass = LivePremiereMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_STREAM_CDN_CHANGED:
                specifyClass = LiveStreamCdnChangedMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_GIFT_UN_LOCK_NOTIFY:
                specifyClass = LiveGiftUnLockNotifyMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_HOST_TASK_COMPLETE_NOTIFY:
                specifyClass = LiveTaskNotify.class;
                break;
            case CommonChatSystemMessage.TYPE_RECALL_GIFT_GUIDE_DIALOG:
                specifyClass = LiveRecallGiftGuideMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_AI_SOUND_PENALTY:
                specifyClass = LiveAiSoundPenalty.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_REC_AI_GIFT:
                specifyClass = LiveGetAiGiftMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_ANNOUNCEMENT_AUDIT_FAILED:
                specifyClass = LiveAuditNotify.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_CAROUSEL_RANK:
                specifyClass = LiveCarouselRankNotify.class;
                break;
            case CommonChatSystemMessage.TYPE_SPRING_FESTIVAL_ACTIVITY:
                specifyClass = LiveSpringFestivalMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_SPRING_FESTIVAL_ACTIVITY_PROGRESS:
                specifyClass = LiveSpringFestivalProgressMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_LIVE_ROOM_AVATAR_DECORATE:
                specifyClass = LiveRoomAvatarDecorate.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_CHAT_AI_HELPER:
                specifyClass = LiveImAiHelperMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_ANIMATION_LAYER:
                specifyClass = LiveAnimationLayerMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_AWARD_GIFT_H5:
                specifyClass = LiveAwardGiftV2Msg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_FANS_NUMBER_OF_PROGRESS:
                specifyClass = LiveFansProgressBean.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_GUARD_GUIDE_MSG:
                specifyClass = LiveGuardGuideBean.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_FIRST_CHARGE:
                specifyClass = FirstChargeNotify.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_AI_CONFIG_UPDATE:
                specifyClass = LiveAnchorAiConfigInfo.GiftScreenshotConfig.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_MORE_LIVE_NOTIFY:
                specifyClass = LiveMoreLiveNotifyMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_ROOM_PREVIEW_INFO_UPDATE:
                specifyClass = LiveRoomPreviewInfoUpdateNotifyMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_NOBLE_EXPIRE:
                specifyClass = LiveNobleExpireNotifyMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_NEW_PK_ANIM:
                specifyClass = LiveNewPkAnimMessage.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_WEAL_CENTER:
                specifyClass = LiveWealCenterModel.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_REMIND_PK:
                specifyClass = LiveRemindPkMsg.class;
                break;
            case TYPE_SYS_MSG_AREA_CONFIG_UPDATE:
                result = LiveAreaConfigListBean.parse(content);
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_USER_BIND_RELATION:
                specifyClass = LiveRelationBindUserMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_PILOT_LEVEL_UPDATE:
                specifyClass = AnchorPilotUpdateMsg.class;
                break;
            case CommonChatSystemMessage.TYPE_SYS_MSG_ANCHOR_BIND_RELATION:
                specifyClass = LiveAnchorRelationBindMsg.class;
                break;

            case CommonChatSystemMessage.TYPE_SYS_MSG_USER_BIND_RELATION_SUCCESS:
                specifyClass = LiveRelationBindUserSuccessMsg.class;
                break;
            default:

                break;
        }

        if (specifyClass != null) {
            try {
                result = LiveGsonUtils.sGson.fromJson(content, specifyClass);
            } catch (Exception e) {
                Logger.d(TAG, "parseContent exception: " + Log.getStackTraceString(e));
                CrashReport.postCatchedException(e);
            }
            if (result instanceof BaseSystemMessage) {
                ((BaseSystemMessage) result).setSystemMessageType(type);
            }
        }

        return result;
    }

    @NonNull
    @Override
    public String toString() {
        return "CommonChatSystemMessage{" +
                ", mType=" + mType +
                ", mContent='" + mContent + '\'' +
                ", mParsedData=" + mParsedData +
                '}';
    }
}
