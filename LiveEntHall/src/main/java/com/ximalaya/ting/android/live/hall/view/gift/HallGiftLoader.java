package com.ximalaya.ting.android.live.hall.view.gift;

import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.base.request.LiveUrlConstants;
import com.ximalaya.ting.android.live.common.lib.gift.panel.BaseGiftLoader;
import com.ximalaya.ting.android.live.common.lib.gift.panel.model.GiftInfoCombine;
import com.ximalaya.ting.android.live.common.lib.gift.panel.multireceiver.GiftReceiverItem;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PGC 聊天室场景 GiftLoader.
 *
 * <AUTHOR>
 */
public class HallGiftLoader extends BaseGiftLoader<HallGiftDialog> {

    /**
     * 房主 uid
     */
    private long mRoomUid;

    public void setRoomUid(long roomUid) {
        mRoomUid = roomUid;
    }

    @Override
    public String getShowType() {
        return ParamsConstantsInLive.GIFT_SHOW_TYPE_HALL;
    }

    @Override
    public boolean isNeedGift() {
        return true;
    }

    @Override
    public boolean isNeedPackage() {
        return true;
    }

    @Override
    public boolean isShowBrocadeBag() {
        return true;
    }

    @Override
    public int getGiftCategory() {
        return ParamsConstantsInLive.GiftCategory.GIFT_CATEGORY_HALL;
    }

    @Override
    public int getPackageCategory() {
        return ParamsConstantsInLive.PackageCategory.PACKAGE_CATEGORY_HALL;
    }

    @Override
    public boolean isLiveTypeGift() {
        return false;
    }

    @Override
    public boolean supportBatchConsecutiveGift() {
        return true;
    }

    @Override
    public int getDefaultPageIndex() {
        return 1;
    }

    @Override
    protected String getUsePackageItemUrl() {
        return LiveUrlConstants.getInstance().getUsePackageItemInHallUrl();
    }

    @Override
    protected String getSendGiftUrl(int giftType) {
        switch (giftType) {
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_BOX:
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_LOT:
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_TYPE_LUCKY_SILK_BAG:
            case GiftInfoCombine.CategoryGift.TabGift.IGiftType.GIFT_DIMENSION_DOOR:
                return LiveUrlConstants.getInstance().getSendBoxGiftUrlForEnt();
        }
        return LiveUrlConstants.getInstance().getSendHallGiftUrl();
    }

    @Override
    protected HashMap<String, String> buildLoadGiftListParams() {
        HashMap<String, String> params = super.buildLoadGiftListParams();

        params.put(ParamsConstantsInLive.ANCHOR_UID, mRoomUid + "");

        return params;
    }

    @Override
    public String getReceiverUidList() {
        if (getDialog() == null || getDialog().getSelectedUsers() == null) {
            return "";
        }
        List<GiftReceiverItem> receiverItems = getDialog().getSelectedUsers();
        StringBuilder selectedUsers = new StringBuilder();

        for (int i = 0; i < receiverItems.size(); i++) {
            GiftReceiverItem item = receiverItems.get(i);

            selectedUsers.append(item.uid);

            if (i < receiverItems.size() - 1) {
                selectedUsers.append(",");
            }
        }
        return selectedUsers.toString();
    }

    @Override
    protected boolean checkSendParams(GiftInfoCombine.GiftInfo info, int giftNum) {
        HallGiftDialog dialog = getDialog();
        return null != dialog && null != dialog.getSelectedUsers() && !dialog.getSelectedUsers().isEmpty();
    }

    @Override
    protected Map<String, String> buildUsePackageItemParams(long itemId, long receiverUid, long expireAtTimestamp,
                                                            int num, int mediaType, long referId, boolean repeat, long conseUnifiedNo,
                                                            boolean roomPackage) {
        Map<String, String> params = new HashMap<>();
        params.put("anchorUid", String.valueOf(LiveRecordInfoManager.getInstance().getAnchorId()));
        params.put("roomId", String.valueOf(LiveRecordInfoManager.getInstance().getRoomId()));
        params.put("itemId", String.valueOf(itemId));
        params.put("amount", String.valueOf(num));
        if (expireAtTimestamp > 0) {
            params.put("expireAtTimestamp", String.valueOf(expireAtTimestamp));
        }
        params.put("roomPackage", String.valueOf(roomPackage));


        params.put(ParamsConstantsInLive.GAME_TYPE, LiveRecordInfoManager.getInstance().getPgcRecordMode());

        // 收礼用户从礼物面板获取
        HallGiftDialog dialog = getDialog();
        if (null != dialog) {
            List<GiftReceiverItem> selectedUsers = dialog.getSelectedUsers();
            boolean mIfNeedMicUidParam = dialog.getIfNeedMicUidParam();
            StringBuilder ids = new StringBuilder();
            if (mIfNeedMicUidParam && null != selectedUsers && 0 < selectedUsers.size()) {
                for (int i = 0; i < selectedUsers.size(); i++) {
                    ids.append(selectedUsers.get(i).uid);
                    if (i < selectedUsers.size() - 1) {
                        ids.append(",");
                    }
                }
            }
            params.put("micUids", ids.toString());
        }

        // 支持连击赠送
        if (repeat) {
            params.put("conseUnifiedNo", String.valueOf(conseUnifiedNo));
        }

        return params;
    }

    @Override
    protected HashMap<String, String> buildSendCommonGiftParams(int giftNum,
                                                                GiftInfoCombine.GiftInfo info,
                                                                long receiverId,
                                                                boolean isLiveGiftType,
                                                                boolean repeat,
                                                                long conseUnifiedNo) {
        HashMap<String, String> params = super.buildSendCommonGiftParams(giftNum, info,
                receiverId, isLiveGiftType, repeat, conseUnifiedNo);


        if (null != params) {
            params.put(ParamsConstantsInLive.OWNER_UID, LiveRecordInfoManager.getInstance().getAnchorId() + "");
            params.put(ParamsConstantsInLive.GAME_TYPE, LiveRecordInfoManager.getInstance().getPgcRecordMode());
        }

        // 收礼用户从礼物面板获取
        HallGiftDialog dialog = getDialog();
        if (null != dialog && null != params) {
            List<GiftReceiverItem> receiverItems = dialog.getSelectedUsers();
            StringBuilder selectedUsers = new StringBuilder();
            for (int i = 0; i < receiverItems.size(); i++) {
                GiftReceiverItem item = receiverItems.get(i);
                selectedUsers.append(item.uid);
                if (i < receiverItems.size() - 1) {
                    selectedUsers.append(",");
                }
            }
            params.put(ParamsConstantsInLive.RECEIVER_UID_LIST, selectedUsers.toString());
            params.remove(ParamsConstantsInLive.RECEIVER_UID);
        }

        return params;
    }
}
