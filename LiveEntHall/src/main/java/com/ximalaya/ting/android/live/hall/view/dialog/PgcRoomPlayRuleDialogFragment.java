package com.ximalaya.ting.android.live.hall.view.dialog;

import android.content.Context;
import android.os.Bundle;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentManager;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.host.fragment.BaseVerticalSlideContentFragment;
import com.ximalaya.ting.android.host.util.ui.VerticalSlideUtil;
import com.ximalaya.ting.android.host.view.OnEdgeListenerScrollView;
import com.ximalaya.ting.android.live.common.lib.utils.LiveBaseAttributeRecord;
import com.ximalaya.ting.android.live.common.lib.utils.LiveContextUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.hall.R;
import com.ximalaya.ting.android.live.hall.entity.RoomPlayRuleInfo;

/**
 * PGC 房间玩法说明弹窗
 */
public class PgcRoomPlayRuleDialogFragment extends BaseVerticalSlideContentFragment {

    private static VerticalSlideUtil.VerticalSlideWrapper<PgcRoomPlayRuleDialogFragment> mWrapper;

    public static void show(
            Context context, FragmentManager fragmentManager, RoomPlayRuleInfo roomPlayRuleInfo
    ) {
        if (roomPlayRuleInfo == null) {
            return;
        }

        context = LiveContextUtil.getContextWithDefault(context);

        if (mWrapper != null && mWrapper.isShowing()) {
            mWrapper.dismiss();
        }

        int height = BaseUtil.getScreenHeight(context) / 2;

        PgcRoomPlayRuleDialogFragment fragment = new PgcRoomPlayRuleDialogFragment();
        fragment.mRoomPlayRuleInfo = roomPlayRuleInfo;

        mWrapper = VerticalSlideUtil.buildSlideWrapper(fragment);
        mWrapper.setHeight(height).setShowSlideView(false);

        if (context.getResources() != null) {
            mWrapper.setBgDrawable(ResourcesCompat.getDrawable(
                    context.getResources(), com.ximalaya.ting.android.live.common.R.drawable.live_common_bg_white_top_corner_15, null
            ));
        }

        mWrapper.show(fragmentManager, "PgcRoomPlayRuleDialogFragment");
    }

    @Override
    protected String getPageLogicName() {
        return "PGC 房间玩法说明弹窗";
    }

    private TextView mContentTv;

    private RoomPlayRuleInfo mRoomPlayRuleInfo;

    @Override
    protected void initUi(Bundle savedInstanceState) {
        mContentTv = findViewById(R.id.live_tv_pgc_rule_content);

        OnEdgeListenerScrollView scrollView = findViewById(R.id.live_pgc_room_rule_scroll_view);
        bindSubScrollerView(scrollView);

        LiveBaseAttributeRecord.getInstance().bindPageData(this);
    }

    @Override
    protected void loadData() {
        if (mRoomPlayRuleInfo == null) {
            return;
        }

        UIStateUtil.safelySetText(mContentTv, mRoomPlayRuleInfo.content);
    }

    private void trackFavoriteClick() {
        if (mRoomPlayRuleInfo == null) {
            return;
        }

        String item = mRoomPlayRuleInfo.favorite ? "已收藏" : "未收藏";

        if (LiveBaseAttributeRecord.getInstance().hasBaseAttributeData()) {
            LiveBaseAttributeRecord.getInstance().getBaseTrace()
                    .click(15787)
                    .put("currPage", "fmMainScreen")
                    .put("Item", item)
                    .createTrace();
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.live_dialog_pgc_room_play_rule;
    }

    @Override
    public void onDestroyView() {
        if (mWrapper != null) {
            mWrapper.dismiss();
            mWrapper = null;
        }
        super.onDestroyView();
    }
}
