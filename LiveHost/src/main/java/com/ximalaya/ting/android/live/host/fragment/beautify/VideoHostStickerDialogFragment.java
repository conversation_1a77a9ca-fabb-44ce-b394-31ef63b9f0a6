package com.ximalaya.ting.android.live.host.fragment.beautify;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.widget.ViewPager2;

import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.util.OneClickHelper;
import com.ximalaya.ting.android.host.MainApplication;
import com.ximalaya.ting.android.host.activity.MainActivity;
import com.ximalaya.ting.android.host.fragment.other.BaseLoadDialogFragment;
import com.ximalaya.ting.android.host.live.video_beautify.BeautifySettingConstants;
import com.ximalaya.ting.android.host.live.video_beautify.BytesUtils;
import com.ximalaya.ting.android.host.live.video_beautify.IVideoBeautifyChangeCallback;
import com.ximalaya.ting.android.host.live.video_beautify.VideoLiveBeautifyCache;
import com.ximalaya.ting.android.host.model.nvs.MaterialInfo;
import com.ximalaya.ting.android.host.util.view.ViewStatusUtil;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.view.widget.LiveTabIndicator;
import com.ximalaya.ting.android.live.host.R;
import com.ximalaya.ting.android.live.host.adapter.IOnClickBeautifyItemCallback;
import com.ximalaya.ting.android.live.host.adapter.LiveStickerSubFragmentAdapter;
import com.ximalaya.ting.android.live.host.manager.aigift.AIGiftShowManager;
import com.ximalaya.ting.android.live.host.manager.beautify.VideoLiveBeautifyToolManager;
import com.ximalaya.ting.android.live.host.utils.LiveHostTraceUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;

/**
 * 视频直播主播端美颜  道具设置弹窗
 *
 * <AUTHOR>
 * @desc 文件描述
 * @email <EMAIL>
 * @wiki 说明文档的链接地址
 * @server 服务端开发人员放在这里
 * @since 2020-08-05 15:52
 */
public class VideoHostStickerDialogFragment extends BaseLoadDialogFragment implements IVideoBeautifyChangeCallback {

    public static final String TAG = VideoHostStickerDialogFragment.class.getCanonicalName();

    private Activity activity;

    //收藏显示区域
    private LinearLayout mLlStarCollect;
    private ImageView mIvShowCollectStar;
    private TextView mTvShowCollectStar;


    private LinearLayout mLlReset;

    private LiveTabIndicator mTabIndicator;

    private ViewPager2 mViewPager;

    private LiveStickerSubFragmentAdapter mFragmentAdapter;

    private VideoLiveBeautifyToolManager mBeautifyToolManager;



    public @interface IPageSource {
        /**
         * 主播端直播间打开美颜滤镜设置
         */
        int LIVE_ANCHOR_FRAGMENT = 0;
        /**
         * 创建直播页面打开美颜滤镜设置
         */
        int CREATE_XIMA_LIVE_FRAGMENT = 1;
    }

    @IPageSource
    private int mPageSource = IPageSource.LIVE_ANCHOR_FRAGMENT;


    private ILiveBeautifyPageShowCallback mPageShowCallback;

    public void setBeautifyPageShowCallback(ILiveBeautifyPageShowCallback callback) {
        this.mPageShowCallback = callback;
    }


    public static VideoHostStickerDialogFragment newInstance(@NonNull Context context) {
        VideoHostStickerDialogFragment fragment = new VideoHostStickerDialogFragment();
        Bundle bundle = new Bundle();
        fragment.setArguments(bundle);

        if (context instanceof MainActivity) {
            fragment.activity = (MainActivity) context;
        } else if (MainApplication.getTopActivity() instanceof MainActivity) {
            fragment.activity = MainApplication.getTopActivity();
        }

        return fragment;
    }

    public void setPageSource(@IPageSource int pageSource) {
        mPageSource = pageSource;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        parentNeedBg = false;
        setStyle(DialogFragment.STYLE_NO_TITLE, com.ximalaya.ting.android.live.common.R.style.live_more_action_dialog);
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.live_host_dialog_host_stickers_setting;
    }

    private boolean isInited = false;

    @Override
    protected void initUi(View view, Bundle savedInstanceState) {

        mBeautifyToolManager = VideoLiveBeautifyToolManager.getInstance();

        mLlStarCollect = (LinearLayout) findViewById(R.id.live_ll_star_show);
        mIvShowCollectStar = (ImageView) findViewById(R.id.live_iv_star_show);
        mTvShowCollectStar = (TextView) findViewById(R.id.live_tv_star_show);


        mLlReset = (LinearLayout) findViewById(R.id.live_ll_setting_reset);
        mTabIndicator = (LiveTabIndicator) findViewById(R.id.live_type_beautify_fuctions);
        mViewPager = (ViewPager2) findViewById(R.id.live_vp_beautify);

        mViewPager.setUserInputEnabled(false);


        initViewPagerShow();

        initListener();

        isInited = true;

    }

    @Override
    public void onResume() {
        super.onResume();

        VideoLiveBeautifyCache.getInstance().addBeautifyItemChangeListener(this);

        MaterialInfo currentSticker = VideoLiveBeautifyCache.getInstance().getCurrentSticker();

        boolean showingAiGift = AIGiftShowManager.getInstance().isShowing();

        //初始化美颜设置
        if (mBeautifyToolManager != null && !showingAiGift) {
            mBeautifyToolManager.setEffectSticker(currentSticker);
        }

        if (mPageShowCallback != null) {
            mPageShowCallback.onGetBeautifyShowChanged(true);
        }
    }

    @Override
    public void onPause() {
        VideoLiveBeautifyCache.getInstance().removeBeautifyItemChangeListener(this);

        if (mPageSource == IPageSource.LIVE_ANCHOR_FRAGMENT) {
            VideoLiveBeautifyCache.getInstance()
                    .reportLiveBeautifySetting(false, "使用道具",
                            LiveRecordInfoManager.getInstance().getLiveId());
        }

        if (mPageShowCallback != null) {
            mPageShowCallback.onGetBeautifyShowChanged(false);
        }

        super.onPause();
    }

    private void initListener() {
        //重置逻辑
        mLlReset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }

                if (getActivity() == null) {
                    return;
                }

                //AI礼物播放期间，不切换贴纸
                if (AIGiftShowManager.getInstance().isShowing()) {
                    CustomToast.showDebugFailToast("AI礼物播放期间，无法进行设置");
                    return;
                }

                // 取消所有道具使用
                if (mBeautifyToolManager != null) {
                    mBeautifyToolManager.setEffectSticker(null);
                }


            }
        });

    }

    private void initViewPagerShow() {
        mTabIndicator.setSelectedTextColor(Color.WHITE);
        mTabIndicator.setIndicatorColor(ContextCompat.getColor(getContext(), R.color.live_white));

        final String[] titles = new String[]{"已收藏", "热门"};
        mTabIndicator.setTitles(titles);
        mTabIndicator.resetTitlesColor();
        mTabIndicator.setVisibility(View.VISIBLE);
        mTabIndicator.setHideIndicator(false);
        mTabIndicator.setCurrentPosition(0, false);

        mTabIndicator.setOnTabClickListener(new LiveTabIndicator.ITabClickListener() {
            @Override
            public void onTabClicked(int lastPosition, int clickPosition) {
                if (clickPosition >= 0 && clickPosition < titles.length) {
                    if (mViewPager != null) {
                        mViewPager.setCurrentItem(clickPosition);
                    }

                }


            }
        });



        mFragmentAdapter = new LiveStickerSubFragmentAdapter((FragmentActivity) mActivity, 2,
                true, new IOnClickBeautifyItemCallback() {
            @Override
            public void onClickSettingItem(int buzType, MaterialInfo info) {
                //TODO
                //TODO
                //TODO
            }

            @Override
            public void onSwitchChanged(int buzType, boolean isOpen) {

            }
        });

        mFragmentAdapter.setPageSource(mPageSource);

        mViewPager.setAdapter(mFragmentAdapter);

        mViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);

                mTabIndicator.setCurrentPosition(position, true);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
            }
        });

        if (!isInited) {
            mTabIndicator.setCurrentPosition(0, true);
            mViewPager.setCurrentItem(0);
        }


        //收藏操作
        mLlStarCollect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(!OneClickHelper.getInstance().onClick(v)) {
                    return;
                }

                clickCurrentSticker();

                if (mPageSource == VideoHostBeautifyDialogFragment.IPageSource.LIVE_ANCHOR_FRAGMENT) {
                    LiveHostTraceUtil.makeDialogClickTrack(33574);
                }
            }
        });
    }

    private void clickCurrentSticker() {

        MaterialInfo currentSticker = VideoLiveBeautifyCache.getInstance().getCurrentSticker();
        if (currentSticker == null) {
            return;
        }

        BytesUtils.CollectOrCancelSticker(currentSticker.id, !currentSticker.hasStar, new IDataCallBack<String>() {
            @Override
            public void onSuccess(@Nullable String object) {
                currentSticker.hasStar = !currentSticker.hasStar;

                VideoLiveBeautifyToolManager.getInstance().setEffectSticker(currentSticker);

            }

            @Override
            public void onError(int code, String message) {

                CustomToast.showFailToast("服务异常！" + message);

            }
        });

    }


    @Override
    public void onStart() {
        Window window = getDialog().getWindow();
        if (null != window) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.0f;

            boolean isLand = ViewStatusUtil.isLandScreenOrientation(activity);

            if (isLand) {
                params.height = WindowManager.LayoutParams.MATCH_PARENT;
                params.width = BaseUtil.dp2px(getContext(), 232);
                params.gravity = Gravity.RIGHT;
                params.windowAnimations = com.ximalaya.ting.android.host.R.style.host_popup_window_from_right_animation;

            } else {
                params.width = WindowManager.LayoutParams.MATCH_PARENT;
                params.gravity = Gravity.BOTTOM;
                params.windowAnimations = com.ximalaya.ting.android.host.R.style.host_popup_window_from_bottom_animation;

            }
            window.setAttributes(params);
        }

        getDialog().getWindow().setBackgroundDrawableResource(R.color.live_transparent_00000000);

        getDialog().getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN);

        super.onStart();

        getDialog().getWindow().getDecorView().setSystemUiVisibility(activity.getWindow().getDecorView().getSystemUiVisibility());
        getDialog().getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
    }

    @Override
    protected void loadData() {

    }


    @Override
    public void dismiss() {
        super.dismiss();
    }

    @Override
    public void dismissAllowingStateLoss() {
        super.dismissAllowingStateLoss();
    }


    @Override
    public void onDestroyView() {
        VideoLiveBeautifyCache.getInstance().removeBeautifyItemChangeListener(this);
        super.onDestroyView();
    }

    @Override
    public void onBeautifyItemChanged(@NonNull String type, MaterialInfo info) {

        if (BeautifySettingConstants.TYPE_STICKER.equals(type)) {
            showStickerStar();
        }
    }

    public void showStickerStar() {
        if (!canUpdateUi()) {
            return;
        }

        MaterialInfo currentSticker = VideoLiveBeautifyCache.getInstance().getCurrentSticker();

        if (currentSticker == null) {

            mLlStarCollect.setVisibility(View.INVISIBLE);

        } else {

            mLlStarCollect.setVisibility(View.VISIBLE);

            mTvShowCollectStar.setText(currentSticker.hasStar ? "已收藏" : "收藏");
            mIvShowCollectStar.setImageResource(currentSticker.hasStar ?
                    R.drawable.live_icon_collect_star : R.drawable.live_icon_collect_unstar);

        }

    }







    private void makeDialogShowTrack(int metaId) {
        new XMTraceApi.Trace()
                .setMetaId(metaId)
                .setServiceId("dialogView")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    protected void makeDialogClickTrack(int metaId) {
        new XMTraceApi.Trace()
                .click(metaId)
                .setServiceId("dialogClick")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    protected void makeDialogClickTrack(int metaId, String name) {
        new XMTraceApi.Trace()
                .click(metaId)
                .setServiceId("dialogClick")
                .put("Item", name)
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }
}
