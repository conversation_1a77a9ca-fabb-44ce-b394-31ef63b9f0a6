<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android">
    <!--消失-->
    <objectAnimator
        android:duration="0"
        android:propertyName="alpha"
        android:valueFrom="1.0"
        android:valueTo="0.0"/>

    <!--旋转-->
    <objectAnimator
        android:duration="300"
        android:propertyName="rotationY"
        android:valueFrom="-180"
        android:valueTo="0"/>

    <!--出现-->
    <objectAnimator
        android:duration="0"
        android:propertyName="alpha"
        android:startOffset="100"
        android:valueFrom="0.0"
        android:valueTo="1.0"/>

    <objectAnimator
        android:duration="150"
        android:startOffset="100"
        android:propertyName="scale"
        android:valueFrom="0.1"
        android:valueTo="1"
        />

    <!--消失-->
    <objectAnimator
        android:duration="300"
        android:startOffset="1400"
        android:propertyName="alpha"
        android:valueFrom="1.0"
        android:valueTo="0.0"/>
</set>