<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/live_container_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/livehost_audio_play_follow_bg"
        app:layout_constraintEnd_toEndOf="parent"
        tools:layout_width="64dp"
        tools:layout_height="30dp">

        <FrameLayout
            android:id="@+id/live_video_fans_fl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:paddingStart="3dp"
            android:paddingTop="2dp"
            android:paddingEnd="3dp"
            android:paddingBottom="1dp"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/live_anchor_fans_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:layout_gravity="center"
                android:src="@drawable/livehost_icon_follow_love"
                android:contentDescription="加入粉丝团" />

            <ImageView
                android:id="@+id/live_anchor_fans_iv_add"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:contentDescription="加入粉丝团"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:src="@drawable/live_icon_follow_love_add" />

        </FrameLayout>

        <TextView
            android:id="@+id/live_follow_tv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="center"
            android:minWidth="35dp"
            android:paddingStart="4dp"
            android:paddingTop="1dp"
            android:paddingEnd="4dp"
            android:includeFontPadding="false"
            android:paddingBottom="1dp"
            android:text="@string/live_btn_follow_text"
            android:textColor="@color/livecomm_color_ffffff"
            android:textSize="11sp"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/live_fans_ok"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0"
            android:visibility="invisible"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="center"
            android:src="@drawable/live_icon_follow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <RelativeLayout
        android:id="@+id/live_rl_fans_grade"
        android:layout_width="20dp"
        android:layout_height="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/livehost_icon_follow_love"
        android:visibility="gone"
        android:gravity="center"
        tools:visibility="visible">

        <TextView
            android:id="@+id/live_tv_fans_grade"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="#DF613E"
            android:textSize="9sp"
            tools:text="11" />

    </RelativeLayout>

</merge>