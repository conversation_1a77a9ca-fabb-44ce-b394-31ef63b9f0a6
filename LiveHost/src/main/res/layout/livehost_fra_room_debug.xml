<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/live_room_debug_root"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:visibility="gone"
            android:layout_marginTop="10dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:background="@color/host_color_333333"
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content">

            <TextView
                android:textColor="@color/host_color_cccccc"
                android:layout_marginStart="5dp"
                android:textSize="8dp"
                android:layout_gravity="center_vertical"
                android:text="LiveDebugFragment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:id="@+id/live_room_top_menu"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:gravity="center_vertical|end"
                android:orientation="horizontal" >
            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:visibility="gone"
            android:layout_marginTop="10dp"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:id="@+id/live_room_debug_info_parent"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:background="@color/host_color_333333">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/live_debug_rv"
                android:layout_width="match_parent"
                android:layout_marginEnd="70dp"
                android:layout_height="match_parent"/>
            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/live_debug_room_avatar"
                app:round_background="true"
                app:corner_radius="100dp"
                android:layout_marginEnd="10dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_width="50dp"
                android:layout_height="50dp"/>
        </RelativeLayout>


        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/live_debug_tab"
                android:layout_width="match_parent"
                android:layout_height="30dp"/>
        </HorizontalScrollView>
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/live_debug_vp"
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="0dp"/>
    </LinearLayout>
</LinearLayout>