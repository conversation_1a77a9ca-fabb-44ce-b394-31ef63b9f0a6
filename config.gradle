ext {
    xmDependencies = [
            paletteV7         :'com.android.support:palette-v7:26.1.0',
            supportV4         : 'com.android.support:support-v4:26.1.0',
            supportAnnotations: 'com.android.support:support-annotations:27.1.1',
            supportAppCompatV7: 'com.android.support:appcompat-v7:26.1.0',
            recyclerView: 'com.android.support:recyclerview-v7:26.1.0',
            gson: 'com.google.code.gson:gson:2.6.1',
            fastJson: 'com.alibaba:fastjson:1.2.54',
            junit: 'junit:junit:4.12',
            glide: 'com.github.bumptech.glide:glide:3.7.0',
            multiDex: 'com.android.support:multidex:1.0.1',
            okHttp: 'com.squareup.okhttp3:okhttp:3.8.1',
            converterGson: 'com.squareup.retrofit2:converter-gson:2.0.0',
            wechatSdkVersion  : 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:1.4.0',
            okHttpUrlConnect : 'com.squareup.okhttp3:okhttp-urlconnection:3.8.1'
    ]
    //编译控制参数
    /**

           ALL_COMPILE_SRC  是否生产全量包
        3. BUILD_PLUGIN_APK  是否生产包含插件的apk

     */
    javaCompileVersion = JavaVersion.VERSION_1_8


    versionCode = "135"
    versionName = "6.3.72.3.dev"
    compileSdkVersion = 26
    buildToolsVersion = "25.0.2"
    minSdkVersion = "16"
    targetSdkVersion = "23"

    openBlockCanary = "false"  //是否开启BlockCanary
    generate_public_xml = false
    //extChannels = "and-d3"//不能命名test/debug等字段开头
    channel = "ceshi"// 开发时渠道改为 ceshi
    needChannel = "false"  //生成的apk中是否需要加入渠道信息
    modifyApplicationName = "false"  //是否需要修改应用名称
    applicationName = "喜马拉雅"
    //pChannels shell 输入参数
    shallReadChannelfromFile = "false"
    isProguard = "false"
    isReleaseDebug = "true"
    isDebugGradle = "true"  //控制是否输出gradle 调试信息
}


if(rootProject.isDebugGradle.toBoolean()){
    println "=====================set ext property start====================="
}
if (project.hasProperty('pChannels')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pChannels")
    }
    rootProject.channel = project.property('pChannels')
}

if (project.hasProperty('pVersionCode')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pVersionCode")
    }
    rootProject.versionCode = project.property('pVersionCode')
}

if (project.hasProperty('pVersionName')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pVersionName")
    }
    rootProject.versionName = project.property('pVersionName')
}

if (project.hasProperty('pIsProguard')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pIsProguard")
    }
    rootProject.isProguard = project.property('pIsProguard')
}

if (project.hasProperty('pIsReleaseDebug')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pIsReleaseDebug")
    }
    rootProject.isReleaseDebug = project.property('pIsReleaseDebug')
}

if (project.hasProperty('pNeedChannel')) {
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pNeedChannel")
    }
    rootProject.needChannel = project.property('pNeedChannel')
}

if(project.hasProperty('pModifyApplicationName')){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pModifyApplicationName")
    }
    rootProject.modifyApplicationName = project.property('pModifyApplicationName')
}

if(project.hasProperty('pApplicationName')){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pApplicationName")
    }
    rootProject.applicationName = project.property('pApplicationName')
}

if(project.hasProperty("pOpenBlockCanary")){
    if(rootProject.isDebugGradle.toBoolean()){
        println("set pOpenBlockCanary")
    }
    rootProject.openBlockCanary = project.property('pOpenBlockCanary')
}

if(rootProject.isDebugGradle.toBoolean()) {
    println "=====================set ext property end====================="
}

task printExtProperties doLast{
    println "============================"
    println "channel: " + channel
    println "versionCode: " + versionCode
    println "versionName: " + versionName
    println "isProguard: " + isProguard
    println "isReleaseDebug: " + isReleaseDebug
    println "needChannel: " + needChannel
    println "modifyApplicationName: " + modifyApplicationName
    println "applicationName: " + applicationName
    println "openBlockCanary: " + openBlockCanary
    println "============================"
}
