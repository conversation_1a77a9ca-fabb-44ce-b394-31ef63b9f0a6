<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="12dp"
    android:paddingVertical="12dp">

    <ImageView
        android:id="@+id/record_iv_content_like"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="20dp"
        android:src="@drawable/record_ic_broker_like"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/record_iv_content_unlike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/record_ic_broker_unlike"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/record_iv_content_like"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>