<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:paddingVertical="8dp">


    <TextView
        android:id="@+id/record_tv_top_date_time"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:text="7.21"
        android:textColor="@color/record_color_99131313"
        android:textSize="32sp"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/record_tv_top_date_line"
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        android:layout_marginVertical="12dp"
        android:layout_marginStart="8dp"
        android:background="@color/host_color_131313"
        app:layout_constraintBottom_toBottomOf="@+id/record_tv_top_date_time"
        app:layout_constraintStart_toEndOf="@+id/record_tv_top_date_time"
        app:layout_constraintTop_toTopOf="@+id/record_tv_top_date_time" />


    <TextView
        android:id="@+id/record_tv_top_date_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="周一\n2024"
        android:textColor="@color/record_color_66131313"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_top_date_time"
        app:layout_constraintStart_toEndOf="@+id/record_tv_top_date_line"
        app:layout_constraintTop_toTopOf="@+id/record_tv_top_date_time" />


    <TextView
        android:id="@+id/record_tv_top_date_mood"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="早上好"
        android:textColor="@color/record_color_99131313"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_top_date_time"
        app:layout_constraintStart_toEndOf="@+id/record_tv_top_date_date"
        app:layout_constraintTop_toTopOf="@+id/record_tv_top_date_time" />


    <ImageView
        android:id="@+id/record_iv_top_date_mood"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="4dp"
        android:layout_marginEnd="5dp"
        android:paddingHorizontal="2dp"
        android:paddingVertical="3dp"
        android:src="@drawable/record_icon_ugc_love_mood"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/record_tv_top_date_time"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/record_tv_top_date_time" />

    <View
        android:id="@+id/record_iv_top_mood_message"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/record_bg_f0f0f0_oval"
        android:backgroundTint="#FF4B61"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/record_iv_top_date_mood"
        app:layout_constraintTop_toTopOf="@+id/record_iv_top_date_mood" />

</androidx.constraintlayout.widget.ConstraintLayout>