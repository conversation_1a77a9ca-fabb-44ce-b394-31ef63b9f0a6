<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="20dp"
    android:paddingLeft="24dp"
    android:paddingRight="24dp"
    android:background="@drawable/main_myspace_bg_shadow_middle_new">

        <androidx.cardview.widget.CardView
            android:id="@+id/record_video_container"
            android:layout_width="116dp"
            android:layout_height="65dp"
            android:foreground="@drawable/record_bg_00000000_12000000_selector"
            app:cardCornerRadius="4dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/record_video_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/host_default_focus_img" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/record_bg_4radius_e5e5e5" />

            <ImageView
                android:id="@+id/record_video_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:scaleType="centerInside"
                android:src="@drawable/host_album_tag_pay"
                android:visibility="invisible"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/record_video_play"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|bottom"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="5dp"
                android:src="@drawable/host_search_btn_list_play" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/record_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            tools:text="我是视频名称我是视频名称我是视频名称我是视频名称我是视频名称我是视频名称"
            android:textSize="14sp"
            android:textColor="@color/host_color_333333_cfcfcf"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_toRightOf="@+id/record_video_container"
            android:layout_toLeftOf="@+id/record_update_time_tv"
            android:layout_marginRight="15dp"
            android:layout_marginLeft="10dp" />

        <TextView
            android:id="@+id/record_update_time_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3天前更新"
            android:textSize="11sp"
            android:textColor="@color/host_color_999999"
            android:layout_alignParentRight="true" />

        <TextView
            android:id="@+id/record_status_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/record_name_tv"
            android:layout_alignLeft="@id/record_name_tv"
            android:layout_gravity="center"
            android:layout_marginTop="7dp"
            android:gravity="center_horizontal"
            android:includeFontPadding="false"
            android:text="审核中"
            android:textColor="@color/record_color_f5a623"
            android:textSize="12sp" />

        <Space
            android:id="@+id/record_status_space"
            android:layout_width="18dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true" />

        <TextView
            android:id="@+id/record_status_desc_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/record_status_tv"
            android:layout_toLeftOf="@id/record_status_space"
            android:padding="2dp"
            android:text="展开"
            android:visibility="invisible"
            android:textColor="@color/record_blue_4990E2"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/record_status_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@id/record_status_tv"
            android:layout_toLeftOf="@id/record_status_desc_more"
            android:layout_toRightOf="@id/record_status_tv"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingLeft="7dp"
            android:textColor="@color/host_color_999999_888888"
            android:textSize="11sp"
            android:visibility="invisible"
            tools:text="一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十" />

        <TextView
            android:id="@+id/record_tv_offline_reason"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7dp"
            android:layout_alignBaseline="@id/record_status_tv"
            android:layout_toRightOf="@id/record_status_tv"
            android:drawableEnd="@drawable/record_ic_offline_next"
            android:drawablePadding="4dp"
            android:padding="5dp"
            android:singleLine="true"
            android:text="查看详情"
            android:textColor="@color/record_color_F5222D"
            android:textSize="12sp"
            android:visibility="invisible" />

        <LinearLayout
            android:id="@+id/record_metion_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/record_status_tv"
            android:layout_marginTop="11dp"
            android:orientation="horizontal"
            android:layout_alignStart="@+id/record_name_tv">

            <TextView
                android:id="@+id/record_duration_tv"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:gravity="center_vertical|left"
                android:drawableLeft="@drawable/host_search_ic_info_time"
                android:drawablePadding="4dp"
                android:includeFontPadding="false"
                android:paddingRight="12dp"
                android:textSize="10sp"
                android:textColor="@color/host_color_bbbbbb_888888"
                android:text="00:32:21" />

            <TextView
                android:id="@+id/record_play_count"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:gravity="center_vertical|left"
                android:includeFontPadding="false"
                android:drawableLeft="@drawable/record_ic_video_play_count"
                android:drawablePadding="4dp"
                android:text="6868.2万"
                android:textSize="10sp"
                android:textColor="@color/host_color_bbbbbb_888888" />

            <TextView
                android:id="@+id/record_split_option"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="12dp"
                android:visibility="gone"
                android:textSize="10sp"
                android:textColor="@color/host_color_bbbbbb_888888"
                android:text="|" />

            <TextView
                android:id="@+id/record_zhuancai_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.75"
                android:visibility="gone"
                android:text="本条转采自：xxxxx"
                android:textSize="10sp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="@color/host_color_bbbbbb_888888" />

        </LinearLayout>

        <View
            android:id="@+id/record_split_v"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/record_color_ededed_353535"
            android:layout_alignStart="@+id/record_name_tv"
            android:layout_below="@id/record_metion_ll"
            android:layout_marginTop="5dp"/>

        <TextView
            android:id="@+id/record_upload_status_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/record_video_container"
            android:layout_alignStart="@+id/record_video_container"
            android:layout_marginLeft="4dp"
            android:layout_marginTop="16dp"
            android:includeFontPadding="false"
            android:text="审核中"
            android:textSize="12sp"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:visibility="invisible"
            tools:visibility="visible"
            android:textColor="@color/record_color_f5a623" />

        <TextView
            android:id="@+id/record_upload_progress_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="99%"
            android:textColor="@color/record_color_f86442"
            android:textSize="12sp"
            android:layout_marginLeft="3dp"
            android:layout_toRightOf="@+id/record_upload_status_tv"
            android:layout_alignBaseline="@+id/record_upload_status_tv"
            android:visibility="invisible"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/record_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="8dp"
            android:layout_below="@+id/record_split_v"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/record_share_tv"
                android:layout_width="60dp"
                android:layout_height="28dp"
                android:layout_toLeftOf="@+id/record_more_tv"
                android:layout_below="@+id/record_split_v"
                android:background="@drawable/record_bg_14corner_e6e6e6"
                android:text="分享"
                android:textSize="14sp"
                android:textColor="@color/host_color_666666_cfcfcf"
                android:gravity="center" />

            <TextView
                android:id="@+id/record_edit_tv"
                android:layout_width="60dp"
                android:layout_height="28dp"
                android:layout_marginLeft="6dp"
                android:background="@drawable/record_bg_14corner_e6e6e6"
                android:text="编辑"
                android:textSize="14sp"
                android:textColor="@color/host_color_666666_cfcfcf"
                android:gravity="center" />

            <TextView
                android:id="@+id/record_publish_tv"
                android:layout_width="60dp"
                android:layout_height="28dp"
                android:layout_marginLeft="6dp"
                android:background="@drawable/record_bg_video_publish"
                android:visibility="gone"
                tools:visibility="visible"
                android:text="发布"
                android:textSize="14sp"
                android:textColor="@color/host_color_ffffff"
                android:gravity="center" />

            <TextView
                android:id="@+id/record_more_tv"
                android:layout_height="27dp"
                android:layout_width="wrap_content"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:layout_marginLeft="6dp"
                android:contentDescription="@string/record_more"
                android:background="@drawable/record_bg_14corner_e6e6e6"
                android:drawableStart="@drawable/record_ic_more_actions"
                android:textColor="@color/host_color_666666_cfcfcf"
                android:gravity="center" />
        </LinearLayout>

        <ProgressBar
            android:id="@+id/record_upload_pb"
            style="?android:attr/progressBarStyleHorizontal"
            android:visibility="invisible"
            tools:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:layout_below="@+id/record_option"
            android:layout_marginTop="5dp"
            android:progress="50"
            android:progressDrawable="@drawable/record_progress_bar_bg" />

</RelativeLayout>