<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/framework_color_ffffff_111111">

    <RelativeLayout
        android:id="@+id/record_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true" />

    <TextView
        android:id="@+id/record_tv_time_range"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/record_title_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:textColor="@color/host_color_666666_888888"
        android:textSize="32sp"
        tools:text="00:11-00:14" />

    <TextView
        android:id="@+id/record_tv_edit_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/record_tv_time_range"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="5dp"
        android:text="编辑区域为选中时间至声音末尾"
        android:textColor="@color/host_color_999999_888888"
        android:textSize="14sp" />


    <View
        android:id="@+id/record_v_tips_anchor"
        android:layout_width="1dp"
        android:layout_height="120dp"
        android:layout_alignTop="@+id/record_audio_wave_view" />

    <com.ximalaya.ting.android.record.view.waveview.AudioWaveView
        android:id="@+id/record_audio_wave_view"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_below="@+id/record_tv_edit_tips"
        android:layout_marginTop="18dp"
        android:clickable="true"
        app:record_showMode="1" />

    <TextView
        android:id="@+id/record_tv_switch_cut"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/record_audio_wave_view"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="43dp"
        android:background="@drawable/record_rect_corner8_f6f7f8"
        android:backgroundTint="@color/host_color_f0f0f0"
        android:drawableLeft="@drawable/record_ic_cut_ugc"
        android:drawablePadding="4dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:text="剪辑"
        android:textColor="@color/host_color_111111_cfcfcf"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/record_tv_is_listening"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/record_ll_control_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="4dp"
        android:text="正在试听：00:11-00:14"
        android:textColor="@color/host_color_999999_888888"
        android:textSize="14sp" />

    <RelativeLayout
        android:id="@+id/record_ll_control_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/record_tv_duration"
        android:layout_alignParentLeft="true">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/record_ll_center"
            android:gravity="center">

            <LinearLayout
                android:id="@+id/record_ll_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/record_bg_rect_1aff7a80_corner_8"
                android:gravity="center"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp">

                <ImageView
                    android:id="@+id/record_iv_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/record_ic_ugc_reset" />

                <TextView
                    android:id="@+id/record_tv_btn_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="重录"
                    app:layout_constraintBottom_toBottomOf="@id/record_iv_record"
                    app:layout_constraintEnd_toStartOf="@id/record_iv_record"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/record_iv_record" />
            </LinearLayout>

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/record_ll_center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/record_iv_center"
                android:layout_width="64dp"
                android:layout_height="match_parent"
                android:background="@drawable/record_rect_corner40_gradient_ff4840_f86442"
                android:padding="20dp"
                android:src="@drawable/record_ic_ugc_play"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </LinearLayout>
        <!--<TextView
            android:id="@+id/record_tv_btn_preview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/record_ic_tool_listen_play"
            android:drawablePadding="1dp"
            android:gravity="center"
            android:text="试听"
            android:textColor="@color/host_color_999999_888888"
            android:textSize="13sp" />-->

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/record_ll_center"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/record_ll_center"
            android:gravity="center">

            <LinearLayout
                android:id="@+id/record_ll_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/record_bg_rect_1aff7a80_corner_8"
                android:gravity="center"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp">

                <ImageView
                    android:id="@+id/record_iv_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/record_ic_ugc_done" />

                <TextView
                    android:id="@+id/record_tv_btn_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="确认"
                    app:layout_constraintBottom_toBottomOf="@id/record_iv_record"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/record_iv_record"
                    app:layout_constraintTop_toTopOf="@id/record_iv_record" />

            </LinearLayout>

        </RelativeLayout>
    </RelativeLayout>

    <TextView
        android:id="@+id/record_tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="23dp"
        android:textColor="#9980AC96"
        android:textSize="12sp"
        tools:text="00:34" />


</RelativeLayout>