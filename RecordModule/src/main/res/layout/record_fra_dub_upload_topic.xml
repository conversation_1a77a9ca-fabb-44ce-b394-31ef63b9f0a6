<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/host_color_ffffff_111111"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/record_title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/host_title_bar_height" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:layout_marginTop="24dp">

        <EditText
            android:id="@+id/record_et_dub_intro"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="137dp"
            android:background="@null"
            android:gravity="start"
            android:hint="完成了一个作品，不说点啥嘛~"
            android:maxLines="2"
            android:enabled="false"
            android:textAlignment="viewStart"
            android:textColor="@color/host_color_999999_888888"
            android:textColorHighlight="@color/host_color_111111_cfcfcf"
            android:textColorHint="@color/record_color_cacaca_1e1e1e"
            android:textSize="15sp" />

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/record_iv_dub_cover"
            android:layout_width="120dp"
            android:layout_height="67.5dp"
            android:layout_gravity="right|center_vertical"
            android:contentDescription="@string/record_cover"
            android:scaleType="centerCrop"
            app:corner_radius="5dp" />

        <TextView
            android:id="@+id/record_tv_select_dub_cover"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|bottom"
            android:background="@drawable/record_bg_00000000_ff000000_ltr"
            android:drawableLeft="@drawable/record_ic_select_cover"
            android:drawablePadding="4dp"
            android:paddingBottom="3dp"
            android:paddingLeft="@dimen/record_margin_6"
            android:paddingRight="@dimen/record_margin_6"
            android:paddingTop="3dp"
            android:text="选择封面"
            android:textColor="@color/host_white"
            android:textSize="12sp" />
    </FrameLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:background="@color/record_color_ececec_1e1e1e" />

    <com.ximalaya.ting.android.record.view.dub.DubDialectCombinationView
        android:id="@+id/record_dialect_combination_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <View
        android:id="@+id/record_view_sync_to_community"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginBottom="17dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:layout_marginTop="17dp"
        android:background="@color/record_color_ececec_1e1e1e"
        android:visibility="gone"
        tools:visibility="visible" />

    <RelativeLayout
        android:id="@+id/record_ll_post_to_community"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="同步到社团"
            android:textSize="15sp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:drawableLeft="@drawable/record_ic_upload_post_to_community"
            android:drawablePadding="11dp"
            android:textColor="@color/host_color_111111_cfcfcf"
            android:layout_marginLeft="14dp" />

        <CheckBox
            android:id="@+id/record_switch_post_to_community"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="14dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:checked="false"
            android:background="@drawable/host_switch_selector"
            android:button="@null" />
    </RelativeLayout>

    <View
        android:id="@+id/record_view_change_public"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginBottom="17dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        android:layout_marginTop="17dp"
        android:background="@color/record_color_ececec_1e1e1e"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/record_ll_change_public"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/record_iv_change_public"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="15dp"
            android:src="@drawable/record_ic_xuanzhe" />

        <TextView
            android:id="@+id/record_tv_change_public"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:paddingLeft="11dp"
            android:text="推荐给其他用户与我合作"
            android:textColor="@color/host_color_999999_888888"
            android:textSize="14sp" />
    </LinearLayout>

    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp">

        <TextView
            android:id="@+id/record_btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="14dp"
            android:layout_marginRight="24dp"
            android:drawablePadding="2dp"
            android:drawableTop="@drawable/record_ic_topic_save_draft"
            android:text="存草稿"
            android:textColor="@color/host_color_666666_888888"
            android:textSize="12sp" />

        <Button
            android:id="@+id/record_btn_upload"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginRight="28dp"
            android:layout_weight="1"
            android:background="@drawable/record_topic_upload_bg"
            android:text="发布"
            android:textColor="@color/host_c_white"
            android:textSize="15sp" />
    </LinearLayout>
</LinearLayout>