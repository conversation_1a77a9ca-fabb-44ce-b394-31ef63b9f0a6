<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingBottom="12dp"
    tools:background="@color/record_color_000_131313">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/record_iv_avatar"
        android:layout_width="33dp"
        android:layout_height="33dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1"
        tools:src="@drawable/host_default_avatar_132" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_cl_record"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="60dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/record_bg_interactive_ai_record_item_play"
        app:layout_constraintTop_toBottomOf="@id/record_iv_avatar">

        <TextView
            android:id="@+id/record_tv_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:drawableStart="@drawable/record_ic_interactive_ai_parsing"
            android:drawablePadding="4dp"
            android:text="音频解析中"
            android:textColor="@color/host_color_ffffff"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/record_v_line" />

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/record_lav_loading_l"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:scaleType="centerInside"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/record_tv_loading"
            app:layout_constraintEnd_toStartOf="@id/record_tv_loading"
            app:layout_constraintTop_toTopOf="@id/record_tv_loading"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/interactive/asr_loading_white.json"
            app:lottie_loop="true" />

        <com.ximalaya.ting.android.host.view.XmLottieAnimationView
            android:id="@+id/record_lav_loading_r"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="8dp"
            android:scaleType="centerInside"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/record_tv_loading"
            app:layout_constraintStart_toEndOf="@id/record_tv_loading"
            app:layout_constraintTop_toTopOf="@id/record_tv_loading"
            app:lottie_autoPlay="true"
            app:lottie_fileName="lottie/interactive/asr_loading_white.json"
            app:lottie_loop="true" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/record_g_asr_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="record_tv_loading, record_lav_loading_l, record_lav_loading_r" />

        <TextView
            android:id="@+id/record_tv_txt_content_single"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="12dp"
            android:paddingEnd="35dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/record_color_ffffff"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/record_v_line"
            tools:text="大家好，欢迎收听xxx的播客节目。我是的" />

        <TextView
            android:id="@+id/record_tv_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/record_ic_interactive_down_arrow"
            android:drawablePadding="2dp"
            android:textColor="@color/record_color_ffffff"
            android:textSize="12sp"
            android:text="展开"
            app:layout_constraintEnd_toEndOf="@id/record_tv_txt_content_single"
            app:layout_constraintTop_toTopOf="@id/record_tv_txt_content_single"
            app:layout_constraintBottom_toBottomOf="@id/record_tv_txt_content_single"/>

        <ScrollView
            android:id="@+id/record_sv_txt_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintHeight_max="100dp"
            android:paddingBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/record_v_line">

            <TextView
                android:id="@+id/record_tv_txt_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="12dp"
                android:ellipsize="end"
                android:textColor="@color/record_color_ffffff"
                android:textSize="12sp"
                android:paddingBottom="12dp"
                tools:text="大家好，欢迎收听xxx的播客节目。" />
        </ScrollView>

        <ImageView
            android:id="@+id/record_iv_play_record"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:paddingBottom="14dp"
            android:src="@drawable/record_drawable_interactive_ai_play"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <SeekBar
            android:id="@+id/record_sb_play"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="18dp"
            android:max="100"
            android:maxHeight="4dp"
            android:minHeight="4dp"
            android:paddingLeft="0dp"
            android:paddingRight="0dp"
            android:progress="50"
            android:progressDrawable="@drawable/record_drawable_interactive_seekbar_progress"
            android:splitTrack="false"
            android:thumb="@drawable/record_seek_bar_thumb_9df960"
            android:thumbOffset="0dp"
            app:layout_constraintBottom_toTopOf="@+id/record_tv_cur_time"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/record_iv_play_record"
            app:layout_constraintTop_toTopOf="@id/record_iv_play_record"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/record_tv_cur_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="00:00"
            android:textColor="@color/record_color_ffffff"
            android:textSize="11sp"
            app:layout_constraintBottom_toBottomOf="@id/record_iv_play_record"
            app:layout_constraintStart_toStartOf="@id/record_sb_play"
            app:layout_constraintTop_toBottomOf="@id/record_sb_play" />

        <TextView
            android:id="@+id/record_tv_end_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/record_color_ffffff"
            android:textSize="11sp"
            app:layout_constraintEnd_toEndOf="@id/record_sb_play"
            app:layout_constraintTop_toTopOf="@id/record_tv_cur_time"
            tools:text="09:20" />

        <View
            android:id="@+id/record_v_line"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_marginTop="10dp"
            android:background="@color/record_color_ffffff_321652"
            android:alpha="0.15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/record_tv_cur_time" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="12dp"
            app:layout_constraintTop_toBottomOf="@id/record_tv_txt_content_single"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.google.android.material.button.MaterialButton
        android:id="@+id/record_btn_re_record"
        style="@style/host_material_button_basic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:backgroundTint="@color/record_color_e5e5e5_1f2229"
        android:drawableLeft="@drawable/record_ic_interactive_refresh_bg"
        android:drawablePadding="4dp"
        android:elevation="0dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingTop="8dp"
        android:paddingRight="10dp"
        android:paddingBottom="8dp"
        android:stateListAnimator="@null"
        android:text="重新录制"
        android:textColor="@color/record_color_000000_805ef1"
        android:textSize="12sp"
        app:cornerRadius="100dp"
        app:elevation="0dp"
        app:layout_constraintEnd_toEndOf="@id/record_cl_record"
        app:layout_constraintTop_toBottomOf="@id/record_cl_record" />
</androidx.constraintlayout.widget.ConstraintLayout>