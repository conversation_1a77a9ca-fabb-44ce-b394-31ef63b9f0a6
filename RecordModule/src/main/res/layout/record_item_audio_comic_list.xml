<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="104dp"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/record_item_audio_comic_cover_card"
        android:layout_width="match_parent"
        android:layout_height="146dp"
        android:background="@drawable/record_bg_stroke_e8e8e8_4corner"
        app:cardCornerRadius="4dp"
        app:contentPadding="0.5dp"
        app:cardElevation="0dp">

        <com.ximalaya.ting.android.framework.view.image.RoundImageView
            android:id="@+id/record_item_audio_comic_cover_img"
            android:layout_width="match_parent"
            android:layout_height="146dp"
            android:scaleType="centerCrop"
            app:corner_radius="4dp"
            tools:src="@drawable/host_default_album" />

        <View
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_gravity="bottom"
            android:background="@drawable/record_bg_material_shadow_gradient" />

        <TextView
            android:id="@+id/record_item_audio_comic_dubbed_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginBottom="5dp"
            android:layout_marginLeft="5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/host_color_ffffff_cfcfcf"
            android:textSize="12sp"
            tools:text="更新至129话" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/record_item_audio_comic_name"
        android:textStyle="bold"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_below="@+id/record_item_audio_comic_cover_card"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/host_color_333333_cfcfcf"
        android:textSize="14sp"
        tools:text="继承者驾到之嘿嘿嘿" />

    <TextView
        android:id="@+id/record_item_audio_comic_update_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:layout_below="@+id/record_item_audio_comic_name"
        android:maxLines="1"
        android:textColor="@color/host_color_999999_888888"
        android:textSize="12sp"
        tools:text="更新至129话" />

</RelativeLayout>