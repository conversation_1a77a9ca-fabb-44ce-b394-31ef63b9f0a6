package com.ximalaya.ting.android.record.view.cover.textmode;

import android.graphics.Paint;
import android.util.Pair;

import androidx.annotation.NonNull;

/**
 * Created by xiaole<PERSON> on 2021/9/9.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 13701664636
 */
class DrawHelper {

    /**
     * 测量水平排列的文本
     * @return  宽、高
     */
    public static Pair<Integer, Integer> measureHorizontalText(@NonNull String txt, @NonNull Paint paint, float mBaseLine) {
        return measureHorizontalText(txt, paint, paint, mBaseLine);
    }

    public static Pair<Integer, Integer> measureHorizontalText(@NonNull String txt, @NonNull Paint firstLinePaint, @NonNull Paint secondLinePaint, float mBaseLine) {
        int width;
        int height;
        Paint.FontMetricsInt pfm = firstLinePaint.getFontMetricsInt();
        if (txt.contains("\n")) {
            String[] str = txt.split("\n");
            float firstLine = firstLinePaint.measureText(str[0]);
            if (str.length > 1) {
                //绘制行间距以两行中最大字体为参考
                Paint.FontMetricsInt maxPfm = firstLinePaint.getTextSize() > secondLinePaint.getTextSize() ?
                        firstLinePaint.getFontMetricsInt() : secondLinePaint.getFontMetricsInt();
                float secondLine = secondLinePaint.measureText(str[1]);
                width = (int) Math.max(firstLine, secondLine);
                height = (int) (2 * maxPfm.bottom - maxPfm.top + mBaseLine);
            } else {
                width = (int) firstLine;
                height = (int) (pfm.bottom + mBaseLine);
            }
        } else {
            width = (int) firstLinePaint.measureText(txt);
            height = (int) (pfm.bottom + mBaseLine);
        }
        return new Pair<>(width, height);
    }

    /**
     * 测量纵向排列的文本
     * @return 宽、高
     */
    public static Pair<Integer, Integer> measureVerticalText(@NonNull String txt, @NonNull Paint paint, float mBaseLine, boolean isAligned) {
        int width;
        int height;
        Paint.FontMetricsInt metricsInt = paint.getFontMetricsInt();
        float h = metricsInt.bottom - metricsInt.top;
        if (txt.contains("\n")) {
            String[] str = txt.split("\n");
            if (str.length > 1) {
                float w1 = getMaxCharWidth(str[0],paint);
                float w2= getMaxCharWidth(str[1],paint);
                //第一列与第二列宽度和
                width = (int) (w1*1.1f+w2);
                float firstLastOne = mBaseLine + h * (str[0].length() - 1) + metricsInt.bottom;
                float secondLastOne = mBaseLine + h * (str[1].length() - 1) + metricsInt.bottom + (isAligned ? 0 : h / 2);
                height = (int) Math.max(firstLastOne, secondLastOne);
            } else {
                width = (int) getMaxCharWidth(str[0],paint);
                height = (int) (mBaseLine + h * (str[0].length() - 1) + metricsInt.bottom);
            }
        } else {
            width = (int) getMaxCharWidth(txt,paint);
            height = (int) (mBaseLine + h * (txt.length() - 1) + metricsInt.bottom);
        }
        return new Pair<>(width, height);
    }

    /**
     * @return 最宽字符的宽度
     */
    public static float getMaxCharWidth(@NonNull String str, @NonNull Paint paint) {
        float cWidth = 0f;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            float width = paint.measureText(String.valueOf(c));
            cWidth = Math.max(cWidth, width);
        }
        return cWidth;
    }

    public static float getCommonBaseLine(@NonNull Paint paint) {
        Paint.FontMetricsInt fmi = paint.getFontMetricsInt();
        //首行文字上间距为文字高度1/3
        return (fmi.bottom - fmi.top) / 3f - fmi.top;
    }
    public static float getBaseLineWithoutMargin(@NonNull Paint paint){
        Paint.FontMetricsInt fmi = paint.getFontMetricsInt();
        return Math.abs(fmi.top);
    }
}
