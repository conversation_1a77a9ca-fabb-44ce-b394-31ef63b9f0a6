package com.ximalaya.ting.android.record.manager.player;

import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;
import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * Create by felix.chen on 2018/6/7.
 *
 * <AUTHOR>
 */

public class UnFocusPlayer {
    private static final String TAG = UnFocusPlayer.class.getSimpleName();
    private MiniPlayer mPlayer;
    private String mPlayPath;
    private long mOffset;
    private long mLength;
    private Handler mProgressUpdateHandler = new Handler(Looper.getMainLooper());
    private Context mAppCtx;
    private PlayProgressListener mPlayProgressListener;
    private FileDescriptor mFDescriptor;
    private AssetFileDescriptor mAFDescriptor;
    private File mBgFile;
    private FileInputStream mInputStream;
    private double mLastProgress = 0;
    private int mCountTime = 10;

    public interface PlayProgressListener {
        void progressUpdate(double progress);
    }

    public UnFocusPlayer(Context ctx) {
        mAppCtx = ctx.getApplicationContext();
        mPlayer = new MiniPlayer();
        mPlayer.setLooping(true);
    }

    private Runnable mUpdateProgressRun = new Runnable() {
        @Override
        public void run() {
            if (mPlayProgressListener != null && getDuration() != 0) {
                double curProgress = getCurrPos() * 1.0 / getDuration();
                if (curProgress == mLastProgress && (mCountTime--) <=0){
                    return;
                }
                mLastProgress = curProgress;
                mPlayProgressListener.progressUpdate(curProgress);
            }
            startUpdateProgress();
        }
    };

    private Runnable mUpdateProgressOneTimeRun = new Runnable() {
        @Override
        public void run() {
            if(mPlayProgressListener != null && getDuration() != 0){
                mPlayProgressListener.progressUpdate(getCurrPos() * 1.0 / getDuration());
            }
        }
    };

    public void seek(int msec) {
        if (mPlayer == null){
            return;
        }
        mPlayer.seekTo(msec);
        mProgressUpdateHandler.post(mUpdateProgressOneTimeRun);
    }

    public void startUpdateProgress() {
        mCountTime = 10;
        mProgressUpdateHandler.removeCallbacksAndMessages(null);
        mProgressUpdateHandler.postDelayed(mUpdateProgressRun,200);
    }

    public void stopUpdateProgress() {
        mProgressUpdateHandler.removeCallbacksAndMessages(null);
    }


    public void setPlayProgressListener(PlayProgressListener playProgressListener) {
        mPlayProgressListener = playProgressListener;
    }

    public void setAudioStreamType(int type) {
        mPlayer.setAudioStreamType(type);
    }

    public void reset() {
        if (mPlayer != null){
            mPlayer.setLooping(true);
            mPlayer.resetPlayer();
        }
        releaseCurrentCache();
    }

    public void setOnCompletionListener(MediaPlayer.OnCompletionListener l) {
        mPlayer.setOnCompletionListener(l);
    }

    public void setPlayerStatusListener(MiniPlayer.PlayerStatusListener l) {
        if (mPlayer != null && l != null){
            mPlayer.setPlayerStatueListener(l);
        }
    }

    public int getSessionId() {
        return mPlayer.getSessionId();
    }

    public boolean isPlaying() {
        if (mPlayer != null) {
            return mPlayer.isPlaying();
        } else {
            return false;
        }
    }

    public boolean isPaused() {
        return mPlayer.isPaused();
    }

    public boolean isStopped() {
        return mPlayer.isStop();
    }

    /**
     * return the length of audio file in milliseconds
     */
    public int getDuration() {
        return mPlayer == null ? 0 : mPlayer.getDuration();
    }

    /**
     * return the current position in milliseconds
     */
    public int getCurrPos() {
        return mPlayer == null ? 0 : mPlayer.getCurrPos();
    }

    private void releaseCurrentCache() {
        if (mPlayer != null)
            mPlayer.stopPlay();
        mFDescriptor = null;
        if (mAFDescriptor != null) {
            try {
                mAFDescriptor.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        mAFDescriptor = null;
        if (mInputStream != null) {
            try {
                mInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        mInputStream = null;
        mBgFile = null;
    }

    public void setVolume(float left, float right) {
        if (mPlayer == null){
            return;
        }
        mPlayer.setVolume(left, right);
    }

    private void initPlayer(String playPath, int seek, boolean[] isFinish) {

        if (mPlayer == null) {
            if (isFinish != null && isFinish.length > 0) isFinish[0] = true;
            return;
        }

        releaseCurrentCache();
        mPlayPath = playPath;
        if (TextUtils.isEmpty(mPlayPath)) {
            if (isFinish != null && isFinish.length > 0) isFinish[0] = true;
            return;
        }
        try {
            mBgFile = new File(mPlayPath);
            mInputStream = new FileInputStream(mBgFile);
            mFDescriptor = mInputStream.getFD();
            mPlayer.init(mFDescriptor, seek, isFinish);
        } catch (Exception e) {
            e.printStackTrace();
            mPlayer.resetPlayer();
        }
    }

    private void initPlayer(String playPath, long offset, long length) {
        if (TextUtils.isEmpty(playPath)) {
            return;
        }
        releaseCurrentCache();
        mPlayPath = playPath;
        mOffset = offset;
        mLength = length;
        try {
            mBgFile = new File(playPath);
            mInputStream = new FileInputStream(mBgFile);
            mFDescriptor = mInputStream.getFD();
            if (mLength != 0) {
                if (mLength > mBgFile.length()) {
                    mLength = mBgFile.length();
                }
                mPlayer.init(mFDescriptor, mOffset, mLength);
            } else {
                mPlayer.init(mFDescriptor, 0, mBgFile.length());
            }
        } catch (Exception e) {
            e.printStackTrace();

            if (mPlayer != null) {
                mPlayer.resetPlayer();
            }
        }
    }

    public void init(String playPath, int seek) {
        initPlayer(playPath, seek, null);
    }

    public void init(String playPath, int seek, boolean[] isFinish) {
        initPlayer(playPath, seek, isFinish);
    }

    public void init(String playPath, long offset, long length) {
        initPlayer(playPath, offset, length);
    }

    public int getStatus() {
        if (mPlayer != null)
            return mPlayer.getStatus();
        else {
            return -1;
        }
    }

    public void init(String playPath) {
        initPlayer(playPath, 0, 0);
    }

    public void play() {
        if (mPlayer != null) {
            startUpdateProgress();
            mPlayer.startPlay();
        }
    }

    public void play(String playPath, long offset, long length) {
        init(playPath, offset, length);
        if (mPlayer != null) {
            startUpdateProgress();
            mPlayer.startPlay();
        }
    }

    public void pause() {
        if (mPlayer != null) {
            stopUpdateProgress();
            mProgressUpdateHandler.post(mUpdateProgressOneTimeRun);
            mPlayer.pausePlay();
        }
    }

    public void stop() {
        if (mPlayer != null) {
            stopUpdateProgress();
            mProgressUpdateHandler.post(mUpdateProgressOneTimeRun);
            mPlayer.stopPlay();
        }
    }

    public void restart() {
        init(mPlayPath);
        mPlayer.startPlay();
    }

    public void release() {
        mProgressUpdateHandler.removeCallbacksAndMessages(null);
        releaseCurrentCache();
        if (mPlayer != null) {
            mPlayer.setOnCompletionListener(null);
            mPlayer.setPlayerStatueListener(null);
            mPlayer.release();
            mPlayer = null;
        }
        mAppCtx = null;
    }

    public void setLooping(boolean isLoop) {
        if (mPlayer != null) {
            mPlayer.setLooping(isLoop);
        }
    }

}
