package com.ximalaya.ting.android.record.data.model.ugc;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-07-25.
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 15721173906
 */
public class UgcMyMaterialModel {

    private int pageNo;
    private int pageSize;
    private int totalCount;
    private int totalPage;
    private List<UgcData> result;

    public int getPageNo() { return pageNo;}

    public void setPageNo(int pageNo) { this.pageNo = pageNo;}

    public int getPageSize() { return pageSize;}

    public void setPageSize(int pageSize) { this.pageSize = pageSize;}

    public int getTotalCount() { return totalCount;}

    public void setTotalCount(int totalCount) { this.totalCount = totalCount;}

    public int getTotalPage() { return totalPage;}

    public void setTotalPage(int totalPage) { this.totalPage = totalPage;}

    public List<UgcData> getResult() {
        return result;
    }

    public void setResult(List<UgcData> result) {
        this.result = result;
    }
}
