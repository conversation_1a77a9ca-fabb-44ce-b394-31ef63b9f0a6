package com.ximalaya.ting.android.record.fragment.upload;

import android.Manifest;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.ximalaya.ting.android.framework.arouter.utils.TextUtils;
import com.ximalaya.ting.android.framework.commoninterface.IHandleOk;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.BitmapUtils;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.bundleframework.Configure;
import com.ximalaya.ting.android.host.manager.bundleframework.model.BundleModel;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.main.IMainFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IMediaMetadataRetriever;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.video.IVideoFunctionAction;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.Router;
import com.ximalaya.ting.android.host.manager.bundleframework.route.router.VideoActionRouter;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.host.model.feed.VideoInfoBean;
import com.ximalaya.ting.android.host.video.VideoPlayerSetting;
import com.ximalaya.ting.android.opensdk.util.MyAsyncTask;
import com.ximalaya.ting.android.player.video.listener.IXmVideoPlayStatusListener;
import com.ximalaya.ting.android.player.video.listener.IXmVideoView;
import com.ximalaya.ting.android.record.R;
import com.ximalaya.ting.android.record.constants.ConstantValues;
import com.ximalaya.ting.android.record.view.upload.RecordVideoChooseCoverView;

import java.io.FileInputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by chendekun on 2020-06-09
 *
 * @anthor chendekun
 * @email <EMAIL>
 * @phone 13032178206
 * describe:
 **/
public class RecordChooseCoverFragment extends BaseFragment2 {

    private VideoInfoBean videoInfoBean;
    private long videoCutLowSecond; // 单位ms
    private long videoCutHighSecond;
    private ImageView videoCoverMask;
    private IVideoFunctionAction functionAction;
    private IMediaMetadataRetriever mmr;
    private MediaMetadataRetriever mmrSys = null;
    private int finalRotate;
    private int finalVideoWidth;
    private int finalVideoHeight;
    private RecordVideoChooseCoverView chooseCoverView;
    private Bitmap[] bitmapArray = new Bitmap[RecordVideoChooseCoverView.MAX_CHOOSE_COVER_PIC];
    private IXmVideoView mVideoPlayer;
    private FrameLayout videoContainer;
    private FrameLayout videoCoverMaskContainer;
    private long videoCoverSecond;

    public static BaseFragment2 newInstance(Bundle bundle){
        RecordChooseCoverFragment fragment = new RecordChooseCoverFragment();
        if (bundle != null) {
            fragment.setArguments(bundle);
        }
        return fragment;
    }

    @Override
    protected String getPageLogicName() {
        return "RecordChooseCoverFragment";
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            videoInfoBean = (VideoInfoBean) bundle.getSerializable(ConstantValues.VIDEO_CUT_INFO_BEAN);
            if (videoInfoBean!=null)videoCoverSecond = videoInfoBean.getVideoChooseCoverSecond();
            if (videoInfoBean !=null){
                videoCutLowSecond = videoInfoBean.getVideoCutLowSecond();
                videoCutHighSecond = videoInfoBean.getVideoCutHithSecond();
            }
        }
    }

    @Override
    protected void initUi(Bundle savedInstanceState) {
        videoContainer = (FrameLayout) findViewById(R.id.record_video_cover_container);
        videoCoverMask = (ImageView) findViewById(R.id.record_video_cover_mask);
        videoCoverMaskContainer = (FrameLayout) findViewById(R.id.record_video_cover_mask_container);
        chooseCoverView =  findViewById(R.id.record_video_choose_cover);
        initVideoPlayer();
        initListener();
    }

    private void initVideoPlayer() {
        Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
            @Override
            public void onInstallSuccess(BundleModel bundleModel) {
                try {
                    functionAction = Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction();
                    mVideoPlayer = functionAction.newXmVideoView(mContext);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (mVideoPlayer != null
                        && mVideoPlayer instanceof View) {
                    videoContainer.addView((View) mVideoPlayer);
                    mVideoPlayer.addXmVideoStatusListener(currentVideoStatusListener);
                    mVideoPlayer.release(true);
                    mVideoPlayer.setVolume(0.0f,0.0f);
                    mVideoPlayer.setVideoPath(videoInfoBean.getPath());
                    if (VideoPlayerSetting.getVideoPlayerType() == VideoPlayerSetting.PV_PLAYER__IjkMediaPlayer) {
                        mVideoPlayer.start();
                    }
                    if (VideoPlayerSetting.getVideoPlayerType() == VideoPlayerSetting.PV_PLAYER__XmExoMediaPlayer) {
                        if (videoCoverSecond>0){
                            mVideoPlayer.seekTo(videoCoverSecond);
                        }else {
                            mVideoPlayer.seekTo(videoCutLowSecond);
                        }
                    }
                }
            }

            @Override
            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

            }

            @Override
            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

            }
        });

    }

    private void initListener() {
        chooseCoverView.setOnRangeListener(new RecordVideoChooseCoverView.onRangeListener() {
            @Override
            public void onChange(float smallLow) {
                if (videoInfoBean!=null && mVideoPlayer!=null){
                    int chooseCoverSecond = (int) (videoInfoBean.getVideoCutLowSecond() + smallLow * (videoInfoBean.getVideoCutHithSecond() - videoInfoBean.getVideoCutLowSecond()) / 100);
                    mVideoPlayer.seekTo(chooseCoverSecond);
                    Fragment parentFragment = getParentFragment();
                    if (parentFragment instanceof RecordChooseVideoCoverFragment){
                        RecordChooseVideoCoverFragment fragment = (RecordChooseVideoCoverFragment) parentFragment;
                        fragment.videoChooseSeconds = chooseCoverSecond;
                    }
                }
            }

            @Override
            public void onTouchDown() {
                Fragment parentFragment = getParentFragment();
                if (parentFragment instanceof RecordChooseVideoCoverFragment){
                    RecordChooseVideoCoverFragment fragment = (RecordChooseVideoCoverFragment) parentFragment;
                    fragment.setCanScroll(false);
                }
            }

            @Override
            public void onTouchUp() {
                Fragment parentFragment = getParentFragment();
                if (parentFragment instanceof RecordChooseVideoCoverFragment){
                    RecordChooseVideoCoverFragment fragment = (RecordChooseVideoCoverFragment) parentFragment;
                    fragment.setCanScroll(true);
                }
            }
        });


    }

    @Override
    protected void loadData() {
        checkPermission(new HashMap<String, Integer>() {{
            put(Manifest.permission.READ_EXTERNAL_STORAGE, com.ximalaya.ting.android.host.R.string.host_failed_to_request_storage_permission);
        }}, new IMainFunctionAction.IPermissionListener() {
            @Override
            public void havedPermissionOrUseAgree() {
                doAfterAnimation(new IHandleOk() {
                    @Override
                    public void onReady() {
                        Router.getActionByCallback(Configure.BUNDLE_VIDEO, new Router.IBundleInstallCallback() {
                            @Override
                            public void onInstallSuccess(BundleModel bundleModel) {
                                getAndSetVideoThumb();
                            }

                            @Override
                            public void onLocalInstallError(Throwable t, BundleModel bundleModel) {

                            }

                            @Override
                            public void onRemoteInstallError(Throwable t, BundleModel bundleModel) {

                            }
                        });
                    }
                });
            }

            @Override
            public void userReject(Map<String, Integer> noRejectPermiss) {
                CustomToast.showFailToast(com.ximalaya.ting.android.host.R.string.host_failed_to_request_storage_permission);
                finishFragment();
            }
        });

    }

    private void getAndSetVideoThumb() {

        final long step = (videoCutHighSecond - videoCutLowSecond) / (RecordVideoChooseCoverView.MAX_CHOOSE_COVER_PIC);

        String rotate = null;
        String videoWidth = null;
        String videoHeigh = null;

        if (mmrSys != null){
            mmrSys.release();
            mmrSys = null;
        }
        try {
            functionAction = Router.<VideoActionRouter>getActionRouter(Configure.BUNDLE_VIDEO).getFunctionAction();
            mmr = functionAction.getMediaMetaRetriever();
            mmr.setDataSource(videoInfoBean.getPath());
            rotate = mmr.extractMetadata(ConstantValues.METADATA_KEY_VIDEO_ROTATION);
            videoWidth = mmr.extractMetadata(ConstantValues.METADATA_KEY_VIDEO_WIDTH);
            videoHeigh = mmr.extractMetadata(ConstantValues.METADATA_KEY_VIDEO_HEIGHT);
        } catch (Exception e) {
            e.printStackTrace();

            FileInputStream inputStream = null;
            try {
                mmrSys = new MediaMetadataRetriever();
                inputStream = new FileInputStream(videoInfoBean.getPath());
                mmrSys.setDataSource(inputStream.getFD());
                rotate = mmrSys.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);
                videoWidth = mmrSys.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
                videoHeigh = mmrSys.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            } catch (Exception e1) {
                e1.printStackTrace();
            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                } catch (Exception e1) {
                }
            }
        }


        finalRotate = TextUtils.isEmpty(rotate) ? 0 : Integer.parseInt(rotate);
        finalVideoWidth = TextUtils.isEmpty(videoWidth) ? 0 : Integer.parseInt(videoWidth);
        finalVideoHeight = TextUtils.isEmpty(videoHeigh) ? 0 : Integer.parseInt(videoHeigh);

        ///宽度大于720，缩放
        if (finalVideoWidth > ConstantValues.MAX_WIDTH_FOR_GET_FRAME_BITMAP) {
            finalVideoHeight = (int) (ConstantValues.MAX_WIDTH_FOR_GET_FRAME_BITMAP * finalVideoHeight / (float) finalVideoWidth);
            finalVideoWidth = ConstantValues.MAX_WIDTH_FOR_GET_FRAME_BITMAP;
        }
        MyAsyncTask.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Bitmap mBitmap = null;
                    Bitmap temBitmap = null;
                    for (int currentPicPosition = 0; currentPicPosition < RecordVideoChooseCoverView.MAX_CHOOSE_COVER_PIC; currentPicPosition++) {

                        final int finalCurrentPicPosition = currentPicPosition;

                        Long currentCutTime = finalCurrentPicPosition == 0 ? videoCutLowSecond  : (long) (videoCutLowSecond + step * finalCurrentPicPosition);

                        if (finalVideoHeight == 0 || finalVideoWidth == 0) {
                            if (mmrSys != null) {
                                mBitmap = mmrSys.getFrameAtTime(currentCutTime * 1000L);//单位为us,需乘1000；
                            } else {
                                mBitmap = mmr.getFrameAtTime(currentCutTime * 1000L);//单位为us,需乘1000；
                            }
                        } else {
                            if (mmrSys != null) {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
                                    mBitmap = mmrSys.getScaledFrameAtTime(currentCutTime * 1000L, MediaMetadataRetriever.OPTION_CLOSEST_SYNC, finalVideoWidth, finalVideoHeight);
                                } else {
                                    mBitmap = BitmapUtils.extractBitmap(mmrSys.getFrameAtTime(currentCutTime * 1000L), finalVideoWidth, finalVideoHeight);
                                }
                            } else {
                                mBitmap = mmr.getScaledFrameAtTime(currentCutTime * 1000L, finalVideoWidth, finalVideoHeight);
                            }

                        }

                        temBitmap = BitmapUtils.centerSquareScaleBitmap(mBitmap, BaseUtil.dp2px(mContext, 100));

                        if (finalRotate != 0) {
                            ///角度不为0，旋转至正常角度
                            temBitmap = BitmapUtils.rotateBitmap(temBitmap, finalRotate);
                            if(temBitmap == null){
                                CustomToast.showFailToast("暂不支持此视频");
                                finishFragment();
                            }
                        }
                        if (temBitmap == null) {
                            CustomToast.showFailToast("暂不支持此视频");
                            finishFragment();
                        }

                        final Bitmap finalTemBitmap = temBitmap;
                        HandlerManager.obtainMainHandler().post(new Runnable() {
                            @Override
                            public void run() {

                                bitmapArray[finalCurrentPicPosition] = finalTemBitmap;
                                chooseCoverView.setBitmapArray(bitmapArray);
                                if(finalCurrentPicPosition == RecordVideoChooseCoverView.MAX_CHOOSE_COVER_PIC -1 ){
                                    if(mmr != null){
                                        mmr.release();
                                    }
                                    if (mmrSys != null){
                                        mmrSys.release();
                                        mmrSys = null;
                                    }
                                }
                            }
                        });

                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    CustomToast.showFailToast("暂不支持此视频");
                    finishFragment();
                }

            }

        });
    }

    IXmVideoPlayStatusListener currentVideoStatusListener = new IXmVideoPlayStatusListener() {

        @Override
        public void onStart(String videoSourceUrl) {

        }

        @Override
        public void onPause(String videoSourceUrl, long playedTime, long duration) {

        }

        @Override
        public void onStop(String videoSourceUrl, long playedTime, long duration) {

        }

        @Override
        public void onComplete(String videoSourceUrl, long duration) {

        }

        @Override
        public void onError(String videoSourceUrl, long playedTime, long duration) {

        }

        @Override
        public void onProgress(String videoSourceUrl, long curPosition, long duration) {

        }

        @Override
        public void onRenderingStart(String videoSourceUrl, long renderingSpentMilliSec) {
            if (mVideoPlayer!=null){
                if (VideoPlayerSetting.getVideoPlayerType() == VideoPlayerSetting.PV_PLAYER__IjkMediaPlayer) {
                    mVideoPlayer.pause();
                    if (videoCoverSecond > 0) {
                        mVideoPlayer.seekTo(videoCoverSecond);
                    } else {
                        mVideoPlayer.seekTo(videoCutLowSecond);
                    }
                }
            }
            if (videoCoverMask!=null)videoCoverMask.setVisibility(View.GONE);
            if (videoCoverMaskContainer!=null)videoCoverMaskContainer.setVisibility(View.GONE);
        }

        @Override
        public void onBlockingStart(String videoSourceUrl) {

        }

        @Override
        public void onBlockingEnd(String videoSourceUrl) {

        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mVideoPlayer != null) {
            mVideoPlayer.removeXmVideoStatusListener(currentVideoStatusListener);
            mVideoPlayer.release(true);
            mVideoPlayer = null;
        }
    }

    @Override
    public int getContainerLayoutId() {
        return R.layout.record_fra_choose_cover;
    }
}
