<html lang=zh-cn>
    <head>
    <meta charset=UTF-8>
    </head><body>
branch: Canary<br>from: ca5e80fb1137615c87825b9528c34e8d6e936407 Merge branch 'auto_merge_branch_liudekai' into 'Canary' 2021-01-19 16:14:06 +0800<br>to: ae456338437d2562ed38cadafb1adb586581b092 Merge branch 'auto_merge_branch_WuJi' into 'Canary' 2021-01-26 18:09:42 +0800

<h2>Jayne</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/57da9f4183a" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c124e103cfe" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc591006e5c" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cec65af27f1" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/31ac3a8bdc9" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cdf627ead54" target=_blank>Merge branch 'auto_merge_branch_wenbin_liu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6b5e39be13c" target=_blank>Merge branch 'Canary' into feature_elderly2</a></li>
</ul>
<h2>Kezhongyang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e75398ab0a" target=_blank>Merge branch 'auto_merge_branch_le' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/48b0c8c1253" target=_blank>Merge branch 'auto_merge_branch_le' into 'Canary'</a></li>
</ul>
<h2>Kezy</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/216e539f5b0" target=_blank>[feat] 播放页提供是否有大图广告或者通栏类广告的方法</a></li>
</ul>
<h2>WolfXu</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/da1d954d8d0" target=_blank>Merge branch 'FeaturePlayResourcePositionOptimization' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bd887fe74b6" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fff91912d10" target=_blank>Merge branch 'auto_merge_branch_luhang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d2c0f98ba23" target=_blank>Merge remote-tracking branch 'origin/Canary' into FeaturePlayResourcePositionOptimization</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6366f6b3a49" target=_blank>[feat] 播放页资源位展开延迟时间由配置中心控制</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6e7dfd0d94d" target=_blank>[feat] 播放页ui调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dbe0f0fe29d" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/08b3641b887" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6ef07a6be0b" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6cb64a9b67e" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a967653d784" target=_blank>[feat] 播放页字幕版权保护</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/23f30eee3ce" target=_blank>Merge branch 'auto_merge_branch_ervin_li' into 'Canary'</a></li>
</ul>
<h2>WuJi</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8bf97bf83b8" target=_blank>[feat] 增加精品页的feed流中控件打标</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2fa29ca3742" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d15fa9d5680" target=_blank>Merge branch 'auto_merge_branch_harry' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f143960abc8" target=_blank>[fix] 处理mCurrentTrackId为-1的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/aeb0a2e009f" target=_blank>[fix] 处理mCurrentTrackId为0的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7f335804a6b" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/24085c5fa84" target=_blank>[feat] 无障碍V1-3</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/78d0bb4845a" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bdfd592b250" target=_blank>[fix] 增加切换账号后的视频tab刷新</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1cfa40fc3ed" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/33137f4e73b" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/df5432f174f" target=_blank>[fix] 处理购买ximi成功后的刷新</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6d01bb03c2e" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c6f2ab952c2" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bdb678feaca" target=_blank>[fix] 增加ximi购买的结果回调</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/03b339a0e3d" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fd58ab5ae50" target=_blank>Merge branch 'Canary' into WholeAlbumReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3f8a4b305a9" target=_blank>[fix] 修改ximi相关的文案</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a5122a373d0" target=_blank>[fix] 修改价格显示</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8141d266530" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ea0b35a9d29" target=_blank>[feat] 会员专享专辑买赠支持买会员礼品卡送好友-售后页部分</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ca057a81732" target=_blank>[fix] 处理ximi抢先听</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7565c2b95ca" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c4b388a4ba0" target=_blank>[fix] 处理点击购买后无反应的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/99025895802" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8b33dc8e4a7" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7797aa52150" target=_blank>Merge branch 'Canary' into XimiReversion</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a98c3ede557" target=_blank>[fix] 调整ximi声音判别方式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/871dbc2ac11" target=_blank>[feat] 播放页-视频tab付费内容购买引导</a></li>
</ul>
<h2>changle.fang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3ee5fd26f29" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/21dd942ad92" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e10653debfb" target=_blank>Merge branch 'FeatCategorySupporth5' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f542e1c449b" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4859e2c8021" target=_blank>Merge branch 'FeatSkin1.1' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2339abe3ec7" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f08e407ece5" target=_blank>[refactor] 删除多余代码</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2efe8b8a7f8" target=_blank>[fix] 无用import</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/edefbcbe2f0" target=_blank>Merge branch 'Canary' into FeatSkin1.1</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/272325cbdf3" target=_blank>Merge branch 'Canary' into FeatSkin1.1 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0049ec8adcb" target=_blank>Merge branch 'Canary' into FeatSkin1.1</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b754a239e65" target=_blank>[feat] 皮肤包1.1</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d5b8d942c7b" target=_blank>Merge branch 'Canary' into FeatCategorySupporth5 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e89c0b5623c" target=_blank>[feat] 支持tabId定位频道落地页</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/56da53f3eb9" target=_blank>Merge branch 'Canary' into FeatCategorySupporth5 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9e5190fb861" target=_blank>Merge branch 'Canary' into FeatCategorySupporth5 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9f21657fdab" target=_blank>[feat] 落地页tab支持h5</a></li>
</ul>
<h2>chendekun</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/decd22d75d9" target=_blank>Merge branch 'FeatCrosstalkHall' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b7797bd1724" target=_blank>Merge remote-tracking branch 'origin/FeatCrosstalkHall' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ae7e4d4dc19" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b8e16cf9912" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/496b795e541" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3078a96e02a" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/38f56381342" target=_blank>[feat] 一起听增加埋点</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a6b249f3d88" target=_blank>[feat] 一起听增加信息补充</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b55ec1ff03a" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2325ea1ee48" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bfd73f6f6b7" target=_blank>[feat] 替换一起听房间接口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/39f1e765da9" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cc864882604" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/746db2766c2" target=_blank>Merge branch 'FeatCrosstalkHall' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a19f9e34461" target=_blank>[feat] 发现页跳转推荐页增加页数控制</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3d76c0498c4" target=_blank>[feat] 夜猫子星球创建房间和匹配前增加资料卡片</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e1bf8f55c0e" target=_blank>Merge branch 'FeatCrosstalkHall' into planet</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/14218184a3b" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4b902047e60" target=_blank>[feat] 夜猫子星球</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/779966d1291" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0af84a17755" target=_blank>[feat] 一起听支持轮滑</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/db24b5fd238" target=_blank>[feat] 一起听相声管信息补充</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4142701dc87" target=_blank>Merge branch 'FeatCrosstalkHall' into planet</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f6fcb1de14b" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/133e9cbe9c2" target=_blank>[feat] 夜猫子星球</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4809796a711" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0ca46e35104" target=_blank>[feat] 夜猫子星球</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/66ed7647291" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/458b29a2b52" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e1ec66da571" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/027095d210b" target=_blank>[feat] 一起听创建房间和匹配增加信息校验</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0b859105a52" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6fb1f3c9c00" target=_blank>[feat] 星球馆首页</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/00d768c1ff8" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ea567a79c05" target=_blank>[feat] 一起听添加声音时带上专辑id</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bca96eec4b6" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/469f7b5a6ea" target=_blank>[feat] 夜猫子星球</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e473b3623a7" target=_blank>Merge branch 'Canary' into FeatCrosstalkHall</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/36a155cbf2e" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_planet</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/712c06ad6eb" target=_blank>[feat] 夜猫子星球</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3b79c44c6ae" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_planet</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e1b4ed8f055" target=_blank>[feat] 夜猫子星球</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1bc4439ce8f" target=_blank>Merge remote-tracking branch 'origin/Canary' into feat_planet</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/708a606aaa4" target=_blank>[feat] 夜猫子星球</a></li>
</ul>
<h2>duruochen</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e9945b445d0" target=_blank>Merge branch '0111_video_optimize' into Canary Signed-off-by: duruochen <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/17f3ddbb69d" target=_blank>Merge remote-tracking branch 'origin/Canary' into 0111_video_optimize Signed-off-by: duruochen <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/736986193df" target=_blank>Merge remote-tracking branch 'origin/Canary' into 0111_video_optimize Signed-off-by: duruochen <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5d0b7518df1" target=_blank>Merge remote-tracking branch 'origin/Canary' into 0111_video_optimize Signed-off-by: duruochen <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b46504f52f5" target=_blank>Merge remote-tracking branch 'origin/Canary' into 0111_video_optimize Signed-off-by: duruochen <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0f65b635a5c" target=_blank>[feat] 视频直播优化，课程直播鉴权</a></li>
</ul>
<h2>ervin.li</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4306a33480c" target=_blank>[feat] 增加codereview报告</a></li>
</ul>
<h2>feiwen</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0be69acfc53" target=_blank>[fix] 修复文稿tab页不能滑动进度条问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5a8d119f3b1" target=_blank>Merge branch 'auto_merge_branch_felix_chen' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/dc98fa6ecf4" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b7c583caf24" target=_blank>[feat] 修改弹幕点赞流程</a></li>
</ul>
<h2>felix.chen</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1cb5a4b4a62" target=_blank>Merge branch 'auto_merge_branch_luhang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/459a0561846" target=_blank>Merge branch 'auto_merge_branch_duruochen' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8771764d85f" target=_blank>[feat] 删除appsFlyer</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ee0b6e863f4" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9fac6f7d53a" target=_blank>[fix] 修改错别字</a></li>
</ul>
<h2>harry</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ae456338437" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/02b9c32bf86" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3a102e9625c" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5180779cfc4" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/47610ee8e77" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc3c0878493" target=_blank>Merge branch 'Canary' into feature_elderly2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/49232e5e974" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6db173d1d12" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5b5e200be72" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8eea684b481" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1b77320ca78" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/afc9181ed14" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f60d165ca81" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc6e2385bce" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f10c2212a92" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f22f054ee30" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a5cac706ba6" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/191a0263a96" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/eda202901ba" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5779a4706f2" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/28cbf626ab9" target=_blank>[feat] 新增老年模式排行榜iting，历史及广播列表页新增doAfterAnimation优化卡顿。</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/74be2634ec7" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f84ee828cd4" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bcc13c0ce74" target=_blank>[feat] 老年模式ui适配。</a></li>
</ul>
<h2>harry.shi</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ae456338437" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/02b9c32bf86" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3a102e9625c" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5180779cfc4" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/47610ee8e77" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc3c0878493" target=_blank>Merge branch 'Canary' into feature_elderly2</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/49232e5e974" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6db173d1d12" target=_blank>Merge branch 'auto_merge_branch_changle_fang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5b5e200be72" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8eea684b481" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1b77320ca78" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/afc9181ed14" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f60d165ca81" target=_blank>Merge branch 'auto_merge_branch_WuJi' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fc6e2385bce" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f10c2212a92" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f22f054ee30" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a5cac706ba6" target=_blank>Merge branch 'auto_merge_branch_kaikai_zhang' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/191a0263a96" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/eda202901ba" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/5779a4706f2" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/28cbf626ab9" target=_blank>[feat] 新增老年模式排行榜iting，历史及广播列表页新增doAfterAnimation优化卡顿。</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/74be2634ec7" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f84ee828cd4" target=_blank>Merge branch 'Canary' into feature_elderly2 Signed-off-by: harry <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bcc13c0ce74" target=_blank>[feat] 老年模式ui适配。</a></li>
</ul>
<h2>kaikai.zhang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1fbabb40682" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4978319df83" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8ddc99a82ed" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c9ec6723918" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fb0e367d3f9" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0f6be383580" target=_blank>Merge branch 'auto_merge_branch_wenbin_liu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6901a3a7763" target=_blank>[fix] 咔嚓倍速变化bug修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/021e1414920" target=_blank>[fix] 咔嚓倍速变化bug修复</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/58d7d464646" target=_blank>Merge branch 'auto_merge_branch_feiwen' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ea5554c62fb" target=_blank>[feat] 弹出截选更多提示</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ebb786c8e9e" target=_blank>[feat] 弹出截选更多提示</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e8b26e7426" target=_blank>Merge branch 'Canary' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a644672d0d3" target=_blank>Merge remote-tracking branch 'origin/Canary' into dev_zsh_post</a></li>
</ul>
<h2>le</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3ee5fd26f29" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/21dd942ad92" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e10653debfb" target=_blank>Merge branch 'FeatCategorySupporth5' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f542e1c449b" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4859e2c8021" target=_blank>Merge branch 'FeatSkin1.1' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/bd6bdb72768" target=_blank>Merge branch '7.3.3.3' of http://gitlab.ximalaya.com/android/MainBundle into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e2920fc0f9d" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/27166c67dbf" target=_blank>[fix] 修复散花广告上报两次问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8c682375eb0" target=_blank>[fix] 修复不能泡泡条第一个泡泡不能点击的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d6ea9ad8ad7" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/99b9996d0df" target=_blank>Merge branch 'FeaturePlayResourcePositionOptimization_ad' into 'FeaturePlayResourcePositionOptimization'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b53de3dbe29" target=_blank>[fix] style为新版本泡泡条添加到弹幕类型中</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2339abe3ec7" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f08e407ece5" target=_blank>[refactor] 删除多余代码</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2efe8b8a7f8" target=_blank>[fix] 无用import</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/edefbcbe2f0" target=_blank>Merge branch 'Canary' into FeatSkin1.1</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/272325cbdf3" target=_blank>Merge branch 'Canary' into FeatSkin1.1 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ea540fff7bc" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: le <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9c061c0d7f1" target=_blank>[feat] 去掉个人页查看大图时修改图片入口</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0049ec8adcb" target=_blank>Merge branch 'Canary' into FeatSkin1.1</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b754a239e65" target=_blank>[feat] 皮肤包1.1</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d5b8d942c7b" target=_blank>Merge branch 'Canary' into FeatCategorySupporth5 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e89c0b5623c" target=_blank>[feat] 支持tabId定位频道落地页</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/56da53f3eb9" target=_blank>Merge branch 'Canary' into FeatCategorySupporth5 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9e5190fb861" target=_blank>Merge branch 'Canary' into FeatCategorySupporth5 Signed-off-by: changle.fang <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9f21657fdab" target=_blank>[feat] 落地页tab支持h5</a></li>
</ul>
<h2>liudekai</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/933d2508de5" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/98eb0037bbe" target=_blank>[feat] 全局置灰增加本地开关</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/caeea8faf73" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/be28a14b50c" target=_blank>[fix] 文稿页使用x5 WebView才重新设置LayoutParams</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/af3f6019b8b" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3689bcfb6a7" target=_blank>[fix] 修复X5 WebView文稿无法展示的问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6add484127c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/3e867a1ed79" target=_blank>[fix] 评论海报增加srcId字段，服务端做统计</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b99ea1a5d97" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4489ac2bdd9" target=_blank>[fix] 账号页获取用户信息接口调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cfbc084ec9c" target=_blank>[fix] 上报收听时长逻辑完善</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/6c65ea07456" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/20f455e8b05" target=_blank>[fix] 播放页播放列表弹窗播放顺序显示调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f24f32214ed" target=_blank>[fix] 修复阿里认证插件无法下载问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/55725a07f2c" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/d20fc6fd691" target=_blank>[feat] 视频tab无障碍优化</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8c16db97a36" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/627b7b1f29e" target=_blank>[feat] 打开大字模式逻辑调整</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/ce85521e998" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary Signed-off-by: liudekai <<EMAIL>></a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/a4b57093fea" target=_blank>[feat] 无障碍优化</a></li>
</ul>
<h2>luhang</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f454e45c724" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4ccf56da904" target=_blank>[fix] iting abtest 跳转H5 取消全屏设置；取消透明设置</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/903cb356cbc" target=_blank>[fix] iting abtest 跳转H5 取消全屏设置</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/90eb6cede5a" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/779f64428ed" target=_blank>[fix] abtest 配置的iting纳入埋点</a></li>
</ul>
<h2>wenbin.liu</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/63c357d9f4d" target=_blank>Merge remote-tracking branch 'origin/Canary' into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/023b1177ec9" target=_blank>[fix] 解析duration单位问题</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cc8a52cd3f8" target=_blank>[feat] 新增duration参数</a></li>
</ul>
<h2>zhangkaikai</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/1fbabb40682" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/4978319df83" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8ddc99a82ed" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c9ec6723918" target=_blank>Merge branch 'auto_merge_branch_zhangshaohua' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fb0e367d3f9" target=_blank>Merge branch 'auto_merge_branch_chendekun' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0f6be383580" target=_blank>Merge branch 'auto_merge_branch_wenbin_liu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/58d7d464646" target=_blank>Merge branch 'auto_merge_branch_feiwen' into 'Canary'</a></li>
</ul>
<h2>zhangshaohua</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/089a4ecfb67" target=_blank>Merge branch 'Canary' of gitlab.ximalaya.com:android/MainBundle into Canary</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8e98a9d7834" target=_blank>[feat] 移除feed流adapter点击事件的无用代码</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/542fdd2172a" target=_blank>Merge remote-tracking branch 'origin/Canary' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/891fbff8cd2" target=_blank>Merge remote-tracking branch 'origin/dev_zsh_post' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/2a7fdc0a8ec" target=_blank>[feat] 个人页动态获取方式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/9cc4c5b8fc8" target=_blank>Merge remote-tracking branch 'origin/Canary' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c7f3c2ac06a" target=_blank>Merge remote-tracking branch 'origin/dev_zsh_post' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/082383393f5" target=_blank>Merge remote-tracking branch 'origin/Canary' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/84c7c8ff55b" target=_blank>[fix] 修改个人页加载adapter</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e762127b5b6" target=_blank>[feat] feed流插件化</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/7eb8ffbd84e" target=_blank>[feat] feed流插件化</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8a5a3d2c669" target=_blank>[feat] 话题列表</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/cfb0cb79278" target=_blank>[feat] 修改接口获取方式</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/f3ab36fa7b5" target=_blank>Merge remote-tracking branch 'origin/Developer' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/b4ee161a069" target=_blank>Merge remote-tracking branch 'origin/Developer' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/0fa35c5a388" target=_blank>Merge remote-tracking branch 'origin/Canary' into dev_zsh_post</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/60485ea4da8" target=_blank>[feat] 发现模块和圈子模块解耦</a></li>
</ul>
<h2>zhupeipei</h2>
<ul>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e14dc02b436" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/8f308e59a67" target=_blank>Merge branch 'auto_merge_branch_WolfXu' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e5d21311f84" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e893b8dd3a4" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/fbf6e70ccd2" target=_blank>Merge branch 'auto_merge_branch_feiwen' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/c8c33b2d5f1" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
<li><a href="http://gitlab.ximalaya.com/android/MainBundle/commit/e728f9b7bc7" target=_blank>Merge branch 'auto_merge_branch_liudekai' into 'Canary'</a></li>
</ul>
</body></html>
