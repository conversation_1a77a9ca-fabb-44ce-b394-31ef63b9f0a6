package com.ximalaya.ting.android.liveaudience.data.model.home;

import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;

import java.util.HashMap;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by lishuo on 2020/4/14.
 * 异常情况
 * 加载时机：点击首页-直播进行拉取数据
 * 重新加载：如加载失败，会重复尝试拉取3次；
 * 点击个人中心按钮：如发现加载失败，重新拉取；
 * 未杀掉APP，会有缓存数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18202765712
 */
public class MineCenterModelManager {

    /**
     * 是否加载成功
     */
    private boolean isLoaded = false;

    private Timer mTimer;

    private int mRepeatTime = 0;

    private int mMaxTime = 3;

    private static MutableLiveData<List<MineCenterModel>> mMineCenterData = new MutableLiveData<>();

    public static boolean isMineCenterDataEmpty() {
        List<MineCenterModel> models = mMineCenterData.getValue();
        return models == null || models.isEmpty();
    }

    private MineCenterModelManager() {

    }

    public static MineCenterModelManager getInstance() {
        return MineCenterManagerHolder.INSTANCE;
    }

    public static MutableLiveData<List<MineCenterModel>> getMineCenterData() {
        return mMineCenterData;
    }

    /**
     * 如加载失败，会重复尝试拉取3次，间隔30秒
     */
    public void getMineCenterModelListUntilGet() {
        if (mTimer == null) {
            mTimer = new Timer();
        }
        long period = 30 * 1000;
        long delay = 1000;
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (isLoaded || mRepeatTime >= mMaxTime) {
                    mTimer.cancel();
                    mRepeatTime = 0;
                    isLoaded = false;
                    mTimer = null;
                    return;
                }
                getMineCenterModelList(null);
                mRepeatTime++;
            }
        }, delay, period);

    }

    public void getMineCenterModelList(final OnMineCenterDataCallback callback) {
        HashMap<String, String> map = new HashMap<>();
        CommonRequestForLive.getMineCenterModelList(map, new IDataCallBack<List<MineCenterModel>>() {
            @Override
            public void onSuccess(@Nullable List<MineCenterModel> object) {
                mMineCenterData.setValue(object);
                isLoaded = true;
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onError(int code, String message) {
                isLoaded = false;
                if (callback != null) {
                    callback.onError();
                }
            }
        });
    }

    private static class MineCenterManagerHolder {
        private static MineCenterModelManager INSTANCE = new MineCenterModelManager();
    }

    public interface OnMineCenterDataCallback {
        void onSuccess();

        void onError();
    }
}
