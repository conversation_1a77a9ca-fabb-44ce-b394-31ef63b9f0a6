package com.ximalaya.ting.android.liveaudience.friends.base;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;

import com.ximalaya.ting.android.live.lib.chatroom.entity.CommonChatQueryRoomModeRsp;
import com.ximalaya.ting.android.liveaudience.fragment.love.ActionCallback;
import com.ximalaya.ting.android.liveaudience.friends.ILamiaRoomFragment;

/**
 * desc: 交友模式听众、嘉宾、主播公用的
 *
 * <AUTHOR> (zhangshixin)
 * @since 04/05/2018.
 */

public interface ILoveModeCommon<F extends ILamiaRoomFragment> extends LifecycleObserver {
    /**
     * 登录聊天室成功后执行初始化
     */
    void initAfterJoinChatRoom();

    /**
     * 收到房间模式消息
     *
     * @param queryRoomModeRsp 房间模式消息
     */
    void onReceivedQueryRoomModeRsp(CommonChatQueryRoomModeRsp queryRoomModeRsp);

    /**
     * 房间状态回调，与聊天室服务端断开连接
     */
    void onDisconnectChatRoom();

    /**
     * 房间状态回调，与聊天室重新连接成功
     */
    void onConnectChatRoom();

    /**
     * 房间状态回调，因为登陆抢占原因等被挤出房间（重复登录，前者被踢出）
     */
    void onKickOutChatRoom();

    /**
     * 查看排麦队列
     */
    void showSeatRequestQueue();

    /**
     * 设置直播间上下文
     *
     * @param roomFragment 直播间
     */
    void setRoomFragment(F roomFragment);

    ActionCallback getActionCallback();

    /**
     * 释放资源和引用
     */
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    void release();
}
