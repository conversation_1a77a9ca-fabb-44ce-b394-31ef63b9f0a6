package com.ximalaya.ting.android.liveaudience.manager.pk;

import static com.ximalaya.ting.android.liveaudience.manager.pk.state.BasePkStateHandler.getCountDownTime;

import android.text.Html;
import android.text.Spanned;

import com.ximalaya.ting.android.common.lib.logger.CommonLiveLogger;
import com.ximalaya.ting.android.host.manager.handler.HandlerManager;
import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkPanelSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkPropPanelNotify;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkRevengeInfo;
import com.ximalaya.ting.android.liveaudience.manager.mode.RoomModeManager;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import PK.Base.PropStatus;
import PK.Base.RandomPkType;


/**
 * 处理 Pk 过程中的计时，主要包括匹配计时和 Pk 计时
 *
 * <AUTHOR> (zhangshixin)
 * @email <EMAIL>
 * @phoneNumber 18789440700
 * @since 2020-03-12.
 */
public interface IPkTimer {

    void clear();

    /**
     * 关键的方法，不同状态时显示不同的内容，有些阶段需要显示这个阶段的倒计时，其他阶段显示总的倒计时
     *
     * @param panelInfo Pk面板消息
     */
    void setPkStatus(CommonPkPanelSyncRsp panelInfo);

    void updateStatusByProPanelStatus(CommonPkPropPanelNotify panelInfo);

    void setPkResult(int hostPkResult);

    void startPkMatchingTiming();

    void resetPkMatchingTiming();

    void resetPkCountDownTiming();

    void startPkCountDownTiming();

    void startPredictCountDownTiming(long timeSecond);

    void resetPkPredictCountDownTiming();

    void setPkMatchingTimeoutSecond(long timeoutSecond);

    void setTimerListener(ITimerListener listener);

    void setEventListener(IOnEventDispatchListener listener);


    /**
     * 超时和结束事件回调，用于发信令
     */
    interface IOnEventDispatchListener {
        void onPkMatchingTimeOutEvent();

        void onPkOverEvent();
    }

    /**
     * 定时回调，用于更新 UI
     */
    interface ITimerListener {
        /**
         * 更新倒计时的状态
         *
         * @param mode
         * @param taskName
         * @param time
         */
        void updateCountDownStatus(String mode, String taskName, String time);

        /**
         * 更新匹配的状态
         *
         * @param tip
         */
        void updateMatchStatus(String tip);

        /**
         * 更新复仇玩法计时
         */
        void updateRevengeTiming(long time);

        /**
         * 更新预言倒计时
         */
        void updatePredictTiming(String time);
    }

    class DefaultPkTimer implements IPkTimer {
        public static final String TAG = "IPkTimer";

        private long mTotalTimes;
        private long mStartTime;

        private static final String PROP_STATUS_DESC_COLLECT_EGG = "彩蛋任务";
        private static final String PROP_STATUS_DESC_COLLECT_GIFT = "礼物任务";
        private String mStatusDesc = "";

        /**
         * 这些阶段需要显示当前的倒计时: 收集礼物、彩蛋任务
         */
        private final List<Integer> sShowCurrentStageTimeStatus = Arrays.asList(
                PropStatus.PROP_STATUS_TASK_COLLECT.getValue(),
                PropStatus.PROP_STATUS_EGG_COLLECT.getValue()
        );
        /**
         * 用来记录是否匹配计算时间中
         */
        private boolean mIsMatchingTiming;
        /**
         * 用来记录是否PK中计时
         */
        private boolean mIsTiming;
        /**
         * Pk匹配中计时，单位秒
         */
        private long mMatchingOffsetTimeSecond;
        /**
         * Pk中、Pk互动时间倒计时，单位秒
         */
        private long mOffsetTimeSecond;
        /**
         * 匹配超时时间，单位秒
         */
        private long mPkMatchingTimeoutSecond;
        /**
         * 复仇玩法倒计时
         */
        private long mPkRevengeTimeSecond;
        /**
         * 预言倒计时
         */
        private long mPkPredictTimeSecond;

        private IOnEventDispatchListener mEventListener;
        private ITimerListener mTimerListener;

        /**
         * pk 状态
         */
        private int mPkStatus;
        /**
         * 排位 pk 的玩法状态
         */
        private int mPropPanelStatus;
        private int mPkResult;
        private int mRandomPkType;

        @Override
        public void clear() {
            mPkStatus = -1;
            mPropPanelStatus = -1;
            mPkResult = -1;
            resetPkCountDownTiming();
            resetPkMatchingTiming();
            resetPkPredictCountDownTiming();
        }

        @Override
        public void setPkStatus(CommonPkPanelSyncRsp panelInfo) {
            if (panelInfo == null) {
                return;
            }
            mRandomPkType = panelInfo.mRandomPkType;
            int pkStatus = panelInfo.mPkStatus;
            int pkMode = panelInfo.mMode;
            int propStatus = -1;
            if (panelInfo.mPropPanel != null) {
                propStatus = panelInfo.mPropPanel.mPropStatus;
            }

            mStartTime = convertLongSafe(panelInfo.mStartTime);
            mTotalTimes = convertLongSafe(panelInfo.mTotalTime);
            long timestamp = convertLongSafe(panelInfo.mTimestamp);

            LiveHelper.pkLog("s1 setPkStatus: " + pkStatus + "/" + mPkStatus
                    + ", " + propStatus + "/" + mPropPanelStatus);

            mPkStatus = pkStatus;
            mPropPanelStatus = propStatus;

//            updateStatusDesc(LivePkManager.getPkTitle());
            log("s1 mStatus " + mStatusDesc);
            if (pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_OFFLINE
                    || pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_FAIL
                    || pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_END) {
                // 离线、匹配失败、Pk结束，重置计时器
                resetPkCountDownTiming();
                resetPkMatchingTiming();

                startRevengePkCountDown(panelInfo, pkStatus, pkMode);
            } else if (pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_ING) {
                // 匹配中，重置计时器
                resetPkCountDownTiming();
                if (mMatchingOffsetTimeSecond == 0) {
                    mMatchingOffsetTimeSecond = (timestamp - mStartTime) / ParamsConstantsInLive.ONE_SECOND;
                }
                startPkMatchingTiming();
            } else if (pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_ING) {
                // PK中
                resetPkCountDownTiming();
                resetPkMatchingTiming();

                updateCountDownTime(LiveTimeUtil.getCountDownTimeSecond(mTotalTimes, timestamp, mStartTime));

                if (panelInfo.mPropPanel == null) {
                    //普通 pk
                    startPkCountDownTiming();
                } else {
                    //排位赛 pk
                    updateStatusByProPanelStatus(panelInfo.mPropPanel);
                }
            } else if (pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY) {
                // PK惩罚，重置计时器
                resetPkCountDownTiming();
                resetPkMatchingTiming();
                updateCountDownTime(LiveTimeUtil.getCountDownTimeSecond(mTotalTimes, timestamp, mStartTime));
                startPkCountDownTiming();
            } else {
                // 重置计时器
                resetPkCountDownTiming();
                resetPkMatchingTiming();
            }

            Logger.d(TAG, "s2 setPkStatus  normal pk : " + mStatusDesc);

        }

        private void startRevengePkCountDown(CommonPkPanelSyncRsp panelInfo, int pkStatus, int pkMode) {
            //排位PK结束，败方可以开启复仇玩法，开始计时
            boolean isRevengePk = pkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_END
                    && pkMode == CommonPkPropPanelNotify.Mode.MODE_DIVISION;
            boolean showRevengeTiming = panelInfo.mPkRevengeInfo != null && panelInfo.mPkRevengeInfo.launchStatus
                    <= CommonPkRevengeInfo.RevengeStatus.STATUS_STARTED;
            boolean hasTimeCalibration = panelInfo.mPkRevengeInfo != null && panelInfo.mPkRevengeInfo.timeCalibration != null;
            if (isRevengePk && showRevengeTiming && hasTimeCalibration) {
                //复仇玩法倒计时
                CommonPkPropPanelNotify.CommonTimeCalibration timeCalibration = panelInfo.mPkRevengeInfo.timeCalibration;
                startRevengePkTiming(LiveTimeUtil.getCountDownTimeSecond(timeCalibration.mTotalTime, timeCalibration.mTimestamp, timeCalibration.mStartTime));
            }
        }

        private void updateCountDownTime(long time) {
            mOffsetTimeSecond = time;
        }

        private void updateStatusDesc(String pkTitle) {
            if (mRandomPkType == RandomPkType.RANDOM_PK_QUALIFIER.getValue()) {
                mStatusDesc = "PK预选赛";
                return;
            }
            mStatusDesc = pkTitle;
        }

        private void log(String s) {
            Logger.i("DebugPk", s);
        }

        /**
         * 排位 pk 有些状态需要显示当前阶段的倒计时
         *
         * @param panelInfo Pk面板消息
         */
        @Override
        public void updateStatusByProPanelStatus(CommonPkPropPanelNotify panelInfo) {
            if (panelInfo == null) {
                return;
            }

            int propStatus = panelInfo.mPropStatus;
            mPropPanelStatus = propStatus;

//            updateStatusDesc(LivePkManager.getPkTitle());

            log("s2 updateStatusByProPanelStatus mStatus " + mStatusDesc + ", propStatus: " + propStatus);

            if (usePropStatusTime(propStatus)) {
                //使用这个阶段的倒计时
                updateCountDownTime(getCountDownTime(panelInfo.mTimeCalibration));

                if (propStatus == PropStatus.PROP_STATUS_TASK_COLLECT.getValue()) {
                    updateStatusDesc(PROP_STATUS_DESC_COLLECT_GIFT);
                } else if (propStatus == PropStatus.PROP_STATUS_EGG_COLLECT.getValue()) {
                    updateStatusDesc(PROP_STATUS_DESC_COLLECT_EGG);
                }else {
                    //清空状态文本
                    updateStatusDesc("");
                }

                log("s3 updateStatusByProPanelStatus needChanged mStatus " + mStatusDesc);
            } else {
                updateStatusDesc("");
                long timestamp;
                if (panelInfo.mTimeCalibration != null && panelInfo.mTimeCalibration.mTimestamp > 0) {
                    timestamp = panelInfo.mTimeCalibration.mTimestamp;
                } else {
                    timestamp = System.currentTimeMillis();
                    Logger.d(TAG, "timestamp = " + timestamp + "，mStartTime = " + mStartTime);
                }
                updateCountDownTime(LiveTimeUtil.getCountDownTimeSecond(mTotalTimes, timestamp, mStartTime));
            }

            Logger.d(TAG, "s3 updateStatusByProPanelStatus  rank pk : "
                    + mStatusDesc + ", " + propStatus);

            startPkCountDownTiming();
        }

        private boolean usePropStatusTime(int propStatus) {
            return sShowCurrentStageTimeStatus.contains(propStatus);
        }

        @Override
        public void setPkResult(int hostPkResult) {
            mPkResult = hostPkResult;
        }

        public void resetPkMatchingTiming() {
            mMatchingOffsetTimeSecond = 0;
            mIsMatchingTiming = false;
            HandlerManager.obtainMainHandler().removeCallbacks(mPkMatchingTimingRunnable);
        }

        public void resetPkCountDownTiming() {
            mOffsetTimeSecond = 0;
            mIsTiming = false;
            HandlerManager.obtainMainHandler().removeCallbacks(mPkCountDownTimingRunnable);
        }

        public void resetPkRevengeCountDownTiming() {
            mPkRevengeTimeSecond = 0;
            HandlerManager.obtainMainHandler().removeCallbacks(mPkRevengeTimingRunnable);
            if (mTimerListener != null) {
                mTimerListener.updateRevengeTiming(mPkRevengeTimeSecond);
            }
        }

        public void resetPkPredictCountDownTiming() {
            Logger.d(TAG, "resetPkPredictCountDownTiming");
            mPkPredictTimeSecond = 0;
            HandlerManager.obtainMainHandler().removeCallbacks(mPkPredictTimingRunnable);
        }

        private Runnable mPkMatchingTimingRunnable = new Runnable() {
            @Override
            public void run() {
                if (mPkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_ING) {
                    updateMatchingTime();

                    mMatchingOffsetTimeSecond++;
                    if (mPkMatchingTimeoutSecond <= 0) {
                        mPkMatchingTimeoutSecond = LivePkManager.DEFAULT_MATCH_TIMEOUT_IN_MILLIS / 1000;
                    }
                    if (mMatchingOffsetTimeSecond == mPkMatchingTimeoutSecond + 2 && mPkMatchingTimeoutSecond != 0) {
                        // 匹配超时，取消匹配
                        if (mEventListener != null) {
                            mEventListener.onPkMatchingTimeOutEvent();
                        }
                    }

                    HandlerManager.obtainMainHandler().postDelayed(this, ParamsConstantsInLive.ONE_SECOND);
                }

                Logger.i(TAG, "runnable mMatchingOffsetTimeSecond = " + mMatchingOffsetTimeSecond + ", status: " + mPkStatus);
            }
        };

        private final Runnable mPkCountDownTimingRunnable = new Runnable() {
            @Override
            public void run() {
                if (mPkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_ING) {
                    mOffsetTimeSecond--;
                    if (mOffsetTimeSecond <= 0) {
                        mOffsetTimeSecond = 1;
                    }

                    showPkingTime();

                    HandlerManager.obtainMainHandler().postDelayed(this, ParamsConstantsInLive.ONE_SECOND);
                } else if (mPkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY) {
                    if (!RoomModeManager.isCommonPkMode()) {
                        return;
                    }
                    mOffsetTimeSecond--;
                    if (mOffsetTimeSecond <= 0) {
                        mOffsetTimeSecond = 1;
                    }

                    showPkInteractionTime();

                    if (mOffsetTimeSecond == 1 && mEventListener != null) {
                        mEventListener.onPkOverEvent();
                    }

                    HandlerManager.obtainMainHandler().postDelayed(this, ParamsConstantsInLive.ONE_SECOND);
                }

                Logger.i(TAG, "mOffsetTimeSecond = " + mOffsetTimeSecond);
            }
        };

        private final Runnable mPkRevengeTimingRunnable = new Runnable() {
            @Override
            public void run() {
                mPkRevengeTimeSecond--;
                if (mPkRevengeTimeSecond <= 0) {
                    resetPkRevengeCountDownTiming();
                    return;
                }

                showRevengeTiming();
                HandlerManager.obtainMainHandler().postDelayed(this, ParamsConstantsInLive.ONE_SECOND);
            }
        };

        private final Runnable mPkPredictTimingRunnable = new Runnable() {
            @Override
            public void run() {
                mPkPredictTimeSecond--;
                if (mPkPredictTimeSecond <= 0) {
                    resetPkPredictCountDownTiming();
                    if (mTimerListener != null) {
                        mTimerListener.updatePredictTiming("待结算");
                    }
                    return;
                }

                showPredictTiming();
                HandlerManager.obtainMainHandler().postDelayed(this, ParamsConstantsInLive.ONE_SECOND);
            }
        };

        public void startPkMatchingTiming() {
            if (mIsMatchingTiming || mPkStatus != CommonPkPropPanelNotify.PkStatus.PK_STATUS_MATCH_ING) {
                return;
            }

            mIsMatchingTiming = true;

            if (mMatchingOffsetTimeSecond <= 0) {
                mMatchingOffsetTimeSecond = 1;
            }

            if (mRandomPkType == RandomPkType.RANDOM_PK_QUALIFIER.getValue()) {
                updateStatusTime("PK预选赛", "", "");
            } else {
                updateStatusTime(LivePkManager.getPkTitle(),"" , "");
            }
            updateMatchingTime();

            HandlerManager.obtainMainHandler().post(mPkMatchingTimingRunnable);
        }

        private void updateMatchingTime() {
            if (mMatchingOffsetTimeSecond < 0) {
                mMatchingOffsetTimeSecond = 0;
            }
            if (mTimerListener != null) {
                mTimerListener.updateMatchStatus(String.format(Locale.CHINA, "正在匹配对手...%ds", mMatchingOffsetTimeSecond));
            }
        }

        private void updateStatusTime(String pkModeString, String taskName, String time) {
            Logger.d(TAG, "updateStatusTime: " + pkModeString +" " +time);
            if (mTimerListener != null) {
                mTimerListener.updateCountDownStatus(pkModeString, taskName, time);
            }
        }

        public void startPkCountDownTiming() {
            if (mIsTiming) {
                return;
            }

            mIsTiming = true;

            if (mOffsetTimeSecond <= 1) {
                mOffsetTimeSecond = 1;
            }

            if (mPkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_ING) {
                showPkingTime();
            } else if (mPkStatus == CommonPkPropPanelNotify.PkStatus.PK_STATUS_PK_PENALTY) {
                showPkInteractionTime();
            }

            HandlerManager.obtainMainHandler().postDelayed(mPkCountDownTimingRunnable, ParamsConstantsInLive.ONE_SECOND);
        }

        private void startRevengePkTiming(long timeSecond) {
            mPkRevengeTimeSecond = timeSecond;
            if (mPkRevengeTimeSecond > 0) {
                HandlerManager.obtainMainHandler().removeCallbacks(mPkRevengeTimingRunnable);
                showRevengeTiming();
                HandlerManager.obtainMainHandler().postDelayed(mPkRevengeTimingRunnable, ParamsConstantsInLive.ONE_SECOND);
            } else {
                resetPkRevengeCountDownTiming();
            }
        }

        public void startPredictCountDownTiming(long timeSecond) {
            mPkPredictTimeSecond = timeSecond;
            if (mPkPredictTimeSecond > 0) {
                HandlerManager.obtainMainHandler().removeCallbacks(mPkPredictTimingRunnable);
                showPredictTiming();
                HandlerManager.obtainMainHandler().postDelayed(mPkPredictTimingRunnable, ParamsConstantsInLive.ONE_SECOND);
            } else {
                resetPkPredictCountDownTiming();
            }
        }

        private void showRevengeTiming() {
            Logger.d(TAG, "updateRevengeTiming: " + mPkRevengeTimeSecond);
            if (mTimerListener != null) {
                mTimerListener.updateRevengeTiming(mPkRevengeTimeSecond);
            }
        }

        private void showPredictTiming() {
            Logger.d(TAG, "showPredictTiming: " + mPkPredictTimeSecond);
            long minute = mPkPredictTimeSecond % 3600 / 60;
            long second = mPkPredictTimeSecond % 60;
            Spanned info = Html.fromHtml(String.format(Locale.CHINA, "%02d:%02d", minute, second));
            if (mTimerListener != null) {
                mTimerListener.updatePredictTiming(info.toString());
            }
        }

        @Override
        public void setPkMatchingTimeoutSecond(long timeoutSecond) {
            mPkMatchingTimeoutSecond = timeoutSecond;
            Logger.i(TAG, "setPkMatchingTimeoutSecond mPkMatchingTimeoutSecond = " + timeoutSecond);
        }

        @Override
        public void setTimerListener(ITimerListener listener) {
            mTimerListener = listener;
        }

        @Override
        public void setEventListener(IOnEventDispatchListener listener) {
            mEventListener = listener;
        }


        /**
         * 互动时间
         */
        private void showPkInteractionTime() {
            long minute = mOffsetTimeSecond % 3600 / 60;
            long second = mOffsetTimeSecond % 60;
//            if (mPkResult == CommonPkPropPanelNotify.PkResult.PK_RESULT_TIE) {
                Spanned info = Html.fromHtml(String.format(Locale.CHINA, "%02d:%02d", minute, second));
                updateStatusTime("","互动时间", info.toString());
//            } else {
//                Spanned info = Html.fromHtml(String.format(Locale.CHINA, "惩罚时间 <b>%02d:%02d</b>", minute, second));
//                updateStatusTime(info.toString());
//            }
        }

        /**
         * pk 中倒计时
         */
        private void showPkingTime() {
            long minute = mOffsetTimeSecond % 3600 / 60;
            long second = mOffsetTimeSecond % 60;

            String format = RoomModeManager.isStarCraftPkMode() ? "%s%02d:%02d" : "%s %02d:%02d";
            Spanned info = Html.fromHtml(String.format(Locale.CHINA, format, "", minute, second));
            CommonLiveLogger.d("pk业务测试-","showPkingTime-"+mStatusDesc+"  "+ info.toString());
            updateStatusTime("",mStatusDesc, info.toString());
        }

        private long convertLongSafe(Long l) {
            return l == null ? 0 : l;
        }
    }

}
