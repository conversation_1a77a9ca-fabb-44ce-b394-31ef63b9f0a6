package com.ximalaya.ting.android.liveaudience.fragment.finish;

import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.ximalaya.ting.android.framework.manager.ImageManager;
import com.ximalaya.ting.android.framework.util.BaseUtil;
import com.ximalaya.ting.android.framework.util.CustomToast;
import com.ximalaya.ting.android.framework.view.refreshload.IRefreshLoadMoreListener;
import com.ximalaya.ting.android.framework.view.refreshload.RefreshLoadMoreListView;
import com.ximalaya.ting.android.host.fragment.BaseFragment2;
import com.ximalaya.ting.android.host.manager.account.AnchorFollowManage;
import com.ximalaya.ting.android.host.manager.account.UserInfoMannage;
import com.ximalaya.ting.android.host.manager.bundleframework.route.action.live.ILivePlaySource;
import com.ximalaya.ting.android.host.util.live.LivePlaySourceUtil;
import com.ximalaya.ting.android.live.R;
import com.ximalaya.ting.android.live.common.dialog.base.LiveBaseDialogFragment;
import com.ximalaya.ting.android.live.common.lib.base.util.LiveCommonITingUtil;
import com.ximalaya.ting.android.live.common.lib.entity.OfficialLiveInfo;
import com.ximalaya.ting.android.live.common.lib.entity.PersonLiveDetail;
import com.ximalaya.ting.android.live.common.lib.manager.LiveFollowInfoManager;
import com.ximalaya.ting.android.live.common.lib.manager.LiveRecordInfoManager;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.live.common.lib.utils.XmLiveRequestIdHelper;
import com.ximalaya.ting.android.live.common.view.widget.LiveStickyNavLayout;
import com.ximalaya.ting.android.live.common.view.widget.LiveTitleLayout;
import com.ximalaya.ting.android.liveaudience.adapter.HomeRecordListAdapter;
import com.ximalaya.ting.android.liveaudience.data.model.home.LiveAudioInfoHolderList;
import com.ximalaya.ting.android.liveaudience.data.model.home.LiveRecordItemInfo;
import com.ximalaya.ting.android.liveaudience.data.request.CommonRequestForLive;
import com.ximalaya.ting.android.liveaudience.util.LiveLamiaUtil;
import com.ximalaya.ting.android.opensdk.datatrasfer.IDataCallBack;
import com.ximalaya.ting.android.xmtrace.AutoTraceHelper;
import com.ximalaya.ting.android.xmtrace.XMTraceApi;
import com.ximalaya.ting.android.xmutil.Logger;

import java.util.Map;

/**
 * 观众端直播间结束直播不跳转，显示此推荐直播弹窗页面。
 *
 * <AUTHOR>
 */
public class LiveAudienceFinishFragment extends LiveBaseDialogFragment implements IRefreshLoadMoreListener, View.OnClickListener {
    RefreshLoadMoreListView mRecommendList;
    LiveAudioInfoHolderList mHomeData;
    HomeRecordListAdapter mRecordListAdapter;
    boolean mIsLoading;
    private long mRecordId;
    private int bizType;
    private TextView mNickName;
    private ImageView mAvatar;
    private ImageView mAvatarVip;
    private TextView mFollow;
    private LiveStickyNavLayout mLiveStickyNavLayout;
    private PersonLiveDetail mRoomDetail;
    private PersonLiveDetail.LiveAnchorInfo mLiveUserInfo;
    private OfficialLiveInfo mOfficialInfo;
    private int mCategoryId;
    private BaseFragment2 mBaseFragment2;
    private boolean isOfficialRoom;
    private TextView mTvSnlIndicator;

    @Override
    protected int getLayoutId() {
        return R.layout.liveaudi_fra_audience_finish;
    }

    public static LiveAudienceFinishFragment newInstance(int bizType, BaseFragment2 fragment2, PersonLiveDetail roomDetail, long recordId, PersonLiveDetail.LiveAnchorInfo info, int categoryId, IDialogFragmentCallBack fragmentCallBack) {
        LiveAudienceFinishFragment finishFragment = new LiveAudienceFinishFragment();
        finishFragment.setFragmentCallBack(fragmentCallBack);
        finishFragment.mRoomDetail = roomDetail;
        finishFragment.mLiveUserInfo = info;
        finishFragment.mCategoryId = categoryId;
        finishFragment.mBaseFragment2 = fragment2;
        finishFragment.bizType = bizType;
        finishFragment.mRecordId = recordId;
        return finishFragment;
    }

    public static LiveAudienceFinishFragment newInstance(int bizType, BaseFragment2 fragment2, PersonLiveDetail roomDetail, OfficialLiveInfo officialInfo, long recordId, PersonLiveDetail.LiveAnchorInfo info, int categoryId, IDialogFragmentCallBack fragmentCallBack, boolean isOfficialRoom) {
        LiveAudienceFinishFragment finishFragment = new LiveAudienceFinishFragment();
        finishFragment.setFragmentCallBack(fragmentCallBack);
        finishFragment.mRoomDetail = roomDetail;
        finishFragment.mLiveUserInfo = info;
        finishFragment.mOfficialInfo = officialInfo;
        finishFragment.mCategoryId = categoryId;
        finishFragment.mBaseFragment2 = fragment2;
        finishFragment.bizType = bizType;
        finishFragment.isOfficialRoom = isOfficialRoom;
        finishFragment.mRecordId = recordId;
        return finishFragment;
    }

    @Override
    public LiveFragmentDialogParams getCustomLayoutParams() {
        LiveFragmentDialogParams liveLayoutParams = new LiveFragmentDialogParams();
        liveLayoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        liveLayoutParams.height = 5 * LiveLamiaUtil.getScreenHeight() / 7;
        liveLayoutParams.gravity = Gravity.BOTTOM;
        liveLayoutParams.animationRes = android.R.style.Animation_InputMethod;
        liveLayoutParams.style = com.ximalaya.ting.android.live.host.R.style.LiveCommonDialog;
        return liveLayoutParams;
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.live_audience_follow) {

            if (null == mLiveUserInfo || null == mRoomDetail) {
                return;
            }

            if (isOfficialRoom && mOfficialInfo == null) {
                //官播间但没官播数据
                return;
            }

            new XMTraceApi.Trace()
                    .setMetaId(33469)
                    .setServiceId("dialogClick")
                    .put("Item", mRoomDetail.isFollowed() ? "取消关注" : "开播提醒我")
                    .put(LiveRecordInfoManager.getInstance().getBaseProps())
                    .createTrace();

            if (!UserInfoMannage.hasLogined()) {
                UserInfoMannage.gotoLogin(getContext());
                return;
            }
            boolean isFollow = false;
            if (isOfficialRoom) {
                isFollow = mOfficialInfo.getFollowFlag();
            } else {
                isFollow = mRoomDetail.isFollowed();
            }

            String specificParams = LiveFollowInfoManager.getInstance().getFollowParams();
            AnchorFollowManage.followV3(
                    mActivity,
                    mLiveUserInfo.uid,
                    isFollow,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM,
                    AnchorFollowManage.FOLLOW_BIZ_TYPE_LIVE_ROOM_FINISH_DIALOG,
                    specificParams,
                    new IDataCallBack<Boolean>() {
                        @Override
                        public void onSuccess(@Nullable Boolean aBoolean) {
                            if (canUpdateUi() && mRoomDetail != null) {
                                if (isOfficialRoom) {
                                    mOfficialInfo.setFollowFlag(aBoolean != null ? aBoolean : false);
                                } else {
                                    mRoomDetail.setFollowed(aBoolean != null ? aBoolean : false);
                                }
                                bindUserData();
                            }
                        }

                        @Override
                        public void onError(int errorCode, String errorMsg) {
                            Logger.i(TAG, "AnchorFollowManage, errorCode = " + errorCode + ", errorMsg = " + errorMsg);

                            if (!canUpdateUi()) {
                                return;
                            }
                            CustomToast.showFailToast("操作失败，请重试");
                        }
                    }, true, true
            );
        }
    }

    @Override
    public void init() {
        mLiveStickyNavLayout = (LiveStickyNavLayout) findViewById(R.id.live_audience__stick_layout);
        mLiveStickyNavLayout.setNavLayoutContext(new LiveStickyNavLayout.NavLayoutContext() {
            @Override
            public int getRootViewHeight() {
                return 5 * LiveLamiaUtil.getScreenHeight() / 7;
            }
        });
        mLiveStickyNavLayout.setScrollChangeListener(new LiveStickyNavLayout.ScrollChangeListener() {
            @Override
            public void onScrollChanged(int oldScrollY, int newScrollY) {

            }

            @Override
            public void onOffsetChanged(float oldOffset, float offset) {

            }

            @Override
            public void onNavHided(View nav, boolean hide) {
                if (nav instanceof TextView) {
                    TextView textView = (TextView) nav;
                    textView.setGravity(hide ? Gravity.CENTER_VERTICAL : Gravity.BOTTOM);
                }
            }
        });
        mNickName = (TextView) findViewById(R.id.live_audience_finish_host_name);
        mAvatar = (ImageView) findViewById(R.id.live_audience_finish_host_avatar);
        mAvatarVip = (ImageView) findViewById(R.id.live_audience_finish_host_avatar_vip);
        mFollow = (TextView) findViewById(R.id.live_audience_follow);
        mFollow.setOnClickListener(this);
        LiveTitleLayout titleLayout = (LiveTitleLayout) findViewById(R.id.live_title);
        titleLayout.setITitleBackListener(new LiveTitleLayout.ITitleClickListener() {
            @Override
            public void onClick() {
                finish();

                new XMTraceApi.Trace()
                        .setMetaId(33470)
                        .setServiceId("dialogClick")
                        .put(LiveRecordInfoManager.getInstance().getBaseProps())
                        .createTrace();
            }
        });
        mRecommendList = (RefreshLoadMoreListView) findViewById(R.id.live_stickynavlayout_scroll_view);
        mRecommendList.setOnRefreshLoadMoreListener(this);
        mRecommendList.getRefreshableView().setSelector(new ColorDrawable(Color.TRANSPARENT));
        mRecordListAdapter = new HomeRecordListAdapter(getActivity(), mHomeData, mRecommendList.getRefreshableView());
        mRecommendList.setAdapter(mRecordListAdapter);
        mRecordListAdapter.setRecordItemClickListener(new HomeRecordListAdapter.OnRecordItemClickListener() {
            @Override
            public void onItemClick(View view,LiveRecordItemInfo info, int position) {
                if (null == info) {
                    return;
                }

                String iTingUrl = LivePlaySourceUtil.appendPlaySourceParamToITing(info.itingUrl,
                        ILivePlaySource.SOURCE_LIVE_ROOM_FINISH_RECOMMEND_LIST);
                LiveCommonITingUtil.handleITing(mActivity, iTingUrl);
                dismiss();

                String requestId = info.getXmRequestId();

                // 点击埋点
                new XMTraceApi.Trace()
                        .setMetaId(33472)
                        .setServiceId("dialogClick")
                        .put("recommendLiveId", String.valueOf(info.id))
                        .put("recommendAnchorId", String.valueOf(info.uid))
                        .put("position", String.valueOf(position))
                        .put("rec_track", info.recTrack)
                        .put("rec_src", info.recSrc)
                        .put(XmLiveRequestIdHelper.createTracePropsForClick(requestId))
                        .createTrace();
            }
        });
        mRecordListAdapter.setOnItemViewExposureListener(new HomeRecordListAdapter.IOnItemViewExposureListener() {
            @Override
            public void onLiveItemViewExposureEvent(LiveRecordItemInfo recordItemInfo, int position) {
                // 曝光埋点
                long recommendLiveId = recordItemInfo != null ? recordItemInfo.id : 0;
                long recommendAnchorId = recordItemInfo != null ? recordItemInfo.uid : 0;
                String recTrack = recordItemInfo != null ? recordItemInfo.recTrack : "";
                String recSrc = recordItemInfo != null ? recordItemInfo.recSrc : "";
                String xmRequestId = recordItemInfo != null ? recordItemInfo.getXmRequestId() : "";
                long roomId = recordItemInfo != null ? recordItemInfo.roomId : 0;

                // 直播间-直播结束弹框_推荐列表  控件曝光
                new XMTraceApi.Trace()
                        .setMetaId(33471)
                        .setServiceId("slipPage")
                        .put("recommendLiveId", String.valueOf(recommendLiveId))
                        .put("recommendAnchorId", String.valueOf(recommendAnchorId))
                        .put("position", String.valueOf(position))
                        .put("rec_track", recTrack)
                        .put("rec_src", recSrc)
                        .put(LiveRecordInfoManager.getInstance().getBaseProps())
                        .put(XmLiveRequestIdHelper.createTracePropsForSlipPage(xmRequestId, String.valueOf(roomId)))
                        .createTrace();
            }
        });
        AutoTraceHelper.bindData(mFollow, AutoTraceHelper.MODULE_DEFAULT, mLiveUserInfo);
        if (isOfficialRoom) {
            mFollow.setVisibility(View.GONE);
        } else {
            mFollow.setVisibility(View.VISIBLE);
        }
        mTvSnlIndicator = (TextView) findViewById(R.id.live_stickynavlayout_indicator);

        new XMTraceApi.Trace()
                .setMetaId(33468)
                .setServiceId("dialogView")
                .put(LiveRecordInfoManager.getInstance().getBaseProps())
                .createTrace();
    }

    private void bindUserData() {
        if (mLiveUserInfo != null && mRoomDetail != null) {
            mNickName.setText(mLiveUserInfo.nickname);
            ImageManager.from(getContext()).displayImage(mAvatar, mLiveUserInfo.largeAvatar, com.ximalaya.ting.android.host.R.drawable.host_default_avatar_88);
            UIStateUtil.showViewsIfTrue(mLiveUserInfo.isVerify, mAvatarVip);
            Drawable drawable;
            boolean isFollow = false;
            if (isOfficialRoom) {
                isFollow = mOfficialInfo.getFollowFlag();
            } else {
                isFollow = mRoomDetail.isFollowed();
            }
            if (UserInfoMannage.hasLogined() && isFollow) {
                drawable = new UIStateUtil.GradientDrawableBuilder().color(Color.parseColor("#CCCCCC"))
                        .cornerRadius(BaseUtil.dp2px(getContext(), 50))
                        .orientation(GradientDrawable.Orientation.RIGHT_LEFT)
                        .build();
                mFollow.setBackground(drawable);
                mFollow.setText("已关注");
                mFollow.setTextColor(Color.WHITE);
            } else {
                drawable = new UIStateUtil.GradientDrawableBuilder().color(new int[]{Color.parseColor("#FF8b70"), Color.parseColor("#F74728")})
                        .cornerRadius(BaseUtil.dp2px(getContext(), 50))
                        .orientation(GradientDrawable.Orientation.RIGHT_LEFT)
                        .build();
                mFollow.setBackground(drawable);
                mFollow.setText("开播提醒我");
                mFollow.setTextColor(Color.WHITE);
            }
        }
    }

    @Override
    public void load() {
        requestHomeRecordData();
    }

    public void requestHomeRecordData() {
        if (mIsLoading) {
            return;
        }
        mIsLoading = true;
        onLoadCompleted(STATE_LOADING);
        Map<String, String> params = LiveHelper.buildTimeParams();
        params.put("recordId", String.valueOf(mRecordId));
        params.put("categoryId", String.valueOf(mCategoryId));
        params.put("bizType", String.valueOf(bizType));
        CommonRequestForLive.getRecommendLiveRecordListForAudience(params, new IDataCallBack<LiveAudioInfoHolderList>() {
            @Override
            public void onSuccess(final LiveAudioInfoHolderList object) {
                mIsLoading = false;
                if (!canUpdateUi()) {
                    return;
                }

                bindUserData();

                mRecommendList.onRefreshComplete(false);
                if (object == null || object.isEmpty()) {
                    onLoadCompleted(STATE_NO_CONTENT);
                    mIsLoading = false;
                    if (mTvSnlIndicator != null) {
                        mTvSnlIndicator.setText("");
                        mTvSnlIndicator.setVisibility(View.VISIBLE);
                    }
                    return;
                }

                if (mTvSnlIndicator != null) {
                    mTvSnlIndicator.setText(getStringSafe(R.string.liveaudi_recommend_subtitle));
                    mTvSnlIndicator.setVisibility(View.VISIBLE);
                }
                onLoadCompleted(STATE_OK);
                mHomeData = object;
                mRecordListAdapter.setList(object);
                mIsLoading = false;
            }

            @Override
            public void onError(int code, String message) {
                if (!canUpdateUi()) {
                    return;
                }

                mRecommendList.onRefreshComplete(false);
                onLoadCompleted(STATE_NETWORK_ERROR);
                mIsLoading = false;
            }
        });
    }

    @Override
    public void onShow(DialogInterface dialogInterface) {
        super.onShow(dialogInterface);
    }

    @Override
    public void onRefresh() {
        load();
    }

    @Override
    public void onMore() {

    }

    @Override
    public void onDestroyView() {
        if (mRecordListAdapter != null) {
            mRecordListAdapter.setRecordItemClickListener(null);
            mRecordListAdapter.setOnItemViewExposureListener(null);
            mRecordListAdapter.destroy();
        }
        super.onDestroyView();
    }
}
