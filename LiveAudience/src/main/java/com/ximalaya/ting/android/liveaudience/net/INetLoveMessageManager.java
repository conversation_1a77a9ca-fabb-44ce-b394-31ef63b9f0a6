package com.ximalaya.ting.android.liveaudience.net;


import com.ximalaya.ting.android.live.biz.pia.entity.CommonPiaStatusRsp;
import com.ximalaya.ting.android.live.lib.chatroom.ChatRoomConnectionManager;
import com.ximalaya.ting.android.live.lib.chatroom.entity.BaseCommonChatRsp;
import com.ximalaya.ting.android.live.lib.chatroom.net.IBaseNet;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveConnectRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveJoinRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveOnlineUserSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveTimeStatusSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveUserStatusSyncRsp;
import com.ximalaya.ting.android.liveaudience.entity.proto.love.CommonLoveWaitUserSyncRsp;

/**
 * 交友模式消息管理类。
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @phoneNumber 18817333656
 * @since 2020-02-05
 */
public interface INetLoveMessageManager extends IBaseNet {

    /**
     * 主播开启交友/Pia 戏模式
     *
     * @param roomId      直播间id
     * @param nickname 主播昵称
     * @param channelName ZegoRoomId
     * @param isPia 是否是 Pia 戏
     */
    void reqStart(long roomId, String nickname, String channelName, boolean isPia, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播关闭交友模式
     *
     * @param roomId 直播间id
     */
    void reqStop(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播接通用户连麦
     */
    void reqConnect(long roomId, long toUserId, final ChatRoomConnectionManager.ISendResultCallback<CommonLoveConnectRsp> callBack);

    /**
     * 主播挂断用户连麦
     */
    void reqHungUp(long roomId, long toUserId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播静音或取消静音用户
     */
    void reqMute(long roomId, long toUserId, boolean isMute, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播锁定或解锁座位
     */
    void reqLockPosition(long roomId, int micNo, boolean isOpen, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播开启心动时刻
     */
    void repStartLoveTime(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播关闭心动时刻
     */
    void repStopLoveTime(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播清空魅力值
     */
    void repCleanLoveValue(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播开启团战Pk玩法
     */
    void startPk(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播关闭团战Pk玩法
     */
    void stopPk(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播增加Pk时间
     */
    void repExtendPkTime(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播开启相亲玩法
     */
    void repStartMarry(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播关闭相亲玩法
     */
    void repStopMarry(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 听众申请连麦
     */
    void reqJoin(long roomId, int micNo, final ChatRoomConnectionManager.ISendResultCallback<CommonLoveJoinRsp> callBack);

    /**
     * 听众断开连麦
     */
    void reqLeave(long roomId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 听众静音或取消静音自己
     */
    void reqMuteSelf(long roomId, boolean isMute, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 听众选择心动对象
     */
    void repSelectLover(long roomId, int micNo, boolean isSelect, int loverMicNo, long loverUserId, final ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     * 主播/用户同步用户状态信息
     */
    void reqSyncUserStatus(long roomId, final ChatRoomConnectionManager.ISendResultCallback<CommonLoveUserStatusSyncRsp> callBack);

    /**
     * 主播/用户同步在线用户列表信息
     */
    void reqSyncOnlineUserList(long roomId, final ChatRoomConnectionManager.ISendResultCallback<CommonLoveOnlineUserSyncRsp> callBack);

    /**
     * 主播/用户同步排麦用户列表信息
     */
    void reqSyncWaitUserList(long roomId, final ChatRoomConnectionManager.ISendResultCallback<CommonLoveWaitUserSyncRsp> callBack);

    /**
     * 主播端心动时刻面板状态同步
     */
    void repSyncLoveTimeStatus(long roomId, final ChatRoomConnectionManager.ISendResultCallback<CommonLoveTimeStatusSyncRsp> callBack);

    /**
     *  选择剧本
     * @param dramaId 剧本id
     **/
    void reqChooseDrama(long dramaId,ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     *  开始pia
     **/
    void startPiaRequest(ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     *  结束pia
     **/
    void stopPiaRequest(ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);

    /**
     *  查询状态
     **/
    void queryPiaStatus(ChatRoomConnectionManager.ISendResultCallback<CommonPiaStatusRsp> callBack);

    /**
     *  开始同步
     **/
    void startProUpdateRequest(float position,ChatRoomConnectionManager.ISendResultCallback<BaseCommonChatRsp> callBack);
}
