package com.ximalaya.ting.android.liveaudience.manager.pk.state;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.CallSuper;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.ximalaya.ting.android.live.common.lib.base.constants.ParamsConstantsInLive;
import com.ximalaya.ting.android.live.common.lib.utils.LiveHelper;
import com.ximalaya.ting.android.live.common.lib.utils.LiveTimeUtil;
import com.ximalaya.ting.android.live.common.lib.utils.UIStateUtil;
import com.ximalaya.ting.android.liveaudience.entity.proto.pk.CommonPkPropPanelNotify;
import com.ximalaya.ting.android.liveaudience.friends.LoveModeLogicHelper;
import com.ximalaya.ting.android.liveaudience.view.pk.PkPanelView;
import com.ximalaya.ting.android.liveaudience.view.pk.PkTvView;

/**
 * desc: pk3.0 不同状态处理器共同的一些操作
 *
 * <AUTHOR> (zhangshixin)
 * @email <EMAIL>
 * @phoneNumber 18789440700
 * @since 2019/3/8.
 */
public abstract class BasePkStateHandler<T> implements IRankPkStateHandler<T> {
    private static final long ESC_ANIMATION_DURATION = 300;
    private static final long ENTER_ANIMATION_DURATION = 500;

    private ViewStateParameter mViewStateParameter;
    protected View mStateParentView;
    private String className;
    protected boolean isReleased;
    private AnimatorSet mAnimatorSet;

    public BasePkStateHandler(@NonNull ViewStateParameter viewStateParameter) {
        mViewStateParameter = viewStateParameter;

        className = getClass().getSimpleName();
    }

    abstract int getViewLayoutId();

    @CallSuper
    @Override
    public void initUI() {
        mStateParentView = getLayoutInflater().inflate(getViewLayoutId(), getPkTvView(), false);
        mStateParentView.setAlpha(0);

        getPkTvView().addView(mStateParentView);
    }

    @CallSuper
    @Override
    public void release() {
        log("release, isReleased: " + isReleased);
        if (isReleased) {
            return;
        }

        if (mStateParentView != null && mStateParentView.getParent() != null) {
            mStateParentView.post(new Runnable() {
                @Override
                public void run() {
                    ViewGroup parent = (ViewGroup) mStateParentView.getParent();
                    parent.removeView(mStateParentView);
                }
            });
        }

        if (mAnimatorSet != null && mAnimatorSet.isRunning()) {
            mAnimatorSet.cancel();
        }
        isReleased = true;
    }

    @Override
    public void enter() {
        log("enter, isReleased: " + isReleased);
        if (isReleased) {
            return;
        }
        alphaEnter();
    }

    protected void alphaEnter() {
        ObjectAnimator alpha = ObjectAnimator.ofFloat(mStateParentView, "alpha", 0, 1);
        alpha.setDuration(ENTER_ANIMATION_DURATION);
        alpha.addListener(new LoveModeLogicHelper.AbsAnimatorListener() {
            @Override
            public void onAnimationStart(Animator animator) {
                log("enter, onAnimationStart ");
                UIStateUtil.showViews(mStateParentView);
            }

            @Override
            public void onAnimationEnd(Animator animator) {
                onEnter();
            }
        });

        if (mAnimatorSet == null) {
            mAnimatorSet = new AnimatorSet();
        }
        mAnimatorSet.play(alpha).after(ESC_ANIMATION_DURATION);
        mAnimatorSet.start();
    }

    //进入动画结束后
    protected void onEnter() {
    }

    @Override
    public void esc() {
        log("esc, isReleased: " + isReleased);
        if (isReleased) {
            return;
        }
        ObjectAnimator alpha = ObjectAnimator.ofFloat(mStateParentView, "alpha", 1, 0);
        alpha.setDuration(ESC_ANIMATION_DURATION);
        alpha.addListener(new LoveModeLogicHelper.AbsAnimatorListener() {
            @Override
            public void onAnimationEnd(Animator animator) {
                log("esc, onAnimationEnd");
                release();
            }
        });
        alpha.start();

    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;

        }
        if (obj instanceof BasePkStateHandler) {

        }
        return false;
    }

    public <V extends View> V findViewById(@IdRes int id) {
        return mStateParentView.findViewById(id);
    }

    public void log(String s) {
        LiveHelper.pkLog(className + " " + s);
    }


    public static void logs(String s) {
        LiveHelper.pkLog(" " + s);
    }


    LayoutInflater getLayoutInflater() {
        return mViewStateParameter.mLayoutInflater;
    }

    PkPanelView getPkPanelView() {
        return mViewStateParameter.mPkPanelView;
    }

    PkTvView getPkTvView() {
        return mViewStateParameter.mPkTvView;
    }

    Context getContext() {
        return mViewStateParameter.mContext;
    }

    Context getAppContext() {
        Context context = LoveModeLogicHelper.getContextWithCheck(mViewStateParameter.mContext);
        if (context != null) {
            return context.getApplicationContext();
        }
        return null;
    }

    FragmentActivity getActivity() {
        return mViewStateParameter.mFragmentActivity;
    }

    boolean isAudience() {
        return mViewStateParameter.mIsAudience;
    }

    public static long getCountDownTime(CommonPkPropPanelNotify.CommonTimeCalibration timeCalibration) {
        if (timeCalibration == null) {
            return 0;
        }

        long startTime = LoveModeLogicHelper.getLongValueCheckBeforeUnBox(timeCalibration.mStartTime);
        long totalTime = LoveModeLogicHelper.getLongValueCheckBeforeUnBox(timeCalibration.mTotalTime);
        long timeStamp = LoveModeLogicHelper.getLongValueCheckBeforeUnBox(timeCalibration.mTimestamp);

        long countDownTimeSecond = LiveTimeUtil.getCountDownTimeSecond(totalTime, timeStamp, startTime);

        logs("countdown: " + startTime + ", " + totalTime + ", " + timeStamp + ",,, " + countDownTimeSecond);

        return countDownTimeSecond;
    }

    public static long getTotalTimeSecond(CommonPkPropPanelNotify.CommonTimeCalibration timeCalibration) {
        if (timeCalibration == null) {
            return 0;
        }

        long totalTimeMs = LoveModeLogicHelper.getLongValueCheckBeforeUnBox(timeCalibration.mTotalTime);

        return totalTimeMs / ParamsConstantsInLive.ONE_SECOND;
    }


}
