package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.teamListen

import android.text.TextUtils
import androidx.viewpager.widget.PagerAdapter
import android.view.ViewGroup
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.ximalaya.ting.android.framework.manager.ImageManager
import androidx.constraintlayout.utils.widget.ImageFilterView
import com.ximalaya.android.componentelementarysdk.util.SdkProxyFunctionUtil
import com.ximalaya.android.nativecomponentsdk.R
import com.ximalaya.ting.android.framework.view.image.RoundImageView
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject

class TeamListenBannerAdapter(private val helper: TeamListenModuleViewHelper?) : PagerAdapter() {
    private var list: List<JSONObject>? = null
    private var mViewMap = mutableMapOf<Int,View>()
    fun setList(list: List<JSONObject>?) {
        this.list = list
        notifyDataSetChanged()
    }

    val initialPosition: Int
        get() = if (count > 1) {
            var i = count / 2
            i = i - i % list!!.size
            i
        } else {
            0
        }

    override fun getCount(): Int {
        if (list == null) {
            return 0
        }
        return if (list!!.size <= 1) {
            list!!.size
        } else 300
    }

    private fun getRealItemCount(): Int {
        return if (list == null) {
            0
        } else {
            list!!.size
        }
    }

    override fun isViewFromObject(view: View, `object`: Any): Boolean {
        return view === `object`
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val context = container.context

        val realPosition = if (getRealItemCount() > 0) {
            position % getRealItemCount()
        } else {
            position
        }
        val data = if (realPosition < (list?.size ?: 0)) {
            list!![realPosition]
        } else {
            null
        }
        data ?: return View(container.context)
        var itemView: View? = mViewMap[realPosition]
        if (itemView == null) {
            itemView = LayoutInflater.from(context)
                .inflate(
                    R.layout.universal_n_module_custom_album_team_listen_item,
                    container,
                    false
                )
//            mViewMap[realPosition] = itemView
        }
        itemView ?: return View(container.context)
        bindDataToView(data, MyViewHolder(itemView))
        if (itemView.parent != null && itemView.parent is ViewGroup) {
            (itemView.parent as ViewGroup).removeView(itemView)
        }
        container.addView(itemView)
        return itemView

    }

    private fun bindDataToView(data: JSONObject, holder: MyViewHolder) {
        ImageManager.from(holder.itemView.context)
            .displayImage(
                holder.vCover,
                data.optString("cover"),
                R.drawable.universal_ic_default_album
            )
        var extJson: JSONObject? = null
        val extStr: String = data.optString("ext")
        if (!TextUtils.isEmpty(extStr)) {
            try {
                extJson = JSONObject(extStr)
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
        var buttonText = ""
        extJson?.let {
            holder.tvTitle.text = it.optString("summary")
            val avatarList: MutableList<String> = mutableListOf()
            it.optString("avatarList").let { avatarListStr ->
                try {
                    val avatarJsonArray = JSONArray(avatarListStr)
                    for (i in 0 until avatarJsonArray.length()) {
                        val avatar = avatarJsonArray.optString(i)
                        if (!avatar.isNullOrEmpty()) {
                            avatarList.add(avatar)
                        }
                    }
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
            }
            val joinSubTitleStr = it.optString("participantDesc")
//            val subTitleStr = it.optString("subTitle")
            if (avatarList.isNotEmpty() && joinSubTitleStr.isNotEmpty()) {
                holder.tvSubTitle.visibility = View.INVISIBLE
                holder.vJoinInfoArea.visibility = View.VISIBLE
                holder.vJoinCover1.visibility = View.INVISIBLE
                holder.vJoinCover2.visibility = View.GONE
                holder.vJoinCover3.visibility = View.GONE
                avatarList.forEachIndexed { index, s ->
                    when (index) {
                        0 -> {
                            holder.vJoinCover1.visibility = View.VISIBLE
                            ImageManager.from(holder.itemView.context)
                                .displayImage(
                                    holder.vJoinCover1,
                                    s,
                                    R.drawable.universal_ic_default_portrait
                                )
                        }
                        1 -> {
                            holder.vJoinCover2.visibility = View.VISIBLE
                            ImageManager.from(holder.itemView.context)
                                .displayImage(
                                    holder.vJoinCover2,
                                    s,
                                    R.drawable.universal_ic_default_portrait
                                )
                        }
                        2 -> {
                            holder.vJoinCover3.visibility = View.VISIBLE
                            ImageManager.from(holder.itemView.context)
                                .displayImage(
                                    holder.vJoinCover3,
                                    s,
                                    R.drawable.universal_ic_default_portrait
                                )
                        }
                    }
                }
                holder.vJoinCountTitle.text = joinSubTitleStr
            } else if (joinSubTitleStr.isNotEmpty()) {
                holder.tvSubTitle.visibility = View.VISIBLE
                holder.vJoinInfoArea.visibility = View.INVISIBLE
                holder.tvSubTitle.text = joinSubTitleStr
            } else {
                holder.tvSubTitle.visibility = View.GONE
                holder.vJoinInfoArea.visibility = View.GONE
            }
            buttonText = it.optString("buttonText")
        }
        val landingPageUrl = data.optString("landingPage")
        if (landingPageUrl.isNullOrEmpty()) {
            holder.vActionButton.visibility = View.GONE
        } else {
            if(buttonText.isNullOrEmpty()){
                buttonText = "去组队"
            }
            holder.vActionButton.text = buttonText
            holder.vActionButton.visibility = View.VISIBLE
            holder.vActionButton.setBackgroundResource(R.drawable.universal_bg_rect_19ffffff_radius_15)
            holder.itemView.setOnClickListener {
                helper?.markPointFunction(
                    TeamListenModuleViewHelper.TYPE_CLICK_TEAM_LISTEN_ITEM,
                    extJson
                )
                SdkProxyFunctionUtil.jumpUrl(landingPageUrl)
            }
        }
//        helper?.markPointFunction(TeamListenModuleViewHelper.TYPE_SHOW_TEAM_LISTEN_ITEM, extJson)
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        if (`object` is View) {
            container.removeView(`object`)
        }
    }

    private class MyViewHolder(val itemView: View) {
        val vCover: ImageFilterView
        val tvTitle: TextView
        val tvSubTitle: TextView
        val vJoinInfoArea: LinearLayout
        val vJoinCover1: RoundImageView
        val vJoinCover2: RoundImageView
        val vJoinCover3: RoundImageView
        val vJoinCountTitle: TextView
        val vActionButton: TextView

        init {
            vCover = itemView.findViewById(R.id.universal_id_cover)
            tvTitle = itemView.findViewById(R.id.universal_id_title)
            tvSubTitle = itemView.findViewById(R.id.universal_id_sub_title)
            vJoinInfoArea = itemView.findViewById(R.id.universal_id_join_info_area)
            vJoinCover1 = itemView.findViewById(R.id.universal_id_join_cover_1)
            vJoinCover2 = itemView.findViewById(R.id.universal_id_join_cover_2)
            vJoinCover3 = itemView.findViewById(R.id.universal_id_join_cover_3)
            vJoinCountTitle = itemView.findViewById(R.id.universal_id_join_count)
            vActionButton = itemView.findViewById(R.id.universal_id_action_btn)
        }
    }
    fun clearCache(){
        mViewMap.clear()
    }
}