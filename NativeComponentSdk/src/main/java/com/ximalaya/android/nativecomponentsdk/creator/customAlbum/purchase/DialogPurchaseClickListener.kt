package com.ximalaya.android.nativecomponentsdk.creator.customAlbum.purchase

import android.view.View
import android.widget.TextView
import com.ximalaya.android.componentelementarysdk.constant.CommonConstant
import com.ximalaya.android.componentelementarysdk.constant.DialogConstant
import com.ximalaya.android.componentelementarysdk.material.UniversalDialogMaterial
import com.ximalaya.android.componentelementarysdk.model.module.defaultModel.customAlbum.PurchaseModuleModel
import com.ximalaya.android.componentelementarysdk.sdkApi.proxy.IClickFunctionProxy
import com.ximalaya.android.componentelementarysdk.util.SdkBaseUtil
import com.ximalaya.android.componentelementarysdk.util.SdkProxyFunctionUtil
import com.ximalaya.android.nativecomponentsdk.R

/**
 * Created by 5Greatest on 2022.01.18
 *
 * <AUTHOR>
 *   On 2022/1/18
 */
@Deprecated("自制页购买模块改为中插条模式，该模块弃用  --2022.09.14")
class DialogPurchaseClickListener(private val material: UniversalDialogMaterial, private val index: Int, private val helper: PurchaseModuleViewHelper?) : View.OnClickListener {
    private var isBuying: Boolean = false
    override fun onClick(p0: View?) {
        if (!SdkBaseUtil.Common.onClick(p0)) {
            return
        }
        if (!SdkProxyFunctionUtil.checkAndAutoLogin()) {
            return
        }

        val tag: Any? = p0?.getTag(com.ximalaya.android.componentelementarysdk.R.id.universal_tag_click_model)
        if (tag is PurchaseModuleModel) {
            val behavior: PurchaseModuleModel.Behavior = SdkBaseUtil.Common.safelyGetItemFromList(tag.layer?.behaviors, index)
                ?: return

            if (p0 is TextView) {
                val extraInfo: Pair<String, String> = Pair(p0.text?.toString()?: "", behavior.dataAnalysis?: "")
                helper?.markPointFunction(PurchaseModuleViewHelper.TYPE_CLICK_PURCHASE_DIALOG_BTN, extraInfo)
            }
            if (isBuying) {
                SdkProxyFunctionUtil.showToast(CommonConstant.TextConstant.TEXT_PURCHASE_PROCESSING)
                return
            }
            isBuying = true
            val couponIds: List<Long>? = tag.extensions?.autoAllocateCoupons
            if (null == couponIds) {
                behavior.processPurchaseBehavior()
                material.controller?.command(DialogConstant.ControlInstruction.CONTROL_CLOSE_DIALOG)
                isBuying = false
            } else {
                val isFunctional: Boolean = SdkProxyFunctionUtil.requestCoupon(couponIds, object : IClickFunctionProxy.AsynchronousClickEventResult<Boolean> {
                    override fun returnResult(result: Boolean?) {
                        // 无在意是否领取成功
                        behavior.processPurchaseBehavior()
                        material.controller?.command(DialogConstant.ControlInstruction.CONTROL_CLOSE_DIALOG)
                        isBuying = false
                    }
                })
                if (!isFunctional) {
                    behavior.processPurchaseBehavior()
                    material.controller?.command(DialogConstant.ControlInstruction.CONTROL_CLOSE_DIALOG)
                    isBuying = false
                }
            }
        }
    }
}