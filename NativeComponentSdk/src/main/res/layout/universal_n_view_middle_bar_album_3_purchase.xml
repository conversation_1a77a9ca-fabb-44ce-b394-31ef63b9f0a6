<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/host_y12"
    android:paddingBottom="@dimen/host_y12"
    android:paddingRight="@dimen/universal_custom_album_module_padding_horizontal"
    android:paddingLeft="@dimen/universal_custom_album_module_padding_horizontal"
    android:orientation="vertical"
    tools:background="@color/universal_color_fff8f8f8_ff131313"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="6dp"
        app:cardBackgroundColor="@color/universal_color_transparent"
        app:cardElevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/universal_id_purchase_disable_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_disable2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/universal_id_price_special_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_special2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/universal_id_price_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_pure_price2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/universal_id_free_listen_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_free_listen2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/universal_id_privilege_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_pure_vip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/universal_id_privilege_area_v2"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_pure_vip3"
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <include
                android:id="@+id/universal_id_combine_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_combine2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

            <include
                android:id="@+id/universal_id_combine_reverse_area"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_combine_reverse2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>
            <include
                android:id="@+id/universal_id_ad_free_listen"
                layout="@layout/universal_n_view_middle_bar_album_3_purchase_ad_free_listen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"/>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
