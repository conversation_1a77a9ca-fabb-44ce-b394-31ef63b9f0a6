<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/vip_iv_character_cover"
        android:layout_width="100dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/host_x8"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@+id/vip_tv_character_name"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/host_ic_avatar_default_rectangle" />

    <TextView
        android:id="@+id/vip_tv_character_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/host_x4"
        android:layout_marginTop="@dimen/host_y6"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:textColor="@color/host_color_393942_dcdcdc"
        android:textFontWeight="500"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vip_iv_character_cover"
        tools:background="@color/host_color_ff4444"
        tools:text="角色名称" />

    <TextView
        android:id="@+id/vip_tv_character_intro"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/host_x4"
        android:layout_marginTop="@dimen/host_y6"
        android:ellipsize="end"
        android:gravity="center"
        android:lines="1"
        android:textColor="@color/host_color_acacaf_66666b"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vip_tv_character_name"
        tools:background="@color/host_color_111111"
        tools:text="最多配六个字" />

</androidx.constraintlayout.widget.ConstraintLayout>