<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/vip_frag_svip_open"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/vip_cv_svip_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/host_transparent"
        android:orientation="vertical"
        app:cardBackgroundColor="@color/host_transparent"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/vip_bg_svip_dialog_title"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="@color/host_color_green" />

            <View
                android:id="@+id/vip_view_top_handle"
                android:layout_width="32dp"
                android:layout_height="4dp"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:alpha="0.2"
                android:background="@drawable/vip_bg_rect_f8f8f8_radius_8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/vip_iv_top_title"
                android:layout_width="wrap_content"
                android:layout_height="22dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="12dp"
                android:scaleType="fitXY"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vip_view_top_handle"
                tools:background="@color/host_white"
                tools:layout_height="22dp"
                tools:layout_width="152dp" />

            <TextView
                android:id="@+id/vip_tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/vip_iv_top_title"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="4dp"
                android:textColor="#FFD8AE"
                app:layout_constraintEnd_toEndOf="parent"
                android:maxLines="1"
                android:ellipsize="end"
                android:paddingHorizontal="@dimen/host_default_side_margin"
                android:alpha="0.85"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vip_iv_top_title"
                tools:text="本专辑为“典藏大师课”系列专辑，开通SVIP会员畅听" />

            <View
                android:layout_width="0dp"
                android:layout_height="32dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vip_tv_subtitle"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <View
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:layout_alignBottom="@+id/vip_cv_svip_title"
        android:background="@drawable/vip_bg_svip_scroll_top" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/vip_cl_bottom_info"
        android:layout_below="@+id/vip_cv_svip_title"
        android:background="@color/host_color_ffffff_131313"
        android:orientation="vertical">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/vip_inner_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:background="@color/host_black"
                    android:scrollbars="none">

                    <LinearLayout
                        android:id="@+id/vip_ll_svip_skus_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/host_x12"
                        tools:layout_height="190dp" />
                </HorizontalScrollView>

                <RelativeLayout
                    android:id="@+id/vip_item_purchase_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/host_default_side_margin"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="6dp"
                    android:focusable="true"
                    android:layout_marginRight="@dimen/host_default_side_margin">
                    <!--                    <TextView-->
                    <com.ximalaya.ting.android.vip.view.VipExpandableContentTextView
                        android:id="@+id/vip_item_purchase_description_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:lineHeight="18dp"
                        android:textColor="@color/host_color_aaaaaa_66666b"
                        android:textSize="11sp"
                        app:vip_button_icon_Tag="tag_text_button_icon"
                        app:vip_button_text_Tag="tag_text_button"
                        app:vip_button_text_color="#909cb6"
                        app:vip_limit_max_line="2"
                        tools:text="售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案售卖说明文案" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignRight="@id/vip_item_purchase_description_text"
                        android:layout_alignBottom="@id/vip_item_purchase_description_text"
                        android:layout_marginRight="13dp"
                        android:layout_marginBottom="-3dp"
                        android:paddingTop="3dp"
                        android:paddingBottom="3dp"
                        android:tag="tag_text_button"
                        android:textColor="#909cb6"
                        android:textSize="11sp"
                        android:visibility="gone"
                        tools:text="  展开"
                        tools:visibility="visible" />

                    <ImageView
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:layout_alignRight="@id/vip_item_purchase_description_text"
                        android:layout_alignBottom="@id/vip_item_purchase_description_text"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="3dp"
                        android:rotation="90"
                        android:scaleType="fitXY"
                        android:src="@drawable/host_ic_standard_arrow_right_12_12"
                        android:tag="tag_text_button_icon"
                        android:tint="#909cb6"
                        android:visibility="gone"
                        tools:visibility="visible" />

                </RelativeLayout>
                <androidx.constraintlayout.utils.widget.ImageFilterView
                    android:id="@+id/vip_iv_privilege_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/host_default_side_margin"
                    android:layout_marginTop="20dp"
                    android:scaleType="fitXY"
                    android:visibility="gone"
                    tools:background="@color/host_red"
                    tools:layout_height="138dp" />

                <LinearLayout
                    android:id="@+id/vip_ll_extra_action_entrance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/host_default_side_margin"
                    android:layout_marginTop="8dp"
                    android:orientation="vertical" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/vip_cl_bottom_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/host_color_ffffff_131313">

        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/host_color_000000_8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


<!--        <LinearLayout-->
<!--            android:id="@+id/vip_ll_rule"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_marginTop="12dp"-->
<!--            android:layout_marginBottom="12dp"-->
<!--            android:gravity="center"-->
<!--            android:orientation="horizontal"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintTop_toBottomOf="@+id/vip_master_buy_action_tv">-->

<!--            <CheckBox-->
<!--                android:id="@+id/vip_tv_purchase_dialog_button_rule_prefix"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginRight="4dp"-->
<!--                android:button="@drawable/host_vip_purchase_protocol_checkbox_v2"-->
<!--                android:clickable="false"-->
<!--                android:paddingLeft="4dp"-->
<!--                android:text="开通前请阅读"-->
<!--                android:textColor="@color/vip_color_b3b3b3_8d8d91"-->
<!--                android:textSize="11dp"-->
<!--                tools:visibility="visible" />-->

<!--            <TextView-->
<!--                android:id="@+id/vip_tv_purchase_dialog_button_rule"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginRight="4dp"-->
<!--                android:textColor="@color/vip_color_b3b3b3_8d8d91"-->
<!--                android:textSize="11dp"-->
<!--                android:visibility="visible"-->
<!--                tools:text="《大师课服务协议》"-->
<!--                tools:visibility="visible" />-->


<!--        </LinearLayout>-->

        <RelativeLayout
            android:id="@+id/vip_rl_purchase_dialog_protocol"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vip_master_buy_action_tv">

            <LinearLayout
                android:id="@+id/vip_ll_rule"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginHorizontal="@dimen/host_x16"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingTop="@dimen/host_y12"
                android:paddingBottom="@dimen/host_y12">

                <ImageView
                    android:id="@+id/vip_iv_purchase_dialog_protocol_checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/host_vip_purchase_protocol_checkbox_v2" />

                <TextView
                    android:id="@+id/vip_tv_purchase_dialog_button_rule"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="-1dp"
                    android:ellipsize="end"
                    android:gravity="top|left"
                    android:includeFontPadding="false"
                    android:maxLines="2"
                    android:minLines="2"
                    android:textColor="@color/vip_color_b3b3b3_8d8d91"
                    android:textSize="11dp"
                    android:visibility="visible"
                    tools:text="开通前请阅读《会员服务协议》"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- 勾选框的点击区域。由于在协议一行时要居中，两行时不居中，且勾选框的点击区域要到最左边。点击区域不好处理，因此这里做一个点击区域 -->
            <View
                android:id="@+id/vip_v_purchase_dialog_protocol_checkbox_click_area"
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/vip_ll_rule"
                android:layout_alignBottom="@+id/vip_ll_rule"
                android:layout_marginStart="-60dp"
                android:layout_alignStart="@+id/vip_ll_rule"/>
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/vip_master_buy_action_tv"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="@dimen/host_default_side_margin"
            android:background="@drawable/vip_bg_svip_btn_right"
            android:paddingLeft="16dp"
            android:paddingRight="22dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/vip_svip_buy_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="立即购买"
                android:textColor="#333949"
                android:textFontWeight="600"
                android:textSize="15dp"
                android:visibility="visible"
                tools:visibility="visible" />
        </FrameLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:clipToPadding="false"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/vip_master_buy_action_tv"
            app:layout_constraintEnd_toEndOf="@+id/vip_master_middle_image"
            app:layout_constraintStart_toStartOf="parent">

            <View
                android:id="@+id/vip_view_master_price_bg"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginLeft="@dimen/host_default_side_margin"
                android:layout_marginTop="8dp"
                android:background="@drawable/vip_bg_svip_btn_left"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


            <TextView
                android:id="@+id/vip_tv_price_symbol"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:fontFamily="sans-serif-medium"
                android:includeFontPadding="false"
                android:text="¥"
                android:textColor="#FFD0A9"
                android:textSize="11dp"
                app:layout_constraintBaseline_toBaselineOf="@+id/vip_price_tv"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/vip_view_master_price_bg"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/vip_price_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:textColor="#FFD0A9"
                android:textSize="23dp"
                android:textFontWeight="700"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/vip_tv_price_symbol"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="699" />

            <TextView
                android:id="@+id/vip_price_unit_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:text="/年"
                android:textColor="#FFD0A9"
                android:textSize="11dp"
                android:visibility="visible"
                app:layout_constraintBaseline_toBaselineOf="@+id/vip_price_tv"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/vip_price_tv"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/vip_tv_desc_single_line"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="24dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#FFD0A9"
                android:textSize="12dp"
                android:visibility="visible"
                app:layout_constraintBaseline_toBaselineOf="@+id/vip_price_tv"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/vip_price_unit_tv"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="VIP会员立减¥200"
                tools:visibility="invisible" />

            <TextView
                android:id="@+id/vip_tv_desc_double_line"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="24dp"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="2"
                android:textColor="#FFD0A9"
                android:textSize="12dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/vip_price_unit_tv"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="VIP会员立减¥200,VIP会员立减¥200"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/vip_master_middle_image"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            android:src="@drawable/vip_ic_master_class_triangle2"
            app:layout_constraintBottom_toBottomOf="@+id/vip_master_buy_action_tv"
            app:layout_constraintRight_toLeftOf="@+id/vip_master_buy_action_tv"
            app:layout_constraintTop_toTopOf="@+id/vip_master_buy_action_tv"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>