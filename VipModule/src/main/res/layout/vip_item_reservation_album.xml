<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="138dp"
    android:layout_height="wrap_content"
    android:layout_marginRight="@dimen/host_x10">

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/vip_iv_reservation_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@color/vip_color_d8ffffff_d8131313"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="8dp"
        tools:background="@color/host_color_green" />

    <LinearLayout
        android:id="@+id/vip_ll_reservation_top"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/vip_tv_online_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/host_color_ffffff"
            android:textFontWeight="600"
            android:textSize="14dp"
            tools:text="5.15上线" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="29dp"
                android:layout_gravity="center_horizontal|bottom"
                android:scaleType="fitXY"
                android:src="@drawable/vip_bg_reservation_album_cover_shadow" />

            <com.ximalaya.ting.android.host.view.AlbumCoverImageFilterView
                android:id="@+id/vip_iv_album_cover"
                android:layout_width="82dp"
                android:layout_height="82dp"
                android:layout_gravity="center_horizontal"
                android:scaleType="centerCrop"
                app:round="3dp"
                tools:src="@color/purple_200" />

            <androidx.constraintlayout.utils.widget.ImageFilterView
                android:id="@+id/vip_iv_album_cover_cd"
                android:layout_width="82dp"
                android:layout_height="82dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="17dp"
                android:src="@drawable/host_album_cover_roght_bg" />
        </FrameLayout>


        <TextView
            android:id="@+id/vip_tv_album_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/host_x10"
            android:layout_marginBottom="3dp"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:minHeight="53dp"
            android:minLines="2"
            android:textColor="@color/host_color_131313_ffffff"
            android:textSize="14sp"
            tools:text="滚雪球｜如何“榨干”寸土寸金的新加坡" />

    </LinearLayout>


    <LinearLayout
        android:id="@+id/vip_ll_reservation_bottom"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="13.5dp"
        android:layout_marginBottom="12dp"
        android:background="@drawable/vip_bg_reservation_bottom_mask"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vip_ll_reservation_top">

        <TextView
            android:id="@+id/vip_tv_reservation_action"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textColor="@color/host_color_131313_ffffff"
            android:textSize="12sp"
            tools:text="立即预约" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>