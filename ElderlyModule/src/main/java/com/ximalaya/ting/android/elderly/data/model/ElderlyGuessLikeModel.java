package com.ximalaya.ting.android.elderly.data.model;

import com.google.gson.annotations.SerializedName;
import com.ximalaya.ting.android.host.model.album.AlbumSubscript;

public class ElderlyGuessLikeModel {
    @SerializedName("album_id")
    private long albumId;
    private String title;
    private String img;
    @SerializedName("script")
    private AlbumSubscript albumSubscript;
    @SerializedName(value = "play_count")
    private long playCount;
    @SerializedName(value = "trace_data")
    private ElderlyTraceData traceData;

    public long getAlbumId() {
        return albumId;
    }

    public void setAlbumId(long albumId) {
        this.albumId = albumId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public AlbumSubscript getAlbumSubscript() {
        return albumSubscript;
    }

    public void setAlbumSubscript(AlbumSubscript albumSubscript) {
        this.albumSubscript = albumSubscript;
    }

    public long getPlayCount() {
        return playCount;
    }

    public void setPlayCount(long playCount) {
        this.playCount = playCount;
    }

    public ElderlyTraceData getTraceData() {
        return traceData;
    }

    public void setTraceData(ElderlyTraceData traceData) {
        this.traceData = traceData;
    }
}
