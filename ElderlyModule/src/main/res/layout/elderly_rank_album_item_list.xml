<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingEnd="16dp"
    tools:background="@color/elderly_color_white"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:id="@+id/elderly_rank_sort_layout"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/elderly_item_history_divider"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="56dp"
        android:layout_height="0dp">

        <ImageView
            android:id="@+id/elderly_iv_top_ranking"
            android:layout_gravity="center"
            android:layout_width="25dp"
            android:layout_height="32dp"
            tools:src="@drawable/elderly_icon_rank_top1"/>

        <TextView
            android:id="@+id/elderly_tv_ranking"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:textColor="@color/elderly_color_bbbbbb"
            android:textSize="18sp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="1" />


    </FrameLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/elderly_item_subscribe_card"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toEndOf="@+id/elderly_rank_sort_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/elderly_item_history_album_cardview"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="@color/elderly_color_transparent"
            app:cardElevation="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="100dp"
            android:layout_height="100dp" >

            <com.ximalaya.ting.android.framework.view.image.RoundImageView
                android:id="@+id/elderly_item_history_iv"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:padding="1px"
                android:background="@drawable/elderly_bg_album_list_stroke"
                tools:src="@drawable/host_default_album"
                app:corner_radius="12dp"
                android:scaleType="centerCrop" />

            <ImageView
                android:id="@+id/elderly_item_album_jp_iv"
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:scaleType="fitXY"
                android:visibility="gone"/>


        </androidx.cardview.widget.CardView>



        <TextView
            android:id="@+id/elderly_item_history_album_tv"
            app:layout_constraintTop_toTopOf="@+id/elderly_item_history_album_cardview"
            app:layout_constraintStart_toEndOf="@+id/elderly_item_history_album_cardview"
            app:layout_constraintEnd_toEndOf="parent"
            android:maxLines="2"
            android:ellipsize="end"
            android:textSize="15sp"
            android:layout_marginStart="12dp"
            android:lineSpacingMultiplier="1.3"
            tools:text="专辑名专辑名专辑名专辑名"
            android:textStyle="bold"
            android:textColor="@color/elderly_color_111111_cfcfcf"
            android:layout_width="0dp"
            android:layout_height="wrap_content" />


        <TextView
            android:id="@+id/elderly_item_count_tv"
            android:textSize="10sp"
            android:layout_marginStart="12dp"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/elderly_item_history_album_cardview"
            app:layout_constraintStart_toEndOf="@+id/elderly_item_history_album_cardview"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="1028"
            android:textColor="@color/elderly_color_666666_888888"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />





    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:id="@+id/elderly_item_history_divider"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toBottomOf="@+id/elderly_item_subscribe_card"
        android:background="@color/elderly_color_e8e8e8_2a2a2a"
        android:layout_width="match_parent"
        android:layout_height="1px" />



</androidx.constraintlayout.widget.ConstraintLayout>