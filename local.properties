## This file must *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.
#Mon Oct 11 16:19:25 CST 2021
sdk.dir=/Users/<USER>/Library/Android/sdk
DEV_BUNDLE_MODE=true
#SYNC_BUNDLE=main,login
BUILD_SO_BUNDLE=ad
BUILD_IN_BUNDLE=main,login,video,commercial,search,reactnative,live
#BUILD_ALL_INCLUDES=main,video,login,search,reactnative,mylisten,read,readsupport,commercial,car,record,ad,elderly,vip,ad
#BUILD_IN_BUNDLE=main,video,login,search,reactnative,mylisten,read,readsupport,commercial,car,record,ad,elderly,vip

buildFast=false

APP_VERSION_CODE=366
APP_VERSION = 9.3.100
BUNDLE_VERSION=380.10
OPEN_LEAKCANARY=true
###??abi ????????????
ABIS=arm64-v8a
#  armeabi-v7a
#  arm64-v8a
#????????????????so ??????
CP_SO=true
REALOBFUSCATE=false
